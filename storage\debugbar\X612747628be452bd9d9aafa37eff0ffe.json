{"__meta": {"id": "X612747628be452bd9d9aafa37eff0ffe", "datetime": "2025-07-31 06:24:30", "utime": **********.06643, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943068.041983, "end": **********.066479, "duration": 2.024496078491211, "duration_str": "2.02s", "measures": [{"label": "Booting", "start": 1753943068.041983, "relative_start": 0, "end": 1753943069.88491, "relative_end": 1753943069.88491, "duration": 1.8429272174835205, "duration_str": "1.84s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753943069.884941, "relative_start": 1.****************, "end": **********.066483, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "182ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1UGNaY0DxjMYtpGTameUM6J4CaCGSlPZ010tIlIf", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-1459467716 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1459467716\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-935178387 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-935178387\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-210227376 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-210227376\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1414309705 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1414309705\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2137223077 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2137223077\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-545749300 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:24:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVaN2FqSEx0OHdUOVN0MUhUanU4N0E9PSIsInZhbHVlIjoiQUpLdVVGR0dlVDhINDVXWXZmSTk3TUZTY0kzSFk1d1kvQ1NtOEh0YmxDZVl0aXBRUWtLcEQ5QnIzcFRYSWE0OVFnaXNNenBRL3gwQW44aDIvQnpCR3VDU1J5Wm1VN01ROTVRVGV6YkVGdkZLalEwUWgwZ0t3Vk1WOUFCWU5EV2t3SUhMSVdwVkFXYThYU0R0RFdyVXZ1VkpIWGJXcTdWZ0hTditHMkZTV2lHd1hyRHNYSUl2QUM4Rk1Cb3Brd1RhTU5acUw5ZDd3UGpIMDdNNkh5alc4Skl3YVdQWE1yN2N6azR2TjRsOWQwNElPaytEZjhoR3FOemhLYkFSQTMxK2ZBUnkxTHl5alpVcUhkeThMaG8rQk8wWkxjK0F5S0dqc0dlQkozOFRJQStyNit2V0ZKTW01MjAvMEZBTUMvdm9rdEdCeG9SNHBlNU5VZ2dXTlgvbzhzY0lpaEo4M3lSS2IwcWhFSVN5TmI2Q0xsemYwdVh2MEh5TFIrRGJXWjVpeGJ0Q0M5Q3FJM2N1OXRWYi83ZmRxY21TdzFvbi9UQUV4aGdwcDhNM0lBZjR0U3I4cVRCMDY3aTlKZUlzZ2E2bm9qQ0V0NEYzYjlMRmpDMDIwVG9tNXR6czhHR3h2YUxUQWFZM1VYMVR0SUVkcjBteEg2ZE40T1h5NGVoTUJCUjYiLCJtYWMiOiI5NWQzZWZiMDIzMGE4ZGQ2NzYwYjQ5NGIxMTRkMGJiMzhjYTczYjY4YTI5Zjk2NTBmM2NhODM5MWU2NjlmNWEwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:24:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im1XMHBlY0dkNithWnpZVWxxWCtTUUE9PSIsInZhbHVlIjoieWZSS3MxQjg5YlQwcHM4OGJxelpPdnltNU0zYVR0dG1menlucGlEN1NaTVNTRGZnNWRsQ0hjd1RiK2JkaDh4OHZLRXFmT1N2d1FsNHoyY3R4VXZzVjZUK1l0OWhkaGVLUFJJUSt5L04rZEtuSTQvS3dKVGRsdytZV0ZkNlNpcW9Ba0Z3UUxETDJaT1pPQmtqQnB2ajhXc3BYZjNTRWlTMktlMi9SQ3Q5MVVteUFPS3QwenozVzlWNWRnRzVDVXY5a3VWdUFLN2czOGhNUWh2ajdVaWZWT1hyWlZYRG94L0pvczBQV0RQaTIyK0xicXBzdXo1cUxSV1cxVlcvWkpNaDVTb2ZNb2VGckNweTY5UnQzZEgvK3owNDUwNHo5Y0lIei9EazZyR21pakp6QTNacE8zSTVuYzlPNUFnTUJ0TkxCSEQ3S0dVd3JGNW1aS241aG1FK1RkMFZrTzlueWNoYlpJM0tvaVNCNk43a1pIYzhKWGlPSEpXZkxCUk9FK1lzYWVjZUdFZW1aTEJwQlVNbkxUalUzT1RWZkhhM0VxeDNPeXBNZXp4ZnExVlVKcnlPUldrUG9aNVozbGhaeUpXZVlmdVNOWE5WRUlWcTZrVFRvVTQrYjZOSjI3aE5zMEJERGRNYjMyc0R6bDJISGdXNVVoNWZXdXNEbGFMYnpzNUciLCJtYWMiOiIyMTY3MWQ4ZmVjZjI0MzkwOWQ2MDVjMDhjYjE1Y2IzZmQwMzU4ZDgzMWZiNzQwNDhkNjU0YWMyNTE1MDcwNTNlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:24:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVaN2FqSEx0OHdUOVN0MUhUanU4N0E9PSIsInZhbHVlIjoiQUpLdVVGR0dlVDhINDVXWXZmSTk3TUZTY0kzSFk1d1kvQ1NtOEh0YmxDZVl0aXBRUWtLcEQ5QnIzcFRYSWE0OVFnaXNNenBRL3gwQW44aDIvQnpCR3VDU1J5Wm1VN01ROTVRVGV6YkVGdkZLalEwUWgwZ0t3Vk1WOUFCWU5EV2t3SUhMSVdwVkFXYThYU0R0RFdyVXZ1VkpIWGJXcTdWZ0hTditHMkZTV2lHd1hyRHNYSUl2QUM4Rk1Cb3Brd1RhTU5acUw5ZDd3UGpIMDdNNkh5alc4Skl3YVdQWE1yN2N6azR2TjRsOWQwNElPaytEZjhoR3FOemhLYkFSQTMxK2ZBUnkxTHl5alpVcUhkeThMaG8rQk8wWkxjK0F5S0dqc0dlQkozOFRJQStyNit2V0ZKTW01MjAvMEZBTUMvdm9rdEdCeG9SNHBlNU5VZ2dXTlgvbzhzY0lpaEo4M3lSS2IwcWhFSVN5TmI2Q0xsemYwdVh2MEh5TFIrRGJXWjVpeGJ0Q0M5Q3FJM2N1OXRWYi83ZmRxY21TdzFvbi9UQUV4aGdwcDhNM0lBZjR0U3I4cVRCMDY3aTlKZUlzZ2E2bm9qQ0V0NEYzYjlMRmpDMDIwVG9tNXR6czhHR3h2YUxUQWFZM1VYMVR0SUVkcjBteEg2ZE40T1h5NGVoTUJCUjYiLCJtYWMiOiI5NWQzZWZiMDIzMGE4ZGQ2NzYwYjQ5NGIxMTRkMGJiMzhjYTczYjY4YTI5Zjk2NTBmM2NhODM5MWU2NjlmNWEwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:24:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im1XMHBlY0dkNithWnpZVWxxWCtTUUE9PSIsInZhbHVlIjoieWZSS3MxQjg5YlQwcHM4OGJxelpPdnltNU0zYVR0dG1menlucGlEN1NaTVNTRGZnNWRsQ0hjd1RiK2JkaDh4OHZLRXFmT1N2d1FsNHoyY3R4VXZzVjZUK1l0OWhkaGVLUFJJUSt5L04rZEtuSTQvS3dKVGRsdytZV0ZkNlNpcW9Ba0Z3UUxETDJaT1pPQmtqQnB2ajhXc3BYZjNTRWlTMktlMi9SQ3Q5MVVteUFPS3QwenozVzlWNWRnRzVDVXY5a3VWdUFLN2czOGhNUWh2ajdVaWZWT1hyWlZYRG94L0pvczBQV0RQaTIyK0xicXBzdXo1cUxSV1cxVlcvWkpNaDVTb2ZNb2VGckNweTY5UnQzZEgvK3owNDUwNHo5Y0lIei9EazZyR21pakp6QTNacE8zSTVuYzlPNUFnTUJ0TkxCSEQ3S0dVd3JGNW1aS241aG1FK1RkMFZrTzlueWNoYlpJM0tvaVNCNk43a1pIYzhKWGlPSEpXZkxCUk9FK1lzYWVjZUdFZW1aTEJwQlVNbkxUalUzT1RWZkhhM0VxeDNPeXBNZXp4ZnExVlVKcnlPUldrUG9aNVozbGhaeUpXZVlmdVNOWE5WRUlWcTZrVFRvVTQrYjZOSjI3aE5zMEJERGRNYjMyc0R6bDJISGdXNVVoNWZXdXNEbGFMYnpzNUciLCJtYWMiOiIyMTY3MWQ4ZmVjZjI0MzkwOWQ2MDVjMDhjYjE1Y2IzZmQwMzU4ZDgzMWZiNzQwNDhkNjU0YWMyNTE1MDcwNTNlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:24:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-545749300\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-781775752 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1UGNaY0DxjMYtpGTameUM6J4CaCGSlPZ010tIlIf</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-781775752\", {\"maxDepth\":0})</script>\n"}}