{"__meta": {"id": "Xa344f192ba7de0f0c019c3d5b9a19e33", "datetime": "2025-07-31 06:24:24", "utime": **********.126068, "method": "GET", "uri": "/login-with-company/exit", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943061.7354, "end": **********.126131, "duration": 2.3907310962677, "duration_str": "2.39s", "measures": [{"label": "Booting", "start": 1753943061.7354, "relative_start": 0, "end": 1753943063.915582, "relative_end": 1753943063.915582, "duration": 2.1801819801330566, "duration_str": "2.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753943063.91561, "relative_start": 2.1802101135253906, "end": **********.126136, "relative_end": 5.0067901611328125e-06, "duration": 0.2105259895324707, "duration_str": "211ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44678072, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login-with-company/exit", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@ExitCompany", "namespace": null, "prefix": "", "where": [], "as": "exit.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1476\" onclick=\"\">app/Http/Controllers/UserController.php:1476-1480</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.03319, "accumulated_duration_str": "33.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0328958, "duration": 0.03202, "duration_str": "32.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 96.475}, {"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Services/ImpersonateManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\lab404\\laravel-impersonate\\src\\Services\\ImpersonateManager.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Services/ImpersonateManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\lab404\\laravel-impersonate\\src\\Services\\ImpersonateManager.php", "line": 137}, {"index": 19, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Models/Impersonate.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\lab404\\laravel-impersonate\\src\\Models\\Impersonate.php", "line": 64}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\UserController.php", "line": 1478}], "start": **********.083673, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 96.475, "width_percent": 3.525}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login-with-company/exit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login-with-company/exit", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1117787492 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1117787492\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1596460291 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1596460291\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkdqaEtmU0NodjVMVVVuM2dIL29KNkE9PSIsInZhbHVlIjoiUDdzMU5yTVZpWnJFZG1sVEhUbzk2bHVQckxpR2h3NjlxVnQyNVJNVU8zKzRtbVFnOXJma1NudTNvYjFIeTFHSUlBM0lrbTB6VVBCbUNpOFZ6bHYvdVRTbldnY3JNc0s0T3BZVkowdzc0Tk45TWFVaFNlMGh2TjQ4VjRHUTdoTkZOcFJYc242KzJlU0p1NGk3WDdoNGQ4ZHR6M1Y5cmc5MlYxdGZleHpld1VtYloxZzA5RWRRUktSNkhNbFI0bmhiZTA0REFLR2UreU5UQkRNRmtOajRxa2xtU1lOa1dKL0lWak4rVXlEWmQycld5VFJvM0Z4NHJKQ2VVWEFTQlhwRVllS3pLQ2Q4dWtseEZkcjAzeFU5QlRERWtwLzFPZFNOWEkvSysxc1E1NUtGTGtjZ1JwdHVrV0xSeVh1Y0MzUlptM1crVVpjSHNUK2xraGZJZ1RMUE9DRFNBMzF4dlZsSWtGeXRVWGNJaWZKSXA2Rm16ZUVaN0dJVlVqSkxWQUErTU96RzJoM2dlR0tKWFVVRnY1cjZGUnVuRjkzZ0t0SFVPcjJOUU1NWUwzY2NMQzhpN3VHK0ZtRnpiZnExZEVQQ09nNE5JRUdxbnEyT0RnM0FXaFdXSTNDVlF6ekhrS1pEVitzblBsSXo1M08zZEh3L1M4NzVQckRPQzRvQWt1ODkiLCJtYWMiOiJiZjljNTRiYTAzOGZjYjZmODRlNGEyN2UwYmNjNDc4MjlkN2Y0MzVhMDU1N2U4ZjEyZjVkYzA3NzljODRjZWE0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkFTcjdhTGtSWnJTU0ZLZWN2MGFtREE9PSIsInZhbHVlIjoiMW9qMzVxOGpZSkFNMm1PRjB3WXJjeGxVMEZudGIvVi9VS1BrdEJqdFhEVWJST3VnejhwQkpwY05DOWsrcER5V3lFOThLZDZTWUVaMzMzcDZyZ1VnOTFpMXllN1p4Y1VRRVVSd2NoVjVsQ09jeUVwRkk5OC96N3dUTko1ZlJEUXE5RGNBeWN2MEIzUlBBMTAvakg5U0c1ZDRVRnNPZFBvc1NTV1MyU1VUU0gvaHJMU1NDMDNyOTRBcEtCcHhMb0tkaWwwNUVjTmlJVEs2c0k3YU1TVUNXRldydFZiYkUwMWxPZmNWelFWb3VlelowOU9TZUNxeXg5d1lYTEtlVCtxVGJybHc4VWhXVDBuVnJoU0pWUUJKR1hJcFh0Zy9LZCtjT3FtSnRhSWpmZlJBUHBROEtwYmtwdHNsbU5uUmNlaTFpTzFVc3FBV0YvQWRETTB4TTc5Tk11Wm56WHFsV3Y5dEp2bStLN3BIeGlCblBnNW1HcDcycUNWTXo0d2c0aWJMZ0M0SnlJdHVMbW13WGtpNE1LbXppZnNTTEEySE9OckJ0SUVNbnNnbk0ycWN1aVViWlBWcy91WEZtYUw4ZmpTdlNsTE9rQi9wM3lwaW40RTFNeVlFdGpoQS9FWGRXc3R4am5sbXNJRzZsMSt1QkNUU1pTK0d0bWpwTmNzQjNJbG0iLCJtYWMiOiJlYTIzNDMxNTU3MTczYjQwM2RkNGM4Y2Y4NmUzOWE5NTdlNDA4ZGVmM2YzODA5NDNkYTE5MWJmYjEwOWIyZTZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1936915139 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5LJND5bUclZnLzzClCMEaxKzZ5Yt8AAmGjprz0I</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1936915139\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-411955324 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:24:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1PUUp3ZUk1bU0xZFkxbHlYd01BU2c9PSIsInZhbHVlIjoiejJFWGlBQ0ZxRis0ZlAvSW13ZEJzYTdZaGtVMHhPU3czVGVoRmhiZFdIMCsyTmtTLzJIZndWblFCZHdXQ1R6WnJmelZkYi9hNzY5K3d0OHc1UWZ1ZnlRTFhGdVplbUh4YlNBUjZvakxHRWZkRkFnWE9PN0hVNkJTQmh6c0xNODV0OXEzVkRUVHM1WkxUeURlc0ZsVGtYcVAxL3BXeENPUkhSdjRKaDhKL2dYV3Q0bE91Y2pjajBCd3FxbVBlaFQzem9ZZ0VaM3JlbEhDeWp4ejUrL1hadzRqWkplMGl4MGlrYXUxd2FVYVNXU01uaGVuZkdCNWhrZkFDV0lQQlF4N0w5SU92TWtqdlpqaC94Q3ZQcUZkMFJORUtZMG9ySnFsYTB2dExVS3Q4RDdhNjdwVEdZQUo1Q0krdCs4LzYwMXRTZVZPSU5KSmFxNTBtWFU0OVd5U2VNaUFsSUc3SnVZU1ZqVG9yT2J1NENBQktpY2QyMkE0dWV0Q0xhZVZrbzlBV3JXY25ycy9OeXQ1d1FLOFBKNDF5UTJUNmNEdFdidURNQXJOQkpGM3pyVWFWNFRHbjAwU3lLb1lwN24xRjFhZS82bi9JamJ3dFdKcSs3T2lDMGRZdENQWm5Ba0ZJLzlPRlB4Mk9RUkViZ3B2ZE13cEsxKzFzWkdoNzVyZ3NyUmIiLCJtYWMiOiJlN2Q4NWVjNjI2NTU1MTFkZmMzYzFiMGE0OGExMmRmYjE5NWZkNGMxZTI2MGM0NjcxMjE4ZmRmMjQwZGExYjMxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:24:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkxaUnYvbmdFOXp2VERSNjMyb0xyeHc9PSIsInZhbHVlIjoiQ1Nzd2laVjVkWUxMbXVSMWYrczB1WmFFMCtsY3FOcVVMQStoSkVOYmRxUDJiWWFTRU5KUlBjcnZLalp2R0VRK2E3TjZMY0VJdXo0T0I2cVBhNm1XMHNvU2F5eSs4SFZSQXNhYmthUHVXdWJQZHBsNXZOQXJVcE9NZlBIZEJRemRUMmUzNHpINmRlL2V6UEFSY0NmSnAxQnkwcUVPVC8vQTgyZDVoS1ZUY05GWXdUWC83UmZiRmhLSWJNd2lDbGlrY2oydjdMbDd1NkFhSThPbi9WZTFxbjB6UTVIeWtVTkxhSEY1VlU0dm1LWWJQTTZxd1RURmJKdFdVeUFieXl4aVlYMTU2MWZkd050NytESlRVeGFTaSt5T2NMZGw0WFlxWWl4a05oOU5SVHhETnlqbXVvQ1pDVEZFZUIwZFNxdlFkWUhLRFI4Q1lqQXhrTk92UWlYYWxrUkpMMDBHR0s3RFpqbzdVNDZSeXlnNCt3ckZxZ0gvYmI0SW8yb2VtU2c2R0ZyYzhoc2lhczhxN1hiRzdTN0doQjdVZWlZajNXQVlDT3p0K0pRNkphRHNoVE03YkZPQlhyNFdmYTZKcmtHcDhEZnJEU25ZT0VQSzVsSVhYa3YwTGJWM2QxVUxMOFN5ZnZlRWo0YVkwYVhuK0JyYnZRRVc5Uks4dktvQUdTWEgiLCJtYWMiOiI2OTkyNjliYzQxYWVmYmZmMmU1NWE0MDU3MWM2Y2EzNWM5OWVlNzNkZmYxZWQ0NjllMzE0NDdmMDkwMjg5YTMxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:24:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1PUUp3ZUk1bU0xZFkxbHlYd01BU2c9PSIsInZhbHVlIjoiejJFWGlBQ0ZxRis0ZlAvSW13ZEJzYTdZaGtVMHhPU3czVGVoRmhiZFdIMCsyTmtTLzJIZndWblFCZHdXQ1R6WnJmelZkYi9hNzY5K3d0OHc1UWZ1ZnlRTFhGdVplbUh4YlNBUjZvakxHRWZkRkFnWE9PN0hVNkJTQmh6c0xNODV0OXEzVkRUVHM1WkxUeURlc0ZsVGtYcVAxL3BXeENPUkhSdjRKaDhKL2dYV3Q0bE91Y2pjajBCd3FxbVBlaFQzem9ZZ0VaM3JlbEhDeWp4ejUrL1hadzRqWkplMGl4MGlrYXUxd2FVYVNXU01uaGVuZkdCNWhrZkFDV0lQQlF4N0w5SU92TWtqdlpqaC94Q3ZQcUZkMFJORUtZMG9ySnFsYTB2dExVS3Q4RDdhNjdwVEdZQUo1Q0krdCs4LzYwMXRTZVZPSU5KSmFxNTBtWFU0OVd5U2VNaUFsSUc3SnVZU1ZqVG9yT2J1NENBQktpY2QyMkE0dWV0Q0xhZVZrbzlBV3JXY25ycy9OeXQ1d1FLOFBKNDF5UTJUNmNEdFdidURNQXJOQkpGM3pyVWFWNFRHbjAwU3lLb1lwN24xRjFhZS82bi9JamJ3dFdKcSs3T2lDMGRZdENQWm5Ba0ZJLzlPRlB4Mk9RUkViZ3B2ZE13cEsxKzFzWkdoNzVyZ3NyUmIiLCJtYWMiOiJlN2Q4NWVjNjI2NTU1MTFkZmMzYzFiMGE0OGExMmRmYjE5NWZkNGMxZTI2MGM0NjcxMjE4ZmRmMjQwZGExYjMxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:24:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkxaUnYvbmdFOXp2VERSNjMyb0xyeHc9PSIsInZhbHVlIjoiQ1Nzd2laVjVkWUxMbXVSMWYrczB1WmFFMCtsY3FOcVVMQStoSkVOYmRxUDJiWWFTRU5KUlBjcnZLalp2R0VRK2E3TjZMY0VJdXo0T0I2cVBhNm1XMHNvU2F5eSs4SFZSQXNhYmthUHVXdWJQZHBsNXZOQXJVcE9NZlBIZEJRemRUMmUzNHpINmRlL2V6UEFSY0NmSnAxQnkwcUVPVC8vQTgyZDVoS1ZUY05GWXdUWC83UmZiRmhLSWJNd2lDbGlrY2oydjdMbDd1NkFhSThPbi9WZTFxbjB6UTVIeWtVTkxhSEY1VlU0dm1LWWJQTTZxd1RURmJKdFdVeUFieXl4aVlYMTU2MWZkd050NytESlRVeGFTaSt5T2NMZGw0WFlxWWl4a05oOU5SVHhETnlqbXVvQ1pDVEZFZUIwZFNxdlFkWUhLRFI4Q1lqQXhrTk92UWlYYWxrUkpMMDBHR0s3RFpqbzdVNDZSeXlnNCt3ckZxZ0gvYmI0SW8yb2VtU2c2R0ZyYzhoc2lhczhxN1hiRzdTN0doQjdVZWlZajNXQVlDT3p0K0pRNkphRHNoVE03YkZPQlhyNFdmYTZKcmtHcDhEZnJEU25ZT0VQSzVsSVhYa3YwTGJWM2QxVUxMOFN5ZnZlRWo0YVkwYVhuK0JyYnZRRVc5Uks4dktvQUdTWEgiLCJtYWMiOiI2OTkyNjliYzQxYWVmYmZmMmU1NWE0MDU3MWM2Y2EzNWM5OWVlNzNkZmYxZWQ0NjllMzE0NDdmMDkwMjg5YTMxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:24:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411955324\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2124295528 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://127.0.0.1:8000/login-with-company/exit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2124295528\", {\"maxDepth\":0})</script>\n"}}