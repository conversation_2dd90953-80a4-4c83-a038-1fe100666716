

<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Edit Pricing Plan')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('system-admin.dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('pricing-plans.index')); ?>"><?php echo e(__('Pricing Plans')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Edit')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><?php echo e(__('Edit Pricing Plan')); ?>: <?php echo e($pricingPlan->name); ?></h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo e(route('pricing-plans.update', $pricingPlan)); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label"><?php echo e(__('Plan Name')); ?> <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="name" name="name" value="<?php echo e(old('name', $pricingPlan->name)); ?>" required>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="plan_type" class="form-label"><?php echo e(__('Plan Type')); ?> <span class="text-danger">*</span></label>
                                    <select class="form-control <?php $__errorArgs = ['plan_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                            id="plan_type" name="plan_type" required>
                                        <option value=""><?php echo e(__('Select Plan Type')); ?></option>
                                        <?php $__currentLoopData = $planTypeOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($key); ?>" <?php echo e(old('plan_type', $pricingPlan->plan_type) == $key ? 'selected' : ''); ?>>
                                                <?php echo e($value); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['plan_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="price" class="form-label"><?php echo e(__('Price')); ?> <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" step="0.01" min="0" 
                                               class="form-control <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="price" name="price" value="<?php echo e(old('price', $pricingPlan->price)); ?>" required>
                                    </div>
                                    <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="duration" class="form-label"><?php echo e(__('Duration')); ?> <span class="text-danger">*</span></label>
                                    <select class="form-control <?php $__errorArgs = ['duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                            id="duration" name="duration" required>
                                        <option value=""><?php echo e(__('Select Duration')); ?></option>
                                        <?php $__currentLoopData = $durationOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($key); ?>" <?php echo e(old('duration', $pricingPlan->duration) == $key ? 'selected' : ''); ?>>
                                                <?php echo e($value); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status" class="form-label"><?php echo e(__('Status')); ?> <span class="text-danger">*</span></label>
                                    <select class="form-control <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                            id="status" name="status" required>
                                        <?php $__currentLoopData = $statusOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($key); ?>" <?php echo e(old('status', $pricingPlan->status) == $key ? 'selected' : ''); ?>>
                                                <?php echo e($value); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-group">
                                    <label for="description" class="form-label"><?php echo e(__('Description')); ?></label>
                                    <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                              id="description" name="description" rows="3"><?php echo e(old('description', $pricingPlan->description)); ?></textarea>
                                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Limits -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="max_users" class="form-label"><?php echo e(__('Max Users')); ?> <span class="text-danger">*</span></label>
                                    <input type="number" min="0" 
                                           class="form-control <?php $__errorArgs = ['max_users'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="max_users" name="max_users" value="<?php echo e(old('max_users', $pricingPlan->max_users)); ?>" required>
                                    <small class="text-muted"><?php echo e(__('Use -1 for unlimited')); ?></small>
                                    <?php $__errorArgs = ['max_users'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="max_customers" class="form-label"><?php echo e(__('Max Customers')); ?> <span class="text-danger">*</span></label>
                                    <input type="number" min="0" 
                                           class="form-control <?php $__errorArgs = ['max_customers'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="max_customers" name="max_customers" value="<?php echo e(old('max_customers', $pricingPlan->max_customers)); ?>" required>
                                    <small class="text-muted"><?php echo e(__('Use -1 for unlimited')); ?></small>
                                    <?php $__errorArgs = ['max_customers'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="max_vendors" class="form-label"><?php echo e(__('Max Vendors')); ?> <span class="text-danger">*</span></label>
                                    <input type="number" min="0" 
                                           class="form-control <?php $__errorArgs = ['max_vendors'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="max_vendors" name="max_vendors" value="<?php echo e(old('max_vendors', $pricingPlan->max_vendors)); ?>" required>
                                    <small class="text-muted"><?php echo e(__('Use -1 for unlimited')); ?></small>
                                    <?php $__errorArgs = ['max_vendors'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="max_clients" class="form-label"><?php echo e(__('Max Clients')); ?> <span class="text-danger">*</span></label>
                                    <input type="number" min="0" 
                                           class="form-control <?php $__errorArgs = ['max_clients'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="max_clients" name="max_clients" value="<?php echo e(old('max_clients', $pricingPlan->max_clients)); ?>" required>
                                    <small class="text-muted"><?php echo e(__('Use -1 for unlimited')); ?></small>
                                    <?php $__errorArgs = ['max_clients'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="storage_limit" class="form-label"><?php echo e(__('Storage Limit (GB)')); ?> <span class="text-danger">*</span></label>
                                    <input type="number" step="0.1" min="0" 
                                           class="form-control <?php $__errorArgs = ['storage_limit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="storage_limit" name="storage_limit" value="<?php echo e(old('storage_limit', $pricingPlan->storage_limit)); ?>" required>
                                    <small class="text-muted"><?php echo e(__('Use -1 for unlimited')); ?></small>
                                    <?php $__errorArgs = ['storage_limit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="sort_order" class="form-label"><?php echo e(__('Sort Order')); ?></label>
                                    <input type="number" min="0" 
                                           class="form-control <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="sort_order" name="sort_order" value="<?php echo e(old('sort_order', $pricingPlan->sort_order)); ?>">
                                    <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Module Permissions -->
                            <div class="col-12 mt-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5><?php echo e(__('Module Permissions')); ?></h5>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-outline-primary me-2" id="selectAllModules">
                                            <?php echo e(__('Select All Modules')); ?>

                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAllModules">
                                            <?php echo e(__('Deselect All')); ?>

                                        </button>
                                    </div>
                                </div>
                                <hr>
                                
                                <?php $__currentLoopData = $availableModules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $moduleKey => $moduleData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $moduleSelected = isset($pricingPlan->module_permissions[$moduleKey]);
                                        $selectedPermissions = $moduleSelected ? $pricingPlan->module_permissions[$moduleKey] : [];
                                        
                                        // Categorize permissions
                                        $createPermissions = [];
                                        $readPermissions = [];
                                        $deletePermissions = [];
                                        $otherPermissions = [];
                                        
                                        foreach($moduleData['permissions'] as $permission) {
                                            if (str_contains($permission, 'create')) {
                                                $createPermissions[] = $permission;
                                            } elseif (str_contains($permission, 'view') || str_contains($permission, 'show') || str_contains($permission, 'dashboard')) {
                                                $readPermissions[] = $permission;
                                            } elseif (str_contains($permission, 'delete')) {
                                                $deletePermissions[] = $permission;
                                            } else {
                                                $otherPermissions[] = $permission;
                                            }
                                        }
                                    ?>
                                    
                                    <div class="card mb-3">
                                        <div class="card-header">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="form-check">
                                                    <input class="form-check-input module-checkbox" type="checkbox" 
                                                           id="module_<?php echo e($moduleKey); ?>" name="modules[<?php echo e($moduleKey); ?>]" 
                                                           data-module="<?php echo e($moduleKey); ?>" <?php echo e($moduleSelected ? 'checked' : ''); ?>>
                                                    <label class="form-check-label fw-bold" for="module_<?php echo e($moduleKey); ?>">
                                                        <?php echo e($moduleData['name']); ?>

                                                    </label>
                                                </div>
                                                <div class="btn-group" role="group" style="<?php echo e($moduleSelected ? 'display: block;' : 'display: none;'); ?>" id="selectButtons_<?php echo e($moduleKey); ?>">
                                                    <button type="button" class="btn btn-sm btn-outline-success select-all-module" 
                                                            data-module="<?php echo e($moduleKey); ?>">
                                                        <?php echo e(__('Select All')); ?>

                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-warning select-create" 
                                                            data-module="<?php echo e($moduleKey); ?>">
                                                        <?php echo e(__('Create')); ?>

                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-info select-read" 
                                                            data-module="<?php echo e($moduleKey); ?>">
                                                        <?php echo e(__('Read')); ?>

                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger select-delete" 
                                                            data-module="<?php echo e($moduleKey); ?>">
                                                        <?php echo e(__('Delete')); ?>

                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body permissions-section" id="permissions_<?php echo e($moduleKey); ?>" 
                                             style="<?php echo e($moduleSelected ? 'display: block;' : 'display: none;'); ?>">
                                            
                                            <?php if(count($createPermissions) > 0): ?>
                                                <div class="permission-group mb-4">
                                                    <h6 class="text-success mb-2">
                                                        <i class="fas fa-plus-circle"></i> <?php echo e(__('Create Permissions')); ?>

                                                    </h6>
                                                    <div class="row">
                                                        <?php $__currentLoopData = $createPermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="col-md-4 mb-2">
                                                                <div class="form-check">
                                                                    <input class="form-check-input permission-checkbox create-permission" type="checkbox" 
                                                                           id="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>" 
                                                                           name="permissions[<?php echo e($moduleKey); ?>][]" 
                                                                           value="<?php echo e($permission); ?>"
                                                                           data-module="<?php echo e($moduleKey); ?>"
                                                                           data-type="create"
                                                                           <?php echo e(in_array($permission, $selectedPermissions) ? 'checked' : ''); ?>>
                                                                    <label class="form-check-label" 
                                                                           for="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>">
                                                                        <?php echo e(ucwords(str_replace('_', ' ', $permission))); ?>

                                                                    </label>
                                                                </div>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                </div>
                                            <?php endif; ?>

                                            <?php if(count($readPermissions) > 0): ?>
                                                <div class="permission-group mb-4">
                                                    <h6 class="text-info mb-2">
                                                        <i class="fas fa-eye"></i> <?php echo e(__('Read/View Permissions')); ?>

                                                    </h6>
                                                    <div class="row">
                                                        <?php $__currentLoopData = $readPermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="col-md-4 mb-2">
                                                                <div class="form-check">
                                                                    <input class="form-check-input permission-checkbox read-permission" type="checkbox" 
                                                                           id="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>" 
                                                                           name="permissions[<?php echo e($moduleKey); ?>][]" 
                                                                           value="<?php echo e($permission); ?>"
                                                                           data-module="<?php echo e($moduleKey); ?>"
                                                                           data-type="read"
                                                                           <?php echo e(in_array($permission, $selectedPermissions) ? 'checked' : ''); ?>>
                                                                    <label class="form-check-label" 
                                                                           for="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>">
                                                                        <?php echo e(ucwords(str_replace('_', ' ', $permission))); ?>

                                                                    </label>
                                                                </div>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                </div>
                                            <?php endif; ?>

                                            <?php if(count($deletePermissions) > 0): ?>
                                                <div class="permission-group mb-4">
                                                    <h6 class="text-danger mb-2">
                                                        <i class="fas fa-trash"></i> <?php echo e(__('Delete Permissions')); ?>

                                                    </h6>
                                                    <div class="row">
                                                        <?php $__currentLoopData = $deletePermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="col-md-4 mb-2">
                                                                <div class="form-check">
                                                                    <input class="form-check-input permission-checkbox delete-permission" type="checkbox" 
                                                                           id="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>" 
                                                                           name="permissions[<?php echo e($moduleKey); ?>][]" 
                                                                           value="<?php echo e($permission); ?>"
                                                                           data-module="<?php echo e($moduleKey); ?>"
                                                                           data-type="delete"
                                                                           <?php echo e(in_array($permission, $selectedPermissions) ? 'checked' : ''); ?>>
                                                                    <label class="form-check-label" 
                                                                           for="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>">
                                                                        <?php echo e(ucwords(str_replace('_', ' ', $permission))); ?>

                                                                    </label>
                                                                </div>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                </div>
                                            <?php endif; ?>

                                            <?php if(count($otherPermissions) > 0): ?>
                                                <div class="permission-group mb-4">
                                                    <h6 class="text-secondary mb-2">
                                                        <i class="fas fa-cog"></i> <?php echo e(__('Other Permissions')); ?>

                                                    </h6>
                                                    <div class="row">
                                                        <?php $__currentLoopData = $otherPermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="col-md-4 mb-2">
                                                                <div class="form-check">
                                                                    <input class="form-check-input permission-checkbox other-permission" type="checkbox" 
                                                                           id="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>" 
                                                                           name="permissions[<?php echo e($moduleKey); ?>][]" 
                                                                           value="<?php echo e($permission); ?>"
                                                                           data-module="<?php echo e($moduleKey); ?>"
                                                                           data-type="other"
                                                                           <?php echo e(in_array($permission, $selectedPermissions) ? 'checked' : ''); ?>>
                                                                    <label class="form-check-label" 
                                                                           for="permission_<?php echo e($moduleKey); ?>_<?php echo e(str_replace(' ', '_', $permission)); ?>">
                                                                        <?php echo e(ucwords(str_replace('_', ' ', $permission))); ?>

                                                                    </label>
                                                                </div>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary"><?php echo e(__('Update Plan')); ?></button>
                                <a href="<?php echo e(route('pricing-plans.index')); ?>" class="btn btn-secondary"><?php echo e(__('Cancel')); ?></a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
    <script>
        $(document).ready(function() {
            // Module checkbox toggle
            $('.module-checkbox').on('change', function() {
                var module = $(this).data('module');
                var permissionsSection = $('#permissions_' + module);
                var permissionCheckboxes = permissionsSection.find('.permission-checkbox');
                var selectButtons = $('#selectButtons_' + module);
                
                if ($(this).is(':checked')) {
                    permissionsSection.show();
                    selectButtons.show();
                } else {
                    permissionsSection.hide();
                    selectButtons.hide();
                    permissionCheckboxes.prop('checked', false);
                }
            });

            // If any permission is checked, ensure module is checked
            $('.permission-checkbox').on('change', function() {
                var module = $(this).data('module');
                var moduleCheckbox = $('#module_' + module);
                var permissionCheckboxes = $('input[name="permissions[' + module + '][]"]');
                var checkedPermissions = permissionCheckboxes.filter(':checked');
                var selectButtons = $('#selectButtons_' + module);
                
                if (checkedPermissions.length > 0) {
                    moduleCheckbox.prop('checked', true);
                    $('#permissions_' + module).show();
                    selectButtons.show();
                } else {
                    moduleCheckbox.prop('checked', false);
                    $('#permissions_' + module).hide();
                    selectButtons.hide();
                }
            });

            // Select All Modules
            $('#selectAllModules').on('click', function() {
                $('.module-checkbox').prop('checked', true).trigger('change');
            });

            // Deselect All Modules
            $('#deselectAllModules').on('click', function() {
                $('.module-checkbox').prop('checked', false).trigger('change');
            });

            // Select All permissions for a specific module
            $('.select-all-module').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .permission-checkbox').prop('checked', true);
            });

            // Select Create permissions for a specific module
            $('.select-create').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .create-permission').prop('checked', true);
            });

            // Select Read permissions for a specific module
            $('.select-read').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .read-permission').prop('checked', true);
            });

            // Select Delete permissions for a specific module
            $('.select-delete').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .delete-permission').prop('checked', true);
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/pricing-plans/edit.blade.php ENDPATH**/ ?>