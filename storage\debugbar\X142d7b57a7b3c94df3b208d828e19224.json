{"__meta": {"id": "X142d7b57a7b3c94df3b208d828e19224", "datetime": "2025-07-31 05:34:54", "utime": **********.277144, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940092.707897, "end": **********.277201, "duration": 1.5693039894104004, "duration_str": "1.57s", "measures": [{"label": "Booting", "start": 1753940092.707897, "relative_start": 0, "end": **********.158005, "relative_end": **********.158005, "duration": 1.4501080513000488, "duration_str": "1.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.15803, "relative_start": 1.****************, "end": **********.277206, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "119ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MGtS61hbRmOJ2udoW0tgZwG3T9uYpcaPEZUk4yWo", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1017763267 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1017763267\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-850627550 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-850627550\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-90978517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-90978517\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-509775773 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-509775773\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1477827148 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1477827148\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1052446253 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:34:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBBSFdFUC9wK0hVdWtXbXByMDZwTlE9PSIsInZhbHVlIjoiTXVoUUpMTit2bU9EeTlaMmlnSkxKTXNQby9Eb2hiQWNmTE1USjlyYjZYWTVaMkVPRjY0Y2NvT2JLZlN2MlZ6dFFlOWFDNzMrZkFFcnhMVmVQVC9xRjdXTG5raXZRVFhCSjNVNE9uRFdYZHlYVXNuNVg5TXZTVGd6TzZTWEhYVXZKcU5KbE15Mkl4c2x4djlVWEtQK0NzNFdVY1R2ZHI2M2R5S0xYc0wyQ0d1eWdxbmRoMmNrd2oyMmJoOFJzSCtqYU1MT3A2OWFmV1J5MkE0cGxaUUdBYVZzUndnSDdHTjBnTVpETDcrRHRpU3FTS1RHYnNIL0k3REdMUkpjWDI4T3lBZnBLNS9sSGRkNEowRm5SMnM1eUxNVnpGZzJOdEpzd2xMcU1WbmozMkUxdGE1eENnQ00rOW9FR2NOci9lbzA4VGNSNk9zQnhSNDkvblYybTQzV2hIdGVHOFBPVERVaThMY1UrUXhwMTZyemxWN1BkZ1NyTjA3WDE0b3p2ZFRERm90MTA3bjNrekVBYTFab0Q5TUt5amNDUDdYemNBUENzVFpJYWMwRExlcW0wUVVyakhOWTBxUUNKOGtVdWc1Q25wVGRTaWxGMlVJb0UxcFRScFl6MUttYVpzWC9WcDk5cTZLRkJZbEFnZWVTV2Y5RzVmRzV2emYxQjJvVWwxaFoiLCJtYWMiOiI2MGY2YjY5Y2MyMWU5M2Y4YTEyMmY4M2E3MGExODc1NzI0NWE5ODkwOWEyMTNiYzZiMzE2YzYwNjUyYjA1YzA5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkZWOTFidGhIRG15WFo0aWNKMm5sN0E9PSIsInZhbHVlIjoiTXFWK0kvQkJCWU5GTzFsYWY1eUZMcHFYemtUajFycjRNZzdYM3ozWGFEcmdqMVBmQTVQWE80QjBNS2FQZmdwOFlGeVk2bXhENzJwZld0aG1YU0d1N2dsMklFQnVGMjlZa3ZvMDlyeTMvM1VuWmVJZmIyM1RSa2YrdXI3VG5qTTMxSmlETXRxbGl3QXZTbVVDZ1RCSzB6Z0svRVU4UmtjalFHdUYzMVlBaTQvK3FEczM3RXVtTVFaVlNrQkdOTnMxNU1iTjl1dENyWWhPVzNERnpSQ0J3QTh4SmdGSzdKODkvS0Q0LzNwYzFDeEpTbmljanpFNHBqYmVPeFlBeEptN2JaL0V4azRxNnpyVUhaMUxUVDlyRmFIdldoWUtEdkRqUmpKb2dLN0JiYlQyU0V5V2lLczF5aU1jSm9raVR2bUs3MzlwMTI0YThVa1F4NzROMGJtdS9tNU1JWXQ4YzdXeXMrYmloZkpJbC9kSk8xM214bWdvdzdJS3VBbVRJWHBXTERjMGcrdlUzdjcyTGhpT1djMWwwckNjZnE3U2dCYnRQdDZMeVBDOXMwcUp2QmpTWG0xbHpGOHRDN0xreVB1Q2FWbG1DZnB4eVlXczMxaHN6UkVpcjN3Ty9oUmNhUC9MV3RIL1FXUHRXaENBQXdzS2IzMGExRGZxV01BVmh0UkMiLCJtYWMiOiIyYzYzZWRlNjhkNDQ0OTk3ZjE4NDkxODE0ZjA3ODYwNzkxZmI5ZWY4ZTU1NWQ4ZjdkODgwODNiYWUzM2YyN2I3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBBSFdFUC9wK0hVdWtXbXByMDZwTlE9PSIsInZhbHVlIjoiTXVoUUpMTit2bU9EeTlaMmlnSkxKTXNQby9Eb2hiQWNmTE1USjlyYjZYWTVaMkVPRjY0Y2NvT2JLZlN2MlZ6dFFlOWFDNzMrZkFFcnhMVmVQVC9xRjdXTG5raXZRVFhCSjNVNE9uRFdYZHlYVXNuNVg5TXZTVGd6TzZTWEhYVXZKcU5KbE15Mkl4c2x4djlVWEtQK0NzNFdVY1R2ZHI2M2R5S0xYc0wyQ0d1eWdxbmRoMmNrd2oyMmJoOFJzSCtqYU1MT3A2OWFmV1J5MkE0cGxaUUdBYVZzUndnSDdHTjBnTVpETDcrRHRpU3FTS1RHYnNIL0k3REdMUkpjWDI4T3lBZnBLNS9sSGRkNEowRm5SMnM1eUxNVnpGZzJOdEpzd2xMcU1WbmozMkUxdGE1eENnQ00rOW9FR2NOci9lbzA4VGNSNk9zQnhSNDkvblYybTQzV2hIdGVHOFBPVERVaThMY1UrUXhwMTZyemxWN1BkZ1NyTjA3WDE0b3p2ZFRERm90MTA3bjNrekVBYTFab0Q5TUt5amNDUDdYemNBUENzVFpJYWMwRExlcW0wUVVyakhOWTBxUUNKOGtVdWc1Q25wVGRTaWxGMlVJb0UxcFRScFl6MUttYVpzWC9WcDk5cTZLRkJZbEFnZWVTV2Y5RzVmRzV2emYxQjJvVWwxaFoiLCJtYWMiOiI2MGY2YjY5Y2MyMWU5M2Y4YTEyMmY4M2E3MGExODc1NzI0NWE5ODkwOWEyMTNiYzZiMzE2YzYwNjUyYjA1YzA5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkZWOTFidGhIRG15WFo0aWNKMm5sN0E9PSIsInZhbHVlIjoiTXFWK0kvQkJCWU5GTzFsYWY1eUZMcHFYemtUajFycjRNZzdYM3ozWGFEcmdqMVBmQTVQWE80QjBNS2FQZmdwOFlGeVk2bXhENzJwZld0aG1YU0d1N2dsMklFQnVGMjlZa3ZvMDlyeTMvM1VuWmVJZmIyM1RSa2YrdXI3VG5qTTMxSmlETXRxbGl3QXZTbVVDZ1RCSzB6Z0svRVU4UmtjalFHdUYzMVlBaTQvK3FEczM3RXVtTVFaVlNrQkdOTnMxNU1iTjl1dENyWWhPVzNERnpSQ0J3QTh4SmdGSzdKODkvS0Q0LzNwYzFDeEpTbmljanpFNHBqYmVPeFlBeEptN2JaL0V4azRxNnpyVUhaMUxUVDlyRmFIdldoWUtEdkRqUmpKb2dLN0JiYlQyU0V5V2lLczF5aU1jSm9raVR2bUs3MzlwMTI0YThVa1F4NzROMGJtdS9tNU1JWXQ4YzdXeXMrYmloZkpJbC9kSk8xM214bWdvdzdJS3VBbVRJWHBXTERjMGcrdlUzdjcyTGhpT1djMWwwckNjZnE3U2dCYnRQdDZMeVBDOXMwcUp2QmpTWG0xbHpGOHRDN0xreVB1Q2FWbG1DZnB4eVlXczMxaHN6UkVpcjN3Ty9oUmNhUC9MV3RIL1FXUHRXaENBQXdzS2IzMGExRGZxV01BVmh0UkMiLCJtYWMiOiIyYzYzZWRlNjhkNDQ0OTk3ZjE4NDkxODE0ZjA3ODYwNzkxZmI5ZWY4ZTU1NWQ4ZjdkODgwODNiYWUzM2YyN2I3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1052446253\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1169168909 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MGtS61hbRmOJ2udoW0tgZwG3T9uYpcaPEZUk4yWo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169168909\", {\"maxDepth\":0})</script>\n"}}