{"__meta": {"id": "Xeac0195f9e44716d009d99482a84f418", "datetime": "2025-07-31 05:19:17", "utime": **********.666826, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939156.686681, "end": **********.666867, "duration": 0.9801859855651855, "duration_str": "980ms", "measures": [{"label": "Booting", "start": 1753939156.686681, "relative_start": 0, "end": **********.587379, "relative_end": **********.587379, "duration": 0.9006979465484619, "duration_str": "901ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.587391, "relative_start": 0.***************, "end": **********.66687, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "79.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0O2NQpkUTncSf0BuxE8cESQu4DiXC3LJQ5jxFyOj", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-673515741 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-673515741\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2102491204 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2102491204\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1065579315 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1065579315\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-710750233 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-710750233\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1221688465 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1221688465\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1723420516 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:19:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFWSzkyR3d4b1c2TmtSNS9vYkduQ2c9PSIsInZhbHVlIjoib2NGT3I2SW15aUh4b2VsVUpWNkFSNm1yUS9uUzROWXBmbTlwbkxGeEZqQ29OM2VWbnFhRExLSS9KQUloNjcrUnFybDJoSmdJTlZta1AyTUUrbnlkZmNmTTFWMldDaTQ3d3orVytjdE1ybG44N3pGb3Q3S1pFVStiY1c0cGFzK3JRaU0zRjdCZnBVclk3N0tnd04rUWVCbHZXNCtCMVcwMzR6aVMvQzZxeDI4Z3RVWmRHSm1GbytKTkRJdlFRNXE0cFFteExES09DVTlpT1VvekpDdU5IVW9BMkVHeHRjYURzMDJqMEtmc0Q2YWk4ZnN1WnJaYURubmNDVkxjVVFXZDZ5Z09DMEhqbDFJa2E1TE8yUHN2d21Qc2d1ajd6SFUwVXlldUJIVHZjQUo1ZXRJaXA5T1AvTzB2QTVEcm80T29FemtiRnNYYW1ZblVleTg1WE50YXFSNjZTcUc1c0hhZS9xYjliV2ltdy82Q1hETTJ2SHEva295L2ExOXI1REVOR0JqR3RFamUvcXNxK1lkRXJkT2lUSytrMzZqVzJmekYzQlpUNGx6SVl2and2bGpSK0FvTmdKRFp6cVFyaWxhT2NIcHVMREFwSnUxa0dGWUJ0UmZ5ZXVNYm9NRlQ3QjVJRUVlQSthZVNxWHkzYi9ucDBhQWVtREpSclNUMXRVNWgiLCJtYWMiOiIzMjIzYjEzMTU1ZGQ1OGE3ZDIzYTkxOWIzZTcyOWQ0YjY0ZDQxMTUyZTIzNzc3M2MzNDE0OTcwMTA4NGQ4MDVkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:19:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Iks5ZzVKNkwvekxkdTZHZ3pRa2w3UkE9PSIsInZhbHVlIjoiOEpwblp2K0Jqck4yVnUwekEvMDFsRnViRlpVY1o4TWRHclNxN2tyK0Nya3hGNVBTMS9ZTDYrb0pURjhET0VDYysrOVM1MDJ2N2VBZ3d3ZnRtQnkwUVR3N1ZDcVhsWkQ3RDNGcU1pdEZkSmFJN2NXcWJHZm1GSUIxYk5RZUhGSUU4ajEwamVTV1RaNE8wdm1JL3ZPZi9UYkxveVB1NElBSkdVaG1xUW9xY3N2RGVFU0xad0xSMm9TMzY0MnhZR2FQTkFlaHRlT3IwMkJKWGpMeFNTOWZqUHcyWkwrVERocGZhUHFSbmI0K3dWZ2k5UlYwVFBtczdHRmZSd1lQTEl5WlhQcWNEa0NOQXJrTm5rMldCekhoTDc0Zm4vV2FQMHZZWVY5OXJnM0tMUzI4OU80Y2JPOC9jMUN6SU1qY2t2dHk1bS94dmhlTTZwNVQvK201K0c3cnhTaWhiajg1R05nVm56RkxOTmpNQklhSmhiM2J1LzJOYVE5YklMS3YvTjZLcWV5bEJoZXBOdkZibVc5M2NzTHV4MDF5MHdyR2FWREtBVWtmSTlEN1V1WUJkd3RUVEx6RlNiTWtqMmdpWThyT1NMWmhCRkEvSFpDVjJiTU9lQnBLY0ZUMkhsSHM3djBjdHdENjcvNE9CNVcyN2s5WUJUSHpjSXRUY3duS2pOZWciLCJtYWMiOiJjNzk2NGQ2Y2FkNzg0NjExMjgzNWY0MjhmZmY3MTI0ODMwYmE3YjRlNmViMDU0YzQ3MTM5MjBjNGNkZWNmMmU0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:19:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFWSzkyR3d4b1c2TmtSNS9vYkduQ2c9PSIsInZhbHVlIjoib2NGT3I2SW15aUh4b2VsVUpWNkFSNm1yUS9uUzROWXBmbTlwbkxGeEZqQ29OM2VWbnFhRExLSS9KQUloNjcrUnFybDJoSmdJTlZta1AyTUUrbnlkZmNmTTFWMldDaTQ3d3orVytjdE1ybG44N3pGb3Q3S1pFVStiY1c0cGFzK3JRaU0zRjdCZnBVclk3N0tnd04rUWVCbHZXNCtCMVcwMzR6aVMvQzZxeDI4Z3RVWmRHSm1GbytKTkRJdlFRNXE0cFFteExES09DVTlpT1VvekpDdU5IVW9BMkVHeHRjYURzMDJqMEtmc0Q2YWk4ZnN1WnJaYURubmNDVkxjVVFXZDZ5Z09DMEhqbDFJa2E1TE8yUHN2d21Qc2d1ajd6SFUwVXlldUJIVHZjQUo1ZXRJaXA5T1AvTzB2QTVEcm80T29FemtiRnNYYW1ZblVleTg1WE50YXFSNjZTcUc1c0hhZS9xYjliV2ltdy82Q1hETTJ2SHEva295L2ExOXI1REVOR0JqR3RFamUvcXNxK1lkRXJkT2lUSytrMzZqVzJmekYzQlpUNGx6SVl2and2bGpSK0FvTmdKRFp6cVFyaWxhT2NIcHVMREFwSnUxa0dGWUJ0UmZ5ZXVNYm9NRlQ3QjVJRUVlQSthZVNxWHkzYi9ucDBhQWVtREpSclNUMXRVNWgiLCJtYWMiOiIzMjIzYjEzMTU1ZGQ1OGE3ZDIzYTkxOWIzZTcyOWQ0YjY0ZDQxMTUyZTIzNzc3M2MzNDE0OTcwMTA4NGQ4MDVkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:19:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Iks5ZzVKNkwvekxkdTZHZ3pRa2w3UkE9PSIsInZhbHVlIjoiOEpwblp2K0Jqck4yVnUwekEvMDFsRnViRlpVY1o4TWRHclNxN2tyK0Nya3hGNVBTMS9ZTDYrb0pURjhET0VDYysrOVM1MDJ2N2VBZ3d3ZnRtQnkwUVR3N1ZDcVhsWkQ3RDNGcU1pdEZkSmFJN2NXcWJHZm1GSUIxYk5RZUhGSUU4ajEwamVTV1RaNE8wdm1JL3ZPZi9UYkxveVB1NElBSkdVaG1xUW9xY3N2RGVFU0xad0xSMm9TMzY0MnhZR2FQTkFlaHRlT3IwMkJKWGpMeFNTOWZqUHcyWkwrVERocGZhUHFSbmI0K3dWZ2k5UlYwVFBtczdHRmZSd1lQTEl5WlhQcWNEa0NOQXJrTm5rMldCekhoTDc0Zm4vV2FQMHZZWVY5OXJnM0tMUzI4OU80Y2JPOC9jMUN6SU1qY2t2dHk1bS94dmhlTTZwNVQvK201K0c3cnhTaWhiajg1R05nVm56RkxOTmpNQklhSmhiM2J1LzJOYVE5YklMS3YvTjZLcWV5bEJoZXBOdkZibVc5M2NzTHV4MDF5MHdyR2FWREtBVWtmSTlEN1V1WUJkd3RUVEx6RlNiTWtqMmdpWThyT1NMWmhCRkEvSFpDVjJiTU9lQnBLY0ZUMkhsSHM3djBjdHdENjcvNE9CNVcyN2s5WUJUSHpjSXRUY3duS2pOZWciLCJtYWMiOiJjNzk2NGQ2Y2FkNzg0NjExMjgzNWY0MjhmZmY3MTI0ODMwYmE3YjRlNmViMDU0YzQ3MTM5MjBjNGNkZWNmMmU0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:19:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723420516\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-643203828 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0O2NQpkUTncSf0BuxE8cESQu4DiXC3LJQ5jxFyOj</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643203828\", {\"maxDepth\":0})</script>\n"}}