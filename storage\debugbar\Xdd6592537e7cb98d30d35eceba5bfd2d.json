{"__meta": {"id": "Xdd6592537e7cb98d30d35eceba5bfd2d", "datetime": "2025-07-31 05:21:19", "utime": **********.667862, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939278.48379, "end": **********.667909, "duration": 1.1841189861297607, "duration_str": "1.18s", "measures": [{"label": "Booting", "start": 1753939278.48379, "relative_start": 0, "end": **********.585401, "relative_end": **********.585401, "duration": 1.1016111373901367, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.585422, "relative_start": 1.****************, "end": **********.667912, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "82.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DLVuCqtVJTk4RAmlYRokebQue6zywt454qX76aVW", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-179869541 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-179869541\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2094784676 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2094784676\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1750907548 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1750907548\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1235539393 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1235539393\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-881056737 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-881056737\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1811463122 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:21:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inplc20yRDViMXhxOFhnQjd1TmtvZ3c9PSIsInZhbHVlIjoiSFN5aHlZVEFseHg1alJ3NzVHc1pmYm9nK0U1R3l5SW50S0FRNjd6c0JBTU1iazFQOFhmME1UbTdXS1VhaWZhSFJ1UWI2OXlJeGlNKzcwZ2dKU01wM213NzZ0b1BYNm9Xdm4zTlVKWHY2d3ZpVUhVMmpqa3FITUpCVG44OXI2L3BHS3QrU2lMTmFmS3hNSVlkV3ZhUE54c1lWVVN0UXV2US9mODRJeTUwczJRdVNlRUNNRmI5cW1ueTZEdllROVZOQWFaR0tkbjFvcFBpaVI2QndyRlB1RWJ0OVZiNHh2bElJTnFMb29Dd3ZyZDNJWXN2ekE4SHlzcWRLREU0UDVCejdDS2M3bFRSVTNOMWI3UVp5eHQ4Lzc3VzFnN2IzaysxcmNERm84Q25jNlJkMWFvQ2g5ZnNBbEpyWk5IMDhINnRBa2xVRmh6K2t4aDQ5OW5Jc1VuQXFsbklzaDBlV3BuT3ptaEtyeWcvTGQrREJyWU5qdmFENGticmVBN1ZCRzFabDV1d2RwWlNBRWlpdnpzSll5OXc2ZS95QmFWWGdSaWh5VThLM2o0aXFrb1VWam16UzNNSStHREpob3I1ek41clMzMDUzWXRyZUVRYndWc2JYZWlJMW14VEtmdFJGeGxhZFdiOGl1REFIK0lCd2lOcnQvUXlSMEI1QTFnY1BGaXkiLCJtYWMiOiI5MDJjMTczNTVlYmYzNWVjZjBmNThiOTI3ZWRhYWRkNWVhN2U0M2NiNzQ2MDZkZWJkZWNiOTM5ZTJkYmM3ZmQ4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:21:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjVKRXN6Tzhma3dkSkxtMGdJeUErZXc9PSIsInZhbHVlIjoiMmp3ek1yNlhidUZUeFAyL21VTkNMcm5aUGhKbnZkc1V0dDFaN01MZUJuV0FpUC8yN1ozeHZYWTZ2NlN5d1BaUHRpS2liZU5vVHdTYTdTUlFXbUdXYmFtZlFDUlBiaUhEM0RwZzZtZ2hjR3BnLzVESkpoNGREQ3VURTJFSk9PbW9ydDNMd1RFWkZMd1J2T21jRVd4a1FJcDlLY3MydUZVK05ZWDgrTEJCNVZ0UnphaFRqOTd5ZEkxcjU5NVJwTTFWRjRUVFlNUWduOUVKc2ZQUm9KazdJSmZadUtJbThVU21rMnFNbktoMmFCMDZHMzBWQ2lYS3ArT2xnaEw2andZcFFGdnhyc3VuYlFleTVMMkFxclhNcGVOeTMzZUU2N3FvUjF5RHlhbkRNU2gwN1NPZmdsaGY5K0RmWTFQTDF2aHpWWTJpMnY1R2owTE9GWnNUZlROeW1hZmFWS2ZPZXNub01mSnB4Z1F3U0RLWXJzcDFyUEZwZEdySThCUmJiU2FFTnVIc0tGRFl5QUQzWlF5ek52WU1HeGxDL3Q5cmZtNU4wZ21QWENkNjJwYitmM2dnYXE4ZGhMVnhMRnc3dHZtbmU5byswYzJDUDRyVFdHTUVLMi8yOVV3aml6dHZVRDIycUhidWVvZERjdGZMTGc1S2ZhaUoyVGFIY1hrbWNBekMiLCJtYWMiOiJjNTI2ZDUyMjI3NDJiZTc3NzhlMGEwOTM5MjI0M2FhMTA4YjljZjk4OTViOGM4YTA1ZmVkODI5ZjY0YjE4M2U3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:21:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inplc20yRDViMXhxOFhnQjd1TmtvZ3c9PSIsInZhbHVlIjoiSFN5aHlZVEFseHg1alJ3NzVHc1pmYm9nK0U1R3l5SW50S0FRNjd6c0JBTU1iazFQOFhmME1UbTdXS1VhaWZhSFJ1UWI2OXlJeGlNKzcwZ2dKU01wM213NzZ0b1BYNm9Xdm4zTlVKWHY2d3ZpVUhVMmpqa3FITUpCVG44OXI2L3BHS3QrU2lMTmFmS3hNSVlkV3ZhUE54c1lWVVN0UXV2US9mODRJeTUwczJRdVNlRUNNRmI5cW1ueTZEdllROVZOQWFaR0tkbjFvcFBpaVI2QndyRlB1RWJ0OVZiNHh2bElJTnFMb29Dd3ZyZDNJWXN2ekE4SHlzcWRLREU0UDVCejdDS2M3bFRSVTNOMWI3UVp5eHQ4Lzc3VzFnN2IzaysxcmNERm84Q25jNlJkMWFvQ2g5ZnNBbEpyWk5IMDhINnRBa2xVRmh6K2t4aDQ5OW5Jc1VuQXFsbklzaDBlV3BuT3ptaEtyeWcvTGQrREJyWU5qdmFENGticmVBN1ZCRzFabDV1d2RwWlNBRWlpdnpzSll5OXc2ZS95QmFWWGdSaWh5VThLM2o0aXFrb1VWam16UzNNSStHREpob3I1ek41clMzMDUzWXRyZUVRYndWc2JYZWlJMW14VEtmdFJGeGxhZFdiOGl1REFIK0lCd2lOcnQvUXlSMEI1QTFnY1BGaXkiLCJtYWMiOiI5MDJjMTczNTVlYmYzNWVjZjBmNThiOTI3ZWRhYWRkNWVhN2U0M2NiNzQ2MDZkZWJkZWNiOTM5ZTJkYmM3ZmQ4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:21:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjVKRXN6Tzhma3dkSkxtMGdJeUErZXc9PSIsInZhbHVlIjoiMmp3ek1yNlhidUZUeFAyL21VTkNMcm5aUGhKbnZkc1V0dDFaN01MZUJuV0FpUC8yN1ozeHZYWTZ2NlN5d1BaUHRpS2liZU5vVHdTYTdTUlFXbUdXYmFtZlFDUlBiaUhEM0RwZzZtZ2hjR3BnLzVESkpoNGREQ3VURTJFSk9PbW9ydDNMd1RFWkZMd1J2T21jRVd4a1FJcDlLY3MydUZVK05ZWDgrTEJCNVZ0UnphaFRqOTd5ZEkxcjU5NVJwTTFWRjRUVFlNUWduOUVKc2ZQUm9KazdJSmZadUtJbThVU21rMnFNbktoMmFCMDZHMzBWQ2lYS3ArT2xnaEw2andZcFFGdnhyc3VuYlFleTVMMkFxclhNcGVOeTMzZUU2N3FvUjF5RHlhbkRNU2gwN1NPZmdsaGY5K0RmWTFQTDF2aHpWWTJpMnY1R2owTE9GWnNUZlROeW1hZmFWS2ZPZXNub01mSnB4Z1F3U0RLWXJzcDFyUEZwZEdySThCUmJiU2FFTnVIc0tGRFl5QUQzWlF5ek52WU1HeGxDL3Q5cmZtNU4wZ21QWENkNjJwYitmM2dnYXE4ZGhMVnhMRnc3dHZtbmU5byswYzJDUDRyVFdHTUVLMi8yOVV3aml6dHZVRDIycUhidWVvZERjdGZMTGc1S2ZhaUoyVGFIY1hrbWNBekMiLCJtYWMiOiJjNTI2ZDUyMjI3NDJiZTc3NzhlMGEwOTM5MjI0M2FhMTA4YjljZjk4OTViOGM4YTA1ZmVkODI5ZjY0YjE4M2U3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:21:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1811463122\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1835984100 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DLVuCqtVJTk4RAmlYRokebQue6zywt454qX76aVW</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1835984100\", {\"maxDepth\":0})</script>\n"}}