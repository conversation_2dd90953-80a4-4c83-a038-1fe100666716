{"__meta": {"id": "Xa466fd92b240fffd8238eaa36eeebe07", "datetime": "2025-07-31 05:56:40", "utime": **********.478688, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753941397.682541, "end": **********.478721, "duration": 2.796180009841919, "duration_str": "2.8s", "measures": [{"label": "Booting", "start": 1753941397.682541, "relative_start": 0, "end": **********.22893, "relative_end": **********.22893, "duration": 2.546389102935791, "duration_str": "2.55s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.228959, "relative_start": 2.****************, "end": **********.478725, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "250ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CwM4hNh09W9A0z12o3UxYYXDnJo6SZvDgUwYSVV8", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-180171223 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-180171223\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-767692689 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-767692689\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1664188739 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1664188739\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1777639493 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1777639493\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1753144457 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1753144457\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1035657824 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:56:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijh6a3JtWWY4aXZOb3dmcElGMkxBOFE9PSIsInZhbHVlIjoiV1NBRVBnV3hUUXo1YldtR3l4Y2ExdGNha1FzeEtKS0M5UExXa1FydUVOcmxmSjNIVUpzNU1VSXR0bE5zZXpFeFpSLzlGSkNRd1hKSzBBMmxobE9RK2I0MEJZaFhFTE1pSlpTMWV5MXczZkhGWVRjaGFGZFRQUGd6cmEwa1JEMkxYVEQ0M2ltZHpyWFczYytCV3ZTSEZ3c3g5dGRqVjdwNmx1c0NHTm9Qblg2VDZnSlIxS3VFamFRY0J1aU1HN2lScHJyVW5OSDE1bTc0aFRlV2t0dHFrRVBxcXY0NFRuc0dkNENkRlRybTl2MURWR2N1ZUdwc1hvaWdJUVNPTXlaTXFXYTYrRlJRSDU4S2hINUFhRlRkMy9SY1F0RnozUHNQV0VHWlZDdDh5NVVpZXBDaGNxdVlMWWxzZFA0cS8vT2JJWFpHRlFpYWRHZDcvdHFHWE9FbEJ2NmZXQ3RjUEdGaUlqaU8xMUk2K3k5MHpDMmVlb1dubENpbHJUbGllWHRuemVFcmFOUWowMUx3NmZ1ZUE2V2dxSkRJWkhFMzhhdnhWQW5mZmVYMnVQczdyVEcyelBBQmRmYUJTdkd5WENDRmRVeUM1TVlMSVN2dXBMZVlrTlZSTitUVGU2Qlh5eHJJT0FFa3g2Ync5ZjNiTmdKQUNEdE8vM1N1dEsyZWl2VDIiLCJtYWMiOiJmNGQ5NDhmMWJhNGU4YjhhZjViZGZjMWIzNGU4Y2Y1ODEwMjBmMzU5Y2I2ZThlODBkNDI5YmQ0NGYxZWFjNGZiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:56:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InR1YmxqL09CTHlBc0FVaEd5TzZ1WGc9PSIsInZhbHVlIjoiTUpCNUd0d1daR044am00WWhZVUFKN2RQbkg0Y0p3Vk9LR20xSk5tQkVZbFV1QmRkM1o0WDgvZFBCZCtHeUhaVDVEanA4K3RVekYyeWlRMUozcVRFanJza2hkem1FMG01dlgrT1Yray85bTZjVjd5K1liSE44VzBqa1JmaWRHNTg3WXZXTXZtNkJiUDRIODA1YUo0RytreSthazk1OThwT2ZoNTNETlFpMUtMeThJejJLOUVzK1dhS1EyOGVSRFJsUVdvNzlLR2MzcXdZOFNuK2RSQTdVQVZFeFZlSFRtOWtWU201QW9TLzM3NTFlcUwzSS82cWZoSzZFdENiSmozUzF2Y2RTZG43MGVtYnpxWXB1MFdNNWM4TXoxT000YzBldXdvR0tVamNGUjVPb2t4QXBhYUFPbGpnb1V5MTdKbTZIbjdhc2tyL3FmTVE4Qkk2VENLSVQzbHJkbkFKbEpnT2JVU1VsWXVuZENqNmtJMUFCTDVqcXZXbEw0eVRXRkU0dUxkRXM0UHBrN3NMVC80a2JNWnFvSzFhN1ROemdDbXZJMlFTa1VDN3J6WmZwb3J4MWd0ZU9iZ2V4T3ZjOXg4RXpqVE53dmVBVlZxQ3cvZkJaYThDaGdIcGliY3I3a0ZpSDEzdk4yMTNtOXFHUG1ybzYxaEdiWUI1UDR1VU56T1QiLCJtYWMiOiI3M2I4NzU2Y2UwNjU0OTcwODJiMGQ4MzVmMzFhZWYyMGZjODdlOGMxNWFjMDhlMWM2NzEyM2Y1MDcxODg2YTdkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:56:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijh6a3JtWWY4aXZOb3dmcElGMkxBOFE9PSIsInZhbHVlIjoiV1NBRVBnV3hUUXo1YldtR3l4Y2ExdGNha1FzeEtKS0M5UExXa1FydUVOcmxmSjNIVUpzNU1VSXR0bE5zZXpFeFpSLzlGSkNRd1hKSzBBMmxobE9RK2I0MEJZaFhFTE1pSlpTMWV5MXczZkhGWVRjaGFGZFRQUGd6cmEwa1JEMkxYVEQ0M2ltZHpyWFczYytCV3ZTSEZ3c3g5dGRqVjdwNmx1c0NHTm9Qblg2VDZnSlIxS3VFamFRY0J1aU1HN2lScHJyVW5OSDE1bTc0aFRlV2t0dHFrRVBxcXY0NFRuc0dkNENkRlRybTl2MURWR2N1ZUdwc1hvaWdJUVNPTXlaTXFXYTYrRlJRSDU4S2hINUFhRlRkMy9SY1F0RnozUHNQV0VHWlZDdDh5NVVpZXBDaGNxdVlMWWxzZFA0cS8vT2JJWFpHRlFpYWRHZDcvdHFHWE9FbEJ2NmZXQ3RjUEdGaUlqaU8xMUk2K3k5MHpDMmVlb1dubENpbHJUbGllWHRuemVFcmFOUWowMUx3NmZ1ZUE2V2dxSkRJWkhFMzhhdnhWQW5mZmVYMnVQczdyVEcyelBBQmRmYUJTdkd5WENDRmRVeUM1TVlMSVN2dXBMZVlrTlZSTitUVGU2Qlh5eHJJT0FFa3g2Ync5ZjNiTmdKQUNEdE8vM1N1dEsyZWl2VDIiLCJtYWMiOiJmNGQ5NDhmMWJhNGU4YjhhZjViZGZjMWIzNGU4Y2Y1ODEwMjBmMzU5Y2I2ZThlODBkNDI5YmQ0NGYxZWFjNGZiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:56:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InR1YmxqL09CTHlBc0FVaEd5TzZ1WGc9PSIsInZhbHVlIjoiTUpCNUd0d1daR044am00WWhZVUFKN2RQbkg0Y0p3Vk9LR20xSk5tQkVZbFV1QmRkM1o0WDgvZFBCZCtHeUhaVDVEanA4K3RVekYyeWlRMUozcVRFanJza2hkem1FMG01dlgrT1Yray85bTZjVjd5K1liSE44VzBqa1JmaWRHNTg3WXZXTXZtNkJiUDRIODA1YUo0RytreSthazk1OThwT2ZoNTNETlFpMUtMeThJejJLOUVzK1dhS1EyOGVSRFJsUVdvNzlLR2MzcXdZOFNuK2RSQTdVQVZFeFZlSFRtOWtWU201QW9TLzM3NTFlcUwzSS82cWZoSzZFdENiSmozUzF2Y2RTZG43MGVtYnpxWXB1MFdNNWM4TXoxT000YzBldXdvR0tVamNGUjVPb2t4QXBhYUFPbGpnb1V5MTdKbTZIbjdhc2tyL3FmTVE4Qkk2VENLSVQzbHJkbkFKbEpnT2JVU1VsWXVuZENqNmtJMUFCTDVqcXZXbEw0eVRXRkU0dUxkRXM0UHBrN3NMVC80a2JNWnFvSzFhN1ROemdDbXZJMlFTa1VDN3J6WmZwb3J4MWd0ZU9iZ2V4T3ZjOXg4RXpqVE53dmVBVlZxQ3cvZkJaYThDaGdIcGliY3I3a0ZpSDEzdk4yMTNtOXFHUG1ybzYxaEdiWUI1UDR1VU56T1QiLCJtYWMiOiI3M2I4NzU2Y2UwNjU0OTcwODJiMGQ4MzVmMzFhZWYyMGZjODdlOGMxNWFjMDhlMWM2NzEyM2Y1MDcxODg2YTdkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:56:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035657824\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-622572554 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CwM4hNh09W9A0z12o3UxYYXDnJo6SZvDgUwYSVV8</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-622572554\", {\"maxDepth\":0})</script>\n"}}