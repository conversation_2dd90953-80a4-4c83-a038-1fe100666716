{"__meta": {"id": "X49fea70464e7a14b26613268fe258284", "datetime": "2025-07-31 05:09:36", "utime": **********.473153, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753938575.325112, "end": **********.473184, "duration": 1.1480720043182373, "duration_str": "1.15s", "measures": [{"label": "Booting", "start": 1753938575.325112, "relative_start": 0, "end": **********.357104, "relative_end": **********.357104, "duration": 1.031991958618164, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.357168, "relative_start": 1.****************, "end": **********.473188, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GnCELgwTelcTxTuCucutSmTyOwxxLjgRjLZ7oENk", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1368067349 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1368067349\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-481819409 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-481819409\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1639249188 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1639249188\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1030233367 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1030233367\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2935511 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2935511\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-828532077 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:09:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktkWWRQd211TE4xSXpNeTdIWE0rMEE9PSIsInZhbHVlIjoiS0JWM1VhOGpHVk0xQ2N0MDFqM1dEUkdiZzVodzF0MXl2ZTloY25qNi9Ec3hzNVMrWGpVZFp5S2JUcXBXZHVpWnd5UXBQemwrdFZnRFBHMHczMmZPcW5Fc0dLeGtrRk1MczB5dzNERzJJSEhRSVY1cSt0RUxLMFdMMXh1NDhwcUNaSy9CTDkyTmlLK2oxekdYL3BxcEVteEJTOW00a0l1bVB4cCtrbXZDaHRhY2VrcDhZSG5vK0RiNXBJUjVMdm5TV1J1Z0t2YWxHUjgvMTdJSXUyYzNLRXJkVEp0Rllza1ZoT0ZoU2tnNCtHRWppQjVwWlNxU1VSbXBZZGFtdGtFcENkTHlWRm8wRGVlMUozbHlRRWdhbUZHckpDMmZyQVlkNnR4aHU5Q1F1ckFGS1VneG1MTU0zQTJ4VFJjcFpRT2VPbHJyVnVYdXBFR3FETzZmTytwWENDb1M3TXR0K2VsYzIxRzlhVjRTRmdpNkR2YUllZ3RCVEJUcWw5K2lmRjlzSUVianNYWHQ1SGl3YldsSFBBU3NQaDRiYmI1blNkZ05FaHk2MFAyUzlLaEUvcGlHME1kT01LcmNZUVl5VTJjcjBQeldrWWlpQ1J3bmtRV1I3ZjFjRGJrN1FnNFF6dFpuRE9PRENQTWxGcmxMOGRhSGdoc21aTHpMRG1idlh0YWUiLCJtYWMiOiI0ZTYwZjI2Y2JjNmYxMjE2OTA5NDIyYTZiYWQ2NjAwNDQ3MDdmMjI4OWQyZjMzNGU5YmY2OThlNzliODBmMDg1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:09:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IngwcFZicGltbTMzdXhha3pCSWtBa2c9PSIsInZhbHVlIjoiY3pCNGxmZXROS3lHT0hGUUhzeVZZODJlb2gydUZkb0hQREt1VE9YdmdHeXdjZGpZUG5XRkkxR3VzNzUrUWwwbVUxMXBJSFl4WDgwUmI3bzVDREo3dlVtdHp4WDEzaEJScjNQQUU3dVZ5Tm9vQ3dhZjRRdjFuSmhHNXZCWS9UNmNIdE94L2EwVGtRYURxVi9YeUlOd3hUMEdOSndNY1ZFOElpNWU0V1lmZkIyRytCNkVLbnZ3d1FxUnBRdFNPUUhWT0FIclp5YklQekZyYTFyYUJRZmszbzQwRGZvUXltSmFSamY5dUlRbkcrOUdIbmwzbTJsRzFZMXZxWG1LZXZtUGQ3aFdPQ05WZnhJei9kR1cxYXMyMm00SndhTU80MCt6eXhYQjJPUFVsMnVMTS9zRVJycGR2N2doZkQrWmJSaW8yVDZZam5FT1BXcFFYOUY4UzRlNmhwTzZ0K2l4cFZ2QWV3bGo0S1NXK3JrdWdLczVOcCtxZzg1dmJpLzd0SDExOUJldDcwRklzVlpyMVU1dmdpV3lRYm1kU0txTWE2MWNibUFxM0haZjhKNUVSZEhCZU4wczFjazZrRnMyeXZOL0JIWEFmZEpCY1FjeXVOSS80NU9OT3NMdU90MzlsS2lUWUZ5M2d1Q204UUFDcFBzTTNhcS8xYmdlcnJMMzRxaFUiLCJtYWMiOiIwNWNlZDZlNWRhMmQzMDgxMGQ1ZTIwMGEwOWEwZjE3ZjFkZDVlM2Y0YjBmMjVjYTVhNGRlMmI0MmY5NmNmZjMxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:09:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktkWWRQd211TE4xSXpNeTdIWE0rMEE9PSIsInZhbHVlIjoiS0JWM1VhOGpHVk0xQ2N0MDFqM1dEUkdiZzVodzF0MXl2ZTloY25qNi9Ec3hzNVMrWGpVZFp5S2JUcXBXZHVpWnd5UXBQemwrdFZnRFBHMHczMmZPcW5Fc0dLeGtrRk1MczB5dzNERzJJSEhRSVY1cSt0RUxLMFdMMXh1NDhwcUNaSy9CTDkyTmlLK2oxekdYL3BxcEVteEJTOW00a0l1bVB4cCtrbXZDaHRhY2VrcDhZSG5vK0RiNXBJUjVMdm5TV1J1Z0t2YWxHUjgvMTdJSXUyYzNLRXJkVEp0Rllza1ZoT0ZoU2tnNCtHRWppQjVwWlNxU1VSbXBZZGFtdGtFcENkTHlWRm8wRGVlMUozbHlRRWdhbUZHckpDMmZyQVlkNnR4aHU5Q1F1ckFGS1VneG1MTU0zQTJ4VFJjcFpRT2VPbHJyVnVYdXBFR3FETzZmTytwWENDb1M3TXR0K2VsYzIxRzlhVjRTRmdpNkR2YUllZ3RCVEJUcWw5K2lmRjlzSUVianNYWHQ1SGl3YldsSFBBU3NQaDRiYmI1blNkZ05FaHk2MFAyUzlLaEUvcGlHME1kT01LcmNZUVl5VTJjcjBQeldrWWlpQ1J3bmtRV1I3ZjFjRGJrN1FnNFF6dFpuRE9PRENQTWxGcmxMOGRhSGdoc21aTHpMRG1idlh0YWUiLCJtYWMiOiI0ZTYwZjI2Y2JjNmYxMjE2OTA5NDIyYTZiYWQ2NjAwNDQ3MDdmMjI4OWQyZjMzNGU5YmY2OThlNzliODBmMDg1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:09:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IngwcFZicGltbTMzdXhha3pCSWtBa2c9PSIsInZhbHVlIjoiY3pCNGxmZXROS3lHT0hGUUhzeVZZODJlb2gydUZkb0hQREt1VE9YdmdHeXdjZGpZUG5XRkkxR3VzNzUrUWwwbVUxMXBJSFl4WDgwUmI3bzVDREo3dlVtdHp4WDEzaEJScjNQQUU3dVZ5Tm9vQ3dhZjRRdjFuSmhHNXZCWS9UNmNIdE94L2EwVGtRYURxVi9YeUlOd3hUMEdOSndNY1ZFOElpNWU0V1lmZkIyRytCNkVLbnZ3d1FxUnBRdFNPUUhWT0FIclp5YklQekZyYTFyYUJRZmszbzQwRGZvUXltSmFSamY5dUlRbkcrOUdIbmwzbTJsRzFZMXZxWG1LZXZtUGQ3aFdPQ05WZnhJei9kR1cxYXMyMm00SndhTU80MCt6eXhYQjJPUFVsMnVMTS9zRVJycGR2N2doZkQrWmJSaW8yVDZZam5FT1BXcFFYOUY4UzRlNmhwTzZ0K2l4cFZ2QWV3bGo0S1NXK3JrdWdLczVOcCtxZzg1dmJpLzd0SDExOUJldDcwRklzVlpyMVU1dmdpV3lRYm1kU0txTWE2MWNibUFxM0haZjhKNUVSZEhCZU4wczFjazZrRnMyeXZOL0JIWEFmZEpCY1FjeXVOSS80NU9OT3NMdU90MzlsS2lUWUZ5M2d1Q204UUFDcFBzTTNhcS8xYmdlcnJMMzRxaFUiLCJtYWMiOiIwNWNlZDZlNWRhMmQzMDgxMGQ1ZTIwMGEwOWEwZjE3ZjFkZDVlM2Y0YjBmMjVjYTVhNGRlMmI0MmY5NmNmZjMxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:09:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-828532077\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1798163514 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GnCELgwTelcTxTuCucutSmTyOwxxLjgRjLZ7oENk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1798163514\", {\"maxDepth\":0})</script>\n"}}