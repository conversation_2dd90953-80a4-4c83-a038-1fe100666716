<?php

namespace App\Services;

use App\Models\CalendarEvent;
use App\Models\EventWeeklyAvailability;
use App\Models\AppointmentBooking;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SlotGeneratorService
{
    /**
     * Generate appointment booking slots for a calendar event
     */
    public function generateSlotsForEvent(CalendarEvent $event)
    {
        Log::info("Starting slot generation for event ID: {$event->id}");
        
        try {
            // Get the event's weekly availability
            $weeklyAvailability = EventWeeklyAvailability::where('calendar_event_id', $event->id)->get();
            
            if ($weeklyAvailability->isEmpty()) {
                Log::warning("No weekly availability found for event ID: {$event->id}");
                return false;
            }
            
            // Parse event dates
            $startDate = Carbon::parse($event->start_date);
            $endDate = Carbon::parse($event->end_date);
            
            Log::info("Event date range: {$startDate->toDateString()} to {$endDate->toDateString()}");
            
            $slotsCreated = 0;
            
            // Loop through each date in the event range
            $currentDate = $startDate->copy();
            while ($currentDate->lte($endDate)) {
                $dayOfWeek = strtolower($currentDate->format('l')); // monday, tuesday, etc.
                
                // Find availability for this day of week
                $dayAvailability = $weeklyAvailability->where('day_of_week', $dayOfWeek)->first();
                
                if ($dayAvailability) {
                    $slotsForDay = $this->generateSlotsForDay(
                        $event,
                        $currentDate,
                        $dayAvailability
                    );
                    $slotsCreated += $slotsForDay;
                }
                
                $currentDate->addDay();
            }
            
            Log::info("Generated {$slotsCreated} slots for event ID: {$event->id}");
            return $slotsCreated;
            
        } catch (\Exception $e) {
            Log::error("Error generating slots for event ID: {$event->id} - " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Generate slots for a specific day
     */
    private function generateSlotsForDay(CalendarEvent $event, Carbon $date, EventWeeklyAvailability $availability)
    {
        $slotsCreated = 0;

        // Parse start and end times for the day
        $startTime = Carbon::parse($availability->start_time)->format('H:i:s');
        $endTime = Carbon::parse($availability->end_time)->format('H:i:s');

        $dayStart = Carbon::parse($date->toDateString() . ' ' . $startTime);
        $dayEnd = Carbon::parse($date->toDateString() . ' ' . $endTime);
        
        // Get event duration in minutes (ensure it's an integer)
        $duration = (int) ($event->duration ?? 60);
        
        Log::info("Generating slots for {$date->toDateString()} from {$dayStart->format('H:i')} to {$dayEnd->format('H:i')} with {$duration}min duration");
        
        // Generate time slots
        $currentSlot = $dayStart->copy();
        while ($currentSlot->copy()->addMinutes($duration)->lte($dayEnd)) {
            $slotEndTime = $currentSlot->copy()->addMinutes($duration);
            
            // Check if slot already exists
            $existingSlot = AppointmentBooking::where('event_id', $event->id)
                ->where('event_date', $date->toDateString())
                ->where('time_slots', $currentSlot->format('H:i'))
                ->first();
            
            if (!$existingSlot) {
                // Create the appointment booking slot
                AppointmentBooking::create([
                    'event_id' => $event->id,
                    'event_location' => $event->location ?? 'online',
                    'event_location_value' => $this->getLocationValue($event),
                    'event_date' => $date->toDateString(),
                    'time_zone' => 'UTC', // You can make this configurable
                    'time_slots' => $currentSlot->format('H:i') . '-' . $slotEndTime->format('H:i')
                ]);
                
                $slotsCreated++;
                Log::debug("Created slot: {$date->toDateString()} {$currentSlot->format('H:i')}-{$slotEndTime->format('H:i')}");
            }
            
            $currentSlot->addMinutes($duration);
        }
        
        return $slotsCreated;
    }
    
    /**
     * Get location value based on event location type
     */
    private function getLocationValue(CalendarEvent $event)
    {
        switch ($event->location) {
            case 'in_person':
                return $event->physical_address ?? 'Physical Location';
            case 'zoom':
            case 'skype':
            case 'meet':
            case 'others':
                return $event->meet_link ?? 'Online Meeting';
            default:
                return 'To be determined';
        }
    }
    
    /**
     * Regenerate all slots for an event (useful for updates)
     */
    public function regenerateSlotsForEvent(CalendarEvent $event)
    {
        Log::info("Regenerating slots for event ID: {$event->id}");
        
        // Delete existing slots
        AppointmentBooking::where('event_id', $event->id)->delete();
        
        // Generate new slots
        return $this->generateSlotsForEvent($event);
    }
}
