{"__meta": {"id": "Xa8f1a7c7413db4de76f9fcbddf4aa675", "datetime": "2025-07-31 05:50:18", "utime": **********.218403, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753941016.259764, "end": **********.218445, "duration": 1.9586811065673828, "duration_str": "1.96s", "measures": [{"label": "Booting", "start": 1753941016.259764, "relative_start": 0, "end": 1753941017.988504, "relative_end": 1753941017.988504, "duration": 1.7287399768829346, "duration_str": "1.73s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753941017.988579, "relative_start": 1.****************, "end": **********.218449, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "230ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eMXE2wYqjCBrPNEsuwxQQzQ7dEs7Lv3olx8JgY6k", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-240468526 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-240468526\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1857974988 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1857974988\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-288683050 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-288683050\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-861609953 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-861609953\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-414822720 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-414822720\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-88171059 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:50:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlvbW8xWmtIWWJaMUkrWjhsZEprcnc9PSIsInZhbHVlIjoieXFuN0pQdzRqa2x0bUl5MUlGTHRndzhmMUpsMXljQnovZzVQMHkvbkVGek5RdlZqMmt5bHZrTmdjck96RnVKRWRZT2R1RlNCOXV6RVF0VVRPS1YyVGQwUS9BckNZM3kyKzdva3NxNHdtcnB6dzZkS0k4ejllY2Q5a3l2NzlVcDdsVDBMOXhzSDd0UG5WQml3MEo1YjFHVTBTcndYcDNSci9nRFBmMkxUaGhqdUtQUlZzQnZ6LzdCT3FkNGpJWnFLV2pRK3d6NXhGekRqZFRrUUFScmF3cmZlQWJSdjZpbjRxbEE3NENUWC81NGwvMnZyYkFXU1hPTyt5UVhzUWJlRHRiUEhCa3Z5cEJlT3ZWUFNEUXI5eWlyQmtYWDRObDRqd2Z5ci9yTzUyMFg5MFdwbFZmaHZ2Q3hnb1NYK2ZQaG1rVnVES3hLTmI2cjZmTGFCSzJad1owWXJCdVVRRk1sNHNnc2N3ZUljNEJNTWwzcS9nNHdBKzg5RDhxc3o3OFVQdFl5L0Z6eFBtM2lpZ2lCYTFiYUhJdENMZlBpOGc2dm5CTXFqYjJxd1BmM241VThuektpbXNESHVnZzM0eUN3S3E3V1F1ditIMms2K1h6cFVFSTZMSjdHZndJRTk1SGhjREZpYm5TQ05rcDM5QjJKNEFTNDZqaTY5UFFEUGVicEciLCJtYWMiOiIxMDlkMWE4ODRmMzBmYzA2ZjEzZjdkZTFkMzE4MWFhZTQwYzJmOGMzMjZmOWI4YmE4OTIyZTZmZjAxZTVjMTVhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:50:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InBNRHlzNlFuMjJiV2xVRStwOWJsSXc9PSIsInZhbHVlIjoiR2NvdnZ2clhyZFVVb0lzZVdGK20xRWRHQ3pBTGxUNWpHV3h5NjcxdWMrR2loU1lIMy9PaStHUTFxeTNFSStrUVh1Q0lRQlZ5SnhjemVxdmtnN2NiQnZKSWpoQm51YzYrU3dmbDg1V1M4ZWxwa241cmI2YTVDSDRYN2drQUVRc3hxbHcvUjJqUTB5dFNqdWN0a2g1TStscW14YWpxUWJFWTJkbHZVY0RvTTUwMGxvYjQ2Q3Z0R2ZHYWR1dkNDdGpyS3MwRDdNek5xRmpPYXZxN0VjOXUrTWtFS0Z2VVFFL1JhL3Q2cEhHOTNZOFZZa1FSVDhHVDZ3Mm14Y2hjMnRLSnhPMzhpd3Q5MFZHcytUc2U3K3B5OE1YMzZMTy9wOG1CQ3N3S09ZYkphV2FBZElQSVE2REZ0dGNPZkZWc2RqMG9pbHV0aHgvak54M0VaS21MZ2loMC9RT2MwRXgrUmExT0ZvTzNqRi96a1UyanVENmlaemgxTkh4anNrMWhLa05welYxR21pTkNnRUZTSmdsMC9VcUlZdDFwUnJMYlBLNEZjYVlXeU5rMlUzeFNBWXA4ZFZCeDlObEFDTldTYWFXTG1rWnVtVmZPdk96c2hCV2hnemVEMmh5TzF5OENCaGYxbXpTU1VCSVZQSDJ2dEdLOHFodU5xaU1lUWtINkd0eEsiLCJtYWMiOiI1NGNiOTQxZWJjNjBkZDkwZDg5N2VlZDY5OTk0ZjAzZjllNzRlYjQ2YmM5OWFlZGUzNDFkMzM4MzQxYjMxYTk3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:50:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlvbW8xWmtIWWJaMUkrWjhsZEprcnc9PSIsInZhbHVlIjoieXFuN0pQdzRqa2x0bUl5MUlGTHRndzhmMUpsMXljQnovZzVQMHkvbkVGek5RdlZqMmt5bHZrTmdjck96RnVKRWRZT2R1RlNCOXV6RVF0VVRPS1YyVGQwUS9BckNZM3kyKzdva3NxNHdtcnB6dzZkS0k4ejllY2Q5a3l2NzlVcDdsVDBMOXhzSDd0UG5WQml3MEo1YjFHVTBTcndYcDNSci9nRFBmMkxUaGhqdUtQUlZzQnZ6LzdCT3FkNGpJWnFLV2pRK3d6NXhGekRqZFRrUUFScmF3cmZlQWJSdjZpbjRxbEE3NENUWC81NGwvMnZyYkFXU1hPTyt5UVhzUWJlRHRiUEhCa3Z5cEJlT3ZWUFNEUXI5eWlyQmtYWDRObDRqd2Z5ci9yTzUyMFg5MFdwbFZmaHZ2Q3hnb1NYK2ZQaG1rVnVES3hLTmI2cjZmTGFCSzJad1owWXJCdVVRRk1sNHNnc2N3ZUljNEJNTWwzcS9nNHdBKzg5RDhxc3o3OFVQdFl5L0Z6eFBtM2lpZ2lCYTFiYUhJdENMZlBpOGc2dm5CTXFqYjJxd1BmM241VThuektpbXNESHVnZzM0eUN3S3E3V1F1ditIMms2K1h6cFVFSTZMSjdHZndJRTk1SGhjREZpYm5TQ05rcDM5QjJKNEFTNDZqaTY5UFFEUGVicEciLCJtYWMiOiIxMDlkMWE4ODRmMzBmYzA2ZjEzZjdkZTFkMzE4MWFhZTQwYzJmOGMzMjZmOWI4YmE4OTIyZTZmZjAxZTVjMTVhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:50:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InBNRHlzNlFuMjJiV2xVRStwOWJsSXc9PSIsInZhbHVlIjoiR2NvdnZ2clhyZFVVb0lzZVdGK20xRWRHQ3pBTGxUNWpHV3h5NjcxdWMrR2loU1lIMy9PaStHUTFxeTNFSStrUVh1Q0lRQlZ5SnhjemVxdmtnN2NiQnZKSWpoQm51YzYrU3dmbDg1V1M4ZWxwa241cmI2YTVDSDRYN2drQUVRc3hxbHcvUjJqUTB5dFNqdWN0a2g1TStscW14YWpxUWJFWTJkbHZVY0RvTTUwMGxvYjQ2Q3Z0R2ZHYWR1dkNDdGpyS3MwRDdNek5xRmpPYXZxN0VjOXUrTWtFS0Z2VVFFL1JhL3Q2cEhHOTNZOFZZa1FSVDhHVDZ3Mm14Y2hjMnRLSnhPMzhpd3Q5MFZHcytUc2U3K3B5OE1YMzZMTy9wOG1CQ3N3S09ZYkphV2FBZElQSVE2REZ0dGNPZkZWc2RqMG9pbHV0aHgvak54M0VaS21MZ2loMC9RT2MwRXgrUmExT0ZvTzNqRi96a1UyanVENmlaemgxTkh4anNrMWhLa05welYxR21pTkNnRUZTSmdsMC9VcUlZdDFwUnJMYlBLNEZjYVlXeU5rMlUzeFNBWXA4ZFZCeDlObEFDTldTYWFXTG1rWnVtVmZPdk96c2hCV2hnemVEMmh5TzF5OENCaGYxbXpTU1VCSVZQSDJ2dEdLOHFodU5xaU1lUWtINkd0eEsiLCJtYWMiOiI1NGNiOTQxZWJjNjBkZDkwZDg5N2VlZDY5OTk0ZjAzZjllNzRlYjQ2YmM5OWFlZGUzNDFkMzM4MzQxYjMxYTk3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:50:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-88171059\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1760755915 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eMXE2wYqjCBrPNEsuwxQQzQ7dEs7Lv3olx8JgY6k</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1760755915\", {\"maxDepth\":0})</script>\n"}}