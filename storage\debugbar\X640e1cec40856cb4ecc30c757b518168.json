{"__meta": {"id": "X640e1cec40856cb4ecc30c757b518168", "datetime": "2025-07-31 05:44:47", "utime": **********.289131, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940685.561268, "end": **********.289217, "duration": 1.7279489040374756, "duration_str": "1.73s", "measures": [{"label": "Booting", "start": 1753940685.561268, "relative_start": 0, "end": **********.167501, "relative_end": **********.167501, "duration": 1.6062328815460205, "duration_str": "1.61s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.167531, "relative_start": 1.****************, "end": **********.289222, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "neTeb5EOPUS5Nv3qw2qf4O3Ij2rG0Rcuhvpn8L1B", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1193655086 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1193655086\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1342800422 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1342800422\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-492129462 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-492129462\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1432664489 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1432664489\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-735532364 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-735532364\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-35397926 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:44:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjYyak9HbTd2eUpqTVIxUlh4MmdpVEE9PSIsInZhbHVlIjoiSXR5UHhSc1h6dEFJNWNvSHVtMFQzOUZHTVNJZnh6dlZHc21PWlRzR1d4RXBML20yaWNSOTZsTWtVaGJaczkxdDkwTzh5UEI3NisxTW9JNHlLeVRQeUNVY1pXU2hKOXd3N2loY0FvMnBISFpmSmhFVCtWYWFQRWpuRFJreU0vZk5ybmtFUmtGTi9waldPNGdydWV1cHNudDZZU20yQ0xMYVFaTzNrVklYNDh3dnZqREFCWk1wOFYyNFVJRFA0MnFabjFkZi9qNG4xNVgvek83c0Rua3N1MjhqNHJBWmVxQlJ5bDBWQ2hGdURwQk5MaWFMOWdHemdYSUoxa3Y2dExMNU56Tmhsb2VZTVJodXUxQnR2amFEUVRuNDhSbkJ2cGgxdXhmWVA1RmRXRll6QWt4amZhVTVyM2FuRlRxcGhZL3lUZ01pTlQ4Ym9MUkxvVXU1OURGUng2U2MyVDRWKzFMeHpnOXBnd2NVd2V0dk5hZElhVFh3RlhkL3FZS1AxQWlXTkJoRHJFVklsT2JHd295eXQvbTJROEJFZHNQN3NNYTFZWCt1NXAwZll6VWdWelAxUWJrL01VUzYrS2lHUDlqcHgza1ZZZEl0dVNteXBrdzlQd00yWlJqTTBTeTB1Wmt6eVJUVWNSNkdxRzMxaTR1TkVmbkpzQ2tnRGVWNHl4V2QiLCJtYWMiOiI2MDRiZjE0MTY3MWIyN2Q0MzUxNjY5YmUxOTVjNzI4MWEzOGYxNDEyYjk1NDA2MzJmOWJmZWQwMzgyZDYwYTM3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:44:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InNXcmMvSFN5aEJsaXRTRDB5aXJpZXc9PSIsInZhbHVlIjoibXlmZHpRcHUxVU41SVNoeHFvNWlwamtsY25TcVJldGYzcGdwYXhhc2d1bDhwRGx4dlUwS2R4Wnk2bXE0TmpRVVRHVHd3ejVpdXVvNHpXaWR2ODRjbUIrblhpczM0SXFmS0luQ3RqQlZicGczcTJQZThmWVpCMlFHOHVWcnZtRXRMazNGMiswQ29aV2JBZUhmb0VHRDFUQW5mQVRMMnV4SlJZQzBRM2E5QmIwTXdlVzduZjE3d2NhbjRyK1o1SFBVZmR2bUJENUhoMldnSUR5bEYxWEtYTG11MnAweUovTWxxOVJTeW9qWGFFaitFNHZob3FwYkFhaEdtTWRzUWlGM3VzRFdKdU42QXZJbENHbDdDenBiMDVXTmJiaWFQNTMxYm1GSEZjT0lqK0pYRFI1VUNCbzBmbWdDSjRkb0J2Q1RCNStGQSt5QzQyWDZ1NzNDSFlFYzdZM0h2a0wzWEZiR21GcTByMFk1MkhJdU5VMEJnUVlqMkpKS1kvSWJrZE01eGwxNi9QcVl0RVV6QVR4UDJPOFZsendsMmo0MnRVUW9mQ3dacE53ME96NkMvL2pGRG8zTTgzTys1OTVGNkxENzFldEk5Sm1zSWJkNi9KMWpsaG42bE5hWUtFOWdHRDVyQTFSNWNwWmM3bGtyVDR2cUtOemhaTm9VdklHeTZTS2giLCJtYWMiOiIwOWQ4NzllNjBjOTk2MTQ5MzUwMDU1Y2NlMzFhZGZjOTViMzVmMjcxOGNhZjc2ZWVmNDZmZjVlNGU4NjlkNzBiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:44:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjYyak9HbTd2eUpqTVIxUlh4MmdpVEE9PSIsInZhbHVlIjoiSXR5UHhSc1h6dEFJNWNvSHVtMFQzOUZHTVNJZnh6dlZHc21PWlRzR1d4RXBML20yaWNSOTZsTWtVaGJaczkxdDkwTzh5UEI3NisxTW9JNHlLeVRQeUNVY1pXU2hKOXd3N2loY0FvMnBISFpmSmhFVCtWYWFQRWpuRFJreU0vZk5ybmtFUmtGTi9waldPNGdydWV1cHNudDZZU20yQ0xMYVFaTzNrVklYNDh3dnZqREFCWk1wOFYyNFVJRFA0MnFabjFkZi9qNG4xNVgvek83c0Rua3N1MjhqNHJBWmVxQlJ5bDBWQ2hGdURwQk5MaWFMOWdHemdYSUoxa3Y2dExMNU56Tmhsb2VZTVJodXUxQnR2amFEUVRuNDhSbkJ2cGgxdXhmWVA1RmRXRll6QWt4amZhVTVyM2FuRlRxcGhZL3lUZ01pTlQ4Ym9MUkxvVXU1OURGUng2U2MyVDRWKzFMeHpnOXBnd2NVd2V0dk5hZElhVFh3RlhkL3FZS1AxQWlXTkJoRHJFVklsT2JHd295eXQvbTJROEJFZHNQN3NNYTFZWCt1NXAwZll6VWdWelAxUWJrL01VUzYrS2lHUDlqcHgza1ZZZEl0dVNteXBrdzlQd00yWlJqTTBTeTB1Wmt6eVJUVWNSNkdxRzMxaTR1TkVmbkpzQ2tnRGVWNHl4V2QiLCJtYWMiOiI2MDRiZjE0MTY3MWIyN2Q0MzUxNjY5YmUxOTVjNzI4MWEzOGYxNDEyYjk1NDA2MzJmOWJmZWQwMzgyZDYwYTM3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:44:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InNXcmMvSFN5aEJsaXRTRDB5aXJpZXc9PSIsInZhbHVlIjoibXlmZHpRcHUxVU41SVNoeHFvNWlwamtsY25TcVJldGYzcGdwYXhhc2d1bDhwRGx4dlUwS2R4Wnk2bXE0TmpRVVRHVHd3ejVpdXVvNHpXaWR2ODRjbUIrblhpczM0SXFmS0luQ3RqQlZicGczcTJQZThmWVpCMlFHOHVWcnZtRXRMazNGMiswQ29aV2JBZUhmb0VHRDFUQW5mQVRMMnV4SlJZQzBRM2E5QmIwTXdlVzduZjE3d2NhbjRyK1o1SFBVZmR2bUJENUhoMldnSUR5bEYxWEtYTG11MnAweUovTWxxOVJTeW9qWGFFaitFNHZob3FwYkFhaEdtTWRzUWlGM3VzRFdKdU42QXZJbENHbDdDenBiMDVXTmJiaWFQNTMxYm1GSEZjT0lqK0pYRFI1VUNCbzBmbWdDSjRkb0J2Q1RCNStGQSt5QzQyWDZ1NzNDSFlFYzdZM0h2a0wzWEZiR21GcTByMFk1MkhJdU5VMEJnUVlqMkpKS1kvSWJrZE01eGwxNi9QcVl0RVV6QVR4UDJPOFZsendsMmo0MnRVUW9mQ3dacE53ME96NkMvL2pGRG8zTTgzTys1OTVGNkxENzFldEk5Sm1zSWJkNi9KMWpsaG42bE5hWUtFOWdHRDVyQTFSNWNwWmM3bGtyVDR2cUtOemhaTm9VdklHeTZTS2giLCJtYWMiOiIwOWQ4NzllNjBjOTk2MTQ5MzUwMDU1Y2NlMzFhZGZjOTViMzVmMjcxOGNhZjc2ZWVmNDZmZjVlNGU4NjlkNzBiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:44:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35397926\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1603342550 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">neTeb5EOPUS5Nv3qw2qf4O3Ij2rG0Rcuhvpn8L1B</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1603342550\", {\"maxDepth\":0})</script>\n"}}