{"__meta": {"id": "X7f28ff1dab1f72ad84f94c38850cae53", "datetime": "2025-07-31 06:28:37", "utime": **********.481666, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943315.312316, "end": **********.481717, "duration": 2.169401168823242, "duration_str": "2.17s", "measures": [{"label": "Booting", "start": 1753943315.312316, "relative_start": 0, "end": **********.341995, "relative_end": **********.341995, "duration": 2.0296790599823, "duration_str": "2.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.342032, "relative_start": 2.****************, "end": **********.481722, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "140ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "kyoY2dj9beMKmyToQJ2RKtXa2NLmpp2OhxiTZEoL", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-1615092768 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1615092768\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1932625523 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1932625523\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1534722399 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1534722399\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2138346106 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138346106\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1954425351 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1954425351\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1758919823 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:28:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlMyNUUrc0lzWXVURDE1MitWZlZ2eVE9PSIsInZhbHVlIjoiS2NrSitJT2xCdnpmM0NPUTRLRDZERTF4OHZwY0gxS3hLSEZWZEhlbWV5TmR0a2s0WnVwTTRNbUlJUWFxZTNzQVFxY1FjT1B2NmNDUmVyMFUvYkI2clNIRUVtTWUzTnJvR1FkNkxZNUFJSmJKeW1ka0pza2xldkwybnl6Q0s0L1Ntd0pjRy9DT1FjbDZkRFVzLzVmNnBodW80TlN4aExvbXRUSUl6WUdQdEJ6OUJhZDBuTTdHWElVTDF4cm04RjFzMmxVWEZwVzVFTTdBaUd0MEZwcE5xWmdNcHY1R015N1ZDSU5sVCtJQzJXR2Y5UzBrUmdXeUVyaFFBV2RxeDh3RDdJVm96ci9rZDJMYnA3cXZnWFZxR3J2dlVLa2k5V3dJVzlzQ1crbndIWGl6eHk4TXd4bFd4MzRoRmtBR1kra3dzRWIzMTRRbmkrQ245VW9DT0wvWWdsanB4RlNmQUdlcEw2dW8za2R5MnBlWk5XMDhjSXdLTkhUeGY2QXV5Q0kwRm5jSXg4KytRRDFEQytmbG1iM2VhZjJYWEdscVlQUTloZHJpYVdNZ20wa2NvLzgyUVUwVXdrcHd1N3JnZmZKY1NRRXZDZDV1Si9JdW9IcXNNTHVDdTl5SjBiai9yU29Gd1pQUklGYTc2c3FXTGpZVzRiQUV6Z2c0ams0a1hqemIiLCJtYWMiOiJlODQxNzQyOWQ5ZmRiZjY2NjdhMGQ4ZmM0ZWRjNWRmODdhYzJkZGVkNzAwOGZmNTM5NDM5OTc2MDk4ZjQ2ZTA4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:28:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkN2Q3NKNE5JQy9PU1FOcnkwWEo2dWc9PSIsInZhbHVlIjoiM01oaWZoUlBEbEh3b1lUQkNTWHIya2RpcFZ4bDRFWXJtSHVkQ1c1RjJvbVo2WUtKcDY4UUJzWUtFNVN6ZDA1Y1ljRkk5R0VxMWJESWdTTXRHcmNNLzBKOElFLy9xbEFsbjR4VnJ2MmNYdXRGWkxMNlArYUdJVHhjUnc5STcwQ1Excjd0M0NobktRNytpdFlvRFZwQytvZnVNMitpeWNVNW1ZTEpGZDg2c0xRUm8yUUdMR1A3VGdJd1lrcTNvSVBWSnQ2VTNQZWdyYjc4Tm9LL0FKZXNVOC9XL0d6U1k3cHF2M21CZEp6QjI5d0JacW14ZTd3eTE0eHczMzhoQ21SY1ZXSFdnUjNiWlVndW5obmtqc2kyTUUyNi9LaUlYRSttVXp5QmJDdzZic0pSQjU0dzRESFgzY1BhZ1czejM0NUp1MUZZTStRdTZZVEhieXlNM3JRbGp3RFMzMVQ0ZE4wM09nTk52YTE0Z2ZqY0dYMkQ3bVJ3Rkkvb2Q4cmUvcmVRcTM0MjBWZXNMSVQ1SHlzbFJFL084eDBXQzZrbEQvWWx1ZzRjWis5d0lGUEo5dDVrQ3NzNTB0aWdyK1Jzcnh0Q2JpU1FvT3hqd3FGcklZL0lhdnppSWZLd2NrRlVhY0ZNN1doL0oyTVZJMFlsYkZBMEdNRnVuWlhlbGQwTUw2bHciLCJtYWMiOiIzYzEyNTc1NTE4ZTY2NWQzYTJkODZmMGY4ZmNjMWM1NGIwY2QyODhjZDY1NTE4NWJlYzI3NGFkM2I4MTE5ZTc5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:28:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlMyNUUrc0lzWXVURDE1MitWZlZ2eVE9PSIsInZhbHVlIjoiS2NrSitJT2xCdnpmM0NPUTRLRDZERTF4OHZwY0gxS3hLSEZWZEhlbWV5TmR0a2s0WnVwTTRNbUlJUWFxZTNzQVFxY1FjT1B2NmNDUmVyMFUvYkI2clNIRUVtTWUzTnJvR1FkNkxZNUFJSmJKeW1ka0pza2xldkwybnl6Q0s0L1Ntd0pjRy9DT1FjbDZkRFVzLzVmNnBodW80TlN4aExvbXRUSUl6WUdQdEJ6OUJhZDBuTTdHWElVTDF4cm04RjFzMmxVWEZwVzVFTTdBaUd0MEZwcE5xWmdNcHY1R015N1ZDSU5sVCtJQzJXR2Y5UzBrUmdXeUVyaFFBV2RxeDh3RDdJVm96ci9rZDJMYnA3cXZnWFZxR3J2dlVLa2k5V3dJVzlzQ1crbndIWGl6eHk4TXd4bFd4MzRoRmtBR1kra3dzRWIzMTRRbmkrQ245VW9DT0wvWWdsanB4RlNmQUdlcEw2dW8za2R5MnBlWk5XMDhjSXdLTkhUeGY2QXV5Q0kwRm5jSXg4KytRRDFEQytmbG1iM2VhZjJYWEdscVlQUTloZHJpYVdNZ20wa2NvLzgyUVUwVXdrcHd1N3JnZmZKY1NRRXZDZDV1Si9JdW9IcXNNTHVDdTl5SjBiai9yU29Gd1pQUklGYTc2c3FXTGpZVzRiQUV6Z2c0ams0a1hqemIiLCJtYWMiOiJlODQxNzQyOWQ5ZmRiZjY2NjdhMGQ4ZmM0ZWRjNWRmODdhYzJkZGVkNzAwOGZmNTM5NDM5OTc2MDk4ZjQ2ZTA4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:28:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkN2Q3NKNE5JQy9PU1FOcnkwWEo2dWc9PSIsInZhbHVlIjoiM01oaWZoUlBEbEh3b1lUQkNTWHIya2RpcFZ4bDRFWXJtSHVkQ1c1RjJvbVo2WUtKcDY4UUJzWUtFNVN6ZDA1Y1ljRkk5R0VxMWJESWdTTXRHcmNNLzBKOElFLy9xbEFsbjR4VnJ2MmNYdXRGWkxMNlArYUdJVHhjUnc5STcwQ1Excjd0M0NobktRNytpdFlvRFZwQytvZnVNMitpeWNVNW1ZTEpGZDg2c0xRUm8yUUdMR1A3VGdJd1lrcTNvSVBWSnQ2VTNQZWdyYjc4Tm9LL0FKZXNVOC9XL0d6U1k3cHF2M21CZEp6QjI5d0JacW14ZTd3eTE0eHczMzhoQ21SY1ZXSFdnUjNiWlVndW5obmtqc2kyTUUyNi9LaUlYRSttVXp5QmJDdzZic0pSQjU0dzRESFgzY1BhZ1czejM0NUp1MUZZTStRdTZZVEhieXlNM3JRbGp3RFMzMVQ0ZE4wM09nTk52YTE0Z2ZqY0dYMkQ3bVJ3Rkkvb2Q4cmUvcmVRcTM0MjBWZXNMSVQ1SHlzbFJFL084eDBXQzZrbEQvWWx1ZzRjWis5d0lGUEo5dDVrQ3NzNTB0aWdyK1Jzcnh0Q2JpU1FvT3hqd3FGcklZL0lhdnppSWZLd2NrRlVhY0ZNN1doL0oyTVZJMFlsYkZBMEdNRnVuWlhlbGQwTUw2bHciLCJtYWMiOiIzYzEyNTc1NTE4ZTY2NWQzYTJkODZmMGY4ZmNjMWM1NGIwY2QyODhjZDY1NTE4NWJlYzI3NGFkM2I4MTE5ZTc5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:28:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1758919823\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1940012288 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kyoY2dj9beMKmyToQJ2RKtXa2NLmpp2OhxiTZEoL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1940012288\", {\"maxDepth\":0})</script>\n"}}