<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('installment_plans', function (Blueprint $table) {
            $table->id();
            $table->string('plan_id')->unique();
            $table->unsignedBigInteger('customer_id');
            $table->string('customer_name');
            $table->string('customer_email');
            $table->string('customer_phone')->nullable();
            $table->unsignedBigInteger('product_id');
            $table->string('product_name');
            $table->decimal('product_price', 15, 2);
            $table->integer('quantity')->default(1);
            $table->decimal('down_payment', 15, 2)->default(0.00);
            $table->decimal('paid_amount', 15, 2)->default(0.00);
            $table->decimal('pending_amount', 15, 2)->default(0.00);
            $table->decimal('discount_amount', 15, 2)->default(0.00);
            $table->decimal('total_amount', 15, 2);
            $table->enum('status', ['active', 'cancelled', 'paused', 'completed', 'overdue'])->default('active');
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->date('next_installment_date')->nullable();
            $table->integer('total_installments');
            $table->integer('paid_installments')->default(0);
            $table->decimal('installment_amount', 15, 2);
            $table->enum('payment_frequency', ['weekly', 'monthly', 'quarterly', 'yearly'])->default('monthly');
            $table->text('description')->nullable();
            $table->text('notes')->nullable();
            $table->string('receipt_url')->nullable();
            $table->enum('payment_method', ['offline', 'online'])->default('offline');
            $table->unsignedBigInteger('created_by');
            $table->timestamps();

            // Indexes
            $table->index(['customer_id', 'status']);
            $table->index(['next_installment_date', 'status']);
            $table->index(['created_by']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('installment_plans');
    }
};
