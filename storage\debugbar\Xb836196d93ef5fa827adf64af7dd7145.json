{"__meta": {"id": "Xb836196d93ef5fa827adf64af7dd7145", "datetime": "2025-07-31 05:20:40", "utime": **********.275427, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939239.136909, "end": **********.275516, "duration": 1.1386070251464844, "duration_str": "1.14s", "measures": [{"label": "Booting", "start": 1753939239.136909, "relative_start": 0, "end": **********.190365, "relative_end": **********.190365, "duration": 1.0534560680389404, "duration_str": "1.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.190403, "relative_start": 1.****************, "end": **********.27552, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "85.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "mUvK9rEA3QG5iTwIREwkA8Gtd9GPSmuid6n1EwJB", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1597410911 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1597410911\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-217085931 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-217085931\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2104389082 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2104389082\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-77242068 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77242068\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1267998566 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1267998566\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1384775638 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:20:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikw2dzczVTJJcTZPbTFPUjRuRnE5dHc9PSIsInZhbHVlIjoiMFBNQjhlL2xwZlp3S1FGRmd5cmIvNEd1ZGVSdDR1N2RMcWcyMHoxajcwb2RmMUQvVHR2aHQ4elNkdE1OMTFjRW5PZHpZRnRVaU0vdTl6Z3hGR2VEaVM1TkZCNWVGalhWSU1oYmt5aTZROFJVS3lTMjRHcmY1aHBmeHFlczh5Z1NFL0JPZHdPMDRBTzFDVm9IRWJqY1NZdFJWZ3J0MVJhYmRHM1JuaDVMS2RXVTNXRTNnc2lqQjI3VGR0Q2szL2h5UisrZ21ndklYTnVzekpxbHdRMTBpdXd2dk5LSG4zbGxNYllhL2YvUDQzMkk0OXNnUFhDOC9rODVvMzBjbE4yUFJqRUQ2TGc1ZE9CWlhFR3grL3NCOHdYU2ltMTVaVkpnV2RRQ2VPaDBuY1A5NERkMHVpaXRNUk9scXNEOWEzNlB1bDdLV0tia1Z2Zjh4NFBMaGFXbVdkNFdHK0F0MWRYeHBsYWNKTUYvZDd4TzRiQ1dJSDFXTW8zZ1FDczcrb2NJWCtnbXBiWjFMbWdYa1hoWVgxOFlxYVZVT3pKN0NNNkFoRTBodlkvcENhVGdtemJ2UDZIYi9BdmdrbEQ0VGxYL0FJT3M5YTVwc2pKdDIvWXJta0JwUjczSFlQR2RwbHJsRWZlQ3I2Y2srM2dkT0RoUnZGbEdxRGpBaGdNUytNdFMiLCJtYWMiOiI0ZmQxYTE1NmI0OTQ2MjViY2RmMjcwODZlNWQ1YzdjOTliZmJjYWM4YTczYTJjMmI2NTFlNDM0MTdlMzJlZmU4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:20:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkVMZXBhaWlSTFp2RHhkOGhDdHUwWFE9PSIsInZhbHVlIjoiQXV0NlRrcTVtZ3A2aFBwK1NkM3RIVzFpRlJjTFRKWHBkZ0RURW9lb25xYmx3TjZDNHlKcHhudmw2eG5lQ0RoclQwNEdVdmVZK2podEw2SlVtQklSZUthMVI5MmVXYTJRZkRpNDl3eG1MUDlqNXQ0VWZpL0pZUmEvVXB5SEVjaTZMU0w5Zm83bDFEeUNkbmE4bWI2UG0rVkRENmFCamV6eVFGV3dwZVFwRU1NRm1KRkZydVZuYU1YemdycEozOUxLb1cxZlh5VVE2bTVDS3Rqb2FPM3FZL1F5WE8zRHllQzNQaC9lMFYrUUgrK0NOVnhYZHhwTXRqdDNVamNUREIwUm5uZEZGb1NRQndnY0NCSXF6UFhwdWNNeDFqdVVxVFIrU1FKRUZ4SUxyc1RLUjJOcWdKTlVXcG1hNWUvUEIyRkZ0ZThydk1xZ3lCcmgvNDFjVzlMb1g3T21zSXIrbXZPNHFnWkE5Vi9pMWVlTU1pa054UnRuNHFZM1JoVUF5UnVpdTVJbkw5Z2xGbStUd242eFRKTXMwVzZsNm1UdE82dGtjZ3ppeWp1UGc2R2c2aFc2alBUNW9RVTY2UkJnTDFGOE51LzBpNDZwVytpbitSRGxNQUJwU01XMElkc1FCSFJqT2F1TXhWZDRVaUhYT1d4V3pocnpxREQzcU05YzJZN04iLCJtYWMiOiI5OGUwYWMyMjE3MWIxMzg1MTQ4ZGJkZmE2NTQwM2IwNGM1ZDJmYjE3OTZjZmI2ZDNiYTIyZmRkMDE0NWEwZDA4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:20:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikw2dzczVTJJcTZPbTFPUjRuRnE5dHc9PSIsInZhbHVlIjoiMFBNQjhlL2xwZlp3S1FGRmd5cmIvNEd1ZGVSdDR1N2RMcWcyMHoxajcwb2RmMUQvVHR2aHQ4elNkdE1OMTFjRW5PZHpZRnRVaU0vdTl6Z3hGR2VEaVM1TkZCNWVGalhWSU1oYmt5aTZROFJVS3lTMjRHcmY1aHBmeHFlczh5Z1NFL0JPZHdPMDRBTzFDVm9IRWJqY1NZdFJWZ3J0MVJhYmRHM1JuaDVMS2RXVTNXRTNnc2lqQjI3VGR0Q2szL2h5UisrZ21ndklYTnVzekpxbHdRMTBpdXd2dk5LSG4zbGxNYllhL2YvUDQzMkk0OXNnUFhDOC9rODVvMzBjbE4yUFJqRUQ2TGc1ZE9CWlhFR3grL3NCOHdYU2ltMTVaVkpnV2RRQ2VPaDBuY1A5NERkMHVpaXRNUk9scXNEOWEzNlB1bDdLV0tia1Z2Zjh4NFBMaGFXbVdkNFdHK0F0MWRYeHBsYWNKTUYvZDd4TzRiQ1dJSDFXTW8zZ1FDczcrb2NJWCtnbXBiWjFMbWdYa1hoWVgxOFlxYVZVT3pKN0NNNkFoRTBodlkvcENhVGdtemJ2UDZIYi9BdmdrbEQ0VGxYL0FJT3M5YTVwc2pKdDIvWXJta0JwUjczSFlQR2RwbHJsRWZlQ3I2Y2srM2dkT0RoUnZGbEdxRGpBaGdNUytNdFMiLCJtYWMiOiI0ZmQxYTE1NmI0OTQ2MjViY2RmMjcwODZlNWQ1YzdjOTliZmJjYWM4YTczYTJjMmI2NTFlNDM0MTdlMzJlZmU4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:20:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkVMZXBhaWlSTFp2RHhkOGhDdHUwWFE9PSIsInZhbHVlIjoiQXV0NlRrcTVtZ3A2aFBwK1NkM3RIVzFpRlJjTFRKWHBkZ0RURW9lb25xYmx3TjZDNHlKcHhudmw2eG5lQ0RoclQwNEdVdmVZK2podEw2SlVtQklSZUthMVI5MmVXYTJRZkRpNDl3eG1MUDlqNXQ0VWZpL0pZUmEvVXB5SEVjaTZMU0w5Zm83bDFEeUNkbmE4bWI2UG0rVkRENmFCamV6eVFGV3dwZVFwRU1NRm1KRkZydVZuYU1YemdycEozOUxLb1cxZlh5VVE2bTVDS3Rqb2FPM3FZL1F5WE8zRHllQzNQaC9lMFYrUUgrK0NOVnhYZHhwTXRqdDNVamNUREIwUm5uZEZGb1NRQndnY0NCSXF6UFhwdWNNeDFqdVVxVFIrU1FKRUZ4SUxyc1RLUjJOcWdKTlVXcG1hNWUvUEIyRkZ0ZThydk1xZ3lCcmgvNDFjVzlMb1g3T21zSXIrbXZPNHFnWkE5Vi9pMWVlTU1pa054UnRuNHFZM1JoVUF5UnVpdTVJbkw5Z2xGbStUd242eFRKTXMwVzZsNm1UdE82dGtjZ3ppeWp1UGc2R2c2aFc2alBUNW9RVTY2UkJnTDFGOE51LzBpNDZwVytpbitSRGxNQUJwU01XMElkc1FCSFJqT2F1TXhWZDRVaUhYT1d4V3pocnpxREQzcU05YzJZN04iLCJtYWMiOiI5OGUwYWMyMjE3MWIxMzg1MTQ4ZGJkZmE2NTQwM2IwNGM1ZDJmYjE3OTZjZmI2ZDNiYTIyZmRkMDE0NWEwZDA4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:20:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1384775638\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1945492183 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mUvK9rEA3QG5iTwIREwkA8Gtd9GPSmuid6n1EwJB</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1945492183\", {\"maxDepth\":0})</script>\n"}}