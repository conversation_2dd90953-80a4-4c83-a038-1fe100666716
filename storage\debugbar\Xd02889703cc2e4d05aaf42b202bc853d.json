{"__meta": {"id": "Xd02889703cc2e4d05aaf42b202bc853d", "datetime": "2025-07-31 05:18:49", "utime": **********.391355, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939127.99196, "end": **********.391387, "duration": 1.3994269371032715, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1753939127.99196, "relative_start": 0, "end": **********.306945, "relative_end": **********.306945, "duration": 1.3149850368499756, "duration_str": "1.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.306983, "relative_start": 1.****************, "end": **********.39139, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "84.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "lXdahX7b58g9xT84pj87WdTwGvW7Z6y2DSXPE6lF", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-35919339 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-35919339\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-289270367 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-289270367\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-295978310 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-295978310\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-833329730 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-833329730\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1114915311 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1114915311\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1915630973 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:18:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpKOS9xZ25tcjcrOC9aK3hlZi9HNlE9PSIsInZhbHVlIjoiL01LMVhIWHNSYk0zTmZkNmV2UExrWlZTRFFIWldORDhpYm1ZVzcxdWNrQXVLWEdKbHY4VUlUUFlmeDZsUEFJRW9IYmlYVmlNTEFLV3ZCU004aWFwM2g1bFF6RStUN1JJdE54aVorbDNQZzROUWl3OWxlTXExZWNIZDVoSEJLMnBxUHRxQzFlOUgwWkVrVThJbkVFaG1PcFFiTjlWTjkrc2xmeUVVdGgzZldIbm9KZ1FXK3hYUGRKSk1INEYxYWVhY3hMdVVnbFQ1eExscXBzd0dvaURZUnYwdkhMbVI3alI3aEcxY2xKQkF6ZGxDZjYyd1hzeFkrYUswMFJFa3NtOFlQSlRyay8rZjVYZmI0aEFuZmRCVlFGVkRxOE1FSjNaWm53eDF6ZkQvOW83dFZxcGtmQU53emczOWhwVldyUFE4SERqekovaXlQUnNIblNpQTJtQ3RiN0NVK3R0dlFkcEQ3MDJYcjBuZ0ZGc00weVZZWlRQKzNEZGZUaEovUVQ1eTE4VjBuRCsrTStMWjczWHRXMXloMHkrUVFyU3BTN2VOQXhoMWt2WXVFOS9YNUR5U1RKcHZGM3J6UXNkTmQxZWFmeWVxVjBRc2NuajdsK1JpZnQvYTVqeTlpRmRqdWdEaGJ3c2NNcTRFSGpOMS9CNXdSdzNDS3ZNSi9peTAzL1UiLCJtYWMiOiJjNmZiNmY1MTczNmI1NWJmZDg0NDgzZDRmMTVjZDlmYjExMDZjN2I2OGMxZDk2NzE3ZWU3ZmNjNTlkN2Y4ODAxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:18:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IisvaUtYb0lnSFcwMWxYSEl0dEgrTGc9PSIsInZhbHVlIjoidHU2eDlBallqeUVNczNYNUQvZlFWcGVWOTJWOVZHQ3Z6WGRUdlpZQW5HODlpTmlHbnNTQmRKNjZ2STlxUjlvV1ZQR2hVSFdDQnhLMG14Y05tdUlOMXFSc2xOMy9UeE11RHdlTzVKZ1FuQkxjMXA5Q1UzSDBSdU1YczJmUW53Y0kxY3MwL21CZG5CUkxTSnl5QW8vZWtsdzVnUW4vdDhtYjVuMTkwQUI2ZEZidmd1MkZ3ejFhUGpUUXRzRGFSOXlHZ0E1cGw4ekNpVmxKNkRGS0s4ZjNJWVg4QVcyTmxLRFV6UkpGcG12a0FxRjhGZnZYdzlRYW5ydmt5NkU5bGU2bVN4dTIwUWNuVmpRd3JDbktBZno2Z1lIT1dUZ3c5TTV5ZlcybjZqSXhYNE9SUWk2SkREWEVUZjQ5azdaSGpLbUsybDZlaGFCTFRYTTdqNUl2QnA1NXByMEpaVWp2MjFzNlNGQkxHc3lId3VGbTVZS1J2UkhlQW4rZHpmdTR0U1M4Sk5DTlVVbmFlOEhvNnR3eTIyall0eEYxcGxUWldTZWtDVmNGUlQyOWhqaVdNUkN4VWdpZEE3aVRlSXZLS3VHTENvSlp0NDhyOGZzcXdGeTZkS050ejJteFp6OElCYitvSkZyLy9ZYmJDRklkbnFkeXRtV1VTZGlQRmF1Nmh1cFgiLCJtYWMiOiIxMDJiYjBlZTRlNzlhNjg1MmRhYmU4NjZiNGFhNzVhOGUzNjAzYjMzN2NhNDUxMDJmYjQ3MTdhNGMwMTk5YzU0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:18:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpKOS9xZ25tcjcrOC9aK3hlZi9HNlE9PSIsInZhbHVlIjoiL01LMVhIWHNSYk0zTmZkNmV2UExrWlZTRFFIWldORDhpYm1ZVzcxdWNrQXVLWEdKbHY4VUlUUFlmeDZsUEFJRW9IYmlYVmlNTEFLV3ZCU004aWFwM2g1bFF6RStUN1JJdE54aVorbDNQZzROUWl3OWxlTXExZWNIZDVoSEJLMnBxUHRxQzFlOUgwWkVrVThJbkVFaG1PcFFiTjlWTjkrc2xmeUVVdGgzZldIbm9KZ1FXK3hYUGRKSk1INEYxYWVhY3hMdVVnbFQ1eExscXBzd0dvaURZUnYwdkhMbVI3alI3aEcxY2xKQkF6ZGxDZjYyd1hzeFkrYUswMFJFa3NtOFlQSlRyay8rZjVYZmI0aEFuZmRCVlFGVkRxOE1FSjNaWm53eDF6ZkQvOW83dFZxcGtmQU53emczOWhwVldyUFE4SERqekovaXlQUnNIblNpQTJtQ3RiN0NVK3R0dlFkcEQ3MDJYcjBuZ0ZGc00weVZZWlRQKzNEZGZUaEovUVQ1eTE4VjBuRCsrTStMWjczWHRXMXloMHkrUVFyU3BTN2VOQXhoMWt2WXVFOS9YNUR5U1RKcHZGM3J6UXNkTmQxZWFmeWVxVjBRc2NuajdsK1JpZnQvYTVqeTlpRmRqdWdEaGJ3c2NNcTRFSGpOMS9CNXdSdzNDS3ZNSi9peTAzL1UiLCJtYWMiOiJjNmZiNmY1MTczNmI1NWJmZDg0NDgzZDRmMTVjZDlmYjExMDZjN2I2OGMxZDk2NzE3ZWU3ZmNjNTlkN2Y4ODAxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:18:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IisvaUtYb0lnSFcwMWxYSEl0dEgrTGc9PSIsInZhbHVlIjoidHU2eDlBallqeUVNczNYNUQvZlFWcGVWOTJWOVZHQ3Z6WGRUdlpZQW5HODlpTmlHbnNTQmRKNjZ2STlxUjlvV1ZQR2hVSFdDQnhLMG14Y05tdUlOMXFSc2xOMy9UeE11RHdlTzVKZ1FuQkxjMXA5Q1UzSDBSdU1YczJmUW53Y0kxY3MwL21CZG5CUkxTSnl5QW8vZWtsdzVnUW4vdDhtYjVuMTkwQUI2ZEZidmd1MkZ3ejFhUGpUUXRzRGFSOXlHZ0E1cGw4ekNpVmxKNkRGS0s4ZjNJWVg4QVcyTmxLRFV6UkpGcG12a0FxRjhGZnZYdzlRYW5ydmt5NkU5bGU2bVN4dTIwUWNuVmpRd3JDbktBZno2Z1lIT1dUZ3c5TTV5ZlcybjZqSXhYNE9SUWk2SkREWEVUZjQ5azdaSGpLbUsybDZlaGFCTFRYTTdqNUl2QnA1NXByMEpaVWp2MjFzNlNGQkxHc3lId3VGbTVZS1J2UkhlQW4rZHpmdTR0U1M4Sk5DTlVVbmFlOEhvNnR3eTIyall0eEYxcGxUWldTZWtDVmNGUlQyOWhqaVdNUkN4VWdpZEE3aVRlSXZLS3VHTENvSlp0NDhyOGZzcXdGeTZkS050ejJteFp6OElCYitvSkZyLy9ZYmJDRklkbnFkeXRtV1VTZGlQRmF1Nmh1cFgiLCJtYWMiOiIxMDJiYjBlZTRlNzlhNjg1MmRhYmU4NjZiNGFhNzVhOGUzNjAzYjMzN2NhNDUxMDJmYjQ3MTdhNGMwMTk5YzU0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:18:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1915630973\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-885606157 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lXdahX7b58g9xT84pj87WdTwGvW7Z6y2DSXPE6lF</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-885606157\", {\"maxDepth\":0})</script>\n"}}