{"__meta": {"id": "X14e680bfb06550a1b1a47f2916e0db46", "datetime": "2025-07-31 05:09:02", "utime": **********.856442, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753938541.907323, "end": **********.856473, "duration": 0.9491500854492188, "duration_str": "949ms", "measures": [{"label": "Booting", "start": 1753938541.907323, "relative_start": 0, "end": **********.782999, "relative_end": **********.782999, "duration": 0.875676155090332, "duration_str": "876ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.783014, "relative_start": 0.****************, "end": **********.856475, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "73.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TofKM30AF1jHjOyX3j0960S2squ6M9DpehjgG3wO", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-421168134 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-421168134\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-229314401 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-229314401\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2054326268 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2054326268\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2097922439 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2097922439\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2079804456 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2079804456\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-212368667 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:09:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllHT0duYXdBVzhZOHNRNTg2K2NjMkE9PSIsInZhbHVlIjoieDFHT1JSNWZ2UURBSVJoOVhuOWlIWVRYV1NQeXpUaklyTS8rdFBFRVFpK1psNHVVUERHQ2F5VWVDSndGTHFET1VqT0JncVAxRW5yazQvNnhkUXMrOU4xK041UmQ3NHA3T3ZCbXN4dFlCMW5SeE1uRnJkbHB3aEs1c2hLaUg1WjZPY2tsT0NvcDYxYVp6Tkg0ckpSYXlrNzFaelZaTDBxdERteTBNdlQ1aGVjV3JBbjVBcDRyMjZWZlR0MnNNU3k4am1FekJEZVdtQjkrREtMSXRFVjVic2ozemZrWW9xbFp3MW9sQmVLTGZlallMUlVWL3VlMVBidXZ2Z0hNSFFUOEZBa3hLZUJGdnhkTUpvWkZTTEc1dnRyV3d3Q0toMlZ3RGZSdm95b0YvTE1Ub09HOW1pQVNCNU9RcXdyZnpHMzJPemc4TmtTL2lUTk5tRUN3czFwLzBHbmpxRmY3VTkvUVV1UXlDZXdwQ1RQU3JKZHNhWDlWUXJTZHhFQkRBUDZ3bm1yRWQvKzFTRWRJSEltNzNIaVRBcS9ERk1hdG45Q1ZOSlRzN0RjVjkwbVRObmFadEh6Mno0VkdtcXJvTFdxaERnK3FJZ1JPWDN2MHJkVHN6UTVnWkxVQW9ETVdrSmxCOGhpRDFPSmhwTTF1Mk9RWW54Z081MEIvQjBPZUVpZjciLCJtYWMiOiJhZGY3OTgyOWNlMzQ0NjY2NDVkYWU1ZTQzNDkzYzM5ODdhZWMyYTY1NThiNDg3YjEwMWIwMzA5NjQ4OGQxMjk0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:09:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Imt2Uk1WUkpHdkl0ekhrdDBXNE83L3c9PSIsInZhbHVlIjoiN3I4V2xMdzhTTHErOGlKSXB2Zm9aY2VPd1VxV1QyM1pQaVRhSDIxb3ZlbnJBSVdyNHZsQlN2MHFCbU81OWZzZ2RSMWhVTHE3VVBFUUQ0MEUrZXM0dEZ4eFA5b0pVSW1LdFQyWFBLZEVZbUJhVW9XU1dYRUVNMURMZElqS0QzY09ab0R0dUFLZzRmSnNiSG5JamVmejkrbnlSVWdkeTJRcFE5SENJbDA4K2tHY3JwUWMxVlFaU2wwWkV1bzRtbTVUOFVXODArUDFZcHA1QUdjYW54dFMwTm0vd2U2OTR0Zm91WE9KNnprNEpFR3I0NStxbHIrZkFjZnlsOEhSRmcrcWpmMGhSdlo1UjBrLzhpbGZaejc0THBVcUxlb2lBV3dpS3MzQjdCdUpyQWdKWGxWWHYwMXZlWXZtRVZuZEg1QUlRYVJRZEJxU1RaQTZTcW84MHJiVFZEZ21Uc1RGQWJyWkNRY1FoWUMwa21FZU1DdVdpNXpjaWZUaEM1UkJXaHJnTXJiaGk0SmU3L3J1bnErT1pWS3JFN1gzY040QTNDM1NpbEJIVytyVWhjbS80OEJLOXhjeExncDRmSW1oK1VDZ25pRVlIaEZjTHg0d3hMSStZaCtTTE9LMXJTUWhydDJueWwxYnRMMDczMEQyc0ozK1pnTjZBdVlwNE94cWtmR3IiLCJtYWMiOiI4Y2I2NzQ2OWE4MDcyNWFjZDA2ZjQ0ZWUwMjAzZDI2ZDEyZTIzN2Q5YjA2ZjRhZjgyODBlNzg0MTg5NTA4ZTIwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:09:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllHT0duYXdBVzhZOHNRNTg2K2NjMkE9PSIsInZhbHVlIjoieDFHT1JSNWZ2UURBSVJoOVhuOWlIWVRYV1NQeXpUaklyTS8rdFBFRVFpK1psNHVVUERHQ2F5VWVDSndGTHFET1VqT0JncVAxRW5yazQvNnhkUXMrOU4xK041UmQ3NHA3T3ZCbXN4dFlCMW5SeE1uRnJkbHB3aEs1c2hLaUg1WjZPY2tsT0NvcDYxYVp6Tkg0ckpSYXlrNzFaelZaTDBxdERteTBNdlQ1aGVjV3JBbjVBcDRyMjZWZlR0MnNNU3k4am1FekJEZVdtQjkrREtMSXRFVjVic2ozemZrWW9xbFp3MW9sQmVLTGZlallMUlVWL3VlMVBidXZ2Z0hNSFFUOEZBa3hLZUJGdnhkTUpvWkZTTEc1dnRyV3d3Q0toMlZ3RGZSdm95b0YvTE1Ub09HOW1pQVNCNU9RcXdyZnpHMzJPemc4TmtTL2lUTk5tRUN3czFwLzBHbmpxRmY3VTkvUVV1UXlDZXdwQ1RQU3JKZHNhWDlWUXJTZHhFQkRBUDZ3bm1yRWQvKzFTRWRJSEltNzNIaVRBcS9ERk1hdG45Q1ZOSlRzN0RjVjkwbVRObmFadEh6Mno0VkdtcXJvTFdxaERnK3FJZ1JPWDN2MHJkVHN6UTVnWkxVQW9ETVdrSmxCOGhpRDFPSmhwTTF1Mk9RWW54Z081MEIvQjBPZUVpZjciLCJtYWMiOiJhZGY3OTgyOWNlMzQ0NjY2NDVkYWU1ZTQzNDkzYzM5ODdhZWMyYTY1NThiNDg3YjEwMWIwMzA5NjQ4OGQxMjk0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:09:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Imt2Uk1WUkpHdkl0ekhrdDBXNE83L3c9PSIsInZhbHVlIjoiN3I4V2xMdzhTTHErOGlKSXB2Zm9aY2VPd1VxV1QyM1pQaVRhSDIxb3ZlbnJBSVdyNHZsQlN2MHFCbU81OWZzZ2RSMWhVTHE3VVBFUUQ0MEUrZXM0dEZ4eFA5b0pVSW1LdFQyWFBLZEVZbUJhVW9XU1dYRUVNMURMZElqS0QzY09ab0R0dUFLZzRmSnNiSG5JamVmejkrbnlSVWdkeTJRcFE5SENJbDA4K2tHY3JwUWMxVlFaU2wwWkV1bzRtbTVUOFVXODArUDFZcHA1QUdjYW54dFMwTm0vd2U2OTR0Zm91WE9KNnprNEpFR3I0NStxbHIrZkFjZnlsOEhSRmcrcWpmMGhSdlo1UjBrLzhpbGZaejc0THBVcUxlb2lBV3dpS3MzQjdCdUpyQWdKWGxWWHYwMXZlWXZtRVZuZEg1QUlRYVJRZEJxU1RaQTZTcW84MHJiVFZEZ21Uc1RGQWJyWkNRY1FoWUMwa21FZU1DdVdpNXpjaWZUaEM1UkJXaHJnTXJiaGk0SmU3L3J1bnErT1pWS3JFN1gzY040QTNDM1NpbEJIVytyVWhjbS80OEJLOXhjeExncDRmSW1oK1VDZ25pRVlIaEZjTHg0d3hMSStZaCtTTE9LMXJTUWhydDJueWwxYnRMMDczMEQyc0ozK1pnTjZBdVlwNE94cWtmR3IiLCJtYWMiOiI4Y2I2NzQ2OWE4MDcyNWFjZDA2ZjQ0ZWUwMjAzZDI2ZDEyZTIzN2Q5YjA2ZjRhZjgyODBlNzg0MTg5NTA4ZTIwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:09:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-212368667\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1720175358 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TofKM30AF1jHjOyX3j0960S2squ6M9DpehjgG3wO</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1720175358\", {\"maxDepth\":0})</script>\n"}}