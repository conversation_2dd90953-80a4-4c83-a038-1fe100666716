{"__meta": {"id": "Xf9156eb895d764520edb38046ac7bc89", "datetime": "2025-07-31 05:34:48", "utime": **********.818025, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940086.919679, "end": **********.818067, "duration": 1.898388147354126, "duration_str": "1.9s", "measures": [{"label": "Booting", "start": 1753940086.919679, "relative_start": 0, "end": **********.681265, "relative_end": **********.681265, "duration": 1.7615861892700195, "duration_str": "1.76s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.681294, "relative_start": 1.****************, "end": **********.818071, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "137ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "bdMe0LtfNv7CIwUcCgLzENhLANkqeahrDblni2jK", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1780638057 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1780638057\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1783659020 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1783659020\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1397159306 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1397159306\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1123212281 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1123212281\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1082008841 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1082008841\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1789110907 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:34:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFRREdzVzN1SnI3dG5VbmlocUNTTnc9PSIsInZhbHVlIjoiMEFyV2FkSVVBeVJZWWpJTmF4V3A3U09rd3doaTVvblVlS2JjTUdlekY0a000UkVCanVKbjNLeTdYQXpRMDgrQU9aR2JOeWI3N3BtclBYZTkxZFhwVXBubHByL2lRdDU4eDRuTERrUHN2OWswV3p1bVY4MFIxaFpLMjIvYW82eUpqTXM5NmUrbEhZREt3M0ZJUEhrcndMS2t6TGt0NmRUc0JvL0N5Q2FBQzNUNEpoUzhwdlFPTDR5d0liRmhZSGF1cGRKY2ZmeHZRZWVHTGR6M3Vzc2hybExqcGVoM0dSakZSQUNOeE8yMWxBMXh4L0h2K0ZzOWV3aU85OGVPNlAwUmkrclh4ZlMxeFRYYnFTSVJoY3FHSEFKN1Q1SUc4SE9BbzBndVNjM0ppVWdmSmJRNU5jbU8zWHBKVnFBRGl3N2tWTVFLSVV6bUNQV201Z21HMURLK1dVTDB0NnRRT3E0OXZ0TmtacXc3ZlFxTkNsSEw0WXNBV1Q2NDVqU0tkQjhScGdqS0VIU2FuNEFCTnhJUTFsN0FyMGpRaTZhaDBMU00vL0d3Tlh5amJnNzU0UVVabU1xQUcrQWQvZG9QVGVrcFhTSU9iNkxnTVJ3ZFBTdSsybU4vSlh1NGZVMGZESU5FNnQ4cnBDRWtSOXVRbUR2ZDZGYkxTNUdPU29VV1dDcnYiLCJtYWMiOiIxZTg0ZWM4NmQzNTYwODJkYjI3NWJmODgxZjE0ZWMzMzg0ODQ2MWZlNzVkOGMyOTg1ZDJlYjM5YTBlZTE5ZGY3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Inl6Z0NvOG5XMENkME5oUStQR2c5L2c9PSIsInZhbHVlIjoiU2trSmhTM2Mxdlk3TC9LQzRYOTMvQlRiTlRqNlZURWZYcG1sT0N3WThtYmFqT3VzZjlFVk5wQlllMHVySkE0S3dsU2ZYdFJiNHZ2ZVg0ODB4T0FSQ1FmdmVJSnlucHUyZ3hZNGwvd1lDYklBS0ttdFJaQlF3TTJJQUE0WklpOGlhVnN0YlErMTVDTDhSa1lFSGorb1VKWW5sR1hsd2hxaHkyU21Kd0lsejhyZG5DdjZWSlNGbWxHUG9vcElPSnA4a0NiSGx1SlFOM2FIT0NJaXFhUW85cjgyd1RRdFdvSmhjUU1tWVR1enNZMjhXcHZNRTZoaVBiL1dEd3hLbE9TcGV2SG1oZTlQaHVZMEo0SzFrT211dDIvc2R6SE5sRi84Z3luZ1IraXdRU2dLMWEzemtVKzZmOXdaVnVibmErVmZEV1pmNENJYlBueWwwNm9YUWJQb3dmalJVeW16MnVBOVJ2dGhDaGZYM2Njc3NFQkdIUURMKy9wUjUvWVl5RHlPSE9rS1BUd21zRGV2UjNuSjF0a3Z5eU9iQjgva2NhZ0VFa2dXdTAvMFE4Rnd2UERFaU4rbXZvS2t5MXlNeTRZS1BwaDlzU2pSdTBOeXZ1QTZKckNqMEplMllLOGRRQUd3NkNJU0RIV2JVa0pMY09tR2lUZ0pLREFSY1JELy9OaTgiLCJtYWMiOiIwYzY5NzlmMWI4MmIyYzRlOTU3YTJmYzVhZjAzNjY3ODhiYjViMGZkMzQ5YTJiYTE5M2FhMDFmZWViMDU5OWQ2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFRREdzVzN1SnI3dG5VbmlocUNTTnc9PSIsInZhbHVlIjoiMEFyV2FkSVVBeVJZWWpJTmF4V3A3U09rd3doaTVvblVlS2JjTUdlekY0a000UkVCanVKbjNLeTdYQXpRMDgrQU9aR2JOeWI3N3BtclBYZTkxZFhwVXBubHByL2lRdDU4eDRuTERrUHN2OWswV3p1bVY4MFIxaFpLMjIvYW82eUpqTXM5NmUrbEhZREt3M0ZJUEhrcndMS2t6TGt0NmRUc0JvL0N5Q2FBQzNUNEpoUzhwdlFPTDR5d0liRmhZSGF1cGRKY2ZmeHZRZWVHTGR6M3Vzc2hybExqcGVoM0dSakZSQUNOeE8yMWxBMXh4L0h2K0ZzOWV3aU85OGVPNlAwUmkrclh4ZlMxeFRYYnFTSVJoY3FHSEFKN1Q1SUc4SE9BbzBndVNjM0ppVWdmSmJRNU5jbU8zWHBKVnFBRGl3N2tWTVFLSVV6bUNQV201Z21HMURLK1dVTDB0NnRRT3E0OXZ0TmtacXc3ZlFxTkNsSEw0WXNBV1Q2NDVqU0tkQjhScGdqS0VIU2FuNEFCTnhJUTFsN0FyMGpRaTZhaDBMU00vL0d3Tlh5amJnNzU0UVVabU1xQUcrQWQvZG9QVGVrcFhTSU9iNkxnTVJ3ZFBTdSsybU4vSlh1NGZVMGZESU5FNnQ4cnBDRWtSOXVRbUR2ZDZGYkxTNUdPU29VV1dDcnYiLCJtYWMiOiIxZTg0ZWM4NmQzNTYwODJkYjI3NWJmODgxZjE0ZWMzMzg0ODQ2MWZlNzVkOGMyOTg1ZDJlYjM5YTBlZTE5ZGY3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Inl6Z0NvOG5XMENkME5oUStQR2c5L2c9PSIsInZhbHVlIjoiU2trSmhTM2Mxdlk3TC9LQzRYOTMvQlRiTlRqNlZURWZYcG1sT0N3WThtYmFqT3VzZjlFVk5wQlllMHVySkE0S3dsU2ZYdFJiNHZ2ZVg0ODB4T0FSQ1FmdmVJSnlucHUyZ3hZNGwvd1lDYklBS0ttdFJaQlF3TTJJQUE0WklpOGlhVnN0YlErMTVDTDhSa1lFSGorb1VKWW5sR1hsd2hxaHkyU21Kd0lsejhyZG5DdjZWSlNGbWxHUG9vcElPSnA4a0NiSGx1SlFOM2FIT0NJaXFhUW85cjgyd1RRdFdvSmhjUU1tWVR1enNZMjhXcHZNRTZoaVBiL1dEd3hLbE9TcGV2SG1oZTlQaHVZMEo0SzFrT211dDIvc2R6SE5sRi84Z3luZ1IraXdRU2dLMWEzemtVKzZmOXdaVnVibmErVmZEV1pmNENJYlBueWwwNm9YUWJQb3dmalJVeW16MnVBOVJ2dGhDaGZYM2Njc3NFQkdIUURMKy9wUjUvWVl5RHlPSE9rS1BUd21zRGV2UjNuSjF0a3Z5eU9iQjgva2NhZ0VFa2dXdTAvMFE4Rnd2UERFaU4rbXZvS2t5MXlNeTRZS1BwaDlzU2pSdTBOeXZ1QTZKckNqMEplMllLOGRRQUd3NkNJU0RIV2JVa0pMY09tR2lUZ0pLREFSY1JELy9OaTgiLCJtYWMiOiIwYzY5NzlmMWI4MmIyYzRlOTU3YTJmYzVhZjAzNjY3ODhiYjViMGZkMzQ5YTJiYTE5M2FhMDFmZWViMDU5OWQ2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1789110907\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bdMe0LtfNv7CIwUcCgLzENhLANkqeahrDblni2jK</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}