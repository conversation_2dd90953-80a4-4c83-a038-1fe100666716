{"__meta": {"id": "X80311b800a39cb7fe9513ddd6bad74f6", "datetime": "2025-07-31 07:47:02", "utime": **********.539939, "method": "GET", "uri": "/storage/expense_receipts/1753947979_logo-dark.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753948021.565903, "end": **********.539966, "duration": 0.9740631580352783, "duration_str": "974ms", "measures": [{"label": "Booting", "start": 1753948021.565903, "relative_start": 0, "end": **********.42474, "relative_end": **********.42474, "duration": 0.8588371276855469, "duration_str": "859ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.424756, "relative_start": 0.****************, "end": **********.539969, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "115ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3043\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1871 to 1877\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1871\" onclick=\"\">routes/web.php:1871-1877</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01399, "accumulated_duration_str": "13.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5014732, "duration": 0.01399, "duration_str": "13.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/expense_receipts/1753947979_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/expense_receipts/1753947979_logo-dark.png", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-550518936 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-550518936\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1597724497 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1597724497\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-359107296 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlRLVXZySTZDYUFLcnBiZkFJaUp2Y2c9PSIsInZhbHVlIjoiUzEyMHZ4YSt2bFlIKzZ4MXBnT2FQUmVGc0RsbEtTazRaYjFaQWxSTHRQenl5TzVyTW5wMlRibmlYYXRvaVlWOVFXNzY0dmM1dWVJZEo1L29DVzJ3WTZ3YUZRSE5NOC95TXlXN3BVc1hZZmRBNzdpRGtwRUpDdjArcmtWSjYxMnMweVhLTVNyZnRsS3RZWWxnMXU4bVMvZmovdFRXRW1nVkVyemxSMWNkUUdVem1Ea0RBUEJ0cTFpY2x4UHZyUkxsSEVFVjEvUjdLdldTSVlwcENwa2l4YUVnRUhoK3paTnQ2MHhodEYwS3oxTlFPV0Y4S1NQeU1ITmNwcG5PQnlpRWdBT3hydkcvZUZMa3dzMmJQN0RYQUpwRDBjU2p4SXBrY2plc0pscWYzSVNoT3kvVzl3TlNkaTN4a3BHS0Eydmo2aTFwa3RiWTNlN1Z2YjFTLzFkaXJLd2szTVR6UVVtODRYODNRUC9NV0dQMGhWdWxYTjUwWnRxWWkvanF6MHZtb2JzcSsxMW9NOEVZU1RKYjNQNkcrYi8vc3pRSHFGem9Gb25wUzl0aUNzOXlmRWpaU2RPcm9BWlZJUzZPSTRsVWdCTnVZSmF1TytXeWNXYjFFa0VFUHQxNkJsNHR3bVNvZ3UxUlA5c2ZkbjlZc3U3QWdlYnhiOGIwNHptSGJsTHgiLCJtYWMiOiI3MTBkNDQ1ODcyMTM3NTVjOWFkOGJlNjJiMmFhNzg0MDZiYjAxY2ZiYzNhODliZGRiYjgxZTcxYWVmZDQ1ZWNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im1UbVg1ai9MaWZIdVk4ZGJLRzc0U3c9PSIsInZhbHVlIjoiWkM0QjQzZGFYYXRCQkFhcVVDLzROK1hlcEtHVkk3UUQrZDMwTzZteXpXSWlqWkdhV0thbEcvdis0SHcrUzJnUVNCU3hUZzNKV2JKWVFIQXdHK01JL0VjdGdxTTVhbGFHb21Jdm5PWVZGVWN0Z3hVYkJYc1k0Qjh6SnBESnB5a1cxTFk0L20vSjRCeW1DQTZhbS9YcDlrYTVidnozdUltT1c3TzJ5ZmF5VWNXd081ZmN3MXdqQVVod01aNDNpdTVtOE9KODllaXhuN3ZjRE1VenBVdmljOWJ6L2FFeGFzZXhSek1HNWdYYXp0MHRLT2kxMzdqY2VWdlI1N29seDRodEVESDUzZUg3SiswY1Rrc09sWEpKLy9CVU1UZTgwOVl0dURQUVpmdzR0TzkxMU1hTnFPeUZYNnY1eXNVeDBZTWJPS2pSTFZyZHNqZmtOM0I2eTd4c1dPV2FJVkpqVzV1SkRKdlZPRmtjbUxQUmV5MVJ5K1dqbXF2YzFWN0JDSGQ2Vk42Vzd2d3RWQjI2ZlhMVkVrVk5jUDNNQS83NXhnQ0Q5eEZlS000WHNQNWNyQ2xmWGxhT0RzOCtZUFV5T0VZSWNYQk1zNnRFcktHN0ZEYXVwNkQ2djdWTGlBUTlOYVI5OWZYbEs2bWs4T1d2V3hzWjJqZW0ybzhQZ3dLS083UU0iLCJtYWMiOiI0Mjc5ZTI4Yjc5MzIyYjVkODE5MDc0NGNmM2IyNmU4ZGJmZWVlNTk4MGZjZWZhYzU2YTI2NWY0MDRjMTU0MGQ0IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Ikh4cEhERHNDT2JJc0NiV0h4UnpXeGc9PSIsInZhbHVlIjoiYXRLbmZSelZyeGQrSEpURkZRZmd4dUlXeTVYUnY5dlZMWUJOYjkremkwNk5ZN2hydkxiZHBFTHNFS2xaMHM4SURGdk13ZDdHeTR1Nm0vMWVFNVlaT2d1Z1ptTkcxbFJhV3RwbHFYN3I0Yno0SlV4b1JJaXZ2QzZSb2V6VWluZ28raVVkL29ONjNib3k0U3IwUXVvUGdSVTNGT1J6OGJjL2JrOFhQWTBZczVRME5rWnNYSlJUR1hLL3JzdHI1U2NVZjFVSmlxTlNkTVNzSlFhWEJQYlFxVXI4eWJHU215OGtSRkRPdmZYVzFPNDZuQThnbkdqMlhsMVBFUTl3VTlSVklUSFN2WExXdXZQNDFFNkNmZFkyY0xXT2drWTRhUDYvaCtYRXk3T29qNG1NMkJnbERodmtJNWt4UVVka2RuREZGZEcrUkdDWkZBdFBORS8rSnhGOTVvazJrV0FmRDlyTHNya1VpQXNoQTByRVJ5NVloRjU5dWQvVFhsTmcvY2tSZUIrUXhYcXlwL1F1WW5vUlI2L1ZzSC90cmJaa293Z05ldUhOSk92S09pSkZNUG45L0x4SWJ0b1kxQ1pJSnArVUJxVDNkV3l5emZsZ0ZkYkQyeHA0WTdhVkdJTXRMQlRndlgyVUN5dnpCOXZVdldxUEYwYTZKSzZUMU9aSVJIdW8iLCJtYWMiOiJiYzhjNzk3YTVjMDRhOGMzMWFiNjEzNDUyMTRmZTIzYjY1NzZkMGU1MzBmNDBhYTg5ZmFhNzQ3OGIyYjc2N2Q2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>if-modified-since</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:46:19 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-359107296\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-769053007 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21K3ZmFzZyr4s2pH9aKWRqwEKhgdUCnDfhl6jx78</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-769053007\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:47:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:46:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25784</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNvM2Vzemg2MEpEL0FjeUdpcml2bGc9PSIsInZhbHVlIjoiSkFPM0pQMTA0TjVZVnVQVCtobWVPUmoxWGoyOXR6Wnlma0xLY2Y2eHRjcGl1STdrYXEvb3p3TWFPWUxqUjFiVkFYbTFrb0JwRnRUOEFqNXlOdmJzd0tEUVRIUHRGYlpSeUdHTHZFMnJXTnl1VUJLZmVxR2s4WEZ4OUk0WVhXSkRVRGhSalA2bG15UUNHR3JBNmNNdU9sRmJobGtWTjc0R1pXNkhYRDM1bXhwS0ZRTm9YdjRFY2U1WXFHMzZZTHZPZlNCcEZVbUJiYjFuMTlTL3JXbUhRdjQ3VnpwQWN4N1NXbE5LeUozWDR1RXRmS0RMazUxK2szcC8vd056NU9GVDZJZFlUdVZUSmpicDZpQ3BsTWdGNjRFU1JsRVFlTzVDbWgwM0tjWEtOTUt4NHlPSWpiR0daQnA2VXhENXRDUFZPUWtjZGlqaE1FQ0J6MVNYRTk1aUpVR0NtWkIwS2hsa3d1Z1IvRUVEWHpCbnYxY1dIcWRhdGFWUEpaNHpKY2pBa1lZbVN0SEhGOW5QNXphVm92UlNzUHpqRjdMQXZpaUJFSUltd2FhNDVIS21zTU0waHFVTFR0UU50YU9QR1F2dHVRVElZU2NhbFJuMjYwdXBud2RvditJRFlYc2pKbGx5R09MRUxNSGRjSG1pbnVoK2gwZTJuOFgzQ3VNUG1jWkYiLCJtYWMiOiI4MmY5NGY5YjIzZDQ1MTMxZDliYzg4ZTQ0MjE5YjBkM2U2NjA1YzE1MTcxMmRhYWRlYzhhNzhjNmNjMDQ5NTA1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:47:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IlRBamxxOG9SREN4UGRLQzFFVStZWGc9PSIsInZhbHVlIjoiRWRDNDRLTFNuK2JUaTZOc24vTVluSkVRNUJRSGRVRGcvcjFJVmFacGMwM2l3dlJWM1pUOWtzVms3U1JsL1dqS2FWUUF0aU12TVVuNzI5bk5mMFFnVytsUm1ZdGYyS2ovK3YzeVpKVUZ0SGlaanFJbWM3OURjanc3ZThDbHkvUmdrWHRsVDFMZHhCL2tyZUFEa0dmdWhOeXVlMmFjMHdpUVB2d09vU2Z1SGxTVllhUDd3d2V5NVBMZlg0Zno4a085bG9reE13VnlwY1l1cDhxaE9GbjNzbGdZUWYwdEozQWJNaktDVlM5bmE0enBVUUxwcE5DUWs2azFuaU1UWHQwbkRwS1h1d1dSUVJ4THNhSndsekF1K0NrV3NqdE1OWDRud2NsSzZYdSt2dFhPd21weHVFVEN1WmpFdG9waDM3OVBGTDBIS1JuT0VMTjhDdmFlNzNjWjh6NmtlNEExakRsQ3YwWkVHSHhkTjdBbEFyNHBOWStKb0xzaFFFRXNmb3oydURkcHhWUUZPaDhWeU5tQ1NUaWt1RngzWFQ0UlNieGtUaittNFpMQ1RreUMwVi9YaytLTjZIUGJDOEdrTmk0UnNwZ01vbEZHRCt2QWhrZE1rYWZ2Um5LeE1ZUFF2U3ZYMDZkY0dwYTV1b2lvdVo2WXNzaW5qMXF1QS9FTnhvWEQiLCJtYWMiOiI2YTIzODEzZmY2Y2JhYTRiYjRlNjQxNjNjNzczODg0ODlkMTlkYzViZmIxNDA3Y2FlYjNkZmJmODM0MWRlMWY2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:47:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNvM2Vzemg2MEpEL0FjeUdpcml2bGc9PSIsInZhbHVlIjoiSkFPM0pQMTA0TjVZVnVQVCtobWVPUmoxWGoyOXR6Wnlma0xLY2Y2eHRjcGl1STdrYXEvb3p3TWFPWUxqUjFiVkFYbTFrb0JwRnRUOEFqNXlOdmJzd0tEUVRIUHRGYlpSeUdHTHZFMnJXTnl1VUJLZmVxR2s4WEZ4OUk0WVhXSkRVRGhSalA2bG15UUNHR3JBNmNNdU9sRmJobGtWTjc0R1pXNkhYRDM1bXhwS0ZRTm9YdjRFY2U1WXFHMzZZTHZPZlNCcEZVbUJiYjFuMTlTL3JXbUhRdjQ3VnpwQWN4N1NXbE5LeUozWDR1RXRmS0RMazUxK2szcC8vd056NU9GVDZJZFlUdVZUSmpicDZpQ3BsTWdGNjRFU1JsRVFlTzVDbWgwM0tjWEtOTUt4NHlPSWpiR0daQnA2VXhENXRDUFZPUWtjZGlqaE1FQ0J6MVNYRTk1aUpVR0NtWkIwS2hsa3d1Z1IvRUVEWHpCbnYxY1dIcWRhdGFWUEpaNHpKY2pBa1lZbVN0SEhGOW5QNXphVm92UlNzUHpqRjdMQXZpaUJFSUltd2FhNDVIS21zTU0waHFVTFR0UU50YU9QR1F2dHVRVElZU2NhbFJuMjYwdXBud2RvditJRFlYc2pKbGx5R09MRUxNSGRjSG1pbnVoK2gwZTJuOFgzQ3VNUG1jWkYiLCJtYWMiOiI4MmY5NGY5YjIzZDQ1MTMxZDliYzg4ZTQ0MjE5YjBkM2U2NjA1YzE1MTcxMmRhYWRlYzhhNzhjNmNjMDQ5NTA1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:47:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IlRBamxxOG9SREN4UGRLQzFFVStZWGc9PSIsInZhbHVlIjoiRWRDNDRLTFNuK2JUaTZOc24vTVluSkVRNUJRSGRVRGcvcjFJVmFacGMwM2l3dlJWM1pUOWtzVms3U1JsL1dqS2FWUUF0aU12TVVuNzI5bk5mMFFnVytsUm1ZdGYyS2ovK3YzeVpKVUZ0SGlaanFJbWM3OURjanc3ZThDbHkvUmdrWHRsVDFMZHhCL2tyZUFEa0dmdWhOeXVlMmFjMHdpUVB2d09vU2Z1SGxTVllhUDd3d2V5NVBMZlg0Zno4a085bG9reE13VnlwY1l1cDhxaE9GbjNzbGdZUWYwdEozQWJNaktDVlM5bmE0enBVUUxwcE5DUWs2azFuaU1UWHQwbkRwS1h1d1dSUVJ4THNhSndsekF1K0NrV3NqdE1OWDRud2NsSzZYdSt2dFhPd21weHVFVEN1WmpFdG9waDM3OVBGTDBIS1JuT0VMTjhDdmFlNzNjWjh6NmtlNEExakRsQ3YwWkVHSHhkTjdBbEFyNHBOWStKb0xzaFFFRXNmb3oydURkcHhWUUZPaDhWeU5tQ1NUaWt1RngzWFQ0UlNieGtUaittNFpMQ1RreUMwVi9YaytLTjZIUGJDOEdrTmk0UnNwZ01vbEZHRCt2QWhrZE1rYWZ2Um5LeE1ZUFF2U3ZYMDZkY0dwYTV1b2lvdVo2WXNzaW5qMXF1QS9FTnhvWEQiLCJtYWMiOiI2YTIzODEzZmY2Y2JhYTRiYjRlNjQxNjNjNzczODg0ODlkMTlkYzViZmIxNDA3Y2FlYjNkZmJmODM0MWRlMWY2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:47:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"71 characters\">http://127.0.0.1:8000/storage/expense_receipts/1753947979_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}