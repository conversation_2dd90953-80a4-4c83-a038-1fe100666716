{"__meta": {"id": "X82673695ab16f4e74f9e039dfdb8ff58", "datetime": "2025-07-31 05:34:00", "utime": **********.420135, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:34:00] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.406867, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753940038.989106, "end": **********.420177, "duration": 1.4310710430145264, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1753940038.989106, "relative_start": 0, "end": **********.179288, "relative_end": **********.179288, "duration": 1.1901819705963135, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.179319, "relative_start": 1.1902129650115967, "end": **********.420182, "relative_end": 5.0067901611328125e-06, "duration": 0.24086308479309082, "duration_str": "241ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50614600, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.371749, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.04405, "accumulated_duration_str": "44.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2571502, "duration": 0.00661, "duration_str": "6.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 15.006}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.292397, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 15.006, "width_percent": 3.224}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.304589, "duration": 0.03408, "duration_str": "34.08ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 18.229, "width_percent": 77.367}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.348133, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 95.596, "width_percent": 4.404}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-7237078 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-7237078\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1422164412 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1422164412\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1484362632 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1484362632\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1090643446 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjFhbGhickVnZVE0R3k0N1R0aVFhcVE9PSIsInZhbHVlIjoicEd2aEc1ZmlxSGpnenVETnJZa3lTQktRbkJJakpFUmcxTFhpNGxEYWtwZjRtNUREVGc4Qzh2SjljSGNmS1BZSW5wSHUvRUNBTXZnTUtMNjl4bEFTVUxRWS96alBGWE9Tekw1OWd3NEpISERON2RudVVObzBDOHU4WXp4WCtSdmxEV25rZ3FockMzNUJKQzE0Ni8vQzJOTGNEbTlpaUw1K0RkVWZuZkt4OHgwblVjVlM0RWVMbnc4bVFMOEVhYTFTdVoxUTFvL3Z1ZzgxenloRTI2UFV3K041L3ZxUzJ0dTZmVjVLWmFTV3dkN3JFck11K1Y3aTBmdm5SM3dEM0EzSXJmZDBNWEVzMFltME1KQjhRaVdSWFp0UWs4b1I5RDNtMjZRTUljSXc5RHN4L1ZyRmM5cnlobjgrRmVvOGF5S3VOaUhVMWRVN05MOFE0M2JncFZqWlZsQmlBZU05czhUaEpubnJaU2lGN0E3THQyOXRaMHE2TDZzbkhWblY5S3o2Q0M2Z3dTTlF6V3VUZnBwRzN2TnhoLzZ3WkcyamlNV1oyM0FhdFFzdXpZaFNBM1hCUUROanNzTUtSNGc2LzFUY3J5c3pZWmdzWUdqNVJSdzljWFRJcHBGR0E0WHNlVTU5UXNFaDVKL2JvcWljK0JaVU0rRnQxZS9qOERyZW5hdHEiLCJtYWMiOiI1ZDhjODZiMzY1ZDM0NDMxOGM0MmI4NDllMmMxNWQ3ZGRkYjhhYjI3NjJhODE5MTRjNzE3NzkzNmQ1OWM0MTQ3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IklZRG8xb3JWeDlhSWNtK1kzU1lSQ1E9PSIsInZhbHVlIjoiZ0swYWo2RENDOUlMTXdRazB1NXRCMXh2MlNveW00YnIvdWM3aGhyZWpLdUhlMjF1Ti8zQmw0endab0FCQjVtQS91VlN3V1crZHlwS0hzc2hUUUZFSVJyVlQzWjRCdGFCcEY3SXpLV0hVN3pRNThRM2JkNW5jYlY0dzJtTys1TFh4M0J4R00yS0VuOTdFTkZpT2hNdmZPNWRGY0t2WTQ0cnNoRjJIRnNscDk1T2h1QmpodDFROU5NcDkrSkgrVmxoNjR1c3FobFg4ZHA0MTdTZit3R3gzTmloV1BvbUdMdDFnd3BlTmcrSm51cU5qOFZwaC9uQnNsT3lvVGRPRnYwMTFqRTVuQnRUdDZZa1B4UGo1TkEvMUpnTEZTNHhFM2NvWUVpSkFPVTFEWTNLbjdLSmQ2MlBtQ0l6aytXM2laZG5IZ3E3T1BpSm90UmhRVGMzRHpqaHlXSlhMQkd0cmw2WXJpQzhGbnZ4Q0J3NUR0REdQUTZhdkFVaXdwdTJ1T2Nrc2pBbDMvTURLQTcxVG5FbXFWdWF2aXh1L3FnTlZBVU5QUHRaamQ4NkdJZFBFZDVLd1lZbWkvRjVtRmpHT3djM2dxS0RpcXRsV0g1S3UxWjhOM1hVOUFLb2VvVWN2bW03eWF5MG1iV0drWS9zSHlNTExtTzhIZXNQRTJ1UForZ3IiLCJtYWMiOiJkNjk3NDcyMjkwOTY2NGE4MjE0MGMyZDk3OGUzYzhmZjk1NjU2NDgxYTM0ZTQzMTIwMTk5OGYxNDcwZWFlOWVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1090643446\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1124361658 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lOWuOQi5K5zalKq6lxC9bmXS2G5Yh3URND5pGg9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1124361658\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1129904325 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:34:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdHRGtMSFZlVklpdERZUWFSQVlKTHc9PSIsInZhbHVlIjoiSGlpMnNBeUppUzhqZWVnNHg5aS9hQUZCd3ptT3JnSHQ4NGJNd0QzMHpBWEhkbUk2WDdpamYzNThoNDlmVVNYbU9WMW9iYTdoNnBvYUFNYUZJOEtuaHIyM0xMdFlLU2R4bjlGZTcrYXZ2dVhMT2F6bm9jaXlZdXJ1V1ZRV1ExaGNJdVladXdTNUg2eExaYUltWTlseWVMS3BpL1UxcFU4NTBQNXRlTStqRU5ET1RIYmZLaStBSGc1YnVZZzBhSnBLZjJRUDY1NTVOeWRsWnlXeXpBQ3phaEJSMDBrendqeFBVRVFXZXJPR01jempmRkhIUjJ5UzVZN2NrMUUzTGZIbG1SMklOZXlEeVdKZFZGVENtY0V3aHNmTm5tSlp6RTVQM212ZWRhanplV29QWlZrQVc0Z2RFR28rVEx2NVJQOXA5YlM5ZnRQbkFvRUwvQ3BPdGo1b3MvT05oYWx1WWk0M3dBcitLY253S2lHWWoyclJRK1piTjZOT1hyRHE3YkZrSXI3eUN2U3hoRzFlODdoYkM1M0phL3pxVG44MjcwWjRvalFKT2lxb2V5bG95L2FwMGZxdTg2QWc3OW9RL2hCQXFpcnhEMkh5MGVIMzNxTjNPeS9mN2g2UnVtYldLZXpVUGl3UFRVSmVjc254eWR4ZXUzdk00UVp0NUswNEJpUjEiLCJtYWMiOiJjYTJhNDVlZTk1YTY4MWQwZjdmMzNiMWNmNGRjODA4N2JhMGJiMzU1YmFhNjBmZDE5ZWI5MmEyNzliNDEwZDZmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlIwclZESDJUdGh1cUFOaFFQN1R4bGc9PSIsInZhbHVlIjoiRTF2ZDhsTUtPWXdKelQvSHRrV3pDajhPVHZkMDZBUUhGeWowQk9jNFFaQWdaQW5mZ2JCdVl1azAvRW5XRDZmTkFmd2NzdEZQSTRrN3l6VGlYbzdYaUxNNlVTUkdlUmNvd2pXZTMvSE9ENTJxbms5Q2V1cGlqN3p1YVNsN1lrNExhbmZYOVVGbmd3Y1VCYUc5c2Qra2V1VFVWRzczYisyMTUwaFlZVWlaMHZIb0Z4Nk5oVVNidGN3VCsvbUZDcVNMMCt5TmJXeURKMWJYTFlONGsrT0JXbTVINnhxb0FZdlRLcTRKR3ZrYlNpeVg4cHdYR21ZazdPbzR0U0MrVWkrb2dTVEQzZytEOW9WU3lRenU4RjlOdEtLRkxuNDdHUDlsUEtJYWhqRmd3M1k0MUF4eXUrRWhPR091ZGZJdnhrbXYrSEhWUTlhYlVJbTJBamwvdWpQblloN1BtNW03S0packlYQzg1Z01sYjdWRGJmbWR2eEgrYVQvd1VlMmVYUGhNcmc5U3phdGsvMStQOS92TVZCemd1bUJiTzlvVnA1TXBFUFF1WXhFSU1YRjgzWWJ1SUZVTWpVT0lXT3VRWVArQnpHMXpmaFFMc0hGQnpOeXhwc1NneVFxQWpnRGNJUUtnNEdkM3ZJQTJKL3RsQTh6UjRSbURzWFBDeWJzWjh1U3QiLCJtYWMiOiI2ZGUyMDczMjI4NjY3ZmYzZjk4ZWJjZmJiNmIwMWNiMjM0MTI5YjkwNWJjNWQyNGMwZDU2NTkwNzk2MTAyM2ZmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdHRGtMSFZlVklpdERZUWFSQVlKTHc9PSIsInZhbHVlIjoiSGlpMnNBeUppUzhqZWVnNHg5aS9hQUZCd3ptT3JnSHQ4NGJNd0QzMHpBWEhkbUk2WDdpamYzNThoNDlmVVNYbU9WMW9iYTdoNnBvYUFNYUZJOEtuaHIyM0xMdFlLU2R4bjlGZTcrYXZ2dVhMT2F6bm9jaXlZdXJ1V1ZRV1ExaGNJdVladXdTNUg2eExaYUltWTlseWVMS3BpL1UxcFU4NTBQNXRlTStqRU5ET1RIYmZLaStBSGc1YnVZZzBhSnBLZjJRUDY1NTVOeWRsWnlXeXpBQ3phaEJSMDBrendqeFBVRVFXZXJPR01jempmRkhIUjJ5UzVZN2NrMUUzTGZIbG1SMklOZXlEeVdKZFZGVENtY0V3aHNmTm5tSlp6RTVQM212ZWRhanplV29QWlZrQVc0Z2RFR28rVEx2NVJQOXA5YlM5ZnRQbkFvRUwvQ3BPdGo1b3MvT05oYWx1WWk0M3dBcitLY253S2lHWWoyclJRK1piTjZOT1hyRHE3YkZrSXI3eUN2U3hoRzFlODdoYkM1M0phL3pxVG44MjcwWjRvalFKT2lxb2V5bG95L2FwMGZxdTg2QWc3OW9RL2hCQXFpcnhEMkh5MGVIMzNxTjNPeS9mN2g2UnVtYldLZXpVUGl3UFRVSmVjc254eWR4ZXUzdk00UVp0NUswNEJpUjEiLCJtYWMiOiJjYTJhNDVlZTk1YTY4MWQwZjdmMzNiMWNmNGRjODA4N2JhMGJiMzU1YmFhNjBmZDE5ZWI5MmEyNzliNDEwZDZmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlIwclZESDJUdGh1cUFOaFFQN1R4bGc9PSIsInZhbHVlIjoiRTF2ZDhsTUtPWXdKelQvSHRrV3pDajhPVHZkMDZBUUhGeWowQk9jNFFaQWdaQW5mZ2JCdVl1azAvRW5XRDZmTkFmd2NzdEZQSTRrN3l6VGlYbzdYaUxNNlVTUkdlUmNvd2pXZTMvSE9ENTJxbms5Q2V1cGlqN3p1YVNsN1lrNExhbmZYOVVGbmd3Y1VCYUc5c2Qra2V1VFVWRzczYisyMTUwaFlZVWlaMHZIb0Z4Nk5oVVNidGN3VCsvbUZDcVNMMCt5TmJXeURKMWJYTFlONGsrT0JXbTVINnhxb0FZdlRLcTRKR3ZrYlNpeVg4cHdYR21ZazdPbzR0U0MrVWkrb2dTVEQzZytEOW9WU3lRenU4RjlOdEtLRkxuNDdHUDlsUEtJYWhqRmd3M1k0MUF4eXUrRWhPR091ZGZJdnhrbXYrSEhWUTlhYlVJbTJBamwvdWpQblloN1BtNW03S0packlYQzg1Z01sYjdWRGJmbWR2eEgrYVQvd1VlMmVYUGhNcmc5U3phdGsvMStQOS92TVZCemd1bUJiTzlvVnA1TXBFUFF1WXhFSU1YRjgzWWJ1SUZVTWpVT0lXT3VRWVArQnpHMXpmaFFMc0hGQnpOeXhwc1NneVFxQWpnRGNJUUtnNEdkM3ZJQTJKL3RsQTh6UjRSbURzWFBDeWJzWjh1U3QiLCJtYWMiOiI2ZGUyMDczMjI4NjY3ZmYzZjk4ZWJjZmJiNmIwMWNiMjM0MTI5YjkwNWJjNWQyNGMwZDU2NTkwNzk2MTAyM2ZmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1129904325\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1552920012 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552920012\", {\"maxDepth\":0})</script>\n"}}