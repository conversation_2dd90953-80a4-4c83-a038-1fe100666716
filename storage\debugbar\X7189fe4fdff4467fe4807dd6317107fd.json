{"__meta": {"id": "X7189fe4fdff4467fe4807dd6317107fd", "datetime": "2025-07-31 05:35:17", "utime": **********.148007, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:35:17] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.142135, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753940115.788221, "end": **********.148035, "duration": 1.359814167022705, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1753940115.788221, "relative_start": 0, "end": 1753940116.959156, "relative_end": 1753940116.959156, "duration": 1.1709351539611816, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753940116.95917, "relative_start": 1.1709492206573486, "end": **********.148038, "relative_end": 2.86102294921875e-06, "duration": 0.18886780738830566, "duration_str": "189ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50614624, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.123314, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.04791, "accumulated_duration_str": "47.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0018451, "duration": 0.02205, "duration_str": "22.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 46.024}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.047476, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 46.024, "width_percent": 2.505}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.057332, "duration": 0.02312, "duration_str": "23.12ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 48.528, "width_percent": 48.257}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.086849, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 96.786, "width_percent": 3.214}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1706341432 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1706341432\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1003956730 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1003956730\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-307300024 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-307300024\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-977063352 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjUzWlI0cG1BZGdjZHRKNi9Nb1ovNUE9PSIsInZhbHVlIjoiSkRxRHZ4LzRRQytBQ3dCa0pvTDVkcDdzOGZNOWoxRWpZbzIxc1pJY3dEVzZpTVZmazBHeTJMdUVBdzVBTTRDRWRjdWNTY0doUEpuTlBNbjhvUzlKZzdBSjdwc21rVjFCbDFFWHYyVk1obnlRSFJXRUhLN3E2ODdGdWZ4MWJxbkNOemFETjJQbHNHWXlJWk5FV3VLWkE1V0duWjhiQ0w0WTlMbnBabFk1K3NzaWMwOUlRdTluOG1qcDYxeThRRjlYdm9Cb2d5NWhySjM2S2FieVFNS1lGbVp5MW04U3RBdVE3Qit6c3BBNVlrV0hwRTZRMFRVeWVEUS8rbGVJZDhSZGV2QUFQOU9Sc2hqaDA2aHFOMGF2YTk4L2VsVStYMDhNcWZnaVNKNkdxZHhHS2ZhK1lFODIwdmxzeWRuY2VEV0pLaTNzWW5yU2dBSmwxbVR0QmtMTWtBS0I4VCs5TjQ3T0NDRlZRdXNNWTh6bitOOWtvR0xKZ2syZUpSS3pBN2xOdnJMaGFBUzVwQXJoVnlSaEx5YndaLzBaRW5BeVB4eGlDTjdOaHlLeDdpaFJTWVZ4SlBGQndndWxNamVCTElRVTNoOStKK1FZVGtock5LZ0I1Q2ZZSXZFdnM1eWVYNnFyRklGcTlmd2JFSzRoVkRFdFZGK1RmaU0wT1V0TU80NjEiLCJtYWMiOiI0Nzc4MjRlMDQ3ODRhZTgwMjRkNmIyNjY5Yzg4ZGZkODk3NzU1ODEzMmI5ODkyMDJlOTk3YmEzMDBmZGU3OThiIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im1OL0J1emM0cHE0OHFjNEp3enM1WHc9PSIsInZhbHVlIjoiTXh6WjJKeDVVOHJycTJyWERyT3ZBSVVkSisyQW8vdWZ2Rzd2NlNxQ2NBM3ZINmNrUkplZTNBSEdzUDFmam9SUjR4SjRxZFNCODRQRkhETklZN2ViV0M4Z1RndTZCbzQyY1dKWkk1TEViQWh3N244Z1lJRHJ6TEhyT0tFV2F5MjVNT2hTaFFmQzBUbnRnYm1DUzN5K2Z1c1p3blQrdGhudi9IaEhBWVF5R1NrYzFyeS9Od3dFN0JWQkk4bzBFVWM4K2RSMHJ5VkpvWHM2QTlhUFYrTktnUTJmZDh6VkFKNUVZS3dERVpyU2t1Z2taZ1J3VHJLcCtueWF4YUM3dE9XcU90K2RkaUVyZHgzaVYyYzlyTUs4czd5bC9VR3dVd0s3djVPQkNZbURGZUYvL1RQU29WazRET3F3djdnV0tLWHZ1OW5CVnVhUTBZV2ZsZGpJYW9Ja1BKdVg2S3htS0s0WFlEbENnbU1ETlVLR2NsazAvN0JybnFmbk5yeCtIMXRxcWk3MkVsSE9XSHl3ekVwUnplUklIQVcybkl0WWlac0FhakFkeldCZEJ2UHdILzcwcTZINTdGVm9pR1IyYW5XTmUwcWRROU9HTlZGYnNMeTc5WmRLSGdhMzVxOVNjRlVaOVZtUW5pRm14NjN6TXVOOEZ2VWUxUE1qNTR0bERJUWkiLCJtYWMiOiIwZDM1MDVkNWI0YjRkMTkzNDliMDU4MDE4MDYyYzQ2ODgwMDAxOTg4YjBlYzM1NDhjNTA3YWU2ZTU3MTA0YmEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-977063352\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-810534509 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lOWuOQi5K5zalKq6lxC9bmXS2G5Yh3URND5pGg9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-810534509\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:35:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdYQlVYVkFodlp6R1lRTkd2VFBzZFE9PSIsInZhbHVlIjoiZkt1ZVZxRzZDVFk3WURodXlNQmZ4aDJRK3hRTmNvMWNIM1Y3U0pvZkd1NllwWlpwK1V2cHdIcGNHcTJ1ZWF6blVvM1g1MEhvT3QyWnhva0NwSFJLdXZ4M1BQNDdyY0xrTzh4Z215NFE2eTNYaER0a3VQVnNHcERRcXRyQkFFay8wTHVGLzlNSHl2Y2c1QVJsSGpCMS92M1p6c2xJeVdPZmY0SmloVzU4TTczZWdTZlphSEtTbTFmbEFSdk5VNUd5R1pGM0Z6Q3IxK2tFNG1BZnIzYnlacVMzZWtwdmtnclQvYzQrQ3c4Vnl3SEdSR1dzZDdMSVJaa0VYVklzbEt5VUIzMEU3cm1hOW9ZUnRkQnJNYTl4b2p5Q1kvZGNDN0QxckN6Rkc4N3h3UU52U0x3OHEzM2VPK1JDVy9LQk53WjY0Vm95ejRzWmxDNEkxa3l2UnprY1hCUFZNa2E3czNlTEg2ZkpuNGVQWHNraW5BblRYV2VwWkMzTjVEanQrOXlPQlBOQm5HRWtIZ3p3VkpmWHFSUWZQdTczbG0rZDZndDJaclQrclZLNkRiQks2SXcyNnlTUTkrY2ozVCt3TEZTTUJpSEl2d1ZaK3FzbFFtK2p2dGxxQmJQN0VxMzVUNDA1Z1l3V0NwSnRLcWUvVEhBQVh1Mk5VNUpvUDJ4YXlqTEIiLCJtYWMiOiIyYjBmYjI3ZjNiMWNkN2VlNTUzMzg4MjBmZGZlMzcxYzcxYTQ5ZDcyY2RhZGM2OGQ3ZTkyMjc5NmJmY2U0OTliIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:35:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImhoZXpScWY1VzVyeVlVVFA0SmpTbXc9PSIsInZhbHVlIjoiZHRxZncvcS9kTzZlZnpUSThud2VVWWZNYk1RMEhwb2ZKVjErTmhDZHZkT1NMdnZ6bk1qNGtFUGdSQzZNQURYUWppcGo4UXZUaUlXeXk5dDB3VWNaTll5LzJZNzVDQXN0dGpmTkpJZDJBRnl2SDN1a05ZR1c5THBqK3FkNzB1SmJsajcrajhvVlZLbzZTVHlVS3lNanp3MHJ2ajJYcy9jTmROSm1ZNUZsMlFzWmtVMURPR1p1MTZRMjcvbGwrd0p6ZmNQVkZQRGQ0M2IrclBzakN3aGRRaGJSNnlGUStPNlU4NkhPdzVFWmE1QlpNaGpQL1FSblBJd1ZOQTNITzd3YWpkWUFEeTZETnJYNkR1YzFWMThVYitiMUZaVnFiTW5KQnFxZldHTjd3dTRmQ3JnTDF2MXNNbi9XTWtkNkNnUkZYNDBUenpnbDVBU0R4RWtuTUZrdWk5allXVU1MVllHU0tzNlNvN2ZHSC95cndGdysyVkdkRm9pdGVPanJYMnBpY3NOOUpSeHE4RHBpd2pEb0twSHgxczNVdDNSMDJJMkM1Y3pNK01hUXhWYWVMRzREV1BhRUxsSURzMWRuYjg2ODJ1Y3hoYU1SZVNxVE9aQVpxNWlLbTBNaU1QWWFiZlYwVjhCTXRjQm90MmZHMEFuWS9aVnpCWVZxZU1yQzFRNlciLCJtYWMiOiJlMzFlNjRkOWUwMmMxMzE5MmM3OGMzNTM4YzEyZTMxZTc3OGY0Y2Q5YTBlNGEzZmE2OWQ5MDEyMGE2MzAwZGM1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:35:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdYQlVYVkFodlp6R1lRTkd2VFBzZFE9PSIsInZhbHVlIjoiZkt1ZVZxRzZDVFk3WURodXlNQmZ4aDJRK3hRTmNvMWNIM1Y3U0pvZkd1NllwWlpwK1V2cHdIcGNHcTJ1ZWF6blVvM1g1MEhvT3QyWnhva0NwSFJLdXZ4M1BQNDdyY0xrTzh4Z215NFE2eTNYaER0a3VQVnNHcERRcXRyQkFFay8wTHVGLzlNSHl2Y2c1QVJsSGpCMS92M1p6c2xJeVdPZmY0SmloVzU4TTczZWdTZlphSEtTbTFmbEFSdk5VNUd5R1pGM0Z6Q3IxK2tFNG1BZnIzYnlacVMzZWtwdmtnclQvYzQrQ3c4Vnl3SEdSR1dzZDdMSVJaa0VYVklzbEt5VUIzMEU3cm1hOW9ZUnRkQnJNYTl4b2p5Q1kvZGNDN0QxckN6Rkc4N3h3UU52U0x3OHEzM2VPK1JDVy9LQk53WjY0Vm95ejRzWmxDNEkxa3l2UnprY1hCUFZNa2E3czNlTEg2ZkpuNGVQWHNraW5BblRYV2VwWkMzTjVEanQrOXlPQlBOQm5HRWtIZ3p3VkpmWHFSUWZQdTczbG0rZDZndDJaclQrclZLNkRiQks2SXcyNnlTUTkrY2ozVCt3TEZTTUJpSEl2d1ZaK3FzbFFtK2p2dGxxQmJQN0VxMzVUNDA1Z1l3V0NwSnRLcWUvVEhBQVh1Mk5VNUpvUDJ4YXlqTEIiLCJtYWMiOiIyYjBmYjI3ZjNiMWNkN2VlNTUzMzg4MjBmZGZlMzcxYzcxYTQ5ZDcyY2RhZGM2OGQ3ZTkyMjc5NmJmY2U0OTliIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:35:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImhoZXpScWY1VzVyeVlVVFA0SmpTbXc9PSIsInZhbHVlIjoiZHRxZncvcS9kTzZlZnpUSThud2VVWWZNYk1RMEhwb2ZKVjErTmhDZHZkT1NMdnZ6bk1qNGtFUGdSQzZNQURYUWppcGo4UXZUaUlXeXk5dDB3VWNaTll5LzJZNzVDQXN0dGpmTkpJZDJBRnl2SDN1a05ZR1c5THBqK3FkNzB1SmJsajcrajhvVlZLbzZTVHlVS3lNanp3MHJ2ajJYcy9jTmROSm1ZNUZsMlFzWmtVMURPR1p1MTZRMjcvbGwrd0p6ZmNQVkZQRGQ0M2IrclBzakN3aGRRaGJSNnlGUStPNlU4NkhPdzVFWmE1QlpNaGpQL1FSblBJd1ZOQTNITzd3YWpkWUFEeTZETnJYNkR1YzFWMThVYitiMUZaVnFiTW5KQnFxZldHTjd3dTRmQ3JnTDF2MXNNbi9XTWtkNkNnUkZYNDBUenpnbDVBU0R4RWtuTUZrdWk5allXVU1MVllHU0tzNlNvN2ZHSC95cndGdysyVkdkRm9pdGVPanJYMnBpY3NOOUpSeHE4RHBpd2pEb0twSHgxczNVdDNSMDJJMkM1Y3pNK01hUXhWYWVMRzREV1BhRUxsSURzMWRuYjg2ODJ1Y3hoYU1SZVNxVE9aQVpxNWlLbTBNaU1QWWFiZlYwVjhCTXRjQm90MmZHMEFuWS9aVnpCWVZxZU1yQzFRNlciLCJtYWMiOiJlMzFlNjRkOWUwMmMxMzE5MmM3OGMzNTM4YzEyZTMxZTc3OGY0Y2Q5YTBlNGEzZmE2OWQ5MDEyMGE2MzAwZGM1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:35:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-813008600 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-813008600\", {\"maxDepth\":0})</script>\n"}}