{"__meta": {"id": "X4db337d3240ede6e38a4cfe1f40c114c", "datetime": "2025-07-31 06:31:49", "utime": **********.440167, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943507.216358, "end": **********.440218, "duration": 2.223860025405884, "duration_str": "2.22s", "measures": [{"label": "Booting", "start": 1753943507.216358, "relative_start": 0, "end": **********.275972, "relative_end": **********.275972, "duration": 2.0596139430999756, "duration_str": "2.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.27612, "relative_start": 2.****************, "end": **********.440232, "relative_end": 1.4066696166992188e-05, "duration": 0.*****************, "duration_str": "164ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XN9k6g5RTXUclPKuAqyRspsDmdagXcLsWt9hQIbG", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-1446826798 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1446826798\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2004401868 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2004401868\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-672444048 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-672444048\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-46297799 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-46297799\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-21625535 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-21625535\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1813920500 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:31:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBITmZIT0c4Ry9Hdll4KzR6UlBOR0E9PSIsInZhbHVlIjoiWHJ1bG56MVhUaEFNSTBSaHYwQnRrdXJ2ejlISFNZVng1MFpHeGJ6ZDV5WjJYdVdyVmpEY3JBMlg0eUl3NjRQVVluZlRjeUl5Ty95UTBzMWJOcnBFMDRKb1o0NXJhbnZRVHR6b2tONWorRVBja25nY1p0K1d1SkRTTWh4MFBRbUtCdGtuNGt2dGE4aTFHR3gzRnoxMUN2ZEZpNjVoeG5xNlNhOEREdUNOclRoVlVTbFY5WEcwWDVadGVvZkFiOWErZnRVaXI3clRoQ1F4SDhVVzhwdmEwVXM5KzBDaGZrclJHM1doRXNlY0d2a2xOQ2ZEYUo5Yk10WUJVeXVSR3dOb3h3K1FXajUyS3IzRDR1ZVNQN295Y2FyK21RejRPY0V3VER3RmdUZEEzMjltbGFkcHhuOE84Mm55RzRjNnpCcUR5N1JzbkFoTkVpZ3JUU1cwRDBVN0ExKyt3RlhXVUJDWmp5T1M5bHFRUHE5Uit6dFZGQU8wREx2bmxDWmNqdnA0azI0RDVDZU1kSyt6Q3F4MnRaYVpGT3cxQXA5azNQZmtqbDNnbzNuOVllSEJaSng4R0FnWkNvb0NCOW5MdHpZUlBINGVhVWduclhIQ254UGZHNVhORU4rWnlMUHBIeFcvcHFVcGkvbUhSYlFXZVpqTUdCMzRsd25yYmkxZm1ZYmUiLCJtYWMiOiJjNDFhMTBjMGJjMjI0YzBhNTQ0MzUyNmY1MWI0ZDAyOWYzYTBiNWU0NWIwODNlOWE1OTlkYjUwMGIyOWJiNGUyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlN2bTRqWWxrM0xKem1Vc3h1Slg4Y0E9PSIsInZhbHVlIjoiV2NFQkZXWldyQ2hubXN0eDZDMzBQc3N4blkwRDg4M0twTXVreDc3V2xIY3lwRisyUDNnS0JRcVAySGRicGVYTFJ1MFhMQjMzTm5mb1JnNkU3b3RPYmFMcm84ekhwUFJONVhCS01iY3dESHdyM0l3SXdDRzk5bUhYQW96NDFRRWNQT295UDZ5a3VDYzBzN1BlWEU0VCtFaXRkbVlDdEp3dXBYelhvTGZzb2xhMis5YzJkZWJPMm1JOEtlL1UrZkViNTdvQ29wcGMyYjdTRDdTTGpJWTRyT01FOTA2b29Wc0FSZWxRNGlxYWJ4Zk50cVB3NG83VEJlZDdNTGx6MVZsb0toOWRjMTFvODIra2toWlgvZHh5WTU0amg3REFZQ01CUTFmRXdvZjdWbW9QbXF1ZXpheHlLb1dON2NmZm13cEk5YUQ2Q1k3VXZvcTlaMlYzTXM3dHMvckx2MU5yZm9IcmZSS3lrK0xoYlFuY3NETDF6UVVsUC81eEdPRlhIZ0J6MnhYWkpwbXp1eS9MVDRiR2t0cVlVb2FyNTVMMG8zRnNwamRvT0w0TU9VN2R3bEQzMm5Lb0NlOWRrNnQ5WG01K2d3cDlrSzVoNGptTE1RUWFLS1JDLzJ6NXQ4TXkrVmgwRW83dUwzR0JxUU5jckxNSlpBUjNaZnc0Z1JpcU13ajAiLCJtYWMiOiIwZDk0ZjE2OGVlOTQ3MGZkMzhhYTYzYTBlOTEzNGQ3ZWI1MzYzYWE1OTZjODA0NWExNjcwZDViNjRjYjQ1NmZlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBITmZIT0c4Ry9Hdll4KzR6UlBOR0E9PSIsInZhbHVlIjoiWHJ1bG56MVhUaEFNSTBSaHYwQnRrdXJ2ejlISFNZVng1MFpHeGJ6ZDV5WjJYdVdyVmpEY3JBMlg0eUl3NjRQVVluZlRjeUl5Ty95UTBzMWJOcnBFMDRKb1o0NXJhbnZRVHR6b2tONWorRVBja25nY1p0K1d1SkRTTWh4MFBRbUtCdGtuNGt2dGE4aTFHR3gzRnoxMUN2ZEZpNjVoeG5xNlNhOEREdUNOclRoVlVTbFY5WEcwWDVadGVvZkFiOWErZnRVaXI3clRoQ1F4SDhVVzhwdmEwVXM5KzBDaGZrclJHM1doRXNlY0d2a2xOQ2ZEYUo5Yk10WUJVeXVSR3dOb3h3K1FXajUyS3IzRDR1ZVNQN295Y2FyK21RejRPY0V3VER3RmdUZEEzMjltbGFkcHhuOE84Mm55RzRjNnpCcUR5N1JzbkFoTkVpZ3JUU1cwRDBVN0ExKyt3RlhXVUJDWmp5T1M5bHFRUHE5Uit6dFZGQU8wREx2bmxDWmNqdnA0azI0RDVDZU1kSyt6Q3F4MnRaYVpGT3cxQXA5azNQZmtqbDNnbzNuOVllSEJaSng4R0FnWkNvb0NCOW5MdHpZUlBINGVhVWduclhIQ254UGZHNVhORU4rWnlMUHBIeFcvcHFVcGkvbUhSYlFXZVpqTUdCMzRsd25yYmkxZm1ZYmUiLCJtYWMiOiJjNDFhMTBjMGJjMjI0YzBhNTQ0MzUyNmY1MWI0ZDAyOWYzYTBiNWU0NWIwODNlOWE1OTlkYjUwMGIyOWJiNGUyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlN2bTRqWWxrM0xKem1Vc3h1Slg4Y0E9PSIsInZhbHVlIjoiV2NFQkZXWldyQ2hubXN0eDZDMzBQc3N4blkwRDg4M0twTXVreDc3V2xIY3lwRisyUDNnS0JRcVAySGRicGVYTFJ1MFhMQjMzTm5mb1JnNkU3b3RPYmFMcm84ekhwUFJONVhCS01iY3dESHdyM0l3SXdDRzk5bUhYQW96NDFRRWNQT295UDZ5a3VDYzBzN1BlWEU0VCtFaXRkbVlDdEp3dXBYelhvTGZzb2xhMis5YzJkZWJPMm1JOEtlL1UrZkViNTdvQ29wcGMyYjdTRDdTTGpJWTRyT01FOTA2b29Wc0FSZWxRNGlxYWJ4Zk50cVB3NG83VEJlZDdNTGx6MVZsb0toOWRjMTFvODIra2toWlgvZHh5WTU0amg3REFZQ01CUTFmRXdvZjdWbW9QbXF1ZXpheHlLb1dON2NmZm13cEk5YUQ2Q1k3VXZvcTlaMlYzTXM3dHMvckx2MU5yZm9IcmZSS3lrK0xoYlFuY3NETDF6UVVsUC81eEdPRlhIZ0J6MnhYWkpwbXp1eS9MVDRiR2t0cVlVb2FyNTVMMG8zRnNwamRvT0w0TU9VN2R3bEQzMm5Lb0NlOWRrNnQ5WG01K2d3cDlrSzVoNGptTE1RUWFLS1JDLzJ6NXQ4TXkrVmgwRW83dUwzR0JxUU5jckxNSlpBUjNaZnc0Z1JpcU13ajAiLCJtYWMiOiIwZDk0ZjE2OGVlOTQ3MGZkMzhhYTYzYTBlOTEzNGQ3ZWI1MzYzYWE1OTZjODA0NWExNjcwZDViNjRjYjQ1NmZlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1813920500\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1931324209 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XN9k6g5RTXUclPKuAqyRspsDmdagXcLsWt9hQIbG</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931324209\", {\"maxDepth\":0})</script>\n"}}