<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Expense extends Model
{
    protected $fillable = [
        'name',
        'category_id',
        'vendor_name',
        'date',
        'description',
        'amount',
        'attachment',
        'project_id',
        'task_id',
        'created_by',
    ];

    // Get Expense based task
    public function task()
    {
        return $this->hasOne('App\Models\ProjectTask', 'id', 'task_id');
    }

    // Get Expense based project
    public function project()
    {
        return $this->hasOne('App\Models\Project', 'id', 'project_id');
    }

    // Get Expense category
    public function category()
    {
        return $this->hasOne('App\Models\ProductServiceCategory', 'id', 'category_id');
    }

    // Format price for display
    public function getFormattedAmountAttribute()
    {
        return number_format($this->amount, 2);
    }
}
