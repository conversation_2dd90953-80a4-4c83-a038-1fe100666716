<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_infos', function (Blueprint $table) {
            $table->id();

            // User relationship
            $table->unsignedBigInteger('created_by');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');

            // General Company Information
            $table->string('company_name')->nullable();
            $table->string('company_email')->nullable();
            $table->string('company_phone')->nullable();
            $table->string('country')->nullable();
            $table->string('currency', 10)->nullable();

            // Business Registration Details
            $table->string('business_gst')->nullable();
            $table->string('business_state')->nullable();
            $table->string('business_logo')->nullable(); // File path for uploaded logo
            $table->boolean('agree_gst_change')->default(false);

            // Business Physical Address
            $table->text('street_address')->nullable();
            $table->string('city')->nullable();
            $table->string('pincode', 20)->nullable();
            $table->string('state_prov_region')->nullable();
            $table->string('address_country')->nullable();
            $table->string('time_zone')->nullable();

            // Authorized Contact Person
            $table->string('contact_person_name')->nullable();
            $table->string('job_position')->nullable();
            $table->string('contact_email')->nullable();
            $table->string('contact_phone')->nullable();

            // Additional Business Details (for future expansion)
            $table->string('business_type')->nullable();
            $table->string('industry')->nullable();
            $table->string('website')->nullable();
            $table->text('business_description')->nullable();
            $table->date('established_date')->nullable();
            $table->string('registration_number')->nullable();
            $table->string('tax_id')->nullable();

            // Timestamps
            $table->timestamps();

            // Indexes for better performance
            $table->index('created_by');
            $table->index(['created_by', 'company_name']);
            $table->index('business_gst');
            $table->index('company_email');

            // Ensure one business info per user
            $table->unique('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_infos');
    }
};