{"__meta": {"id": "X11e1130fa6434e669a3448c2b1a9b67e", "datetime": "2025-07-31 05:34:46", "utime": **********.885811, "method": "GET", "uri": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940085.392947, "end": **********.885861, "duration": 1.4929139614105225, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": 1753940085.392947, "relative_start": 0, "end": **********.760831, "relative_end": **********.760831, "duration": 1.3678841590881348, "duration_str": "1.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.760853, "relative_start": 1.***************, "end": **********.885866, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "125ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "oElZWdQzsUwfuVkXdK1fjwFVxdAFbqbutz7df8kl", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "status_code": "<pre class=sf-dump id=sf-dump-635390415 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-635390415\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-623782716 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-623782716\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1477107387 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1477107387\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-354955846 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-354955846\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-18549378 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-18549378\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2077561183 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:34:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBaNXZvNVFxbUM5WFlzV3ZUKzJrbEE9PSIsInZhbHVlIjoiMmkrdFdpU0RiUkNzSW81N0xyaWpDTTdoTmlEU1dkeVkrNFNGMm0yeVUyYXpIVUhhZjBqREFqYlg0eXB2MTlyT2RhQ1ZtT0Zqa0pQL0FEWjRpMTZ2Qk1MTkcrdGlrTXJnZVlRVE9KZU1FYnQ4eWZVVTNWdlRjMWM0bmJaYUJaNUFWSmNONHMyck1Pd1JnMlFzaFdFT3lLM1N4ZWxkM0JsS3JSak5mRUp5blMxdktwTUhzVnV6M1R6UG5NaUk2QWgxRGZ4ZUN6T3JDUmoyNW14bWE3UGJ6eTdPVjlEY1ZzT3R5YmpKb3Y2Y054SmhGV2ZFemp1MnVDWFB4UUpxeVgwQXc2cTNIUXFNOEJTSlQwSDZEL3doeExleHgvNTdDYWd4YzcrL0syd0NNRlplUzlGWEFnMEs2MnZoZW8yc0hBckh5Vjl4M2dINFE1VXAyQUs2Sm90aEhQQlZHZEJ4QVJaaU5DWk45ZXI3Nmx4ZXBvK1ozRmJaNjNSWlFhSjYvaHl5ai9DUytNVGkxaU1NcjlFQmptVEE2clZ5SlkvaHpzTDNOcGJJNUZham1UeGk4bVRwdGhxakpySlpJV3l1bWdBSGRrUWlJVjJDTWJJTzB1aTBGVnJRUUNRanJaODBvMklEN1ZnM1BUdE9yTGVHeFdMR3ZDMXptaVBNQzdSTmJqaUIiLCJtYWMiOiJlMDAxYzFkZGViODhhOWNmMTUxYjI1NTU4MTI2OWM4OTE0ZWM3OWJiZmQxZjJkYjQ0Y2QzZTlhZTE0NWM0MGNkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkhEeEo1Tk9pbnZ4ZU1TbXVYckMyZVE9PSIsInZhbHVlIjoiRWJqN2JuKzJGeFBvTUNRK1pJejhJRS8rSUc4dGY2d0dHWFRnZVFnTDQrbGN2RCtLTFY2ZEdERXhBZHVSbzk2MG9zTWh1L29HM2Q3dmdrTjc2aldQSk1Feis3UzhuRUJ1cVMydmNPVDgwY1JlZWlWbGJDRXRvNlk0WHVndm1ldzBkTXdrRGI5OTdjZk9uS2pOSGdtY2FKN2JtQzQvNEFEdFA3STRrYmRia1hXS0tqOFcrc3NwNzZBOXNKVkM5b1ZMd3R5QkhqR1IrUW9EczZMSElaL1VXazVtT0N1QWh1ckZtc282aC9ubTVKSUpKem1KdmVsUjdaNWxlSVlURXgwOHh5N2JmYWloZlk3ZmRKcVpQaW5Gb1BZTWF0ZFppQmNZYVBEOSt1dzNLRDE2NkJMSUJSb093WU44ZlFhMUFPYzE4TVYzakVmdEtXdlF6MDNaZjlFZWhLS3NaYVo1dWhoMzNPcWg4VHN0WGU3WVpRRlVBMyttZVJUT0NJMW9IUlJZeFpvWFJqSGZJM0xOTW03QjRLejhmeVBkL0V3NllieEZvOFQ4VFRjZEU1d2hVbnNkYlJlVUkxaFFxMS9LYzgwQVlRSmtjc2I1UDhDSHEwOEtPYmpuOWwzYlkzem5XK1Y5Z1hqZ0RWNTVuWWZYZXpjckdxWm1YQ2hBR3lFbHk4YmgiLCJtYWMiOiI2NDc2NjBlMjJmMzc4YWE2ZWM0YjRkN2RkODI1YjZhNzBlMTAzM2VkNjlkZjk5NjMzNzA4MzU0NGQ5MzUzNmQ1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBaNXZvNVFxbUM5WFlzV3ZUKzJrbEE9PSIsInZhbHVlIjoiMmkrdFdpU0RiUkNzSW81N0xyaWpDTTdoTmlEU1dkeVkrNFNGMm0yeVUyYXpIVUhhZjBqREFqYlg0eXB2MTlyT2RhQ1ZtT0Zqa0pQL0FEWjRpMTZ2Qk1MTkcrdGlrTXJnZVlRVE9KZU1FYnQ4eWZVVTNWdlRjMWM0bmJaYUJaNUFWSmNONHMyck1Pd1JnMlFzaFdFT3lLM1N4ZWxkM0JsS3JSak5mRUp5blMxdktwTUhzVnV6M1R6UG5NaUk2QWgxRGZ4ZUN6T3JDUmoyNW14bWE3UGJ6eTdPVjlEY1ZzT3R5YmpKb3Y2Y054SmhGV2ZFemp1MnVDWFB4UUpxeVgwQXc2cTNIUXFNOEJTSlQwSDZEL3doeExleHgvNTdDYWd4YzcrL0syd0NNRlplUzlGWEFnMEs2MnZoZW8yc0hBckh5Vjl4M2dINFE1VXAyQUs2Sm90aEhQQlZHZEJ4QVJaaU5DWk45ZXI3Nmx4ZXBvK1ozRmJaNjNSWlFhSjYvaHl5ai9DUytNVGkxaU1NcjlFQmptVEE2clZ5SlkvaHpzTDNOcGJJNUZham1UeGk4bVRwdGhxakpySlpJV3l1bWdBSGRrUWlJVjJDTWJJTzB1aTBGVnJRUUNRanJaODBvMklEN1ZnM1BUdE9yTGVHeFdMR3ZDMXptaVBNQzdSTmJqaUIiLCJtYWMiOiJlMDAxYzFkZGViODhhOWNmMTUxYjI1NTU4MTI2OWM4OTE0ZWM3OWJiZmQxZjJkYjQ0Y2QzZTlhZTE0NWM0MGNkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkhEeEo1Tk9pbnZ4ZU1TbXVYckMyZVE9PSIsInZhbHVlIjoiRWJqN2JuKzJGeFBvTUNRK1pJejhJRS8rSUc4dGY2d0dHWFRnZVFnTDQrbGN2RCtLTFY2ZEdERXhBZHVSbzk2MG9zTWh1L29HM2Q3dmdrTjc2aldQSk1Feis3UzhuRUJ1cVMydmNPVDgwY1JlZWlWbGJDRXRvNlk0WHVndm1ldzBkTXdrRGI5OTdjZk9uS2pOSGdtY2FKN2JtQzQvNEFEdFA3STRrYmRia1hXS0tqOFcrc3NwNzZBOXNKVkM5b1ZMd3R5QkhqR1IrUW9EczZMSElaL1VXazVtT0N1QWh1ckZtc282aC9ubTVKSUpKem1KdmVsUjdaNWxlSVlURXgwOHh5N2JmYWloZlk3ZmRKcVpQaW5Gb1BZTWF0ZFppQmNZYVBEOSt1dzNLRDE2NkJMSUJSb093WU44ZlFhMUFPYzE4TVYzakVmdEtXdlF6MDNaZjlFZWhLS3NaYVo1dWhoMzNPcWg4VHN0WGU3WVpRRlVBMyttZVJUT0NJMW9IUlJZeFpvWFJqSGZJM0xOTW03QjRLejhmeVBkL0V3NllieEZvOFQ4VFRjZEU1d2hVbnNkYlJlVUkxaFFxMS9LYzgwQVlRSmtjc2I1UDhDSHEwOEtPYmpuOWwzYlkzem5XK1Y5Z1hqZ0RWNTVuWWZYZXpjckdxWm1YQ2hBR3lFbHk4YmgiLCJtYWMiOiI2NDc2NjBlMjJmMzc4YWE2ZWM0YjRkN2RkODI1YjZhNzBlMTAzM2VkNjlkZjk5NjMzNzA4MzU0NGQ5MzUzNmQ1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2077561183\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1252718675 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oElZWdQzsUwfuVkXdK1fjwFVxdAFbqbutz7df8kl</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1252718675\", {\"maxDepth\":0})</script>\n"}}