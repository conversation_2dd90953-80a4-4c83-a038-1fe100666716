{"__meta": {"id": "X86762947c3742fa2d4a396129ae3c7e3", "datetime": "2025-07-31 05:22:56", "utime": **********.552075, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939375.553228, "end": **********.552115, "duration": 0.9988870620727539, "duration_str": "999ms", "measures": [{"label": "Booting", "start": 1753939375.553228, "relative_start": 0, "end": **********.477007, "relative_end": **********.477007, "duration": 0.9237790107727051, "duration_str": "924ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.477062, "relative_start": 0.****************, "end": **********.552133, "relative_end": 1.811981201171875e-05, "duration": 0.*****************, "duration_str": "75.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "r7LaRQMJoWaGsmG8VCeEWAwgxditrR703dZDI6xH", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1476916253 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1476916253\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-439660843 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-439660843\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1095238809 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1095238809\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-916682104 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-916682104\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-591175133 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-591175133\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-544739404 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:22:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5JaHloMkZlUmxIOUpvcW9NU2pBUHc9PSIsInZhbHVlIjoiTzVhN2FDTGFmMmoxdVVWc2ZxZWlkeTQ1cS9GSDNZaXkrT0ZNeHFldU5LNDQ1TXRWSkhvY3hTNFRyY2w3TWhYTjUvN1JNSU9ocFgrZ2FPK2haclRvUysrOVBtQVplOHlUSVZSdzU5U3ZoWS9WTnA0ZDRaeTJhbXdzZE55MkQ5YXFNWGFTT1dPSTlwZWJ1eWdoUlJxNDBJdlZ0ajNQbzVJVmFib3J1bCszZ1JFUW05LzlpbnVaWWtXWGk3SCtNcHg3VSt1MVp1dXMyUm15ZDRHTjJyUGNTMzhuODFsUHlrRFdpSnI4ZCtwbS83aHNOWVJwSlhrU1p0dUVrdlV5eE5xTHdoYzVkeTBLOVBvU2VCZHVQNEVmVmd2K2hhT09DNTV0WmZJMGhnZWxDR1d2WGZ3WEZob1NyWk5ZVDZFN2dzckNDVlM2WU9LOE1NU3pKYjk5alpkc3JqQzJBTjJCOHJZcXJIVzM2VTI2NTVIbDFGSDZqcUFScVBpcDNpTnFxSzdtdFZyN1Y4eDNnODNVc090QWNGeW95bEltdEc3bzVwbm1hdXJNTEVXbUxIQ1hpejNuNFk2MFh3Y1lvSFcySzFWK2JMeTJoWEplN0VWdC9lM0ZKM2xRU1RPUlNrWEpYbDB3azlDNFh2ZW5oTXR4ZG5MVzdCV213UUVVUzVNcWZ1Y1kiLCJtYWMiOiI2MDY2Y2IyMmY3YjhlOTg5Y2QzMDU0Nzg0MzIxODk0MDUwZTMwOTQ1ODRhZDYyMmJlOThjYjdiOWY5NDdhNjhjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:22:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkpCRUlRaGFGTmZvdit5UnlzaFMxM2c9PSIsInZhbHVlIjoiem9vYWFDSG1XYnJ6cVVFQmFBMTlEdE5WN0dDNWVZNTZrTG9IZXJYc2tKeXExbG83QnRCTzBKNjBHSFU3NkJKWmRkSEUwUUkvcWxHN1o5Y1RMc3B3K0pkRS9aNitSNG91ME01bll0N0J2Tm9SUTZocWd0MG9rbEJvWkVDZzFFR3JXSUJaZ1VZMnpOYjI0cVBoK0tyVzJ6NFBOMThJMGFwM1ZMSDUxN2xUbmFwMURPTlJWbEg2ZFlMcVJPVVJxS2YxcVRybFBZU3VWeWMxbUNsTG1EUkRhNUtHcENCMC9LejZsM3hMckRkTEFhdmRva3dOY1dKMFJRQklTaU8yZDRqWU1QMWNhS2JIMHc4SVdCNGRSenhYaXF3T2lGSFhZQXMvNU1MVFlxRE15VE5kZ0djZSt0MmRzaXNydzg5VTM2MUYyTmlpRTZVQ0J2YWVGbExOMUhzdkUyOStTTWZlS0YzNWtYM0Zxajd3V3hvOTI3aVJrQmNrZ0o1L3FiOXRYaGpUSkhUcm9rMmdkVEFuWE52andIK0p6Ny9PNERrZzlHeW85aVdJQWtKRmhSUzl2ZG1GVTYybE4vSkdwUHB2bldsL1lENlRKTFk0VjJGRHVRVktIWmFHeFZLSVdoU0prMjh4V3NQWlFBYXpCUzNKcUtWazJuZStncUlkZzgzVjVyb0YiLCJtYWMiOiJmNmVmZDIxODIxYTY3YzI1NTAzMDllMmNhZDUwNGUwMDNhYmYzMzY4ZTNmZTk1NTA3YTU3OGM3ZmJjM2NjYTQ5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:22:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5JaHloMkZlUmxIOUpvcW9NU2pBUHc9PSIsInZhbHVlIjoiTzVhN2FDTGFmMmoxdVVWc2ZxZWlkeTQ1cS9GSDNZaXkrT0ZNeHFldU5LNDQ1TXRWSkhvY3hTNFRyY2w3TWhYTjUvN1JNSU9ocFgrZ2FPK2haclRvUysrOVBtQVplOHlUSVZSdzU5U3ZoWS9WTnA0ZDRaeTJhbXdzZE55MkQ5YXFNWGFTT1dPSTlwZWJ1eWdoUlJxNDBJdlZ0ajNQbzVJVmFib3J1bCszZ1JFUW05LzlpbnVaWWtXWGk3SCtNcHg3VSt1MVp1dXMyUm15ZDRHTjJyUGNTMzhuODFsUHlrRFdpSnI4ZCtwbS83aHNOWVJwSlhrU1p0dUVrdlV5eE5xTHdoYzVkeTBLOVBvU2VCZHVQNEVmVmd2K2hhT09DNTV0WmZJMGhnZWxDR1d2WGZ3WEZob1NyWk5ZVDZFN2dzckNDVlM2WU9LOE1NU3pKYjk5alpkc3JqQzJBTjJCOHJZcXJIVzM2VTI2NTVIbDFGSDZqcUFScVBpcDNpTnFxSzdtdFZyN1Y4eDNnODNVc090QWNGeW95bEltdEc3bzVwbm1hdXJNTEVXbUxIQ1hpejNuNFk2MFh3Y1lvSFcySzFWK2JMeTJoWEplN0VWdC9lM0ZKM2xRU1RPUlNrWEpYbDB3azlDNFh2ZW5oTXR4ZG5MVzdCV213UUVVUzVNcWZ1Y1kiLCJtYWMiOiI2MDY2Y2IyMmY3YjhlOTg5Y2QzMDU0Nzg0MzIxODk0MDUwZTMwOTQ1ODRhZDYyMmJlOThjYjdiOWY5NDdhNjhjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:22:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkpCRUlRaGFGTmZvdit5UnlzaFMxM2c9PSIsInZhbHVlIjoiem9vYWFDSG1XYnJ6cVVFQmFBMTlEdE5WN0dDNWVZNTZrTG9IZXJYc2tKeXExbG83QnRCTzBKNjBHSFU3NkJKWmRkSEUwUUkvcWxHN1o5Y1RMc3B3K0pkRS9aNitSNG91ME01bll0N0J2Tm9SUTZocWd0MG9rbEJvWkVDZzFFR3JXSUJaZ1VZMnpOYjI0cVBoK0tyVzJ6NFBOMThJMGFwM1ZMSDUxN2xUbmFwMURPTlJWbEg2ZFlMcVJPVVJxS2YxcVRybFBZU3VWeWMxbUNsTG1EUkRhNUtHcENCMC9LejZsM3hMckRkTEFhdmRva3dOY1dKMFJRQklTaU8yZDRqWU1QMWNhS2JIMHc4SVdCNGRSenhYaXF3T2lGSFhZQXMvNU1MVFlxRE15VE5kZ0djZSt0MmRzaXNydzg5VTM2MUYyTmlpRTZVQ0J2YWVGbExOMUhzdkUyOStTTWZlS0YzNWtYM0Zxajd3V3hvOTI3aVJrQmNrZ0o1L3FiOXRYaGpUSkhUcm9rMmdkVEFuWE52andIK0p6Ny9PNERrZzlHeW85aVdJQWtKRmhSUzl2ZG1GVTYybE4vSkdwUHB2bldsL1lENlRKTFk0VjJGRHVRVktIWmFHeFZLSVdoU0prMjh4V3NQWlFBYXpCUzNKcUtWazJuZStncUlkZzgzVjVyb0YiLCJtYWMiOiJmNmVmZDIxODIxYTY3YzI1NTAzMDllMmNhZDUwNGUwMDNhYmYzMzY4ZTNmZTk1NTA3YTU3OGM3ZmJjM2NjYTQ5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:22:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-544739404\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r7LaRQMJoWaGsmG8VCeEWAwgxditrR703dZDI6xH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}