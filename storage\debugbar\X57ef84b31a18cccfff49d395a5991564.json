{"__meta": {"id": "X57ef84b31a18cccfff49d395a5991564", "datetime": "2025-07-31 06:30:37", "utime": **********.014662, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943434.860242, "end": **********.014728, "duration": 2.1544861793518066, "duration_str": "2.15s", "measures": [{"label": "Booting", "start": 1753943434.860242, "relative_start": 0, "end": 1753943436.825779, "relative_end": 1753943436.825779, "duration": 1.9655370712280273, "duration_str": "1.97s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753943436.825845, "relative_start": 1.****************, "end": **********.014734, "relative_end": 5.9604644775390625e-06, "duration": 0.****************, "duration_str": "189ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "brdt2VWoNbvQtr5l0vLOW8a3jeohXEGDuzlcAcdG", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-2120790004 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2120790004\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2140693748 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2140693748\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-692152361 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-692152361\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1599915388 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1599915388\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1723191565 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1723191565\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1753509554 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:30:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5WSWRHOUVGU1loQ3FDMmpTbkdtN1E9PSIsInZhbHVlIjoianJIODJSZ0VHcm1SM2pHeGMvcW1ybnRNbEkwR3BWYjFFMUpjOEFSdGRpamJBNEVGdWdvOHVnVzN0cHBlK3hZRmlrSituMTNYZUJYVGVxb0UxOVcrVnIwSTN5S2VsWXliRU5DLy9FZ00wYnFweHFCeHpJVGZqL2lXUGY3SWx5emYxZzI5MEtjb2UwV2E0YXpVdS9LRCtEbldKbjdhK2dmTmNwQk40cHZMSGhoa2Zkd0Zyb3lXT1pmbFFpeGpHSGVxMlBScEFVOWFZUlVZd2hWKzZwZ3BDNExDVE9MQzNaUzI4cUsxL0RJcHpvWW0ySjJoYmpYd0lucEFKMTYwZXV3OEhsbFF0bUdmOUliYzYzSmFGemlzak5PNDArWjIxZDJyeVljVkpCZmZxemloMzYwWmMyNlducE0wOXpJSCtZZjdvWjdPTjRYNGM1YjFENnAvbjg2TStUWlJ5TlVDNVdtcDlsWlFYNmhSVC83RS9PdFJibzZEQkE4RFV6bXdvMEduNGdTc3hQRDl1VHNzaFFySU8zdHVVaUxhdk0vRTZ2Y0RIV2p0YiszUXdYd2thOWFibEVxMmtmN0VMYmhMUEtTcmx2WGRMMVRrQ20wUEdkZTdieVBwWEFvak5DbW9vZ2xjaUxpT2dNdDhROEdaY0gzYWI5TmlHSHdmQWVoQVlhWEMiLCJtYWMiOiI3ODY4MjRkNzI4YTFjYmVkODc5ZTAwNzQ0YWRhNGNkZGIxMmFiODZhNDY2ODc0MzA5YmVkNzNiOTc3MDYzYTMwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:30:36 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjFybjFXRkRwZ0JOQ3c0SXZGajYxT3c9PSIsInZhbHVlIjoiNHpGUUVBV25iY0dJVm1rQzhpemMvWHpaOERldzJuYzJzUitvU3p3VGJyMHZkYlZUak1rTjFzMWlLa0dYNjVKZFBxMFBESEd3S0RvamFqQXRQemMzOFlwMHkrRFJWREgveThSWmxYUVMrQmVKZENhUERPWU1zUXN3V0gxeVNVbElWMjRNNkFWOFNsWTBMeU1IYmdUcHgvZm5NU29mYWxZVFp2clhQUkh5cVJDc3VVWEZJUGMxamp2ZkRHYkVHNWE0dWNBbUxhWXRLTSs2dDVYOXdjQ2N6dy81bHZEUzZOV3piV29TRWZzd00xSzVhNk84QkM0Y051ajRLcUxuZjd6SENtbHJHVmwrMTBRczJHR2VzMW95TkNZNytFb1FJSHB6cXNZdjVtUnFicEpZQklCZHhzNlJFOGUrb1hwaUhuN3dGeDBHV0pzUm1ZSVBlUGpsMDRRSFlaUndGTFJuVVNwMkdJNFJqZGRKZ3B3T0RZV1FZSTZLc2dSWUtobGw0SXJxcTR2SVgrR1VzS3BvL3NIR2N3UWRKUWhGQWY5WitwZFl6OTFYby8zTTlVVCtURXZHTFNIR3owY2FjYjhFbE9lVnpQaFcvNEVXT0ZDZEIzSzF4dzk5bldWcFhCa2hZUnpMb2FCU1hEamJuVkU1dHA1cng5N3BMVXhtNE05em1qaDMiLCJtYWMiOiIwYzI3OTIzMTg3OGI1YzViZGU0OThiNTY0OTdkOTJkYWQ4NTg4MDJmYzk5NDgzY2I2N2M0ZTk0NWI2OTRlNjgzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:30:36 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5WSWRHOUVGU1loQ3FDMmpTbkdtN1E9PSIsInZhbHVlIjoianJIODJSZ0VHcm1SM2pHeGMvcW1ybnRNbEkwR3BWYjFFMUpjOEFSdGRpamJBNEVGdWdvOHVnVzN0cHBlK3hZRmlrSituMTNYZUJYVGVxb0UxOVcrVnIwSTN5S2VsWXliRU5DLy9FZ00wYnFweHFCeHpJVGZqL2lXUGY3SWx5emYxZzI5MEtjb2UwV2E0YXpVdS9LRCtEbldKbjdhK2dmTmNwQk40cHZMSGhoa2Zkd0Zyb3lXT1pmbFFpeGpHSGVxMlBScEFVOWFZUlVZd2hWKzZwZ3BDNExDVE9MQzNaUzI4cUsxL0RJcHpvWW0ySjJoYmpYd0lucEFKMTYwZXV3OEhsbFF0bUdmOUliYzYzSmFGemlzak5PNDArWjIxZDJyeVljVkpCZmZxemloMzYwWmMyNlducE0wOXpJSCtZZjdvWjdPTjRYNGM1YjFENnAvbjg2TStUWlJ5TlVDNVdtcDlsWlFYNmhSVC83RS9PdFJibzZEQkE4RFV6bXdvMEduNGdTc3hQRDl1VHNzaFFySU8zdHVVaUxhdk0vRTZ2Y0RIV2p0YiszUXdYd2thOWFibEVxMmtmN0VMYmhMUEtTcmx2WGRMMVRrQ20wUEdkZTdieVBwWEFvak5DbW9vZ2xjaUxpT2dNdDhROEdaY0gzYWI5TmlHSHdmQWVoQVlhWEMiLCJtYWMiOiI3ODY4MjRkNzI4YTFjYmVkODc5ZTAwNzQ0YWRhNGNkZGIxMmFiODZhNDY2ODc0MzA5YmVkNzNiOTc3MDYzYTMwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:30:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjFybjFXRkRwZ0JOQ3c0SXZGajYxT3c9PSIsInZhbHVlIjoiNHpGUUVBV25iY0dJVm1rQzhpemMvWHpaOERldzJuYzJzUitvU3p3VGJyMHZkYlZUak1rTjFzMWlLa0dYNjVKZFBxMFBESEd3S0RvamFqQXRQemMzOFlwMHkrRFJWREgveThSWmxYUVMrQmVKZENhUERPWU1zUXN3V0gxeVNVbElWMjRNNkFWOFNsWTBMeU1IYmdUcHgvZm5NU29mYWxZVFp2clhQUkh5cVJDc3VVWEZJUGMxamp2ZkRHYkVHNWE0dWNBbUxhWXRLTSs2dDVYOXdjQ2N6dy81bHZEUzZOV3piV29TRWZzd00xSzVhNk84QkM0Y051ajRLcUxuZjd6SENtbHJHVmwrMTBRczJHR2VzMW95TkNZNytFb1FJSHB6cXNZdjVtUnFicEpZQklCZHhzNlJFOGUrb1hwaUhuN3dGeDBHV0pzUm1ZSVBlUGpsMDRRSFlaUndGTFJuVVNwMkdJNFJqZGRKZ3B3T0RZV1FZSTZLc2dSWUtobGw0SXJxcTR2SVgrR1VzS3BvL3NIR2N3UWRKUWhGQWY5WitwZFl6OTFYby8zTTlVVCtURXZHTFNIR3owY2FjYjhFbE9lVnpQaFcvNEVXT0ZDZEIzSzF4dzk5bldWcFhCa2hZUnpMb2FCU1hEamJuVkU1dHA1cng5N3BMVXhtNE05em1qaDMiLCJtYWMiOiIwYzI3OTIzMTg3OGI1YzViZGU0OThiNTY0OTdkOTJkYWQ4NTg4MDJmYzk5NDgzY2I2N2M0ZTk0NWI2OTRlNjgzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:30:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1753509554\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1775334270 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">brdt2VWoNbvQtr5l0vLOW8a3jeohXEGDuzlcAcdG</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1775334270\", {\"maxDepth\":0})</script>\n"}}