{"__meta": {"id": "Xe5abf21b72951969b2d8ed3659c9321b", "datetime": "2025-07-31 06:29:57", "utime": **********.506938, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:29:57] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.498186, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943395.471777, "end": **********.506996, "duration": 2.0352189540863037, "duration_str": "2.04s", "measures": [{"label": "Booting", "start": 1753943395.471777, "relative_start": 0, "end": **********.231631, "relative_end": **********.231631, "duration": 1.7598540782928467, "duration_str": "1.76s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.23167, "relative_start": 1.7598929405212402, "end": **********.507001, "relative_end": 5.0067901611328125e-06, "duration": 0.2753310203552246, "duration_str": "275ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45918384, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.023119999999999998, "accumulated_duration_str": "23.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3644571, "duration": 0.01925, "duration_str": "19.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 83.261}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.429698, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 83.261, "width_percent": 7.18}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.445844, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 90.441, "width_percent": 4.801}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.457139, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 95.242, "width_percent": 4.758}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies/79/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-860538589 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-860538589\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-881800273 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-881800273\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1645787627 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1645787627\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-123550982 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/system-admin/companies/79/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlFGbHJOMUxLendlWHJFRVJ2b3R2dkE9PSIsInZhbHVlIjoiV2E0VG1WMG8zVDhoNkhmeE9VM1MyWXk0bmdaWTRIMldyM1V5ZHJucjNZbVVHbjJJbkk1d3BnSS9HK2hoc3pXQWErcU04OEw2N3N4bmh5Z2syTnFuNUs5SXJ6dEVVenZkOWhnR0xSak1kdmE0YzR4em1HOVpROERGNUdNdlRldkkrYVVmdThKeUVEL1F3VC9BdGsvK01TZnFCR2J1c200UDBpVWQ4TVR1WGIzaTVNSG8xRGV3ZThjM0pSVjBpTkF2am1MOXNadG16emlLVExvZlowNktEQ1g1Y1l5cVY4UDZNYkcxUlRiU1JVeWRQUklaV0VmWURWQlUrT1FSR3k4VG5MVzVaNUtyRG96cVRyZGJpWUYxWHRXclRPU0xmTmhjK2IwN3NXU1VaekdnR0ZVU281S1lTWTQrajN4L1l5TlUvZHJJbCt6WmZKazBNbTBrNUZnL1NLT0xDWWgwekdpUE5HSDBMa3IxWjV6anlGZTNYWnRWSDJlWkE5VVA3Y3kyR3FDUitsN0hxczRTOEFEQXVwZnpDMUo0MHlzVktNQ1hXZkpWRDBxS1lKM3BodVBiWmhqakZLTnZPMVVTY21GZzhkT3pldGhwM0pZelF4OTBqTUJ3UzNMMVVtVU1HMnFoQTFZR3RmSlBkcjlMWVR4cXlZWkgrbHdhMndteG5lTHgiLCJtYWMiOiI3YmI2NmQyMjEzZTliM2UwODNhMGZiZmMyMDIxOGQ0OWYzOTI2YTI0ZTg3NGJkOTZkZDc2NmU5YWRlZjY2ODEyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkR5R0ZyVzE3RHJ4UzFOblBud0xNVVE9PSIsInZhbHVlIjoiTG1KR3FTcXRJdWpzTmp5L0VHV0dDN1JBM0g0V1ltNHNyM29SeGFIb1R0ekl1OXBuRU9kRG51a3BjVWtFaytLZ0dOeElYQjNTMW5FL2VoQkQxVjFhOUVjK0JVUnhPNFJkSGhZQWNTcGg2TzU1cGRzeXVPeUJyQmp5bGhWSFhKSzhFajd6SjdyZy9NYXMxV1RVRlc0UXl2Z2hqZTlMU2VKakhVdFg5QUV3a2toMlh5VnNGdThiQmtINm40bC9iR2Qydm8vOW44Wmc3R3B6a2xRMXdkVUM4Yk1WSGRQMlZLZjY1MDFVYTZPaThFTHlaTURPOUl3b3FzN3R2bjhJWVo2UUFrMHY0bXZpNWNCVlc5NDBtbTltd1FWcVRqbUhuMkV0Ri9oWXhaa2g0M3l6NWVuRWNySC9JUjVEdENDK3ZGbVFoeTF2d21YZ20yU3o0amUyUTlJeUxSTnJsYTRGYU1UZDNmTG1tMG44MjI2Q2daTVJiMUdFTmwwWUVTakZJeG0yZFJSbVBubkpHNm5zM3hYeFFnMkgrdjhnNWRlUTJLNThOVkNQeWFpeVFTRTE2WlZxT1Y5UDFyU2VzbHl3bWZsdTZueWpaZjJWTVlzWVJGR3RZNk5UaU1mb3ppZkR6ZW1NNURVTkZsWTJaVkw3TGxIRXM5ZDJjdXFYQklQWm1wSG4iLCJtYWMiOiI1NmRmZmEwZjJhMWIwZGY1NDY3OTM4OGFhMmUzNzVmYjYyN2U3ZTU2MmJjYjBlNTc1NzY2ZGY0OGZkNmZjM2IxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-123550982\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1729089310 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1729089310\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1474200014 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:29:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikk5d0JyeUFHUm9FeFZzNnRMalVGbVE9PSIsInZhbHVlIjoiZkxUK2E4bHNCK1FxbW1vWWpSMmlSZjNmQnR0VWh5dFZiOHY4dDh1Z2hjblI0SVZvY1dRTmwvc2M4Um9tcVlWSXRTUWV0VDAvQ0c3S09XNU0xMFdCK0pMdXh3eW15WmY5YlJsSGJYcEhiY2x2OHNPVDc3aGlBYXg2dnRQRVR4QmRmcnJYbUVDK2xCU3NNMTdlcGNNZVp6cnl4YjlBUDFwdURkbzJNd2wzOU9CUmZ6cWoyTkhsdUx5NEVSdHdWSmdLQWdYUXM2SDNaZzhLaER2MU5Vd3hTdE81dDlPYjAxTnNmQUJXaDErT05QcWRRNDdRMDFURXlPSGNvTkVac0E3M2ljNU9xTHBYbDNjSzU1cll6dGtwb0lYYWVBd3Jiem5hUUFDUStxVm1xNDcxNlJGYVZEV3NFYWN3cWd1YlJMT1VHMHgvMXp6S05uaUlBdjZJU05FNVF4aDJZdk9LUmpYc0IwZFVkbVRONFhqd1NpczhpV3NEUDRYZ2JmWTZQOHlzTHFuS29Uak1lbWZGZDZtYVNwcVZ6dWRXUkpBY0JFd0habjZqVkxaeXZORUNZT3UrWkJ6dUxYb3IycklkR2RHTGJmajlHV3o4bXJ0OG5ZNG9QU3NzVTNWaGgzNzBOZkRrYkFiMXVBclZEeHN2cFREZ04vNHNxS3ZqNWpVV040amciLCJtYWMiOiJkNTljNTBlMTc1YTc4Mzk2Yjk4Mzk4NTIzZjY0YmFhZjE5NjA1MWRjNjA0NmVkMTZiOWY5MDUwMTBmN2U1OTUxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im5vWFJlMzRsOGJ1cVhHU09zakUzc3c9PSIsInZhbHVlIjoianFmRDI0dzlMbldqaU5PV2ZJczg1cXZaZU1abFFKNUhxMVR4MSs0V3pRNzNzdW5WMUx3S1FmZmsxRklZSVJ1MUM4OXVkWXhyMGxyeHVlYmtTS3Q4clJKelhEMUtjVlMxdDhBa0hYTm1BSWVqdVhiN2FwMXBzQ1EydVdPTWRRQndpeXBVWjZWN1o2c24wUFZvc3RWQ0RJdGozSEZ0KzJYR0Y1bEN6WEtOdGxyQmE3NmVBeG5WYXJiczNjbW9PQUtCTXY5RXBPYWRDYmlSc01jY2RnNFRHWGxwK1BIQUZReU1aYUxsTzd3WkxnWE9hd2dDc3QyTXY1UDF3Uy95cEoydXVIbUpMc0RsTWdYTDV6M2FCc3NPd2FJUUFnbGRZL1FvU0d1QUtXUWhRbUlvSUZIMEFrUzg2MVdlVUZnSHN5aXJLcCtrTG9DSWJlU29mcG5ockVML2owL0NYSk5BdUd0WVJrdzc3R1N6R2xadHU4NzlpVVNza2xBWEc1RlY2R1c0VzhVQm9rK21TVWRNSGlWdGtXNUk1ZFlBMy9UTUJyeCs4T2FMKy9XSThabEgyWlFKZWdFSkI1c0FQem5rUkJqLzBzcnNGRW11enpseFRXUjlzN1dzM1ZKbEMzOXg2Q3MveXNHcWhjQ1pQZ0krSU9YTEl6WFVERVByZVQ0N0V3N3UiLCJtYWMiOiJjZWZlMTM0ZWFlZjRmYzEwN2M4ZDVjMmJmYTM4MWVlMzU2NWFjYTk2OGNhY2NiN2ZjODc3NmZjMTU0MmUzMWFkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikk5d0JyeUFHUm9FeFZzNnRMalVGbVE9PSIsInZhbHVlIjoiZkxUK2E4bHNCK1FxbW1vWWpSMmlSZjNmQnR0VWh5dFZiOHY4dDh1Z2hjblI0SVZvY1dRTmwvc2M4Um9tcVlWSXRTUWV0VDAvQ0c3S09XNU0xMFdCK0pMdXh3eW15WmY5YlJsSGJYcEhiY2x2OHNPVDc3aGlBYXg2dnRQRVR4QmRmcnJYbUVDK2xCU3NNMTdlcGNNZVp6cnl4YjlBUDFwdURkbzJNd2wzOU9CUmZ6cWoyTkhsdUx5NEVSdHdWSmdLQWdYUXM2SDNaZzhLaER2MU5Vd3hTdE81dDlPYjAxTnNmQUJXaDErT05QcWRRNDdRMDFURXlPSGNvTkVac0E3M2ljNU9xTHBYbDNjSzU1cll6dGtwb0lYYWVBd3Jiem5hUUFDUStxVm1xNDcxNlJGYVZEV3NFYWN3cWd1YlJMT1VHMHgvMXp6S05uaUlBdjZJU05FNVF4aDJZdk9LUmpYc0IwZFVkbVRONFhqd1NpczhpV3NEUDRYZ2JmWTZQOHlzTHFuS29Uak1lbWZGZDZtYVNwcVZ6dWRXUkpBY0JFd0habjZqVkxaeXZORUNZT3UrWkJ6dUxYb3IycklkR2RHTGJmajlHV3o4bXJ0OG5ZNG9QU3NzVTNWaGgzNzBOZkRrYkFiMXVBclZEeHN2cFREZ04vNHNxS3ZqNWpVV040amciLCJtYWMiOiJkNTljNTBlMTc1YTc4Mzk2Yjk4Mzk4NTIzZjY0YmFhZjE5NjA1MWRjNjA0NmVkMTZiOWY5MDUwMTBmN2U1OTUxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im5vWFJlMzRsOGJ1cVhHU09zakUzc3c9PSIsInZhbHVlIjoianFmRDI0dzlMbldqaU5PV2ZJczg1cXZaZU1abFFKNUhxMVR4MSs0V3pRNzNzdW5WMUx3S1FmZmsxRklZSVJ1MUM4OXVkWXhyMGxyeHVlYmtTS3Q4clJKelhEMUtjVlMxdDhBa0hYTm1BSWVqdVhiN2FwMXBzQ1EydVdPTWRRQndpeXBVWjZWN1o2c24wUFZvc3RWQ0RJdGozSEZ0KzJYR0Y1bEN6WEtOdGxyQmE3NmVBeG5WYXJiczNjbW9PQUtCTXY5RXBPYWRDYmlSc01jY2RnNFRHWGxwK1BIQUZReU1aYUxsTzd3WkxnWE9hd2dDc3QyTXY1UDF3Uy95cEoydXVIbUpMc0RsTWdYTDV6M2FCc3NPd2FJUUFnbGRZL1FvU0d1QUtXUWhRbUlvSUZIMEFrUzg2MVdlVUZnSHN5aXJLcCtrTG9DSWJlU29mcG5ockVML2owL0NYSk5BdUd0WVJrdzc3R1N6R2xadHU4NzlpVVNza2xBWEc1RlY2R1c0VzhVQm9rK21TVWRNSGlWdGtXNUk1ZFlBMy9UTUJyeCs4T2FMKy9XSThabEgyWlFKZWdFSkI1c0FQem5rUkJqLzBzcnNGRW11enpseFRXUjlzN1dzM1ZKbEMzOXg2Q3MveXNHcWhjQ1pQZ0krSU9YTEl6WFVERVByZVQ0N0V3N3UiLCJtYWMiOiJjZWZlMTM0ZWFlZjRmYzEwN2M4ZDVjMmJmYTM4MWVlMzU2NWFjYTk2OGNhY2NiN2ZjODc3NmZjMTU0MmUzMWFkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1474200014\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1646406830 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/system-admin/companies/79/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1646406830\", {\"maxDepth\":0})</script>\n"}}