{"__meta": {"id": "Xbc230f54ee4f59aca30456099c050e73", "datetime": "2025-07-31 05:17:38", "utime": **********.338287, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939057.459168, "end": **********.338313, "duration": 0.8791451454162598, "duration_str": "879ms", "measures": [{"label": "Booting", "start": 1753939057.459168, "relative_start": 0, "end": **********.255072, "relative_end": **********.255072, "duration": 0.7959041595458984, "duration_str": "796ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.255106, "relative_start": 0.****************, "end": **********.338316, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "83.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4RDGnT6ntAuZC0bTSDpoM9gCEleos4PUlMyjFvLf", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1002479577 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1002479577\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1304403706 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1304403706\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1362982649 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1362982649\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2136198061 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2136198061\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-652848385 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-652848385\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:17:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InUxTXA4ejB1Nys0Z3RuY09EUG9GQ2c9PSIsInZhbHVlIjoicUVWTkoxY0x6bzk4dTBWYjFOdkx5ZElRY1JBVVFRSEJlNXhJQlZLSURPWmhBRHg5MlN2cWx1Vkt5cXd2R09NQXU0UmlkMTJ4REtqNTdmenc5R01Za0lzVFlrMDNHT0lBTFA0dEpBWFdHN25YREgwVWxodmtrRkZsRk1uTVgyQlIyMCt1b2ZCUzRkTDZuKzRRTlYrRjNUbElmcTZ5eHdNT2J0UXNmN1JWUC9FVjlzWFlJaWMwdU43WEREZTlXWDlDSzVjUVhNdzBmZjFla1FlTmZIc0pmY2djYlc4SWZLWkU2TUp4S2pUenYxM21MeFEvNDhmN2Y2dHdvZk9XUVFmcy8xZERNZTF6TDFrY3l4ckZrUUhjMFl1VTBmRnNGbnVJQkxXN3V2cHJWL1NhVU9RRXdRMjFmQWFYb05WdHIxVW5hOTYvUjg4UG9OUHF3d01aRWZsTkM0SmVuaUU1dGlDZ3FpTWxrcU9YU29icThFazFMdHNXRFlRZVBiaTRNZVBKUHV5aW9NS1RGb2szRG85Y1JvWHp0Q21nUGpQcldKcjd3UnpSZmQwVWwxQmJUMXZpMWtmVUhWaDAwalpGMHkxSnVHcHlwWnZrVFFsS3huK2JoMzdDWGsyOUNOWDBudzEwVGtienRiemQ3TjNHLzBWY1RyVi85V0ZIS2VVREo0MDAiLCJtYWMiOiJiNzM2YTMzYTE0NjlkOTI0NGE4YzI4MzgwODdjNGRlY2I2MWFmNTQyYzk0NzQ4NGQ0MjcwMmFhMThjODYzMThlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:17:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImNPUG44aElmcDJEWTRFYlRwOVpBM1E9PSIsInZhbHVlIjoiaGVXOCswcUhhZFA2K2I3Ykp4d2RWVE0zOGt6MTFSUmlGUnNPQkxJaWtNWFMxTXNvMVlzL1RSZ0tCNnpsT3hsTWpOMjdmRXpDbVRUSW5VYWluZ29DU1YvNjNsT0pSQ1RMV1dXVk1OSDVvdE53VlNRVHNuZmdsME81b2VJSTFDMzVMeVFnQWcyWEo1RENJazkvTG1WUHFybjdzeFU1c2dNQlhnNk1PbkZFUCtNZ1g0aWZkWHVvMk1vTzZaNlpoZ0tBZVlTd0pkRWZzTUxSL200Si9UT09HWlhKZVJodFYvdTI4OTdPa2hRT0E2RTlpa051SldEZzNhVll4akZTdVpqQVRmeE02VzRoRURCUWJpbEduaEpDaE9URGNoaUNiMDVUUUVhR05MUldXdkRNQzlUYU1RZ3ZidGJXT3dSYzZoV1NIYXNXRjNHSld2VHZPWnQ5U3NOTmVGOVhpV2RJc1RzeVZlSGdaSENIa0dXV3dEYUtJeWY3bzdDNHNzSFpaR1djNHY2L3FTVTFHazVQZ0FzNEVEeWlzQnV1L0VWWDZWUGJqcHBycWNOVFA4UU1MOWNvUnZESEpQSGVFQWxjOU9aSDZUcDJOaDl0UnFDSVhCQ01sUzk3WWhzN1N0a21POWlnSExQYkIxcFZ5eUNnemlpZG9weVdvMUxBNVdRaEVybTAiLCJtYWMiOiJkZDdmMDYzNTUxOTk5YzgzMjhhZTdmNTBlNGI4NjE0YTljZWQ5NWU4MDNlN2U1YWIyN2YwZDkwYmQ3YWJjYjhhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:17:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InUxTXA4ejB1Nys0Z3RuY09EUG9GQ2c9PSIsInZhbHVlIjoicUVWTkoxY0x6bzk4dTBWYjFOdkx5ZElRY1JBVVFRSEJlNXhJQlZLSURPWmhBRHg5MlN2cWx1Vkt5cXd2R09NQXU0UmlkMTJ4REtqNTdmenc5R01Za0lzVFlrMDNHT0lBTFA0dEpBWFdHN25YREgwVWxodmtrRkZsRk1uTVgyQlIyMCt1b2ZCUzRkTDZuKzRRTlYrRjNUbElmcTZ5eHdNT2J0UXNmN1JWUC9FVjlzWFlJaWMwdU43WEREZTlXWDlDSzVjUVhNdzBmZjFla1FlTmZIc0pmY2djYlc4SWZLWkU2TUp4S2pUenYxM21MeFEvNDhmN2Y2dHdvZk9XUVFmcy8xZERNZTF6TDFrY3l4ckZrUUhjMFl1VTBmRnNGbnVJQkxXN3V2cHJWL1NhVU9RRXdRMjFmQWFYb05WdHIxVW5hOTYvUjg4UG9OUHF3d01aRWZsTkM0SmVuaUU1dGlDZ3FpTWxrcU9YU29icThFazFMdHNXRFlRZVBiaTRNZVBKUHV5aW9NS1RGb2szRG85Y1JvWHp0Q21nUGpQcldKcjd3UnpSZmQwVWwxQmJUMXZpMWtmVUhWaDAwalpGMHkxSnVHcHlwWnZrVFFsS3huK2JoMzdDWGsyOUNOWDBudzEwVGtienRiemQ3TjNHLzBWY1RyVi85V0ZIS2VVREo0MDAiLCJtYWMiOiJiNzM2YTMzYTE0NjlkOTI0NGE4YzI4MzgwODdjNGRlY2I2MWFmNTQyYzk0NzQ4NGQ0MjcwMmFhMThjODYzMThlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:17:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImNPUG44aElmcDJEWTRFYlRwOVpBM1E9PSIsInZhbHVlIjoiaGVXOCswcUhhZFA2K2I3Ykp4d2RWVE0zOGt6MTFSUmlGUnNPQkxJaWtNWFMxTXNvMVlzL1RSZ0tCNnpsT3hsTWpOMjdmRXpDbVRUSW5VYWluZ29DU1YvNjNsT0pSQ1RMV1dXVk1OSDVvdE53VlNRVHNuZmdsME81b2VJSTFDMzVMeVFnQWcyWEo1RENJazkvTG1WUHFybjdzeFU1c2dNQlhnNk1PbkZFUCtNZ1g0aWZkWHVvMk1vTzZaNlpoZ0tBZVlTd0pkRWZzTUxSL200Si9UT09HWlhKZVJodFYvdTI4OTdPa2hRT0E2RTlpa051SldEZzNhVll4akZTdVpqQVRmeE02VzRoRURCUWJpbEduaEpDaE9URGNoaUNiMDVUUUVhR05MUldXdkRNQzlUYU1RZ3ZidGJXT3dSYzZoV1NIYXNXRjNHSld2VHZPWnQ5U3NOTmVGOVhpV2RJc1RzeVZlSGdaSENIa0dXV3dEYUtJeWY3bzdDNHNzSFpaR1djNHY2L3FTVTFHazVQZ0FzNEVEeWlzQnV1L0VWWDZWUGJqcHBycWNOVFA4UU1MOWNvUnZESEpQSGVFQWxjOU9aSDZUcDJOaDl0UnFDSVhCQ01sUzk3WWhzN1N0a21POWlnSExQYkIxcFZ5eUNnemlpZG9weVdvMUxBNVdRaEVybTAiLCJtYWMiOiJkZDdmMDYzNTUxOTk5YzgzMjhhZTdmNTBlNGI4NjE0YTljZWQ5NWU4MDNlN2U1YWIyN2YwZDkwYmQ3YWJjYjhhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:17:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4RDGnT6ntAuZC0bTSDpoM9gCEleos4PUlMyjFvLf</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}