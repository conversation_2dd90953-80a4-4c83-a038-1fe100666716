
<?php $__env->startSection('page-title'); ?>
    <?php echo e(ucwords($project->project_name) . __("'s Tasks")); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-page'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/summernote/summernote-bs4.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/plugins/dragula.min.css')); ?>" id="main-style-link">
<?php $__env->stopPush(); ?>
<?php $__env->startPush('script-page'); ?>
    <script src="<?php echo e(asset('css/summernote/summernote-bs4.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/plugins/dragula.min.js')); ?>"></script>
    <script>
        // Initialize Dragula for Kanban Board
        var dragulaInstance = null;

        function initializeKanbanDragula() {
            // Destroy existing instance if it exists
            if (dragulaInstance) {
                dragulaInstance.destroy();
            }

            // Get all kanban containers
            var containers = [];
            $('.kanban-box').each(function() {
                containers.push(this);
            });

            if (containers.length > 0) {
                dragulaInstance = dragula(containers, {
                    moves: function(el, container, handle) {
                        // Allow dragging on empty areas of the card (not on buttons, links, etc.)
                        return !$(handle).closest('a, button, .btn, .dropdown, input, textarea, select').length &&
                               $(el).hasClass('draggable-item');
                    },
                    accepts: function(el, target, source, sibling) {
                        return $(target).hasClass('kanban-box');
                    },
                    removeOnSpill: false,
                    revertOnSpill: true,
                    direction: 'vertical'
                });

                // Add visual feedback during drag
                dragulaInstance.on('drag', function(el, source) {
                    $(el).addClass('gu-transit');
                    $('body').addClass('dragging-task');
                });

                dragulaInstance.on('dragend', function(el) {
                    $(el).removeClass('gu-transit');
                    $('body').removeClass('dragging-task');
                });

                // Handle drop event
                dragulaInstance.on('drop', function(el, target, source, sibling) {
                    var sort = [];
                    $("#" + target.id + " > div").each(function() {
                        sort[$(this).index()] = $(this).attr('id');
                    });

                    var id = el.id;
                    var old_stage = $("#" + source.id).data('status');
                    var new_stage = $("#" + target.id).data('status');
                    var project_id = '<?php echo e($project->id); ?>';

                    // Update task counts
                    $("#" + source.id).parent().find('.count').text($("#" + source.id + " > div").length);
                    $("#" + target.id).parent().find('.count').text($("#" + target.id + " > div").length);

                    // Send AJAX request to update task order and stage
                    $.ajax({
                        url: '<?php echo e(route('tasks.update.order', [$project->id])); ?>',
                        type: 'PATCH',
                        data: {
                            id: id,
                            sort: sort,
                            new_stage: new_stage,
                            old_stage: old_stage,
                            project_id: project_id,
                            "_token": "<?php echo e(csrf_token()); ?>"
                        },
                        success: function(data) {
                            show_toastr('success', "Task moved successfully!", 'success');
                        },
                        error: function(data) {
                            show_toastr('error', "Something went wrong while moving the task.", 'error');
                            // Revert the move on error
                            dragulaInstance.cancel(true);
                        }
                    });
                });
            }
        }

        $(document).ready(function() {
            // Initialize dragula for kanban board
            initializeKanbanDragula();

            // Add touch support for mobile devices
            $('.kanban-wrapper').on('touchstart', '.draggable-item', function(e) {
                $(this).addClass('touch-active');
            });

            $('.kanban-wrapper').on('touchend', '.draggable-item', function(e) {
                $(this).removeClass('touch-active');
            });
            // User assignment is now handled by checkboxes in the forms

            $(document).on("click", ".del_task", function() {
                var id = $(this);
                $.ajax({
                    url: $(this).attr('data-url'),
                    type: 'DELETE',
                    dataType: 'JSON',
                    data: {
                        "_token": "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(data) {
                        $('#' + data.task_id).remove();
                        show_toastr('<?php echo e(__('success')); ?>',
                            '<?php echo e(__('Task Deleted Successfully!')); ?>');
                    },
                });
            });

            /*For Task Comment*/
            $(document).on('click', '#comment_submit', function(e) {
                var curr = $(this);

                var comment = $.trim($("#form-comment textarea[name='comment']").val());
                if (comment != '') {
                    $.ajax({
                        url: $("#form-comment").data('action'),
                        data: {
                            comment: comment,
                            "_token": "<?php echo e(csrf_token()); ?>"
                        },
                        type: 'POST',
                        success: function(data) {
                            data = JSON.parse(data);
                            var html = "<div class='list-group-item px-0 mb-1'>" +
                                "                    <div class='row align-items-center'>" +
                                "                        <div class='col-auto'>" +
                                "                            <a href='#' class='avatar avatar-sm  ms-2'>" +
                                "                                <img src=" + data.default_img +
                                " alt='' class='avatar-sm rounded border-2 border border-primary ml-3'>" +
                                "                            </a>" +
                                "                        </div>" +
                                "                        <div class='col ml-n2'>" +
                                "                            <p class='d-block h6 text-sm font-weight-light mb-0 text-break'>" +
                                data.comment + "</p>" +
                                "                            <small class='d-block'>" + data
                                .current_time + "</small>" +
                                "                           </div>" +
                                "                        <div class='col-auto'><div class='action-btn me-4'><a href='#' class='mx-3 btn btn-sm  align-items-center delete-comment bg-danger' data-url='" +
                                data.deleteUrl +
                                "'><i class='ti ti-trash text-white'></i></a></div></div>" +
                                "                    </div>" +
                                "                </div>";

                            $("#comments").prepend(html);
                            $("#form-comment textarea[name='comment']").val('');
                            load_task(curr.closest('.task-id').attr('id'));
                            show_toastr('<?php echo e(__('success')); ?>',
                                '<?php echo e(__('Comment Added Successfully!')); ?>');
                        },
                        error: function(data) {
                            show_toastr('error', '<?php echo e(__('Some Thing Is Wrong!')); ?>');
                        }
                    });
                } else {
                    show_toastr('error', '<?php echo e(__('Please write comment!')); ?>');
                }
            });
            $(document).on("click", ".delete-comment", function() {
                var btn = $(this);

                $.ajax({
                    url: $(this).attr('data-url'),
                    type: 'DELETE',
                    dataType: 'JSON',
                    data: {
                        "_token": "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(data) {
                        load_task(btn.closest('.task-id').attr('id'));
                        show_toastr('<?php echo e(__('success')); ?>',
                            '<?php echo e(__('Comment Deleted Successfully!')); ?>');
                        btn.closest('.list-group-item').remove();
                    },
                    error: function(data) {
                        data = data.responseJSON;
                        if (data.message) {
                            show_toastr('error', data.message);
                        } else {
                            show_toastr('error', '<?php echo e(__('Some Thing Is Wrong!')); ?>');
                        }
                    }
                });
            });

            /*For Task Checklist*/
            $(document).on('click', '#checklist_submit', function() {
                var name = $("#form-checklist input[name=name]").val();
                if (name != '') {
                    $.ajax({
                        url: $("#form-checklist").data('action'),
                        data: {
                            name: name,
                            "_token": "<?php echo e(csrf_token()); ?>"
                        },
                        type: 'POST',
                        success: function(data) {
                            data = JSON.parse(data);
                            load_task($('.task-id').attr('id'));
                            show_toastr('<?php echo e(__('success')); ?>',
                                '<?php echo e(__('Checklist Added Successfully!')); ?>');
                            var html =
                                '<div class="card border shadow-none checklist-member">' +
                                '                    <div class="px-3 py-2 row align-items-center">' +
                                '                        <div class="col">' +
                                '                            <div class="form-check form-check-inline">' +
                                '                                <input type="checkbox" class="form-check-input" id="check-item-' +
                                data.id + '" value="' + data.id + '" data-url="' + data
                                .updateUrl + '">' +
                                '                                <label class="form-check-label h6 text-sm" for="check-item-' +
                                data.id + '">' + data.name + '</label>' +
                                '                            </div>' +
                                '                        </div>' +
                                '                        <div class="col-auto"> <div class="action-btn  ms-2">' +
                                '                            <a href="#" class="mx-3 btn btn-sm  align-items-center delete-checklist bg-danger" role="button" data-url="' +
                                data.deleteUrl + '">' +
                                '                                <i class="ti ti-trash text-white"></i>' +
                                '                            </a>' +
                                '                        </div></div>' +
                                '                    </div>' +
                                '                </div>'

                            $("#checklist").append(html);
                            $("#form-checklist input[name=name]").val('');
                            $("#form-checklist").collapse('toggle');
                        },
                        error: function(data) {
                            data = data.responseJSON;
                            show_toastr('error', data.message);
                        }
                    });
                } else {
                    show_toastr('error', '<?php echo e(__('Please write checklist name!')); ?>');
                }
            });
            $(document).on("change", "#checklist input[type=checkbox]", function() {
                $.ajax({
                    url: $(this).attr('data-url'),
                    type: 'POST',
                    dataType: 'JSON',
                    data: {
                        "_token": "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(data) {
                        load_task($('.task-id').attr('id'));
                        show_toastr('<?php echo e(__('Success')); ?>',
                            '<?php echo e(__('Checklist Updated Successfully!')); ?>', 'success');
                    },
                    error: function(data) {
                        data = data.responseJSON;
                        if (data.message) {
                            show_toastr('error', data.message);
                        } else {
                            show_toastr('error', '<?php echo e(__('Some Thing Is Wrong!')); ?>');
                        }
                    }
                });
            });
            $(document).on("click", ".delete-checklist", function() {
                var btn = $(this);
                $.ajax({
                    url: $(this).attr('data-url'),
                    type: 'DELETE',
                    dataType: 'JSON',
                    data: {
                        "_token": "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(data) {
                        load_task($('.task-id').attr('id'));
                        show_toastr('<?php echo e(__('success')); ?>',
                            '<?php echo e(__('Checklist Deleted Successfully!')); ?>');
                        btn.closest('.checklist-member').remove();
                    },
                    error: function(data) {
                        data = data.responseJSON;
                        if (data.message) {
                            show_toastr('error', data.message);
                        } else {
                            show_toastr('error', '<?php echo e(__('Some Thing Is Wrong!')); ?>');
                        }
                    }
                });
            });

            /*For Task Attachment*/
            $(document).on('click', '#file_attachment_submit', function() {
                var file_data = $("#task_attachment").prop("files")[0];
                if (file_data != '' && file_data != undefined) {
                    var formData = new FormData();
                    formData.append('file', file_data);
                    formData.append('_token', "<?php echo e(csrf_token()); ?>");
                    $.ajax({
                        url: $("#file_attachment_submit").data('action'),
                        type: 'POST',
                        data: formData,
                        cache: false,
                        processData: false,
                        contentType: false,
                        success: function(data) {
                            $('#task_attachment').val('');
                            $('.attachment_text').html('<?php echo e(__('Choose a file…')); ?>');
                            data = JSON.parse(data);
                            load_task(data.task_id);
                            show_toastr('<?php echo e(__('success')); ?>',
                                '<?php echo e(__('File Added Successfully!')); ?>');

                            var delLink = '';
                            if (data.deleteUrl.length > 0) {
                                delLink =
                                    ' <div class="action-btn "><a href="#" class=" delete-comment-file mx-3 btn btn-sm  align-items-center  bg-danger" role="button" data-url="' +
                                    data.deleteUrl + '">' +
                                    '                                        <i class="ti ti-trash text-white"></i>' +
                                    '                                    </a></div>';
                            }

                            var html = '<div class="card mb-3 border shadow-none task-file">' +
                                '                    <div class="px-3 py-3">' +
                                '                        <div class="row align-items-center">' +
                                '                            <div class="col ml-n2">' +
                                '                                <h6 class="text-sm mb-0">' +
                                '                                    <a href="#">' + data.name +
                                '</a>' +
                                '                                </h6>' +
                                '                                <p class="card-text small text-muted">' +
                                data.file_size + '</p>' +
                                '                           </div>' +
                                '                            <div class="col-auto"> <div class="action-btn me-2">' +
                                '                                <a href="<?php echo e(asset(Storage::url('uploads/tasks'))); ?>/' +
                                data.file + '" download class="mx-3 btn btn-sm  align-items-center  bg-secondary" role="button">' +
                                '                                    <i class="ti ti-download text-white"></i>' +
                                '                                </a>' +
                                '                            </div>' +
                                delLink +
                                '</div>                        </div>' +
                                '                    </div>' +
                                '                </div>';

                            $("#comments-file").prepend(html);
                        },
                        error: function(data) {
                            data = data.responseJSON;
                            if (data.message) {
                                show_toastr('error', data.errors.file[0]);
                                $('#file-error').text(data.errors.file[0]).show();
                            } else {
                                show_toastr('error', '<?php echo e(__('Some Thing Is Wrong!')); ?>');
                            }
                        }
                    });
                } else {
                    show_toastr('error', '<?php echo e(__('Please select file!')); ?>');
                }
            });
            $(document).on("click", ".delete-comment-file", function() {
                var btn = $(this);
                $.ajax({
                    url: $(this).attr('data-url'),
                    type: 'DELETE',
                    dataType: 'JSON',
                    data: {
                        "_token": "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(data) {
                        load_task(btn.closest('.task-id').attr('id'));
                        show_toastr('<?php echo e(__('success')); ?>',
                            '<?php echo e(__('File Deleted Successfully!')); ?>');
                        btn.closest('.task-file').remove();
                    },
                    error: function(data) {
                        data = data.responseJSON;
                        if (data.message) {
                            show_toastr('error', data.message);
                        } else {
                            show_toastr('error', '<?php echo e(__('Some Thing Is Wrong!')); ?>');
                        }
                    }
                });
            });

            /*For Favorite*/
            $(document).on('click', '#add_favourite', function() {
                $.ajax({
                    url: $(this).attr('data-url'),
                    type: 'POST',
                    data: {
                        "_token": "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(data) {
                        if (data.fav == 1) {
                            $('#add_favourite').addClass('action-favorite');
                        } else if (data.fav == 0) {
                            $('#add_favourite').removeClass('action-favorite');
                        }
                    }
                });
            });

            /*For Complete*/
            $(document).on('change', '#complete_task', function() {
                $.ajax({
                    url: $(this).attr('data-url'),
                    type: 'POST',
                    data: {
                        "_token": "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(data) {
                        if (data.com == 1) {
                            $("#complete_task").prop("checked", true);
                        } else if (data.com == 0) {
                            $("#complete_task").prop("checked", false);
                        }
                        $('#' + data.task).insertBefore($('#task-list-' + data.stage +
                            ' .empty-container'));
                        load_task(data.task);
                    }
                });
            });

            /*Progress Move*/
            $(document).on('change', '#task_progress', function() {
                var progress = $(this).val();
                $('#t_percentage').html(progress);
                $.ajax({
                    url: $(this).attr('data-url'),
                    data: {
                        progress: progress,
                        "_token": "<?php echo e(csrf_token()); ?>"
                    },
                    type: 'POST',
                    success: function(data) {
                        load_task(data.task_id);
                    }
                });
            });
        });

        function load_task(id) {
            $.ajax({
                url: "<?php echo e(route('projects.tasks.get', '_task_id')); ?>".replace('_task_id', id),
                dataType: 'html',
                data: {
                    "_token": "<?php echo e(csrf_token()); ?>"
                },
                success: function(data) {
                    $('#' + id).html('');
                    $('#' + id).html(data);
                    // Reinitialize dragula after task content is updated
                    setTimeout(function() {
                        initializeKanbanDragula();
                    }, 100);
                }
            });
        }

        // Function to reinitialize dragula (useful for dynamic content)
        window.reinitializeDragula = function() {
            initializeKanbanDragula();
        };
    </script>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('projects.index')); ?>"><?php echo e(__('Project')); ?></a></li>
    <li class="breadcrumb-item"><a
            href="<?php echo e(route('projects.show', $project->id)); ?>"><?php echo e(ucwords($project->project_name)); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Task')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('action-btn'); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-sm-12">
            <div class="row kanban-wrapper horizontal-scroll-cards" data-containers='<?php echo e(json_encode($stageClass)); ?>'
                data-plugin="dragula">
                <?php $__currentLoopData = $stages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php ($tasks = $stage->tasks); ?>
                    <div class="col">
                        <div class="crm-sales-card mb-4">
                            <div class="card-header d-flex align-items-center justify-content-between gap-3">
                                <h4 class="mb-0"><?php echo e($stage->name); ?></h4>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create project task')): ?>
                                    <a href="#" data-size="lg"
                                        data-url="<?php echo e(route('projects.tasks.create', [$project->id, $stage->id])); ?>"
                                        data-ajax-popup="true" data-bs-toggle="tooltip"
                                        title="<?php echo e(__('Add Task in ') . $stage->name); ?>" class="btn btn-sm btn-light-primary">
                                        <i class="ti ti-plus"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                            <div class="sales-item-wrp kanban-box" id="task-list-<?php echo e($stage->id); ?>"
                                data-status="<?php echo e($stage->id); ?>">
                                <?php $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $taskDetail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="sales-item draggable-item" id="<?php echo e($taskDetail->id); ?>">
                                        <div class="sales-item-top border-bottom">
                                            <div class="d-flex align-items-center">
                                                <h5 class="mb-0 flex-1">
                                                    <a href="#" class="dashboard-link"
                                                    data-url="<?php echo e(route('projects.tasks.show', [$project->id, $taskDetail->id])); ?>"
                                                    data-ajax-popup="true" data-size="lg"
                                                    data-bs-original-title="<?php echo e($taskDetail->name); ?>"><?php echo e($taskDetail->name); ?></a>
                                                </h5>
                                                <div class="btn-group card-option">
                                                    <button type="button" class="btn p-0 border-0"
                                                        data-bs-toggle="dropdown" aria-haspopup="true"
                                                        aria-expanded="false">
                                                        <i class="ti ti-dots-vertical"></i>
                                                    </button>

                                                    <div class="dropdown-menu icon-dropdown icon-dropdown dropdown-menu-end">
                                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view project task')): ?>
                                                            <a href="#!" data-size="md"
                                                                data-url="<?php echo e(route('projects.tasks.show', [$project->id, $taskDetail->id])); ?>"
                                                                data-ajax-popup="true" class="dropdown-item"
                                                                data-bs-original-title="<?php echo e(__('View')); ?>">
                                                                <i class="ti ti-eye"></i>
                                                                <span><?php echo e(__('View')); ?></span>
                                                            </a>
                                                        <?php endif; ?>
                                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit project task')): ?>
                                                            <a href="#!" data-size="lg"
                                                                data-url="<?php echo e(route('projects.tasks.edit', [$project->id, $taskDetail->id])); ?>"
                                                                data-ajax-popup="true" class="dropdown-item"
                                                                data-bs-original-title="<?php echo e(__('Edit ') . $taskDetail->name); ?>">
                                                                <i class="ti ti-pencil"></i>
                                                                <span><?php echo e(__('Edit')); ?></span>
                                                            </a>
                                                        <?php endif; ?>
                                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete project task')): ?>
                                                            <?php echo Form::open(['method' => 'DELETE', 'route' => ['projects.tasks.destroy', [$project->id, $taskDetail->id]]]); ?>

                                                            <a href="#!" class="dropdown-item bs-pass-para">
                                                                <i class="ti ti-trash"></i>
                                                                <span> <?php echo e(__('Delete')); ?> </span>
                                                            </a>
                                                            <?php echo Form::close(); ?>

                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="badge-wrp d-flex flex-wrap align-items-center gap-2">
                                                <span
                                                class="badge p-2 bg-light-<?php echo e(\App\Models\ProjectTask::$priority_color[$taskDetail->priority]); ?> rounded text-md f-w-600">
                                                <?php echo e(__(\App\Models\ProjectTask::$priority[$taskDetail->priority])); ?></span>
                                            </div>
                                        </div>
                                        <div
                                            class="sales-item-center border-bottom d-flex align-items-center justify-content-between">
                                            <ul class="d-flex flex-wrap align-items-center gap-2 p-0 m-0">
                                                <li class="d-inline-flex align-items-center gap-1 p-1 px-2 border rounded-1"
                                                    data-bs-toggle="tooltip" title="<?php echo e(__('Files')); ?>">
                                                    <i class="f-16 ti ti-file"></i>
                                                    <?php echo e(count($taskDetail->taskFiles)); ?>

                                                </li>
                                                <li class="d-inline-flex align-items-center gap-1 p-1 px-2 border rounded-1"
                                                    data-bs-toggle="tooltip" title="<?php echo e(__('Task Progress')); ?>">
                                                    <?php if(str_replace('%', '', $taskDetail->taskProgress($taskDetail)['percentage']) > 0): ?>
                                                        <span
                                                            class="text-md"><?php echo e($taskDetail->taskProgress($taskDetail)['percentage']); ?></span>
                                                    <?php endif; ?>
                                                </li>
                                            </ul>
                                            <?php if(!empty($taskDetail->end_date) && $taskDetail->end_date != '0000-00-00'): ?>
                                                <span data-bs-toggle="tooltip" title="<?php echo e(__('End Date')); ?>"
                                                    <?php if(strtotime($taskDetail->end_date) < time()): ?> class="text-danger" <?php endif; ?>><?php echo e(Utility::getDateFormated($taskDetail->end_date)); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="sales-item-bottom d-flex align-items-center justify-content-between">
                                            <ul class="d-flex flex-wrap align-items-center gap-2 p-0 m-0">

                                                <li class="d-inline-flex align-items-center gap-1 p-1 px-2 border rounded-1"
                                                    data-bs-toggle="tooltip" title="<?php echo e(__('Comments')); ?>">
                                                    <i class="f-16 ti ti-message"></i>
                                                    <?php echo e(count($taskDetail->comments)); ?>

                                                </li>

                                                <li class="d-inline-flex align-items-center gap-1 p-1 px-2 border rounded-1"
                                                    data-bs-toggle="tooltip" title="<?php echo e(__('Task Checklist')); ?>">
                                                    <i
                                                        class="f-16 ti ti-list"></i><?php echo e($taskDetail->countTaskChecklist()); ?>

                                                </li>
                                            </ul>
                                            <div class="user-group">
                                                <?php $__currentLoopData = $taskDetail->users(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <img <?php if($user->avatar): ?> src="<?php echo e(asset('/storage/uploads/avatar/' . $user->avatar)); ?>" <?php else: ?> src="<?php echo e(asset('/storage/uploads/avatar/avatar.png')); ?>" <?php endif; ?>
                                                        alt="image" data-bs-toggle="tooltip"
                                                        title="<?php echo e(!empty($user) ? $user->name : ''); ?>">
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/project_task/index.blade.php ENDPATH**/ ?>