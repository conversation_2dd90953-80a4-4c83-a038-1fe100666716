{"__meta": {"id": "X010206ea6e01abf42db159fedb127457", "datetime": "2025-07-31 05:22:18", "utime": **********.185523, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939337.152883, "end": **********.185631, "duration": 1.0327479839324951, "duration_str": "1.03s", "measures": [{"label": "Booting", "start": 1753939337.152883, "relative_start": 0, "end": **********.088235, "relative_end": **********.088235, "duration": 0.9353518486022949, "duration_str": "935ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.088251, "relative_start": 0.****************, "end": **********.185659, "relative_end": 2.7894973754882812e-05, "duration": 0.*****************, "duration_str": "97.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "IrbGqVSOHATfod2Kgyx9Tn0Nr42EPLX6ACVPVdNX", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-966818724 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-966818724\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1593534508 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1593534508\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1466877279 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1466877279\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1556931025 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1556931025\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1151686024 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1151686024\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1052583221 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:22:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iks4U1lETllSb0FtbEVUMXhVWWoyNnc9PSIsInZhbHVlIjoib3FZWFQ2M21FUU9seUswQTNBTDhwYlRJUXEraTJxdTE2Tk41NlZFZnE1aHdqbUZQWjFrR0YyWm9sUXpnMHdzandoMkdnenRRaWZ3NFJIaG41aE1CM1FBU0JaS0JTT1hDOWJuY2E4eVRMdThuV3J3eGQyWjc1M2pTclhkKzhMaWRrblJmSENOQnAxU3BrNkpBUDFrdGJ3YnZNQ0hHS0QzTVgzSlp0UlpxZlFmVzc1TnAvQzAwMlB6Tm04cnppcW4xdS9HMGU1eVltTmNQMllLZFlSYkYxeWNsMjY0UGNQcWkydzFGSFBKQXh6K3A2MXZjTkNtQ1ErbnBmNHFvSkhjbWJFUmtCWXh5cmxhQVVjRk1wdlZGRGt2bkRUOWFBY0IxU2tVUkF6a04rM0xnWTQxTWEvb1BoUTRmOFVTbVJhdmt3SkZZNlJ2KzA1R3JUTzV2L1lPTnZrT1Y1Y3NMYytuVkl1VzUwbm1VRkxxRGEycmpwNHVOM2MyUk5XVXYxMFBpaDBTTlpiN1AwdElyVzNzSmE0cVJmRDZqdVk3enl2MWZDTTBHT0dJblAybWlUSnhRMFFocnVHaENiWWo1SVhBZmpnczA5TjJpdkNYeUJUV1k0TzB0QzhIZFRYTEJza1NlV3NJL0xNT2V3RHVLTVB1eCtQVk5JV0NMQVlHUFRscUYiLCJtYWMiOiJhOWY3ZjdmYmYzYjVmMmU5OTQ1MGEzMmJiZDY2NjU5YmEwMWNlYjMyNGQxN2ZiMDYxYWM0ZWY4OTJiODMwZTcyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:22:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik9zUVhHWUNmSnQ2enhLbi9KckFSa3c9PSIsInZhbHVlIjoiS0R2R3h6bGZjV05mNVB6TDdERFY0RXYvK082ZlVFOUlkQ3ZjOVNkNVlxSzlxYWd0cHpEY1FZcE4rdTB1Sk5weitEMURwVE91QmJhVXNUYjJXTWdaanl3TDBhNXpuSXRxVmZ4V09CVU5GU3ZZZ0w5ZkYwTVhKaVVGbWk1eCt4U1RMNUF6MktMcDJFWG93RThWZ2JBeTRpR1lEQUhlTUZKbExqNkhmdUlueXQ2bHNFM3k0MnN3WkFJam9xclhCWDdwQ2duejR3U1M0UWpNRWFpdFJVKy9aVGJlME1hQ3JzR3I5Q3Q3b1hnYkYwMHhjd2IrMGdhajhRUForUld3S21oM0ovWXVvSEp6akt2T3d3aHRKRzh2aW1Db211Qkk3VW4vSkpMcVFIakVmSVFQWXh2VGJzbVc3bWNOaCtHTWI3TGhaWjBpblBabmMzUmpZdG01ek1YdDFYTzBEVURDNjBsaEpxWjVrUDF4aXNCWG9Fdm1CS1FObis1dnIraHkxbEJaUkFoRlQ3Vmx3QXJsWk1VL2RRWUNUcDlMOEQzZWpXbm5OQmlHWnhxZEpiK1ZqbUdBc2orazlaMG5pWTdzL1NUKzZMTngxTHlRSUNTMU9EL3Jra2NVVytDQjRQUHYzQit6NVZTbGN4Qmp6SWR6TG9kemhHVHpEWXpxZ2ViS3orS1oiLCJtYWMiOiJiZjhlYzE3ODYyMDExZWJlZTY2YWY2OTU5ZTgwZTE1NDgyZjcwZjJmYTc5MjU1NjI3N2MwNzE3ZDc0ZjdiY2NmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:22:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iks4U1lETllSb0FtbEVUMXhVWWoyNnc9PSIsInZhbHVlIjoib3FZWFQ2M21FUU9seUswQTNBTDhwYlRJUXEraTJxdTE2Tk41NlZFZnE1aHdqbUZQWjFrR0YyWm9sUXpnMHdzandoMkdnenRRaWZ3NFJIaG41aE1CM1FBU0JaS0JTT1hDOWJuY2E4eVRMdThuV3J3eGQyWjc1M2pTclhkKzhMaWRrblJmSENOQnAxU3BrNkpBUDFrdGJ3YnZNQ0hHS0QzTVgzSlp0UlpxZlFmVzc1TnAvQzAwMlB6Tm04cnppcW4xdS9HMGU1eVltTmNQMllLZFlSYkYxeWNsMjY0UGNQcWkydzFGSFBKQXh6K3A2MXZjTkNtQ1ErbnBmNHFvSkhjbWJFUmtCWXh5cmxhQVVjRk1wdlZGRGt2bkRUOWFBY0IxU2tVUkF6a04rM0xnWTQxTWEvb1BoUTRmOFVTbVJhdmt3SkZZNlJ2KzA1R3JUTzV2L1lPTnZrT1Y1Y3NMYytuVkl1VzUwbm1VRkxxRGEycmpwNHVOM2MyUk5XVXYxMFBpaDBTTlpiN1AwdElyVzNzSmE0cVJmRDZqdVk3enl2MWZDTTBHT0dJblAybWlUSnhRMFFocnVHaENiWWo1SVhBZmpnczA5TjJpdkNYeUJUV1k0TzB0QzhIZFRYTEJza1NlV3NJL0xNT2V3RHVLTVB1eCtQVk5JV0NMQVlHUFRscUYiLCJtYWMiOiJhOWY3ZjdmYmYzYjVmMmU5OTQ1MGEzMmJiZDY2NjU5YmEwMWNlYjMyNGQxN2ZiMDYxYWM0ZWY4OTJiODMwZTcyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:22:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik9zUVhHWUNmSnQ2enhLbi9KckFSa3c9PSIsInZhbHVlIjoiS0R2R3h6bGZjV05mNVB6TDdERFY0RXYvK082ZlVFOUlkQ3ZjOVNkNVlxSzlxYWd0cHpEY1FZcE4rdTB1Sk5weitEMURwVE91QmJhVXNUYjJXTWdaanl3TDBhNXpuSXRxVmZ4V09CVU5GU3ZZZ0w5ZkYwTVhKaVVGbWk1eCt4U1RMNUF6MktMcDJFWG93RThWZ2JBeTRpR1lEQUhlTUZKbExqNkhmdUlueXQ2bHNFM3k0MnN3WkFJam9xclhCWDdwQ2duejR3U1M0UWpNRWFpdFJVKy9aVGJlME1hQ3JzR3I5Q3Q3b1hnYkYwMHhjd2IrMGdhajhRUForUld3S21oM0ovWXVvSEp6akt2T3d3aHRKRzh2aW1Db211Qkk3VW4vSkpMcVFIakVmSVFQWXh2VGJzbVc3bWNOaCtHTWI3TGhaWjBpblBabmMzUmpZdG01ek1YdDFYTzBEVURDNjBsaEpxWjVrUDF4aXNCWG9Fdm1CS1FObis1dnIraHkxbEJaUkFoRlQ3Vmx3QXJsWk1VL2RRWUNUcDlMOEQzZWpXbm5OQmlHWnhxZEpiK1ZqbUdBc2orazlaMG5pWTdzL1NUKzZMTngxTHlRSUNTMU9EL3Jra2NVVytDQjRQUHYzQit6NVZTbGN4Qmp6SWR6TG9kemhHVHpEWXpxZ2ViS3orS1oiLCJtYWMiOiJiZjhlYzE3ODYyMDExZWJlZTY2YWY2OTU5ZTgwZTE1NDgyZjcwZjJmYTc5MjU1NjI3N2MwNzE3ZDc0ZjdiY2NmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:22:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1052583221\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-498119659 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IrbGqVSOHATfod2Kgyx9Tn0Nr42EPLX6ACVPVdNX</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-498119659\", {\"maxDepth\":0})</script>\n"}}