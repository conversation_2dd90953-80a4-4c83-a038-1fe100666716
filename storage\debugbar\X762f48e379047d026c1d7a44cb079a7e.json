{"__meta": {"id": "X762f48e379047d026c1d7a44cb079a7e", "datetime": "2025-07-31 05:36:55", "utime": **********.61152, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:36:55] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.598614, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753940213.665746, "end": **********.611763, "duration": 1.9460170269012451, "duration_str": "1.95s", "measures": [{"label": "Booting", "start": 1753940213.665746, "relative_start": 0, "end": **********.301197, "relative_end": **********.301197, "duration": 1.635451078414917, "duration_str": "1.64s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.301228, "relative_start": 1.6354820728302002, "end": **********.611776, "relative_end": 1.3113021850585938e-05, "duration": 0.3105480670928955, "duration_str": "311ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45913808, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0308, "accumulated_duration_str": "30.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.421405, "duration": 0.02485, "duration_str": "24.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 80.682}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.506615, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 80.682, "width_percent": 7.89}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.526956, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 88.571, "width_percent": 5.065}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.53973, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 93.636, "width_percent": 6.364}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-121964735 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-121964735\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-212201893 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-212201893\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1349446600 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349446600\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2100070644 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkoySDdVaENHUXpxRnNqRVpET0lncWc9PSIsInZhbHVlIjoiR1JKWVJkNlZDV2VLcG90SXlTbW5NRUt0MW9RM2NyQjJkcFg3TUtPUzBvL3ZxOUwwSkhHbHlXeVF1aE02SndSZk1SaWl0QS9LaStMbURNQVpadFpJa29MTWdOM3haanljWU5DcWtDc01ZWUVyMStpNU55U2dQTnEyN1lyd0l2eUpJT1NodUljNmVTUFZEOUtsbENBczhucGhtdEg1enNMNUdFeFIwc3JDSkZNUUlwSVRHdmNDSTJFbDA5R2xTNGV5UEprbjJOTUttK3c4amQyVDBLL3pmdythUldic052Rzhka3dVdHhqZHVGeEVFQ2ZlNm9PTFZ5eWZaT01DMzA1VlduVzZONFh3eXRycW1WMFhrL2E3ZFlaUDg2REExODBlVTB4ZmJpSlAvOGE0c0hxR2pQZzBpazVya015Q3V6M1JRWmZMczN2RHNTYzFBNStXVlYwOEw3Nm5nQVlwR042RUppdStaN21uL043ZlRJTEI3a2x1aWh4MGpFaUFxUmI5dzdoWThSeTZRenhBdzR0dGtOdFdHd3VKQkRWZWxLUGNjV1c3bzkzZlZCWFVSNy9yRDRqcVF6QzJiTkVDbC9EMUZRRVBhK0VkQ3NraG82Rm95Tm9ad2RBWTBnK1FVK2o4UjlIektnV2FjbTRDenpEMlg2UXIzeUlnYW44YmRiTWMiLCJtYWMiOiI0M2UyYTcxOTExNTYyNzRiOWI3YzU2ZDMzYzZmOTcyMjdlMTc2MzQ5MTg2OWIyYjJlMmIxZWU3ZDVhM2M0MThiIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjVVaWNiRVcyNHJCSjlvM3c5dWVSTXc9PSIsInZhbHVlIjoiZk9HOHpQTG5SVHlQNDh4UmpuSzBIUEU5OU1JbXlJQklveGdrMjZXdHB0ZGpFOHBDb2Q4cEFzb3hlRHFIT3BmMmlWbm5oTFJBWHNLbjZGcjd0S0dLamUvTXRMZVVLZldMVllTWEdMRXd1M20rVVl5NEcwbjNUaGpzMEJkSEE1R09KZ1BNSEx2T2c3bzN6RHpQUEJNRVRMbER5OS9iR0srVkwrb29VZHYvTnV3dkF2eXQ4OEVqVDVHU2lNczZCRWVUNzNScTNMNG1mb2tMWUM4N01BclEwQ0d3eEZFR1RGWURTdUN4a2Z3QWp2TjY0NmI2WnYrams0cVM2UGRScWE5NkFNY1dQenpkb3BKenFyNzRtODRZTThDeFYyd21rbmJscUM1NVp1dU8yQlc5RW81cmFCSmVIUnV4dHdzUVhVUlhzbVBMOVc2aVorNFNJNGc3ZEtMVVNvQWZsdHJ4RXZLWHRMRHBPMG41eXZ6UjdMMWlnaDdwamp3RDZEZ2pFUWpPdXFJajVVaTZBTFBrcHY0bWZJWnVITU5nS1VCS05ScnhWV3c4b1czN0lmenJPZndtcWZRdFJURjh5K3pGMU5PdmRKQzF2WmVMOStVZHZCalN0dFo1YXdLNmtMb05nazlPeSsvdUFaOEVHNnJvN2FkQ3prN25yRjBUbkY4QnExRVQiLCJtYWMiOiJhNmMyZmY0NmQxYTI4MDVhM2RmNTA1ZGU1Y2U1YzI0MzE4NWQ3ZGJlMDRiMGJmNjVmYjcyYzZiYjQ3MTY5MTNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2100070644\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1509985006 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lOWuOQi5K5zalKq6lxC9bmXS2G5Yh3URND5pGg9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509985006\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-660823713 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:36:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpsUzVxM0lPbVYwT2N0eU1hVDltd2c9PSIsInZhbHVlIjoiUWIwOFplYTQ4ekdwbDBOYnlzMXJKalZVcytZcWQrUHdaZ3pxSFUwTE1MR0MxKzBacmpkY2RsSHZDWE5XaElMWGVtZWpITkovRGRLb05MWlMzVjVsMmZ6MVYxeXdqdUl6YWxNc1IrUC9nRklkazNVd3Z2b0FWSTZqWTJTS2YrYUJudzNKY0tJZldVSUJZRy9ML29NODFLdjUyTDFzQUhTR2VKWEN2UXRZb1llQnVGSDRrSVJuZlk5R0JiZGxWY3NZdGg2b2RrbGxNdDlwSDRwUEVsYTRJMEdsWlVaQk9FT21uQktZMjFPMFROOTdINC9naktRc2RhbVdSSTJ4Wk5Yc1NNMUZXcVNIUnYvZHZmc3dyWllNTGJVbHZQcHpCUDdPWmJPSUVVYVhCMDBzeFlVWmVLNXZGci9peGRJd1Z5VGV2aFU2R0ZQc1NWbXVRb3Ara25hYUM3Mm4ybDlVQStLOFFSUUMyU052dERJemFzTkF4TnZybVBrZ2szNDFOQStjRW9aL3ZWV3BhS09tekNMbit3SnM1akpqNmp6dUZjY200NXk5amx6bmpBdUhhZEVjQmR0bm9SelRkdnhFcFF0c2xIV2duNEkwNGNsaVZybjlmZWtwaU9oTEUrU0gyUFRvNlI3WFJvcFRqOE40QnROVFhKMWRmZGllTkI2YzJXQ0IiLCJtYWMiOiIzYTc5YzlkOWUyZDRhMzQ3ZDBiZDQ5NzAyYjY2YWZhZmY3YzRkNDBlNDEyOTk5Mjk2YjBkMGFmNjVhMzJhNTU3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:36:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InB3K3NUaDNJMnU2dHVhWUY3U3FWcVE9PSIsInZhbHVlIjoiUEZ2YkZEdFFmT00rblZqa08xL0FPcDFGYjZhajYwWHhpWTJ2ZTh6c2JDbmpHZWVMN2plVmZEbDFtZVFUVEx0VVZCWElvMTBEeTIwZ3VCSWEzVEhTRXNaUTNRSGJuM2hkZmlqT0IxN21JSERYcXZ2cTQzNEVab0U4Y2RGUm1udlUxMzNYcTIwd2NlKzA0QjVET0FPYVlFK3g1YUx5UTVSc1RXSDFRNXJYZHpsK0h1MzJISU1yc3pCWGJDVTdUQ1NNN3p4b1NMTWYzQkI2RG8zMGZaTEs3a3I1OC9pcGR2TTVxRWMxWGpyQWE1U2Z5MHMrcEp4Mmp4bzV6aEhqSkZBSEQxdldnc0V1NVZ3SDIyTTczdlNxMVlnNTJkbDdNbW9Ic0pqNzI2TGlFTkR5dWdrS01iSHlJTi9UYmpjaXNMdXhJU3BiNzgrYktCS1JYQnFqK2N6eUNXbVlnWUNKUGs3T2xIaXA1SkFIUC8xL1g2UGxGNC9LWGgyNExFR0U2RCtsNFNZcHVBNmI0YjBxMi9TUjBPT250bUJQK0hneVFEaEpMQkNuVDdQWFhCYXc5YSsvNWNnaC9uci83dWhmUW0yOXc0alZCM1N6WjFPaG1BWmk1OU42enk2UUV2UHRoK0lNMDJZczR0bUJqVnBYN0Q4aUF0N2pCSDJlL2p2YlE3ZkIiLCJtYWMiOiIxNWI1YWQzM2EwNWE2MDhmODc2ZTg1NzQzMWYwZjk4ZWNiYTQzZGFlOWM0ZDVkMWNlYWIzNjg4NzNkMWYwMzMzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:36:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpsUzVxM0lPbVYwT2N0eU1hVDltd2c9PSIsInZhbHVlIjoiUWIwOFplYTQ4ekdwbDBOYnlzMXJKalZVcytZcWQrUHdaZ3pxSFUwTE1MR0MxKzBacmpkY2RsSHZDWE5XaElMWGVtZWpITkovRGRLb05MWlMzVjVsMmZ6MVYxeXdqdUl6YWxNc1IrUC9nRklkazNVd3Z2b0FWSTZqWTJTS2YrYUJudzNKY0tJZldVSUJZRy9ML29NODFLdjUyTDFzQUhTR2VKWEN2UXRZb1llQnVGSDRrSVJuZlk5R0JiZGxWY3NZdGg2b2RrbGxNdDlwSDRwUEVsYTRJMEdsWlVaQk9FT21uQktZMjFPMFROOTdINC9naktRc2RhbVdSSTJ4Wk5Yc1NNMUZXcVNIUnYvZHZmc3dyWllNTGJVbHZQcHpCUDdPWmJPSUVVYVhCMDBzeFlVWmVLNXZGci9peGRJd1Z5VGV2aFU2R0ZQc1NWbXVRb3Ara25hYUM3Mm4ybDlVQStLOFFSUUMyU052dERJemFzTkF4TnZybVBrZ2szNDFOQStjRW9aL3ZWV3BhS09tekNMbit3SnM1akpqNmp6dUZjY200NXk5amx6bmpBdUhhZEVjQmR0bm9SelRkdnhFcFF0c2xIV2duNEkwNGNsaVZybjlmZWtwaU9oTEUrU0gyUFRvNlI3WFJvcFRqOE40QnROVFhKMWRmZGllTkI2YzJXQ0IiLCJtYWMiOiIzYTc5YzlkOWUyZDRhMzQ3ZDBiZDQ5NzAyYjY2YWZhZmY3YzRkNDBlNDEyOTk5Mjk2YjBkMGFmNjVhMzJhNTU3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:36:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InB3K3NUaDNJMnU2dHVhWUY3U3FWcVE9PSIsInZhbHVlIjoiUEZ2YkZEdFFmT00rblZqa08xL0FPcDFGYjZhajYwWHhpWTJ2ZTh6c2JDbmpHZWVMN2plVmZEbDFtZVFUVEx0VVZCWElvMTBEeTIwZ3VCSWEzVEhTRXNaUTNRSGJuM2hkZmlqT0IxN21JSERYcXZ2cTQzNEVab0U4Y2RGUm1udlUxMzNYcTIwd2NlKzA0QjVET0FPYVlFK3g1YUx5UTVSc1RXSDFRNXJYZHpsK0h1MzJISU1yc3pCWGJDVTdUQ1NNN3p4b1NMTWYzQkI2RG8zMGZaTEs3a3I1OC9pcGR2TTVxRWMxWGpyQWE1U2Z5MHMrcEp4Mmp4bzV6aEhqSkZBSEQxdldnc0V1NVZ3SDIyTTczdlNxMVlnNTJkbDdNbW9Ic0pqNzI2TGlFTkR5dWdrS01iSHlJTi9UYmpjaXNMdXhJU3BiNzgrYktCS1JYQnFqK2N6eUNXbVlnWUNKUGs3T2xIaXA1SkFIUC8xL1g2UGxGNC9LWGgyNExFR0U2RCtsNFNZcHVBNmI0YjBxMi9TUjBPT250bUJQK0hneVFEaEpMQkNuVDdQWFhCYXc5YSsvNWNnaC9uci83dWhmUW0yOXc0alZCM1N6WjFPaG1BWmk1OU42enk2UUV2UHRoK0lNMDJZczR0bUJqVnBYN0Q4aUF0N2pCSDJlL2p2YlE3ZkIiLCJtYWMiOiIxNWI1YWQzM2EwNWE2MDhmODc2ZTg1NzQzMWYwZjk4ZWNiYTQzZGFlOWM0ZDVkMWNlYWIzNjg4NzNkMWYwMzMzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:36:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-660823713\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1650482244 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1650482244\", {\"maxDepth\":0})</script>\n"}}