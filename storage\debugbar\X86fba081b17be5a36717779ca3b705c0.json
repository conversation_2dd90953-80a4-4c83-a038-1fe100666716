{"__meta": {"id": "X86fba081b17be5a36717779ca3b705c0", "datetime": "2025-07-31 05:50:10", "utime": **********.586353, "method": "GET", "uri": "/account-dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.565443, "end": **********.586417, "duration": 5.****************, "duration_str": "5.02s", "measures": [{"label": "Booting", "start": **********.565443, "relative_start": 0, "end": **********.608766, "relative_end": **********.608766, "duration": 2.***************, "duration_str": "2.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.608795, "relative_start": 2.***************, "end": **********.586425, "relative_end": 8.106231689453125e-06, "duration": 2.***************, "duration_str": "2.98s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "1x dashboard.account-dashboard", "param_count": null, "params": [], "start": **********.037244, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/dashboard/account-dashboard.blade.phpdashboard.account-dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fdashboard%2Faccount-dashboard.blade.php&line=1", "ajax": false, "filename": "account-dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "dashboard.account-dashboard"}, {"name": "1x layouts.admin", "param_count": null, "params": [], "start": **********.231, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php&line=1", "ajax": false, "filename": "admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.admin"}, {"name": "1x partials.admin.menu", "param_count": null, "params": [], "start": **********.258718, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/menu.blade.phppartials.admin.menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.menu"}, {"name": "1x partials.admin.header", "param_count": null, "params": [], "start": **********.435145, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/header.blade.phppartials.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.header"}, {"name": "1x partials.admin.footer", "param_count": null, "params": [], "start": **********.512967, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/footer.blade.phppartials.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.footer"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.530967, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}, {"name": "1x Chatify::layouts.footerLinks", "param_count": null, "params": [], "start": **********.539476, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/footerLinks.blade.phpChatify::layouts.footerLinks", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FfooterLinks.blade.php&line=1", "ajax": false, "filename": "footerLinks.blade.php", "line": "?"}, "render_count": 1, "name_original": "Chatify::layouts.footerLinks"}]}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=82\" onclick=\"\">app/Http/Controllers/DashboardController.php:82-182</a>"}, "queries": {"nb_statements": 168, "nb_failed_statements": 0, "accumulated_duration": 0.*****************, "accumulated_duration_str": "277ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.763741, "duration": 0.011619999999999998, "duration_str": "11.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 4.199}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.840051, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 4.199, "width_percent": 0.719}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 94}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.87484, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 4.918, "width_percent": 0.52}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 94}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8892221, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 5.438, "width_percent": 0.546}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 94}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.90374, "duration": 0.01142, "duration_str": "11.42ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 5.984, "width_percent": 4.126}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.0150578, "duration": 0.0043, "duration_str": "4.3ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 10.11, "width_percent": 1.554}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.0654638, "duration": 0.01763, "duration_str": "17.63ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 11.664, "width_percent": 6.37}, {"sql": "select * from `revenues` where `created_by` = 79 order by `id` desc limit 5", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 95}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.703566, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:95", "source": "app/Http/Controllers/DashboardController.php:95", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=95", "ajax": false, "filename": "DashboardController.php", "line": "95"}, "connection": "radhe_same", "start_percent": 18.034, "width_percent": 0.488}, {"sql": "select * from `payments` where `created_by` = 79 order by `id` desc limit 5", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.711983, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:96", "source": "app/Http/Controllers/DashboardController.php:96", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=96", "ajax": false, "filename": "DashboardController.php", "line": "96"}, "connection": "radhe_same", "start_percent": 18.522, "width_percent": 0.296}, {"sql": "select * from `product_service_categories` where `created_by` = 79 and `type` = 'income'", "type": "query", "params": [], "bindings": ["79", "income"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 100}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.72119, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:100", "source": "app/Http/Controllers/DashboardController.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=100", "ajax": false, "filename": "DashboardController.php", "line": "100"}, "connection": "radhe_same", "start_percent": 18.818, "width_percent": 0.401}, {"sql": "select * from `product_service_categories` where `created_by` = 79 and `type` = 'expense'", "type": "query", "params": [], "bindings": ["79", "expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 116}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.728552, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:116", "source": "app/Http/Controllers/DashboardController.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=116", "ajax": false, "filename": "DashboardController.php", "line": "116"}, "connection": "radhe_same", "start_percent": 19.22, "width_percent": 0.304}, {"sql": "select sum(`amount`) as aggregate from `payments` where `payments`.`category_id` = 6 and `payments`.`category_id` is not null and `created_by` = 79 and YEAR(date) ='2025'", "type": "query", "params": [], "bindings": ["6", "79", "2025"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\ProductServiceCategory.php", "line": 70}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 123}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.739644, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:70", "source": "app/Models/ProductServiceCategory.php:70", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FProductServiceCategory.php&line=70", "ajax": false, "filename": "ProductServiceCategory.php", "line": "70"}, "connection": "radhe_same", "start_percent": 19.523, "width_percent": 0.231}, {"sql": "select * from `bills` where `bills`.`category_id` = 6 and `bills`.`category_id` is not null", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\ProductServiceCategory.php", "line": 72}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 123}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.744696, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:72", "source": "app/Models/ProductServiceCategory.php:72", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FProductServiceCategory.php&line=72", "ajax": false, "filename": "ProductServiceCategory.php", "line": "72"}, "connection": "radhe_same", "start_percent": 19.754, "width_percent": 0.22}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 1 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 856}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.756448, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "User.php:856", "source": "app/Models/User.php:856", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=856", "ajax": false, "filename": "User.php", "line": "856"}, "connection": "radhe_same", "start_percent": 19.975, "width_percent": 0.246}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 1 and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "1", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 724}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 857}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7629528, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "User.php:724", "source": "app/Models/User.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=724", "ajax": false, "filename": "User.php", "line": "724"}, "connection": "radhe_same", "start_percent": 20.22, "width_percent": 0.47}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 1 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 863}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.76983, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "User.php:863", "source": "app/Models/User.php:863", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=863", "ajax": false, "filename": "User.php", "line": "863"}, "connection": "radhe_same", "start_percent": 20.69, "width_percent": 0.314}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 1 and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "1", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 789}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 864}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.776835, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "User.php:789", "source": "app/Models/User.php:789", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=789", "ajax": false, "filename": "User.php", "line": "789"}, "connection": "radhe_same", "start_percent": 21.005, "width_percent": 0.401}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 2 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 856}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.783942, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "User.php:856", "source": "app/Models/User.php:856", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=856", "ajax": false, "filename": "User.php", "line": "856"}, "connection": "radhe_same", "start_percent": 21.406, "width_percent": 0.314}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 2 and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "2", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 724}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 857}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7909079, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "User.php:724", "source": "app/Models/User.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=724", "ajax": false, "filename": "User.php", "line": "724"}, "connection": "radhe_same", "start_percent": 21.72, "width_percent": 0.408}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 2 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 863}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7977479, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "User.php:863", "source": "app/Models/User.php:863", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=863", "ajax": false, "filename": "User.php", "line": "863"}, "connection": "radhe_same", "start_percent": 22.128, "width_percent": 0.264}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 2 and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "2", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 789}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 864}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8043709, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "User.php:789", "source": "app/Models/User.php:789", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=789", "ajax": false, "filename": "User.php", "line": "789"}, "connection": "radhe_same", "start_percent": 22.392, "width_percent": 0.412}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 3 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 856}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8107169, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "User.php:856", "source": "app/Models/User.php:856", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=856", "ajax": false, "filename": "User.php", "line": "856"}, "connection": "radhe_same", "start_percent": 22.804, "width_percent": 0.267}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 3 and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "3", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 724}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 857}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.816591, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "User.php:724", "source": "app/Models/User.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=724", "ajax": false, "filename": "User.php", "line": "724"}, "connection": "radhe_same", "start_percent": 23.071, "width_percent": 0.56}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 3 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 863}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.824209, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "User.php:863", "source": "app/Models/User.php:863", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=863", "ajax": false, "filename": "User.php", "line": "863"}, "connection": "radhe_same", "start_percent": 23.631, "width_percent": 0.296}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 3 and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "3", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 789}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 864}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.830784, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "User.php:789", "source": "app/Models/User.php:789", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=789", "ajax": false, "filename": "User.php", "line": "789"}, "connection": "radhe_same", "start_percent": 23.928, "width_percent": 0.416}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 4 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 856}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.838909, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "User.php:856", "source": "app/Models/User.php:856", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=856", "ajax": false, "filename": "User.php", "line": "856"}, "connection": "radhe_same", "start_percent": 24.343, "width_percent": 0.282}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 4 and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "4", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 724}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 857}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.845056, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "User.php:724", "source": "app/Models/User.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=724", "ajax": false, "filename": "User.php", "line": "724"}, "connection": "radhe_same", "start_percent": 24.625, "width_percent": 0.365}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 4 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 863}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8516672, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "User.php:863", "source": "app/Models/User.php:863", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=863", "ajax": false, "filename": "User.php", "line": "863"}, "connection": "radhe_same", "start_percent": 24.99, "width_percent": 0.228}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 4 and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "4", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 789}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 864}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.855927, "duration": 0.00081000000********, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "User.php:789", "source": "app/Models/User.php:789", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=789", "ajax": false, "filename": "User.php", "line": "789"}, "connection": "radhe_same", "start_percent": 25.218, "width_percent": 0.293}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 5 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 856}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.860441, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "User.php:856", "source": "app/Models/User.php:856", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=856", "ajax": false, "filename": "User.php", "line": "856"}, "connection": "radhe_same", "start_percent": 25.51, "width_percent": 0.264}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 5 and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "5", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 724}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 857}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.866878, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "User.php:724", "source": "app/Models/User.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=724", "ajax": false, "filename": "User.php", "line": "724"}, "connection": "radhe_same", "start_percent": 25.774, "width_percent": 0.571}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 5 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 863}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.873818, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "User.php:863", "source": "app/Models/User.php:863", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=863", "ajax": false, "filename": "User.php", "line": "863"}, "connection": "radhe_same", "start_percent": 26.345, "width_percent": 0.264}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 5 and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "5", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 789}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 864}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.880185, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "User.php:789", "source": "app/Models/User.php:789", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=789", "ajax": false, "filename": "User.php", "line": "789"}, "connection": "radhe_same", "start_percent": 26.609, "width_percent": 0.383}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 6 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 856}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8871632, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "User.php:856", "source": "app/Models/User.php:856", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=856", "ajax": false, "filename": "User.php", "line": "856"}, "connection": "radhe_same", "start_percent": 26.992, "width_percent": 0.228}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 6 and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "6", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 724}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 857}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.892983, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "User.php:724", "source": "app/Models/User.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=724", "ajax": false, "filename": "User.php", "line": "724"}, "connection": "radhe_same", "start_percent": 27.22, "width_percent": 0.434}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 6 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 863}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9014199, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "User.php:863", "source": "app/Models/User.php:863", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=863", "ajax": false, "filename": "User.php", "line": "863"}, "connection": "radhe_same", "start_percent": 27.653, "width_percent": 0.347}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 6 and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "6", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 789}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 864}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.910824, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "User.php:789", "source": "app/Models/User.php:789", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=789", "ajax": false, "filename": "User.php", "line": "789"}, "connection": "radhe_same", "start_percent": 28, "width_percent": 0.611}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 7 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 856}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.919777, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "User.php:856", "source": "app/Models/User.php:856", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=856", "ajax": false, "filename": "User.php", "line": "856"}, "connection": "radhe_same", "start_percent": 28.611, "width_percent": 0.332}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 7 and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "7", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 724}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 857}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.926456, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "User.php:724", "source": "app/Models/User.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=724", "ajax": false, "filename": "User.php", "line": "724"}, "connection": "radhe_same", "start_percent": 28.943, "width_percent": 0.43}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 7 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 863}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.935318, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "User.php:863", "source": "app/Models/User.php:863", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=863", "ajax": false, "filename": "User.php", "line": "863"}, "connection": "radhe_same", "start_percent": 29.373, "width_percent": 0.275}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 7 and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "7", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 789}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 864}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9438019, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "User.php:789", "source": "app/Models/User.php:789", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=789", "ajax": false, "filename": "User.php", "line": "789"}, "connection": "radhe_same", "start_percent": 29.648, "width_percent": 0.538}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 8 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 856}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.954494, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "User.php:856", "source": "app/Models/User.php:856", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=856", "ajax": false, "filename": "User.php", "line": "856"}, "connection": "radhe_same", "start_percent": 30.186, "width_percent": 0.35}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 8 and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "8", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 724}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 857}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.963523, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "User.php:724", "source": "app/Models/User.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=724", "ajax": false, "filename": "User.php", "line": "724"}, "connection": "radhe_same", "start_percent": 30.537, "width_percent": 0.575}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 8 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 863}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.974562, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "User.php:863", "source": "app/Models/User.php:863", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=863", "ajax": false, "filename": "User.php", "line": "863"}, "connection": "radhe_same", "start_percent": 31.111, "width_percent": 0.397}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 8 and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "8", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 789}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 864}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.982386, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "User.php:789", "source": "app/Models/User.php:789", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=789", "ajax": false, "filename": "User.php", "line": "789"}, "connection": "radhe_same", "start_percent": 31.509, "width_percent": 0.502}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 9 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "9"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 856}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.990246, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "User.php:856", "source": "app/Models/User.php:856", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=856", "ajax": false, "filename": "User.php", "line": "856"}, "connection": "radhe_same", "start_percent": 32.011, "width_percent": 0.314}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 9 and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "9", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 724}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 857}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9971619, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "User.php:724", "source": "app/Models/User.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=724", "ajax": false, "filename": "User.php", "line": "724"}, "connection": "radhe_same", "start_percent": 32.325, "width_percent": 0.452}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 9 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "9"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 863}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.006875, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "User.php:863", "source": "app/Models/User.php:863", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=863", "ajax": false, "filename": "User.php", "line": "863"}, "connection": "radhe_same", "start_percent": 32.777, "width_percent": 0.379}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 9 and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "9", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 789}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 864}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.016755, "duration": 0.00309, "duration_str": "3.09ms", "memory": 0, "memory_str": null, "filename": "User.php:789", "source": "app/Models/User.php:789", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=789", "ajax": false, "filename": "User.php", "line": "789"}, "connection": "radhe_same", "start_percent": 33.156, "width_percent": 1.117}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 10 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "10"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 856}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.028107, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "User.php:856", "source": "app/Models/User.php:856", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=856", "ajax": false, "filename": "User.php", "line": "856"}, "connection": "radhe_same", "start_percent": 34.273, "width_percent": 0.538}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 10 and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "10", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 724}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 857}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.039481, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "User.php:724", "source": "app/Models/User.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=724", "ajax": false, "filename": "User.php", "line": "724"}, "connection": "radhe_same", "start_percent": 34.811, "width_percent": 0.64}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 10 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "10"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 863}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.049631, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "User.php:863", "source": "app/Models/User.php:863", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=863", "ajax": false, "filename": "User.php", "line": "863"}, "connection": "radhe_same", "start_percent": 35.451, "width_percent": 0.419}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 10 and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "10", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 789}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 864}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.057344, "duration": 0.00129000000********, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "User.php:789", "source": "app/Models/User.php:789", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=789", "ajax": false, "filename": "User.php", "line": "789"}, "connection": "radhe_same", "start_percent": 35.87, "width_percent": 0.466}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 11 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "11"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 856}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.074541, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "User.php:856", "source": "app/Models/User.php:856", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=856", "ajax": false, "filename": "User.php", "line": "856"}, "connection": "radhe_same", "start_percent": 36.336, "width_percent": 0.365}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 11 and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "11", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 724}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 857}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.081629, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "User.php:724", "source": "app/Models/User.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=724", "ajax": false, "filename": "User.php", "line": "724"}, "connection": "radhe_same", "start_percent": 36.701, "width_percent": 0.528}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 11 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "11"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 863}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.09254, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "User.php:863", "source": "app/Models/User.php:863", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=863", "ajax": false, "filename": "User.php", "line": "863"}, "connection": "radhe_same", "start_percent": 37.229, "width_percent": 0.383}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 11 and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "11", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 789}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 864}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.103152, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "User.php:789", "source": "app/Models/User.php:789", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=789", "ajax": false, "filename": "User.php", "line": "789"}, "connection": "radhe_same", "start_percent": 37.612, "width_percent": 0.679}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 12 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 856}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1140869, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "User.php:856", "source": "app/Models/User.php:856", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=856", "ajax": false, "filename": "User.php", "line": "856"}, "connection": "radhe_same", "start_percent": 38.291, "width_percent": 0.34}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where YEAR(invoices.send_date) = '2025' and MONTH(invoices.send_date) = 12 and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025", "12", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 724}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 857}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.124273, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "User.php:724", "source": "app/Models/User.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=724", "ajax": false, "filename": "User.php", "line": "724"}, "connection": "radhe_same", "start_percent": 38.631, "width_percent": 0.481}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and year(`date`) = '2025' and month(`date`) = 12 limit 1", "type": "query", "params": [], "bindings": ["79", "2025", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 863}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.134831, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "User.php:863", "source": "app/Models/User.php:863", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=863", "ajax": false, "filename": "User.php", "line": "863"}, "connection": "radhe_same", "start_percent": 39.111, "width_percent": 0.372}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(bill_products.quantity) as total_quantity, SUM(bill_products.discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where YEAR(bills.send_date) = '2025' and MONTH(bills.send_date) = 12 and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025", "12", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 789}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 864}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 130}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.14537, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "User.php:789", "source": "app/Models/User.php:789", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=789", "ajax": false, "filename": "User.php", "line": "789"}, "connection": "radhe_same", "start_percent": 39.483, "width_percent": 0.509}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and date = '2025-07-31' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-31"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 897}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.156154, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "User.php:897", "source": "app/Models/User.php:897", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=897", "ajax": false, "filename": "User.php", "line": "897"}, "connection": "radhe_same", "start_percent": 39.993, "width_percent": 0.332}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-07-31' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-31", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 899}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.165827, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 40.325, "width_percent": 0.712}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and date = '2025-07-31' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-31"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 904}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.177749, "duration": 0.00104000000********, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "User.php:904", "source": "app/Models/User.php:904", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=904", "ajax": false, "filename": "User.php", "line": "904"}, "connection": "radhe_same", "start_percent": 41.037, "width_percent": 0.376}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-07-31' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-31", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 906}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.187907, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 41.413, "width_percent": 0.575}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and date = '2025-07-30' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 897}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.199735, "duration": 0.00129000000********, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "User.php:897", "source": "app/Models/User.php:897", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=897", "ajax": false, "filename": "User.php", "line": "897"}, "connection": "radhe_same", "start_percent": 41.987, "width_percent": 0.466}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-07-30' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-30", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 899}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.20892, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 42.453, "width_percent": 0.56}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and date = '2025-07-30' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 904}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.219845, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "User.php:904", "source": "app/Models/User.php:904", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=904", "ajax": false, "filename": "User.php", "line": "904"}, "connection": "radhe_same", "start_percent": 43.014, "width_percent": 0.325}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-07-30' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-30", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 906}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2290192, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 43.339, "width_percent": 0.491}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and date = '2025-07-29' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-29"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 897}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.240259, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "User.php:897", "source": "app/Models/User.php:897", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=897", "ajax": false, "filename": "User.php", "line": "897"}, "connection": "radhe_same", "start_percent": 43.83, "width_percent": 0.329}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-07-29' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-29", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 899}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.25006, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 44.159, "width_percent": 0.564}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and date = '2025-07-29' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-29"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 904}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.258025, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "User.php:904", "source": "app/Models/User.php:904", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=904", "ajax": false, "filename": "User.php", "line": "904"}, "connection": "radhe_same", "start_percent": 44.723, "width_percent": 0.336}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-07-29' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-29", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 906}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.265194, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 45.059, "width_percent": 0.571}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and date = '2025-07-28' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-28"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 897}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.273071, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "User.php:897", "source": "app/Models/User.php:897", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=897", "ajax": false, "filename": "User.php", "line": "897"}, "connection": "radhe_same", "start_percent": 45.63, "width_percent": 0.311}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-07-28' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-28", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 899}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.279394, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 45.94, "width_percent": 0.397}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and date = '2025-07-28' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-28"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 904}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.287379, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "User.php:904", "source": "app/Models/User.php:904", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=904", "ajax": false, "filename": "User.php", "line": "904"}, "connection": "radhe_same", "start_percent": 46.338, "width_percent": 0.264}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-07-28' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-28", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 906}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2940881, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 46.602, "width_percent": 0.369}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and date = '2025-07-27' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-27"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 897}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.301153, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "User.php:897", "source": "app/Models/User.php:897", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=897", "ajax": false, "filename": "User.php", "line": "897"}, "connection": "radhe_same", "start_percent": 46.97, "width_percent": 0.275}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-07-27' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-27", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 899}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3079588, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 47.245, "width_percent": 0.343}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and date = '2025-07-27' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-27"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 904}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.317294, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "User.php:904", "source": "app/Models/User.php:904", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=904", "ajax": false, "filename": "User.php", "line": "904"}, "connection": "radhe_same", "start_percent": 47.588, "width_percent": 0.343}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-07-27' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-27", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 906}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.325696, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 47.931, "width_percent": 0.578}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and date = '2025-07-26' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-26"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 897}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.336275, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "User.php:897", "source": "app/Models/User.php:897", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=897", "ajax": false, "filename": "User.php", "line": "897"}, "connection": "radhe_same", "start_percent": 48.509, "width_percent": 0.426}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-07-26' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-26", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 899}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.345565, "duration": 0.00154000000********, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 48.936, "width_percent": 0.556}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and date = '2025-07-26' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-26"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 904}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3561308, "duration": 0.00077000000********, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "User.php:904", "source": "app/Models/User.php:904", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=904", "ajax": false, "filename": "User.php", "line": "904"}, "connection": "radhe_same", "start_percent": 49.492, "width_percent": 0.278}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-07-26' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-26", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 906}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.366267, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 49.771, "width_percent": 0.629}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and date = '2025-07-25' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-25"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 897}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.377759, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "User.php:897", "source": "app/Models/User.php:897", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=897", "ajax": false, "filename": "User.php", "line": "897"}, "connection": "radhe_same", "start_percent": 50.399, "width_percent": 0.336}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-07-25' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-25", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 899}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3861759, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 50.735, "width_percent": 0.347}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and date = '2025-07-25' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-25"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 904}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3950222, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "User.php:904", "source": "app/Models/User.php:904", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=904", "ajax": false, "filename": "User.php", "line": "904"}, "connection": "radhe_same", "start_percent": 51.082, "width_percent": 0.354}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-07-25' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-25", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 906}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.404844, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 51.436, "width_percent": 0.542}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and date = '2025-07-24' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-24"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 897}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4159958, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "User.php:897", "source": "app/Models/User.php:897", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=897", "ajax": false, "filename": "User.php", "line": "897"}, "connection": "radhe_same", "start_percent": 51.978, "width_percent": 0.34}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-07-24' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-24", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 899}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4249701, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 52.318, "width_percent": 0.416}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and date = '2025-07-24' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-24"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 904}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.433305, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "User.php:904", "source": "app/Models/User.php:904", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=904", "ajax": false, "filename": "User.php", "line": "904"}, "connection": "radhe_same", "start_percent": 52.734, "width_percent": 0.271}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-07-24' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-24", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 906}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4404929, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 53.005, "width_percent": 0.524}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and date = '2025-07-23' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 897}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.449104, "duration": 0.00089000000********, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "User.php:897", "source": "app/Models/User.php:897", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=897", "ajax": false, "filename": "User.php", "line": "897"}, "connection": "radhe_same", "start_percent": 53.528, "width_percent": 0.322}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-07-23' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-23", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 899}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.455704, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 53.85, "width_percent": 0.502}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and date = '2025-07-23' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 904}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.465002, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "User.php:904", "source": "app/Models/User.php:904", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=904", "ajax": false, "filename": "User.php", "line": "904"}, "connection": "radhe_same", "start_percent": 54.352, "width_percent": 0.296}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-07-23' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-23", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 906}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.472613, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 54.649, "width_percent": 0.444}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and date = '2025-07-22' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 897}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.482793, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "User.php:897", "source": "app/Models/User.php:897", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=897", "ajax": false, "filename": "User.php", "line": "897"}, "connection": "radhe_same", "start_percent": 55.093, "width_percent": 0.369}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-07-22' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-22", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 899}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4925568, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 55.462, "width_percent": 0.473}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and date = '2025-07-22' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 904}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.501462, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "User.php:904", "source": "app/Models/User.php:904", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=904", "ajax": false, "filename": "User.php", "line": "904"}, "connection": "radhe_same", "start_percent": 55.935, "width_percent": 0.358}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-07-22' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-22", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 906}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.509916, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 56.293, "width_percent": 0.434}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and date = '2025-07-21' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-21"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 897}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.517677, "duration": 0.00089000000********, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "User.php:897", "source": "app/Models/User.php:897", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=897", "ajax": false, "filename": "User.php", "line": "897"}, "connection": "radhe_same", "start_percent": 56.726, "width_percent": 0.322}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-07-21' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-21", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 899}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.526535, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 57.048, "width_percent": 0.513}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and date = '2025-07-21' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-21"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 904}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.536939, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "User.php:904", "source": "app/Models/User.php:904", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=904", "ajax": false, "filename": "User.php", "line": "904"}, "connection": "radhe_same", "start_percent": 57.561, "width_percent": 0.412}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-07-21' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-21", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 906}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.546418, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 57.973, "width_percent": 0.535}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and date = '2025-07-20' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-20"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 897}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.557394, "duration": 0.00089000000********, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "User.php:897", "source": "app/Models/User.php:897", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=897", "ajax": false, "filename": "User.php", "line": "897"}, "connection": "radhe_same", "start_percent": 58.508, "width_percent": 0.322}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-07-20' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-20", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 899}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.566868, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 58.829, "width_percent": 0.668}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and date = '2025-07-20' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-20"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 904}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.578674, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "User.php:904", "source": "app/Models/User.php:904", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=904", "ajax": false, "filename": "User.php", "line": "904"}, "connection": "radhe_same", "start_percent": 59.498, "width_percent": 0.354}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-07-20' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-20", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 906}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5879462, "duration": 0.00153000000********, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 59.852, "width_percent": 0.553}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and date = '2025-07-19' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-19"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 897}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.597856, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "User.php:897", "source": "app/Models/User.php:897", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=897", "ajax": false, "filename": "User.php", "line": "897"}, "connection": "radhe_same", "start_percent": 60.405, "width_percent": 0.325}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-07-19' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-19", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 899}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6072772, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 60.73, "width_percent": 0.802}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and date = '2025-07-19' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-19"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 904}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6181688, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "User.php:904", "source": "app/Models/User.php:904", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=904", "ajax": false, "filename": "User.php", "line": "904"}, "connection": "radhe_same", "start_percent": 61.532, "width_percent": 0.65}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-07-19' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-19", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 906}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.628209, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 62.182, "width_percent": 0.499}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and date = '2025-07-18' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-18"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 897}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.639687, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "User.php:897", "source": "app/Models/User.php:897", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=897", "ajax": false, "filename": "User.php", "line": "897"}, "connection": "radhe_same", "start_percent": 62.681, "width_percent": 0.318}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-07-18' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-18", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 899}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6498551, "duration": 0.00187000000********, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 62.999, "width_percent": 0.676}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and date = '2025-07-18' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-18"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 904}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.660031, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "User.php:904", "source": "app/Models/User.php:904", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=904", "ajax": false, "filename": "User.php", "line": "904"}, "connection": "radhe_same", "start_percent": 63.675, "width_percent": 0.354}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-07-18' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-18", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 906}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.670412, "duration": 0.00246, "duration_str": "2.46ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 64.029, "width_percent": 0.889}, {"sql": "select sum(amount) amount from `revenues` where `created_by` = 79 and date = '2025-07-17' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 897}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.681066, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "User.php:897", "source": "app/Models/User.php:897", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=897", "ajax": false, "filename": "User.php", "line": "897"}, "connection": "radhe_same", "start_percent": 64.918, "width_percent": 0.318}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '2025-07-17' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-17", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 899}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6924398, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 65.236, "width_percent": 0.535}, {"sql": "select sum(amount) amount from `payments` where `created_by` = 79 and date = '2025-07-17' limit 1", "type": "query", "params": [], "bindings": ["79", "2025-07-17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 904}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.702267, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "User.php:904", "source": "app/Models/User.php:904", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=904", "ajax": false, "filename": "User.php", "line": "904"}, "connection": "radhe_same", "start_percent": 65.771, "width_percent": 0.397}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '2025-07-17' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-17", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 906}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 131}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.712406, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 66.168, "width_percent": 0.524}, {"sql": "select count(*) as aggregate from `taxes` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 136}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.724176, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:136", "source": "app/Http/Controllers/DashboardController.php:136", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=136", "ajax": false, "filename": "DashboardController.php", "line": "136"}, "connection": "radhe_same", "start_percent": 66.692, "width_percent": 0.347}, {"sql": "select count(*) as aggregate from `product_service_categories` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 137}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.732829, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:137", "source": "app/Http/Controllers/DashboardController.php:137", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=137", "ajax": false, "filename": "DashboardController.php", "line": "137"}, "connection": "radhe_same", "start_percent": 67.039, "width_percent": 0.372}, {"sql": "select count(*) as aggregate from `product_service_units` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 138}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.74394, "duration": 0.00081000000********, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:138", "source": "app/Http/Controllers/DashboardController.php:138", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=138", "ajax": false, "filename": "DashboardController.php", "line": "138"}, "connection": "radhe_same", "start_percent": 67.411, "width_percent": 0.293}, {"sql": "select count(*) as aggregate from `bank_accounts` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.753738, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:139", "source": "app/Http/Controllers/DashboardController.php:139", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=139", "ajax": false, "filename": "DashboardController.php", "line": "139"}, "connection": "radhe_same", "start_percent": 67.704, "width_percent": 0.397}, {"sql": "select * from `bank_accounts` where `created_by` = 79 limit 5", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 141}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7626388, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:141", "source": "app/Http/Controllers/DashboardController.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=141", "ajax": false, "filename": "DashboardController.php", "line": "141"}, "connection": "radhe_same", "start_percent": 68.101, "width_percent": 0.34}, {"sql": "select `invoices`.*, `customers`.`name` as `customer_name` from `invoices` inner join `customers` on `invoices`.`customer_id` = `customers`.`id` where `invoices`.`created_by` = 79 order by `invoices`.`id` desc limit 5", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 147}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.776954, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:147", "source": "app/Http/Controllers/DashboardController.php:147", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=147", "ajax": false, "filename": "DashboardController.php", "line": "147"}, "connection": "radhe_same", "start_percent": 68.441, "width_percent": 0.448}, {"sql": "select `invoices`.`invoice_id` as `invoice`, sum((invoice_products.price * invoice_products.quantity) - invoice_products.discount) as price, (SELECT SUM(credit_notes.amount) FROM credit_notes WHERE credit_notes.invoice = invoices.id) as credit_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as total_tax from `invoices` left join `invoice_products` on `invoice_products`.`invoice_id` = `invoices`.`id` where `issue_date` >= '2025-07-21' and `issue_date` <= '2025-07-31' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-21", "2025-07-31", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 962}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1132}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 149}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.788527, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "User.php:962", "source": "app/Models/User.php:962", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=962", "ajax": false, "filename": "User.php", "line": "962"}, "connection": "radhe_same", "start_percent": 68.889, "width_percent": 0.636}, {"sql": "select `invoices`.`invoice_id` as `invoice`, sum((invoice_payments.amount)) as pay_price from `invoices` left join `invoice_payments` on `invoice_payments`.`invoice_id` = `invoices`.`id` where `issue_date` >= '2025-07-21' and `issue_date` <= '2025-07-31' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-21", "2025-07-31", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 973}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1132}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 149}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.80085, "duration": 0.0049299999999999995, "duration_str": "4.93ms", "memory": 0, "memory_str": null, "filename": "User.php:973", "source": "app/Models/User.php:973", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=973", "ajax": false, "filename": "User.php", "line": "973"}, "connection": "radhe_same", "start_percent": 69.525, "width_percent": 1.781}, {"sql": "select `invoices`.`invoice_id` as `invoice`, sum((invoice_products.price * invoice_products.quantity) - invoice_products.discount) as price, (SELECT SUM(credit_notes.amount) FROM credit_notes WHERE credit_notes.invoice = invoices.id) as credit_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as total_tax from `invoices` left join `invoice_products` on `invoice_products`.`invoice_id` = `invoices`.`id` where `issue_date` >= '2025-07-01' and `issue_date` <= '2025-07-31' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-01", "2025-07-31", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 962}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1145}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 150}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.819268, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "User.php:962", "source": "app/Models/User.php:962", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=962", "ajax": false, "filename": "User.php", "line": "962"}, "connection": "radhe_same", "start_percent": 71.306, "width_percent": 0.882}, {"sql": "select `invoices`.`invoice_id` as `invoice`, sum((invoice_payments.amount)) as pay_price from `invoices` left join `invoice_payments` on `invoice_payments`.`invoice_id` = `invoices`.`id` where `issue_date` >= '2025-07-01' and `issue_date` <= '2025-07-31' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["2025-07-01", "2025-07-31", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 973}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1145}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 150}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.832649, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "User.php:973", "source": "app/Models/User.php:973", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=973", "ajax": false, "filename": "User.php", "line": "973"}, "connection": "radhe_same", "start_percent": 72.188, "width_percent": 0.549}, {"sql": "select `bills`.*, `venders`.`name` as `vender_name` from `bills` inner join `venders` on `bills`.`vender_id` = `venders`.`id` where `bills`.`created_by` = 79 order by `bills`.`id` desc limit 5", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 156}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.844259, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:156", "source": "app/Http/Controllers/DashboardController.php:156", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=156", "ajax": false, "filename": "DashboardController.php", "line": "156"}, "connection": "radhe_same", "start_percent": 72.737, "width_percent": 0.444}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_products.price * bill_products.quantity) - bill_products.discount) as price, (SELECT SUM(debit_notes.amount) FROM debit_notes\nWHERE debit_notes.bill = bills.id) as debit_price, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as total_tax from `bills` left join `bill_products` on `bill_products`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-07-21' and `bill_date` <= '2025-07-31' and `bills`.`created_by` = 79 and `bills`.`type` = 'Bill' group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-21", "2025-07-31", "79", "Bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1021}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1158}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 158}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.856535, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "User.php:1021", "source": "app/Models/User.php:1021", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1021", "ajax": false, "filename": "User.php", "line": "1021"}, "connection": "radhe_same", "start_percent": 73.182, "width_percent": 0.665}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_payments.amount)) as pay_price from `bills` left join `bill_payments` on `bill_payments`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-07-21' and `bill_date` <= '2025-07-31' and `bills`.`created_by` = 79 and `bills`.`type` = 'Bill' group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-21", "2025-07-31", "79", "Bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1033}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1158}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 158}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.869209, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "User.php:1033", "source": "app/Models/User.php:1033", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1033", "ajax": false, "filename": "User.php", "line": "1033"}, "connection": "radhe_same", "start_percent": 73.846, "width_percent": 0.864}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_products.price * bill_products.quantity) - bill_products.discount) as price, (SELECT SUM(debit_notes.amount) FROM debit_notes\nWHERE debit_notes.bill = bills.id) as debit_price, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as total_tax from `bills` left join `bill_products` on `bill_products`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-07-21' and `bill_date` <= '2025-07-31' and `bills`.`created_by` = 79 and `bills`.`type` = 'Expense' group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-21", "2025-07-31", "79", "Expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1083}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1159}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 158}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.882353, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "User.php:1083", "source": "app/Models/User.php:1083", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1083", "ajax": false, "filename": "User.php", "line": "1083"}, "connection": "radhe_same", "start_percent": 74.71, "width_percent": 0.708}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_payments.amount)) as pay_price from `bills` left join `bill_payments` on `bill_payments`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-07-21' and `bill_date` <= '2025-07-31' and `bills`.`created_by` = 79 and `bills`.`type` = 'Expense' group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-21", "2025-07-31", "79", "Expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1095}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1159}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 158}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8949451, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "User.php:1095", "source": "app/Models/User.php:1095", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1095", "ajax": false, "filename": "User.php", "line": "1095"}, "connection": "radhe_same", "start_percent": 75.418, "width_percent": 0.484}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_products.price * bill_products.quantity) - bill_products.discount) as price, (SELECT SUM(debit_notes.amount) FROM debit_notes\nWHERE debit_notes.bill = bills.id) as debit_price, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as total_tax from `bills` left join `bill_products` on `bill_products`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-07-01' and `bill_date` <= '2025-07-31' and `bills`.`created_by` = 79 and `bills`.`type` = 'Bill' group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-01", "2025-07-31", "79", "Bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1021}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1171}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.907387, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "User.php:1021", "source": "app/Models/User.php:1021", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1021", "ajax": false, "filename": "User.php", "line": "1021"}, "connection": "radhe_same", "start_percent": 75.902, "width_percent": 0.762}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_payments.amount)) as pay_price from `bills` left join `bill_payments` on `bill_payments`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-07-01' and `bill_date` <= '2025-07-31' and `bills`.`created_by` = 79 and `bills`.`type` = 'Bill' group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-01", "2025-07-31", "79", "Bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1033}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1171}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.919857, "duration": 0.00129000000********, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "User.php:1033", "source": "app/Models/User.php:1033", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1033", "ajax": false, "filename": "User.php", "line": "1033"}, "connection": "radhe_same", "start_percent": 76.665, "width_percent": 0.466}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_products.price * bill_products.quantity) - bill_products.discount) as price, (SELECT SUM(debit_notes.amount) FROM debit_notes\nWHERE debit_notes.bill = bills.id) as debit_price, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as total_tax from `bills` left join `bill_products` on `bill_products`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-07-01' and `bill_date` <= '2025-07-31' and `bills`.`created_by` = 79 and `bills`.`type` = 'Expense' group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-01", "2025-07-31", "79", "Expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1083}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1172}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.931282, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "User.php:1083", "source": "app/Models/User.php:1083", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1083", "ajax": false, "filename": "User.php", "line": "1083"}, "connection": "radhe_same", "start_percent": 77.131, "width_percent": 0.625}, {"sql": "select `bills`.`bill_id` as `bill`, sum((bill_payments.amount)) as pay_price from `bills` left join `bill_payments` on `bill_payments`.`bill_id` = `bills`.`id` where `bill_date` >= '2025-07-01' and `bill_date` <= '2025-07-31' and `bills`.`created_by` = 79 and `bills`.`type` = 'Expense' group by `bill`", "type": "query", "params": [], "bindings": ["2025-07-01", "2025-07-31", "79", "Expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1095}, {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 1172}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.94362, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "User.php:1095", "source": "app/Models/User.php:1095", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1095", "ajax": false, "filename": "User.php", "line": "1095"}, "connection": "radhe_same", "start_percent": 77.756, "width_percent": 0.426}, {"sql": "select * from `goals` where `created_by` = 79 and `is_display` = 1", "type": "query", "params": [], "bindings": ["79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 160}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9570398, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:160", "source": "app/Http/Controllers/DashboardController.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=160", "ajax": false, "filename": "DashboardController.php", "line": "160"}, "connection": "radhe_same", "start_percent": 78.182, "width_percent": 0.394}, {"sql": "select * from `users` where `users`.`id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 163}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.968134, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:163", "source": "app/Http/Controllers/DashboardController.php:163", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=163", "ajax": false, "filename": "DashboardController.php", "line": "163"}, "connection": "radhe_same", "start_percent": 78.576, "width_percent": 0.658}, {"sql": "select * from `plans` where `plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Plan.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 164}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9806888, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "Plan.php:67", "source": "app/Models/Plan.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPlan.php&line=67", "ajax": false, "filename": "Plan.php", "line": "67"}, "connection": "radhe_same", "start_percent": 79.234, "width_percent": 0.596}, {"sql": "select count(*) as aggregate from `customers` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 624}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/dashboard/account-dashboard.blade.php", "line": 346}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.056025, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "User.php:624", "source": "app/Models/User.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=624", "ajax": false, "filename": "User.php", "line": "624"}, "connection": "radhe_same", "start_percent": 79.83, "width_percent": 0.387}, {"sql": "select count(*) as aggregate from `venders` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 629}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/dashboard/account-dashboard.blade.php", "line": 381}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.0719008, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "User.php:629", "source": "app/Models/User.php:629", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=629", "ajax": false, "filename": "User.php", "line": "629"}, "connection": "radhe_same", "start_percent": 80.217, "width_percent": 0.394}, {"sql": "select count(*) as aggregate from `invoices` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 634}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/dashboard/account-dashboard.blade.php", "line": 416}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.082825, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "User.php:634", "source": "app/Models/User.php:634", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=634", "ajax": false, "filename": "User.php", "line": "634"}, "connection": "radhe_same", "start_percent": 80.611, "width_percent": 0.459}, {"sql": "select count(*) as aggregate from `bills` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 639}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/dashboard/account-dashboard.blade.php", "line": 451}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.094068, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "User.php:639", "source": "app/Models/User.php:639", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=639", "ajax": false, "filename": "User.php", "line": "639"}, "connection": "radhe_same", "start_percent": 81.07, "width_percent": 0.416}, {"sql": "select sum(`amount`) as aggregate from `revenues` where `created_by` = 79 and Date(date) = CURDATE() and `created_by` = 79", "type": "query", "params": [], "bindings": ["79", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 644}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/dashboard/account-dashboard.blade.php", "line": 614}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.1071682, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "User.php:644", "source": "app/Models/User.php:644", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=644", "ajax": false, "filename": "User.php", "line": "644"}, "connection": "radhe_same", "start_percent": 81.485, "width_percent": 0.672}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where (invoices.send_date) = '25-07-31' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["25-07-31", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 739}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 645}, {"index": 15, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/dashboard/account-dashboard.blade.php", "line": 614}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1173022, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "User.php:739", "source": "app/Models/User.php:739", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=739", "ajax": false, "filename": "User.php", "line": "739"}, "connection": "radhe_same", "start_percent": 82.157, "width_percent": 0.925}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 79 and `created_by` = 79 and Date(date) = CURDATE()", "type": "query", "params": [], "bindings": ["79", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 654}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/dashboard/account-dashboard.blade.php", "line": 632}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.129239, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "User.php:654", "source": "app/Models/User.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=654", "ajax": false, "filename": "User.php", "line": "654"}, "connection": "radhe_same", "start_percent": 83.082, "width_percent": 0.387}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where (bills.send_date) = '25-07-31' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["25-07-31", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 806}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 656}, {"index": 15, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/dashboard/account-dashboard.blade.php", "line": 632}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.138915, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "User.php:806", "source": "app/Models/User.php:806", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=806", "ajax": false, "filename": "User.php", "line": "806"}, "connection": "radhe_same", "start_percent": 83.469, "width_percent": 0.614}, {"sql": "select sum(`amount`) as aggregate from `revenues` where `created_by` = 79 and MONTH(date) = '07'", "type": "query", "params": [], "bindings": ["79", "07"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 666}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/dashboard/account-dashboard.blade.php", "line": 650}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.14887, "duration": 0.00104000000********, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "User.php:666", "source": "app/Models/User.php:666", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=666", "ajax": false, "filename": "User.php", "line": "666"}, "connection": "radhe_same", "start_percent": 84.083, "width_percent": 0.376}, {"sql": "select `invoice_products`.`invoice_id` as `invoice`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(price * quantity)  as sub_total, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM invoice_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, invoice_products.tax) > 0\nWHERE invoice_products.invoice_id = invoices.id) as tax_values from `invoice_products` left join `invoices` on `invoice_products`.`invoice_id` = `invoices`.`id` where MONTH(invoices.send_date) = '07' and `invoices`.`created_by` = 79 group by `invoice`", "type": "query", "params": [], "bindings": ["07", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 754}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 667}, {"index": 15, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/dashboard/account-dashboard.blade.php", "line": 650}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.161147, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "User.php:754", "source": "app/Models/User.php:754", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=754", "ajax": false, "filename": "User.php", "line": "754"}, "connection": "radhe_same", "start_percent": 84.459, "width_percent": 0.697}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 79 and MONTH(date) = '07'", "type": "query", "params": [], "bindings": ["79", "07"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 697}, {"index": 17, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/dashboard/account-dashboard.blade.php", "line": 668}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.176281, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "User.php:697", "source": "app/Models/User.php:697", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=697", "ajax": false, "filename": "User.php", "line": "697"}, "connection": "radhe_same", "start_percent": 85.156, "width_percent": 0.412}, {"sql": "select `bill_products`.`bill_id` as `bill`, SUM(quantity) as total_quantity, SUM(discount) as total_discount, SUM(bill_products.price * bill_products.quantity)  as sub_total, (SELECT SUM(bill_accounts.price) FROM bill_accounts\nWHERE bill_accounts.ref_id = bills.id) as acc_price, (SELECT SUM((price * quantity - discount) * (taxes.rate / 100)) FROM bill_products\nLEFT JOIN taxes ON FIND_IN_SET(taxes.id, bill_products.tax) > 0\nWHERE bill_products.bill_id = bills.id) as tax_values from `bill_products` left join `bills` on `bill_products`.`bill_id` = `bills`.`id` where MONTH(bills.send_date) = '07' and `bills`.`created_by` = 79 group by `bill`", "type": "query", "params": [], "bindings": ["07", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 823}, {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 698}, {"index": 15, "namespace": "view", "name": "dashboard.account-dashboard", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/dashboard/account-dashboard.blade.php", "line": 668}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.187301, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "User.php:823", "source": "app/Models/User.php:823", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=823", "ajax": false, "filename": "User.php", "line": "823"}, "connection": "radhe_same", "start_percent": 85.568, "width_percent": 0.878}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/admin.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.234358, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 86.446, "width_percent": 1.254}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/admin.blade.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.247038, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 87.7, "width_percent": 0.524}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.271416, "duration": 0.003, "duration_str": "3ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 88.224, "width_percent": 1.084}, {"sql": "select * from `email_templates` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\EmailTemplate.php", "line": 27}, {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.284548, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "EmailTemplate.php:27", "source": "app/Models/EmailTemplate.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FEmailTemplate.php&line=27", "ajax": false, "filename": "EmailTemplate.php", "line": "27"}, "connection": "radhe_same", "start_percent": 89.308, "width_percent": 0.517}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.2943199, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "partials.admin.menu:20", "source": "view::partials.admin.menu:20", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=20", "ajax": false, "filename": "menu.blade.php", "line": "20"}, "connection": "radhe_same", "start_percent": 89.825, "width_percent": 0.387}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 211}, {"index": 22, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 1296}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.362344, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "User.php:211", "source": "app/Models/User.php:211", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=211", "ajax": false, "filename": "User.php", "line": "211"}, "connection": "radhe_same", "start_percent": 90.211, "width_percent": 0.358}, {"sql": "select * from `module_integrations` where `name` = 'Automatish' and `enabled` = 1 limit 1", "type": "query", "params": [], "bindings": ["Automatish", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 1322}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.3906932, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "partials.admin.menu:1322", "source": "view::partials.admin.menu:1322", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1322", "ajax": false, "filename": "menu.blade.php", "line": "1322"}, "connection": "radhe_same", "start_percent": 90.569, "width_percent": 0.542}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4387722, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 91.111, "width_percent": 0.799}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'radhe_same' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, {"index": 14, "namespace": "view", "name": "partials.admin.header", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 6}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.4484339, "duration": 0.018269999999999998, "duration_str": "18.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:527", "source": "app/Models/Utility.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=527", "ajax": false, "filename": "Utility.php", "line": "527"}, "connection": "radhe_same", "start_percent": 91.91, "width_percent": 6.602}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, {"index": 18, "namespace": "view", "name": "partials.admin.header", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 6}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.477448, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:533", "source": "app/Models/Utility.php:533", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=533", "ajax": false, "filename": "Utility.php", "line": "533"}, "connection": "radhe_same", "start_percent": 98.511, "width_percent": 0.314}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 16}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.488837, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 98.826, "width_percent": 0.499}, {"sql": "select count(*) as aggregate from `ch_messages` where `to_id` = 79 and `seen` = 0", "type": "query", "params": [], "bindings": ["79", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.497504, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "partials.admin.header:20", "source": "view::partials.admin.header:20", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=20", "ajax": false, "filename": "header.blade.php", "line": "20"}, "connection": "radhe_same", "start_percent": 99.324, "width_percent": 0.257}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 5748}, {"index": 15, "namespace": "view", "name": "partials.admin.footer", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/partials/admin/footer.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5156412, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 99.581, "width_percent": 0.419}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 1598, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 1180, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Revenue": {"value": 27, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FRevenue.php&line=1", "ajax": false, "filename": "Revenue.php", "line": "?"}}, "App\\Models\\Payment": {"value": 27, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}}, "count": 2838, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 20, "messages": [{"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 79,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.698874, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.306157, "xdebug_link": null}, {"message": "[ability => show crm dashboard, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2083107296 data-indent-pad=\"  \"><span class=sf-dump-note>show crm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show crm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2083107296\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.308939, "xdebug_link": null}, {"message": "[ability => manage lead, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-757742198 data-indent-pad=\"  \"><span class=sf-dump-note>manage lead</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage lead</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757742198\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.316784, "xdebug_link": null}, {"message": "[ability => manage project task, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1591144550 data-indent-pad=\"  \"><span class=sf-dump-note>manage project task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage project task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1591144550\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.325033, "xdebug_link": null}, {"message": "[ability => manage booking, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1890239099 data-indent-pad=\"  \"><span class=sf-dump-note>manage booking</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage booking</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1890239099\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.336865, "xdebug_link": null}, {"message": "[ability => manage project, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-185448325 data-indent-pad=\"  \"><span class=sf-dump-note>manage project</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage project</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-185448325\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.343222, "xdebug_link": null}, {"message": "[ability => manage personal task, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1256957575 data-indent-pad=\"  \"><span class=sf-dump-note>manage personal task</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">manage personal task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1256957575\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.355164, "xdebug_link": null}, {"message": "[ability => access omx flow, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-440025943 data-indent-pad=\"  \"><span class=sf-dump-note>access omx flow</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">access omx flow</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-440025943\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.379747, "xdebug_link": null}, {"message": "[ability => access automatish, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1507564339 data-indent-pad=\"  \"><span class=sf-dump-note>access automatish</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">access automatish</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1507564339\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.388409, "xdebug_link": null}, {"message": "[ability => manage client, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-841080263 data-indent-pad=\"  \"><span class=sf-dump-note>manage client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-841080263\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.402806, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1088743817 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1088743817\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.404097, "xdebug_link": null}, {"message": "[ability => manage employee, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1927572034 data-indent-pad=\"  \"><span class=sf-dump-note>manage employee</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927572034\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.407854, "xdebug_link": null}, {"message": "[ability => manage client, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2122297503 data-indent-pad=\"  \"><span class=sf-dump-note>manage client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2122297503\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.410688, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 79,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1732010719 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1732010719\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.412609, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 79,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1532425943 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532425943\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.414428, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1349214991 data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349214991\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.42199, "xdebug_link": null}, {"message": "[\n  ability => manage company settings,\n  result => true,\n  user => 79,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1420138219 data-indent-pad=\"  \"><span class=sf-dump-note>manage company settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage company settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1420138219\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.424072, "xdebug_link": null}, {"message": "[ability => manage pricing plan, result => null, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage pricing plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage pricing plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.430992, "xdebug_link": null}, {"message": "[ability => manage order, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.433094, "xdebug_link": null}]}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjVBVVg1VGZnbEw0QzhiRjV6VjFOS2c9PSIsInZhbHVlIjoiNWU1UFcra0laNlVGbVlwU1FVS1ExMHQwWlpRUkhINVQwYUZJaW84ZWVYazJzdG12OUpOeGFNZ2g2eGF0bHZVb2x1YlJrb2J2OVUyN3VIdnFDc1lOQ3cxVlE2bDJnWVEwOTZxRC8rbU5WQk9NY1VCMjNWWHVHZk1NUS9Db2tFdlBHNGhvTFR4Q1VGL3krVHhuNm5idUNHdWRLUjZxTFhLWWdWYnBLVGRKNnZJdjM0Rjl6Mmc1Q3l6ZVJvQnVnTi9ZcDhGbmVtdzR3MGlMeG91Tmo2enlKVWtTQXVxSEYwdTlHd2V2c09QaXNlRzhwVDVDMmI3cjhqYjN6T09jeEtvVEx6dGplS045KzR1S2lGRFhNbll1cDZ4NmV2SzF5bit1REtqQzRtL3BxLzErMHZXVXlqU2NGbG9RRklaRWdJNElWTU8xREx6aTY0Y2NPVDVWYTBkbHNDZi9XTE9jM0dMZEtubVRHaDJsa1VWNlFYcUpubmlld0d0a1c3VmxJRXArVXVwZ0swSUcwS2NUd2tEZUo2UXpSQXVkU0VpbFU4Rm8vYmVMc213VFlucTMraHdDZTZmQTJObUwzblE4eUtIbmxJbXFVY3QxMVRjemlWLzFIQnNzRCtSYlpYWFFpNW9SMTkvR3NGaUVTWnhndDc1UlB6a0FYTTJKZ1RKN2xiSlYiLCJtYWMiOiJhODZmNWI3MGMyODlhN2Q4MDgwYjQyYmRiYjI4ZTQ3YzY2ZWU0OGQwNzAyZGU1OTUzNmRhOGIxNDc5MzA2MDQ4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImwrOGpMSkg4VUR6MnVTdEVKVkVRQWc9PSIsInZhbHVlIjoiMUFrWE10WWZTQ0NTcEIwUjBEejY4MWN0ZWhVMkNsNUNRNGRHTmZNU2swREtIZEplRS9HTTM1c2toSEtUeDFRN2NJNmhUaHhTcVJsdCtNV3pjRDh0TjFsbDlmMXA1SVl1MEpBNlJ0WFJadXZrdFE1b04zSks5eW44VFNyVk1GVnU1Y2dQODVWek5Ob21yREJ0L2xEcG1Cb1k0RkYvNU8yUHdiWllva056Ynk4SnpxT1FzUk5ueldyeVJjQ1A1ZG9GOXNQOGpQb29LZXN1MGZJQVlIcFRIdERTV0VVZTl1Mi9tZnBLRjVKSXdIazAxMUtkdFZKdDNta1pTZXFhUjBjWm1HVEZKUUVFTkRZSVpzZ0dXQUk3MUdWYzhrZ2RxNTk5alZ4NFZzS3J2bWVJY0pKVGh2RXV0dVE0RGppaHBoaTA5YytNS2FlOE9NV0krWS9pLy9HMXU3R2g0dm52a0ZmaXZXWThaVFhLYkViWWlsUFlYR0JBL1p5Z0g1Ykxmb2M4ODdEQlhBZzBOUXV2cGpoNUJjSGtGdHdiZlJxenM2czlJb2VOWEY5dVowQ2xuVWpwekdoU0JoTGxhNjFKLzBJRllUcEF4Mkxja1pCTjIxYjRsYWVFSXVVUHlJUzVzRzNqUUpFRDhzZ1JObFl0M3ZodnluQjYxOCtsajAvZTlISloiLCJtYWMiOiJlNzE3YzJkNzJhMTMyZjRiNWNhNzRlM2UyY2Q2Yzk4ZTgyZGRmMGE1YjRlY2JmODQxZmY0M2EyNDYzY2M5Yjg5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-321660402 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5LJND5bUclZnLzzClCMEaxKzZ5Yt8AAmGjprz0I</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-321660402\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-290119821 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:50:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJ5QitnaXRHcTBRK0oyaGN2WG9DQ3c9PSIsInZhbHVlIjoiTHgzQzlhekVKUkhFWHoyVE9xK1FRMVhvWUpEdzF3clNCVzEzbCt1YWlGWCtxTE5jNjRzcCtRSjNna1BoMWpyOEhoT3loVFZuSEI2SGR2OGM5Y0t6dCt4c29qNHVDK05CeXRNTnBPU09ZUVVpa2VMVXdIemVHK040d2p0aXpkT0NWTjVIY2J2TkM1ZUR0WXdNTVRNU05SOGphYkthSmp4Z1h5VithMXV6VTFpRHNQWjg5OUJKNm0wZDFwOUlaenYwUHFCeVdFT0MyVWpjVEF1MmpuTHd4bjIzWXV3dnVjblZVbU9tZWI2ZDlPMm1PWDRHZ3dzbzU2TStqdW9WUmphM0tFeGJVU21JZno1TEhLYXBBV0tDU3VYeTBjNGcxdExacUhqYWdEMGdaME5wZHM2bmJaM1dlK1U3V2ljOUY0cEdsNno5RHJ3VXF5LzlIenh4L28zMkoyUm16U3plSUVLM3RJQWZoQTRGWnJ6RFhyMUxJb1ZObVNqallYMVpVS3hZVU93RlVzbWNkS0hidjczUnhtN2ZzdmUvbndVdm9GbDlhQkVXR0YvQWdUbjRZRGI0UkRjeEZMalBsMVZqNDVhYTE4bDAxSGdSYUFtQ3BvQkg3cVlJNTJBS2xrTk0vb3JjVkpmVGVzRll5MEZsTHhaNTJoVEptelFDSHdwUkppY1AiLCJtYWMiOiJjYzVjODQ0ZmRmZWNkMDdmZDk3Mjc3YWZiZjNlOWZjZWJhMDExMjAzZmJlNDM3ZTExY2UyZDVmNGNhZTNlYTFhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:50:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjNWdW91SmhiWlFsdENmakhLR3owOWc9PSIsInZhbHVlIjoiMzNnRUVaTnZJdUZXYVRZL3QrbnFQb3ExKzQ3VEMzZ2Q2cGZBdVBiYngyM3MwY3lOUkN0ZzhkMUZYZWI0czVyams4L28xWXcySkVHcXl1NUxKV29TM0x4Nk5TRzgwK1ltbENhV3pBMU5QUGNLdmNsaVhOVHhNTFlwVWVHN3E4WGUyTnhzaDlkNjFIeXhwT2hET1o1UDhEUHJCMy9ObklrYUxQck54aVdnRlBOZVBWK3FBeTY0dXhtcTNQYjFKQ0wvKzR0OGh0MzdGd3Z3aXBKNUpNK1Ara0VYc1QrMzdFZmo3MWh5U0taMlAyYUxIZXUwaUVmNHJqdlBVOGluTzlSc1RWM0ZRR3YwcCtXMHdQbG5wRlduNlk5aG8vSEROVExPajJEcWVsUHpQVWY5T25sZFlzS3J4UDlHNEQ5Z0ZMUWpmdnZlWUlFT3ZBMjd3R0hkdlRoRWVGcHhlSm1ZVEQ1bFYzUFNyV0cvc3dIM2gzMDJyV1FZd0wvUlNMcmViQUxtNkZOMldXZC90WkpXTjZLZU0vaGFkeStMcDB3azdMRlk0NnBFbDVoOU94MWZBcUV4bVh5UUczRzM1U1ZIRlN3N25rL0tjczlaR2FiczRnMkdzazZydjlOMWtnQ1ViUnVXUm0rUmlYS0orVUFSYlVQTXA1SDl5SkwwODVlWTlmTzQiLCJtYWMiOiIyMzdmNDQ0ZDY0MDNmMmU4NTQ2ZGIyOWE5ZDc2ZTUyMGU5OGFjZjYwNzQ2YjE1NzU0ZDQ3YjA2NzNjNzMxZDFhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:50:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJ5QitnaXRHcTBRK0oyaGN2WG9DQ3c9PSIsInZhbHVlIjoiTHgzQzlhekVKUkhFWHoyVE9xK1FRMVhvWUpEdzF3clNCVzEzbCt1YWlGWCtxTE5jNjRzcCtRSjNna1BoMWpyOEhoT3loVFZuSEI2SGR2OGM5Y0t6dCt4c29qNHVDK05CeXRNTnBPU09ZUVVpa2VMVXdIemVHK040d2p0aXpkT0NWTjVIY2J2TkM1ZUR0WXdNTVRNU05SOGphYkthSmp4Z1h5VithMXV6VTFpRHNQWjg5OUJKNm0wZDFwOUlaenYwUHFCeVdFT0MyVWpjVEF1MmpuTHd4bjIzWXV3dnVjblZVbU9tZWI2ZDlPMm1PWDRHZ3dzbzU2TStqdW9WUmphM0tFeGJVU21JZno1TEhLYXBBV0tDU3VYeTBjNGcxdExacUhqYWdEMGdaME5wZHM2bmJaM1dlK1U3V2ljOUY0cEdsNno5RHJ3VXF5LzlIenh4L28zMkoyUm16U3plSUVLM3RJQWZoQTRGWnJ6RFhyMUxJb1ZObVNqallYMVpVS3hZVU93RlVzbWNkS0hidjczUnhtN2ZzdmUvbndVdm9GbDlhQkVXR0YvQWdUbjRZRGI0UkRjeEZMalBsMVZqNDVhYTE4bDAxSGdSYUFtQ3BvQkg3cVlJNTJBS2xrTk0vb3JjVkpmVGVzRll5MEZsTHhaNTJoVEptelFDSHdwUkppY1AiLCJtYWMiOiJjYzVjODQ0ZmRmZWNkMDdmZDk3Mjc3YWZiZjNlOWZjZWJhMDExMjAzZmJlNDM3ZTExY2UyZDVmNGNhZTNlYTFhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:50:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjNWdW91SmhiWlFsdENmakhLR3owOWc9PSIsInZhbHVlIjoiMzNnRUVaTnZJdUZXYVRZL3QrbnFQb3ExKzQ3VEMzZ2Q2cGZBdVBiYngyM3MwY3lOUkN0ZzhkMUZYZWI0czVyams4L28xWXcySkVHcXl1NUxKV29TM0x4Nk5TRzgwK1ltbENhV3pBMU5QUGNLdmNsaVhOVHhNTFlwVWVHN3E4WGUyTnhzaDlkNjFIeXhwT2hET1o1UDhEUHJCMy9ObklrYUxQck54aVdnRlBOZVBWK3FBeTY0dXhtcTNQYjFKQ0wvKzR0OGh0MzdGd3Z3aXBKNUpNK1Ara0VYc1QrMzdFZmo3MWh5U0taMlAyYUxIZXUwaUVmNHJqdlBVOGluTzlSc1RWM0ZRR3YwcCtXMHdQbG5wRlduNlk5aG8vSEROVExPajJEcWVsUHpQVWY5T25sZFlzS3J4UDlHNEQ5Z0ZMUWpmdnZlWUlFT3ZBMjd3R0hkdlRoRWVGcHhlSm1ZVEQ1bFYzUFNyV0cvc3dIM2gzMDJyV1FZd0wvUlNMcmViQUxtNkZOMldXZC90WkpXTjZLZU0vaGFkeStMcDB3azdMRlk0NnBFbDVoOU94MWZBcUV4bVh5UUczRzM1U1ZIRlN3N25rL0tjczlaR2FiczRnMkdzazZydjlOMWtnQ1ViUnVXUm0rUmlYS0orVUFSYlVQTXA1SDl5SkwwODVlWTlmTzQiLCJtYWMiOiIyMzdmNDQ0ZDY0MDNmMmU4NTQ2ZGIyOWE5ZDc2ZTUyMGU5OGFjZjYwNzQ2YjE1NzU0ZDQ3YjA2NzNjNzMxZDFhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:50:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-290119821\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}