@php
    // Get stages for the current user/creator
    $stages = \App\Models\TaskStage::where('created_by', \Auth::user()->creatorId())->orderBy('order')->get();

    // If no stages exist, create default ones
    if ($stages->count() == 0) {
        $defaultStages = ['To Do', 'In Progress', 'Review', 'Done'];
        foreach ($defaultStages as $key => $stageName) {
            \App\Models\TaskStage::create([
                'name' => $stageName,
                'order' => $key,
                'created_by' => \Auth::user()->creatorId(),
            ]);
        }
        // Re-fetch stages after creating defaults
        $stages = \App\Models\TaskStage::where('created_by', \Auth::user()->creatorId())->orderBy('order')->get();
    }

    // Get all tasks for this project
    $allTasks = \App\Models\Project::projectTask($project->id);

    // Add tasks to each stage
    foreach ($stages as $stage) {
        $stage->tasks = $allTasks->where('stage_id', $stage->id);
    }
@endphp

@if($stages->count() > 0)
    <div class="row g-3 kanban-wrapper" data-containers='{{ json_encode($stages->pluck('id')->map(function($id) { return 'task-list-' . $id; })->toArray()) }}' data-plugin="dragula">
        @foreach($stages as $stage)
            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12">
                <div class="kanban-column">
                    <div class="kanban-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">{{ $stage->name }}</h6>
                            <div class="d-flex align-items-center gap-2">
                                <span class="badge bg-light text-dark">{{ $stage->tasks->count() }}</span>
                                @can('create project task')
                                    <a href="#" data-size="lg"
                                       data-url="{{ route('projects.tasks.create', [$project->id, $stage->id]) }}"
                                       data-ajax-popup="true" data-bs-toggle="tooltip"
                                       title="{{ __('Add Task to') }} {{ $stage->name }}"
                                       class="btn btn-sm btn-light-primary">
                                        <i class="ti ti-plus"></i>
                                    </a>
                                @endcan
                            </div>
                        </div>
                    </div>
                    <div class="kanban-tasks task-list-{{ $stage->id }}" data-stage-id="{{ $stage->id }}">
                        @foreach($stage->tasks as $task)
                            <div class="kanban-task-card task-card-draggable"
                                 data-task-id="{{ $task->id }}"
                                 data-stage-id="{{ $task->stage_id }}"
                                 data-project-id="{{ $project->id }}">

                                <!-- Task Header -->
                                <div class="task-header">
                                    <h6 class="task-title">
                                        <a href="#" data-url="{{ route('projects.tasks.show', [$project->id, $task->id]) }}"
                                           data-ajax-popup="true" data-size="lg"
                                           data-bs-original-title="{{ $task->name }}">{{ $task->name }}</a>
                                    </h6>
                                    <span class="task-priority-badge priority-{{ $task->priority }}">
                                        {{ $task->priority }}
                                    </span>
                                </div>

                                <!-- Task Description -->
                                @if($task->description)
                                    <p class="task-description">{{ Str::limit($task->description, 80) }}</p>
                                @endif

                                <!-- Task Footer -->
                                <div class="task-footer">
                                    <div class="task-assignees">
                                        @if($task->assign_to)
                                            @foreach(explode(',', $task->assign_to) as $userId)
                                                @php $user = \App\Models\User::find($userId); @endphp
                                                @if($user)
                                                    <div class="task-avatar" title="{{ $user->name }}">
                                                        {{ substr($user->name, 0, 1) }}
                                                    </div>
                                                @endif
                                            @endforeach
                                        @endif
                                    </div>
                                    <div class="task-date">
                                        {{ \Carbon\Carbon::parse($task->end_date)->format('M d') }}
                                    </div>
                                </div>

                                <!-- Task Stats -->
                                @php
                                    $taskComments = $task->comments ?? collect();
                                    $taskFiles = $task->taskFiles ?? collect();
                                @endphp
                                @if($taskComments->count() > 0 || $taskFiles->count() > 0)
                                    <div class="task-stats">
                                        @if($taskComments->count() > 0)
                                            <div class="task-stat">
                                                <i class="ti ti-message-circle"></i>
                                                <span>{{ $taskComments->count() }}</span>
                                            </div>
                                        @endif
                                        @if($taskFiles->count() > 0)
                                            <div class="task-stat">
                                                <i class="ti ti-paperclip"></i>
                                                <span>{{ $taskFiles->count() }}</span>
                                            </div>
                                        @endif
                                    </div>
                                @endif


                            </div>
                        @endforeach

                        @if($stage->tasks->count() == 0)
                            <div class="empty-stage">
                                <div class="empty-stage-icon">
                                    <i class="ti ti-plus"></i>
                                </div>
                                <p class="empty-stage-text">{{ __('Drop tasks here') }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endforeach
    </div>
@else
    <div class="text-center py-5">
        <i class="ti ti-layout-kanban" style="font-size: 3rem; color: #ccc;"></i>
        <h5 class="mt-3 text-muted">{{ __('No Task Stages Found') }}</h5>
        <p class="text-muted">{{ __('Task stages should have been created automatically.') }}</p>
        <p class="text-muted small">
            {{ __('Debug Info:') }}<br>
            {{ __('Stages found: ') . $stages->count() }}<br>
            {{ __('Tasks found: ') . $allTasks->count() }}<br>
            {{ __('Creator ID: ') . \Auth::user()->creatorId() }}
        </p>
    </div>
@endif
