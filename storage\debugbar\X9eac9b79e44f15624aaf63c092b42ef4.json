{"__meta": {"id": "X9eac9b79e44f15624aaf63c092b42ef4", "datetime": "2025-07-31 06:24:54", "utime": **********.328366, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:24:54] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.314412, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943092.522826, "end": **********.328409, "duration": 1.8055830001831055, "duration_str": "1.81s", "measures": [{"label": "Booting", "start": 1753943092.522826, "relative_start": 0, "end": **********.113088, "relative_end": **********.113088, "duration": 1.5902619361877441, "duration_str": "1.59s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.113117, "relative_start": 1.5902910232543945, "end": **********.328413, "relative_end": 4.0531158447265625e-06, "duration": 0.21529603004455566, "duration_str": "215ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50618600, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.281802, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0279, "accumulated_duration_str": "27.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1921082, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 12.366}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.2134879, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 12.366, "width_percent": 2.76}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2208462, "duration": 0.02206, "duration_str": "22.06ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 15.125, "width_percent": 79.068}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.249805, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 94.194, "width_percent": 5.806}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-936090124 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-936090124\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1226356975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1226356975\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-582396907 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582396907\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-113000883 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlZVejNXNW9MWXVpZHdDS1oxWDRBUGc9PSIsInZhbHVlIjoiZHBzdDlLOUI2RFR4a0FYL1dyeWhpeWVzRm1LQVBsY2lFMzV1aStvZ1djK095ekFOR2Y5c2tYM3FhVjAzTjBNZ2twZ09MNUJlRWNtaUs5NGxlekgwZ2djNzlnNDF4QzRGc20xamFEU2pwdmNTRVNBZ0ozRUVZWFhOUXFTRFRtcXJJZ09NY0VkczhkMTF5WEtTTVpZVHNZUVk0ZTE5aTRsZUNRUXBEWFRKL1c0Uk0rN1RsUFJwQTd5TXorL2VRYWV2OVFkL3YyOFltR25hbklkcDNsM2s1Wk1aRVRDVzVpbzdCKzNpTlFsSDFGREZMbE5XbzcxdmdkNytlWkJKM3pMNWdPZzY3VFVTdDRUMXVnODBvVnBwT01QRCtxV1hWU25BdDhRVlJCUFJJMld5RElDb2JVZW9Tc3Y3UUp5Q1FsL3Judlp4cnlJWFRlWXJuU3g4TW5reHZjSW9aU0dVWUR0a3N2TnFaVVlqYTRsTmszbzFFTWVSVmVNWkR3d1AzYkdDTGdFNU95REt4c01BR1hHV0grOU1CeTRDTzdjSjNOanZ0clNkV29HOWIyQ3JXUk82U3pSNm4xS2Zrcks4NGRnRkd4M3FaUDNQdVNwdkdSUjdiV2JHbXI4Sndta2hGRVBjUDd1M3RzMDc0L001UXhFd0dWTG93Skp4cjNYdm93ZGkiLCJtYWMiOiIyOTE5MGI4YjgwZmVmYTU1NjA0YzZjOTM4OWM0MzNmMGFlOTRhNGIxMzEzMDAwNTdlYWMzYTU0YzY3Mzg0YmMyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ikg3K3V1bEJBZUN3enlXQllCeUMxY3c9PSIsInZhbHVlIjoiR09RdlBQOU0zai8ralFwbkF1TnFnNWdvSllYOFBsMHF2NFA0c1hwYVBmczdkbG1XOTNOcVZlcDNIM0MveUZpSkxjSHYrWUI5UVpWV2VoSDdFY2FXbE43SE1HcjRHRE5FWE96TmFVWk44ekRMWHZ0ZlkrdGJTd2VSN3FZbFloS2E0elY1N0RlWnI2TGV1cFNsNVBQU2VzNlVRTWhUK2tHN2NhOXFwaDlrRkhRRmpYeVNFRXBJUnh0M1M4S05wZ3lyamNsUlFHeEtwelhoZ2wxdGxKbFVXcTZ3dXIxYWV3UkJTRDBYdGNPaHQ1ZTJMck1PZnBMY0JOYTdxWlVWTVd5VWtrUnM3aGx2UXVERVdjQk9kaCtpUkMwMDliVFk0bHZuMEpEbERiSHcyTVhzNWRIem9FTkF2RURqcUZESHE4SDNJajM4MCszSk1sam9jdC8rWXRRNDlLemF3cWo2OXNuZHU3ak9HQlZMMUJFNTdjZjFWSWdwaGhodWdOMnFROHVQeEt5K3ZLNnNOc29yUVI1Q0hJNGxHMmRvVzJoeXQveUtnc3JvdVZ5bEZlZWwvR0ZWU25tbnNsQUNHV1pBUUJvWFp5SzdRWFZGZWttT1p2QVY3a29JdzJqcGxncmV4OE8yYU1kUDRzMU1hRktxSjRONjl0V0FBTlpZWFI2ejM1dDQiLCJtYWMiOiI0N2U0NjM0OWE3M2M2ZDA4NTk1OGYyZmM2ZmQ2N2I5YWRiYjAwNjM1NDEwNmY3ZjVlMzBmY2NjZWExZjdlYTU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113000883\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1477596216 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1477596216\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-88362689 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:24:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZuSW1DNzIyOHd2aWZkdnNtVDJ3N1E9PSIsInZhbHVlIjoicXRLRTdYMkVEcUUrdk1UNUJFVSswMzFrTUl3em5RSkdKMEpNNXBqaGVMTnZObDR1d1dDUVRxRkJDSnZzRURxQVJ0RU5JbnpWSEs0b0o1aHlKUUxrb0VGbmdOQlZYMjY0SVZYSUMrbEcyK0RGVVF3U21sdzNBODlxQmZyOHR2OHE1T1BKOU9rVVEyVzM3dFBER0d5OGJGeHBkcmUrdGxraHMzWEJxZXUrcWIwR010TXl1RzVvV2R1VDZqbzQ3SHlTWDdXRThZanR5YlFjU2VHTytsOG1LMFdNcUdkZHpRVENJeHFwMW51aXhnM3QweEtYTW9NLzZpV1FxM2h6Q0hPUXNiL2hnZTgrZEd6S1NqbDFPcWFhMXg5K2N0Vzh4WWxuRWNIemVqbUZybUhIbmdhMXprNmI5a0RwT0l1M3RMMVJTOXFtZWsvNG9YeERCRUVmUE96NDZFUnJWamZneUlPMFlodDhpNmNTY010UlE5bmZSak5pVy9ncFhDVkYxMkNRbis5enZlK3lLSDRKWmtWdnplRmg5T3lDcmZmcmR1SGpSbGNHaDJRK1RjcnErTE1INnpGVkJIR2FBQXNFWHBwVHhVQkFKcm5Ndk1xWWVkWFFmbTNmOWF3eVdXaDlxdVhmdUlROXh5dmRjcmcyWWRtdzdMdTJjdXZraHQwS3p0ME4iLCJtYWMiOiJlNDBhYTE4MmZkMGMzNjNhNWQ0YjcyNjA2ZGQxYmQxMzM5MjYxMTI5ZjhlNmQxMTdkM2ZlYmY5NTUyOTJhNGQ5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:24:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InIxNGFzZUh0Skpwa3VQMG5VTDJ2aUE9PSIsInZhbHVlIjoiM3B2dnE5SGRZVW0zVUJ4VDNKQWxUY0lHb1RONVpET2ZHakxUVUtLcTZpRURtenUxeFJBdWlIMUpaSUVSRWMzVEJTdktoYjVEVU41a0NFeXFiaWRGYURnZm9FZ2VnelpTWWVRRktQYTZtTkFpMXlVVjZHaEVMN3ZqNVJla25pdzdudHdiQ29vZVFUMkdQMmwySUg0a2prSER2bGJBdktwc1hVdjhGejJjRFZEN0FVb0tzRnorY24vNmM0MVE3TzdPQ21CMHBpSVkxQ3RkL1B4azVjOHdwOHNHRlQ4YlJKVm1qWlRmUktuZWpNRkM2aURRWEMrUnBBeGF6UG5xWXlob002a3dZcThpcTM2Yk9FUjRYSmJJZGRtNmV6Qkc3dGxQam5Gc2g2N1lXYmtoQ25UWVMrbUU1VnBNNWxYVUN6UFBkcWNpd2k1WVcvYnNUd0VsNERsemkzWENJL2NhWU9BUzFkejY0SndLTFlIZVRxMUVUVkFFZGN3UzhGNmRPNmsxaFdJQk5JS05ISVorUEgzcE5CYnkycXJNbjhtRGV6b3dYaUlGY2tOVXROaGIzS2oxdmlvOUgwWnorcGlVYm0veXkrelB4T3JZSDRXNXNsTGFFYm9NWlJDbUdGVmRKUm5iY0FMTFFDSjU4a3l3ZW9zUm5MY2JhMTFhMDhESnB3SUIiLCJtYWMiOiJjNGNiYmUzOWE4YmM2YzdlYmE5YjFjMmExMjBlMjFhNjhlYjJiNWQxZjE0ZGJiNDM4ZGExNmU0YTE3MGU2YTZkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:24:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZuSW1DNzIyOHd2aWZkdnNtVDJ3N1E9PSIsInZhbHVlIjoicXRLRTdYMkVEcUUrdk1UNUJFVSswMzFrTUl3em5RSkdKMEpNNXBqaGVMTnZObDR1d1dDUVRxRkJDSnZzRURxQVJ0RU5JbnpWSEs0b0o1aHlKUUxrb0VGbmdOQlZYMjY0SVZYSUMrbEcyK0RGVVF3U21sdzNBODlxQmZyOHR2OHE1T1BKOU9rVVEyVzM3dFBER0d5OGJGeHBkcmUrdGxraHMzWEJxZXUrcWIwR010TXl1RzVvV2R1VDZqbzQ3SHlTWDdXRThZanR5YlFjU2VHTytsOG1LMFdNcUdkZHpRVENJeHFwMW51aXhnM3QweEtYTW9NLzZpV1FxM2h6Q0hPUXNiL2hnZTgrZEd6S1NqbDFPcWFhMXg5K2N0Vzh4WWxuRWNIemVqbUZybUhIbmdhMXprNmI5a0RwT0l1M3RMMVJTOXFtZWsvNG9YeERCRUVmUE96NDZFUnJWamZneUlPMFlodDhpNmNTY010UlE5bmZSak5pVy9ncFhDVkYxMkNRbis5enZlK3lLSDRKWmtWdnplRmg5T3lDcmZmcmR1SGpSbGNHaDJRK1RjcnErTE1INnpGVkJIR2FBQXNFWHBwVHhVQkFKcm5Ndk1xWWVkWFFmbTNmOWF3eVdXaDlxdVhmdUlROXh5dmRjcmcyWWRtdzdMdTJjdXZraHQwS3p0ME4iLCJtYWMiOiJlNDBhYTE4MmZkMGMzNjNhNWQ0YjcyNjA2ZGQxYmQxMzM5MjYxMTI5ZjhlNmQxMTdkM2ZlYmY5NTUyOTJhNGQ5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:24:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InIxNGFzZUh0Skpwa3VQMG5VTDJ2aUE9PSIsInZhbHVlIjoiM3B2dnE5SGRZVW0zVUJ4VDNKQWxUY0lHb1RONVpET2ZHakxUVUtLcTZpRURtenUxeFJBdWlIMUpaSUVSRWMzVEJTdktoYjVEVU41a0NFeXFiaWRGYURnZm9FZ2VnelpTWWVRRktQYTZtTkFpMXlVVjZHaEVMN3ZqNVJla25pdzdudHdiQ29vZVFUMkdQMmwySUg0a2prSER2bGJBdktwc1hVdjhGejJjRFZEN0FVb0tzRnorY24vNmM0MVE3TzdPQ21CMHBpSVkxQ3RkL1B4azVjOHdwOHNHRlQ4YlJKVm1qWlRmUktuZWpNRkM2aURRWEMrUnBBeGF6UG5xWXlob002a3dZcThpcTM2Yk9FUjRYSmJJZGRtNmV6Qkc3dGxQam5Gc2g2N1lXYmtoQ25UWVMrbUU1VnBNNWxYVUN6UFBkcWNpd2k1WVcvYnNUd0VsNERsemkzWENJL2NhWU9BUzFkejY0SndLTFlIZVRxMUVUVkFFZGN3UzhGNmRPNmsxaFdJQk5JS05ISVorUEgzcE5CYnkycXJNbjhtRGV6b3dYaUlGY2tOVXROaGIzS2oxdmlvOUgwWnorcGlVYm0veXkrelB4T3JZSDRXNXNsTGFFYm9NWlJDbUdGVmRKUm5iY0FMTFFDSjU4a3l3ZW9zUm5MY2JhMTFhMDhESnB3SUIiLCJtYWMiOiJjNGNiYmUzOWE4YmM2YzdlYmE5YjFjMmExMjBlMjFhNjhlYjJiNWQxZjE0ZGJiNDM4ZGExNmU0YTE3MGU2YTZkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:24:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-88362689\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-616240856 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-616240856\", {\"maxDepth\":0})</script>\n"}}