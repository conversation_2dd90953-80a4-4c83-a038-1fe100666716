{"__meta": {"id": "X9881cdac9c1bd512e89df01ed4b874c1", "datetime": "2025-07-31 07:48:30", "utime": **********.749917, "method": "GET", "uri": "/storage/expense_receipts/1753948061_Class%205%20math%20question.pdf", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.046488, "end": **********.749936, "duration": 0.7034480571746826, "duration_str": "703ms", "measures": [{"label": "Booting", "start": **********.046488, "relative_start": 0, "end": **********.663281, "relative_end": **********.663281, "duration": 0.6167929172515869, "duration_str": "617ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.663298, "relative_start": 0.****************, "end": **********.749939, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "86.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3043\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1871 to 1877\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1871\" onclick=\"\">routes/web.php:1871-1877</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.0067800000000000004, "accumulated_duration_str": "6.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.720484, "duration": 0.0067800000000000004, "duration_str": "6.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/expense_receipts/1753948061_Class%205%20math%20question.pdf\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/expense_receipts/1753948061_Class%205%20math%20question.pdf", "status_code": "<pre class=sf-dump id=sf-dump-1015742159 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1015742159\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/pdf", "request_query": "<pre class=sf-dump id=sf-dump-1481766779 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1481766779\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1208060074 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1208060074\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-620251593 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">embed</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlRLVXZySTZDYUFLcnBiZkFJaUp2Y2c9PSIsInZhbHVlIjoiUzEyMHZ4YSt2bFlIKzZ4MXBnT2FQUmVGc0RsbEtTazRaYjFaQWxSTHRQenl5TzVyTW5wMlRibmlYYXRvaVlWOVFXNzY0dmM1dWVJZEo1L29DVzJ3WTZ3YUZRSE5NOC95TXlXN3BVc1hZZmRBNzdpRGtwRUpDdjArcmtWSjYxMnMweVhLTVNyZnRsS3RZWWxnMXU4bVMvZmovdFRXRW1nVkVyemxSMWNkUUdVem1Ea0RBUEJ0cTFpY2x4UHZyUkxsSEVFVjEvUjdLdldTSVlwcENwa2l4YUVnRUhoK3paTnQ2MHhodEYwS3oxTlFPV0Y4S1NQeU1ITmNwcG5PQnlpRWdBT3hydkcvZUZMa3dzMmJQN0RYQUpwRDBjU2p4SXBrY2plc0pscWYzSVNoT3kvVzl3TlNkaTN4a3BHS0Eydmo2aTFwa3RiWTNlN1Z2YjFTLzFkaXJLd2szTVR6UVVtODRYODNRUC9NV0dQMGhWdWxYTjUwWnRxWWkvanF6MHZtb2JzcSsxMW9NOEVZU1RKYjNQNkcrYi8vc3pRSHFGem9Gb25wUzl0aUNzOXlmRWpaU2RPcm9BWlZJUzZPSTRsVWdCTnVZSmF1TytXeWNXYjFFa0VFUHQxNkJsNHR3bVNvZ3UxUlA5c2ZkbjlZc3U3QWdlYnhiOGIwNHptSGJsTHgiLCJtYWMiOiI3MTBkNDQ1ODcyMTM3NTVjOWFkOGJlNjJiMmFhNzg0MDZiYjAxY2ZiYzNhODliZGRiYjgxZTcxYWVmZDQ1ZWNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InVIdEFyS0lDWGdHS0VZWHlRMXVyTEE9PSIsInZhbHVlIjoiYWwxaFpuMm1LZDd1V2pSRW5yb3ZVRkQ1R0cybWRpQnMxMXBQcVBucDZreEhoSncreHErWWJlZUtIa2JHQzhvUWJZc0ZFM0JKQkN6empBWG9ITmJ0bml2WVBKRi9UV1hQZUk2bklUdnZ0TWNITDdsSEo5MWtGbXhZNkY1SnBvd3haSmRyUUYrUTM1NURUOEF4VVA5UG5DZGVCLzJnT3dxMFphT215TDNlVVp4dWppN1B1NTBKcEE0bWJKNTNoMzBzNm10NTNRZEpWV3VncXppUlE1VU53QWM4TWJzSE1VaHFNVDVpZmVRV0FjY1c4ZThsdkdXdFN0eUJLcHVyVFllbFJpb3A5TjZxbUlzNVVsNllkRmVOSHZSQjlqYncxdUFLMFpjN2ZaRTNhY1dDejgvcExhRjV3YmFtWGNEMi9lU2VLemdvRi9DTy9yWUhZYlJnNmdZdHFRa0NSWWZCWUNBaUpvOStRN1M5eldURTlVeU1wQnMvK2NWcGtTaStiZGswUGVYVi9HbXpiYnBxczl0eVRGZGhEMTNMNGY0OTNNZmxZOFg1WkVHbURuUWhlWUtuM28wYmVXQ0tMaFpkRUZyWkx6NWZHeXMzYm5pZGNxUU5CVWdIMlRBREE4aGJFL3c3bTJyaUJDMkV4ZkwxMlA5Q2tJdlZqbm45cFZXSEhhRCsiLCJtYWMiOiJjMTU5YmFmOGU4MzdiMzNiODY0NDk1YjcwZjZhYTc0OWQwMjRmMWY3MmUyMTQxZTYzMTY2MTdkMGQ3M2JmOWE1IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6InJFaTA1NnV2bFlKWXBRaUlUWUpUMmc9PSIsInZhbHVlIjoiUjE4cGp0YllYa0YwVmRBQzRSd29pYkZmLytXL05qU08zaWQzU3AzOWVhOWhnVnpGQlI0QUdqKy9PUlVWRytTcmdySjZZb0d3WTdzMnN1NUtoZEdUZVJuNHdKTWlwOVJ4S3E5YmMxSjFZSDU0czVJdDFXcEwySjh0UkRVY2F4Nmlsc3VUK0pHSHpmdUVtc0x6bEJjN3MvL2FjVlM2TnkvaDhEQ3hIWEV4UWpIWDUxeG82ZzhmN1ZGaWxyUXRiUmRyNGlhY3k2V3dEeFl5V3A4dEpaU2dFeVlGV0tSckJjYnFBd3lYT3QzT1pXUFM3ZHhBQXVGNWUxRS95OWVSWHlYV3dnNGtLcVg2OVN3WVBUSFYrcFBZWTltNysxWnMrcjdCeUZPMFZDdnhZb3N1UWlQVkpxL3p3MEJUMUxFaS85UmwvbVUrdFVJS3hLWjF4VVEwdmxaUmZFQ25JSmVWNkhhMm9XT2cwU3JvaUtXTjNuSDZZWmhLM0dDZWxnallTYUVkVmd6eFJTbGRJelVLKzcvNVQ2RWp0d1VSak1kUUpaLzJqekF3azl4SUdpU0RFUU9GVDNvTTBwOEU2QVFuQUJtS0pZUUlZV0Z1Smg5K3E4VEs1bGI4S1pzMTArY1ArZnVhOGRaYUFBZ2JXL2lFQ0xMQ1g3NURYdEwyNG1WRElieXoiLCJtYWMiOiJkODExNjA4Zjk0NTU5N2Y2MjdkMDIwMWE2NzBjYzJmZTA4M2Y0NWY4OWFhM2Y5YmRkZDFjYjk3NTI2YzQ2MGIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-620251593\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1158104109 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21K3ZmFzZyr4s2pH9aKWRqwEKhgdUCnDfhl6jx78</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1158104109\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:48:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:47:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">application/pdf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">831289</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhLd0pMWXBVWWNIWkdFMmthMkhiN3c9PSIsInZhbHVlIjoiOXBvNkJCWFV0dDlkRVNpQm9uTFRSdVhCdjIwWWh4SDJ0OFYwZHNEOVkzV0dmQnROOHRyWnZaNDdQWlROdVlpTjAwL0I0QVE1bUtZQ0lOQ2V6UitwU1VUb0l3dlhvYjVtSm0wWjA0YjhvZFJ5Qk1JRERhd0cwQmIxRVV1aXV1TWlCY1MrWkhBL2xRT29YVDNHSVc4UVNpV0t1TjZPMUhqdm94VU9nMyt5OFlscFRlNnlEUlNjUHlXblZJSzZuK0U2WHMrcFZtTVJOUmUxUnRVZjZoQjlJVnhnNVhkaWtaU0M1czFTR2JjMTN2Z2dHYmpxNU9Ud3ZRUlJ1UVFtbElCTTM5MFY2cmV4bUQ3T0xySEdkWXRxOUF4S0dRMnNjQnJZQWcrZWFCUW9POWhRZWF0UEZZUW01a2tUMXo3OFpHbTQyeWFQLzhDSTIxbHZJcDBpS0c4eWdsZlNVeUJHaDQ3VFZ0LzdmcjNEZUQ1Nk9XS2hJT2RHMndIbnNNbmJqOFF3SmVzOCtkNE9qVE1zMHB3S1E4WDNXSlArN1k0Um9JYnhqNXNHb0ZXMmExK0lvSUFzbVZJdTF3dG1iS0VzTnRDcXBpZkxBOGk4NldiUk5EcVpvamdjcktqaGhtdk1GczF3elBzMmNkeVY1RjJBcWY2WTExS2dqSm44WWNUbVV2UEoiLCJtYWMiOiI4NGVhZWM0Zjc2N2NkOGY0NzMzNmU4MGMzOTM0ZTdmOGFiZDFlNWY0ZWZhZGIxYWMxYWI5N2U3NTcwZTE1MDdhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:48:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IkpIM3BUampJTmhnUVdBdXRTdUExOVE9PSIsInZhbHVlIjoiNmVXZ044MUd1NmY1MUJpQ2ZvM2FXRkVQTjFFRG9UclNKTEpEdWFEN0ttMXZ4aUNYUDJ1VG1qOGEvMkJVUmlac09RME1uVFBkb2taVnBWdTFVU0ZRaUtlbVhYbmlUR2hNdjFFYnlaUG5IZ2ZIUGxLQzRZUmhVeDJMWlZ4VDh4NE9sM04zTFduSGk5ZFRtUUFtbm9EOGx0aG1ZYi91ZXBlUE1GcGtoMnNURzdHMFZwQXJtL3JablQxMzJlQlhxbUFMdGd1YnRYT0JqR000Q3FOZG5QMGRMY1lWK2t4WmF1V1V3Z1pyN2xnYkxtREU2ZHhFYjhyTDRwemxKZTZQb0x6WjVXRER6MS9kdDVrRzZnVnVQTmtwQ2h4SzBVdmxNOXd0VXZDWEFCQ0V4RUVzUWsvcFlFT1dibGdINTdReWZCbWQ2TTRwZXdDOGJBVDBrOWgrd0czb0xUdzFKOEdWaVVTdVVHQzJOeVBnWkJaZjBxTGsyUTJvY3czcWY1d0hoRDhpS3VwQ0NMbFR3dzJMTHdTWDBlWWRSVklWMXJOQUsyOGJHdHM2bTAxekxnUUNxWU5aYnAweC9TSUJUZzlkZ1NpMkJKb0RuMWtlUThJMEZKR1l2TVB1ZFZ0NlJMVGN2bFc0dnNVL1psZllPSyttSEpLOGFGQWxZcGhPajN6MFB4ZXgiLCJtYWMiOiI4MDU0YzQ4MWMzMWIwMTdmMzg5MzRkNzE0MmQwMmVlNmI3YjhmMjc2ZDVhZDE1OTIzOGViODJhMjU2MzkzMDYxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:48:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhLd0pMWXBVWWNIWkdFMmthMkhiN3c9PSIsInZhbHVlIjoiOXBvNkJCWFV0dDlkRVNpQm9uTFRSdVhCdjIwWWh4SDJ0OFYwZHNEOVkzV0dmQnROOHRyWnZaNDdQWlROdVlpTjAwL0I0QVE1bUtZQ0lOQ2V6UitwU1VUb0l3dlhvYjVtSm0wWjA0YjhvZFJ5Qk1JRERhd0cwQmIxRVV1aXV1TWlCY1MrWkhBL2xRT29YVDNHSVc4UVNpV0t1TjZPMUhqdm94VU9nMyt5OFlscFRlNnlEUlNjUHlXblZJSzZuK0U2WHMrcFZtTVJOUmUxUnRVZjZoQjlJVnhnNVhkaWtaU0M1czFTR2JjMTN2Z2dHYmpxNU9Ud3ZRUlJ1UVFtbElCTTM5MFY2cmV4bUQ3T0xySEdkWXRxOUF4S0dRMnNjQnJZQWcrZWFCUW9POWhRZWF0UEZZUW01a2tUMXo3OFpHbTQyeWFQLzhDSTIxbHZJcDBpS0c4eWdsZlNVeUJHaDQ3VFZ0LzdmcjNEZUQ1Nk9XS2hJT2RHMndIbnNNbmJqOFF3SmVzOCtkNE9qVE1zMHB3S1E4WDNXSlArN1k0Um9JYnhqNXNHb0ZXMmExK0lvSUFzbVZJdTF3dG1iS0VzTnRDcXBpZkxBOGk4NldiUk5EcVpvamdjcktqaGhtdk1GczF3elBzMmNkeVY1RjJBcWY2WTExS2dqSm44WWNUbVV2UEoiLCJtYWMiOiI4NGVhZWM0Zjc2N2NkOGY0NzMzNmU4MGMzOTM0ZTdmOGFiZDFlNWY0ZWZhZGIxYWMxYWI5N2U3NTcwZTE1MDdhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:48:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IkpIM3BUampJTmhnUVdBdXRTdUExOVE9PSIsInZhbHVlIjoiNmVXZ044MUd1NmY1MUJpQ2ZvM2FXRkVQTjFFRG9UclNKTEpEdWFEN0ttMXZ4aUNYUDJ1VG1qOGEvMkJVUmlac09RME1uVFBkb2taVnBWdTFVU0ZRaUtlbVhYbmlUR2hNdjFFYnlaUG5IZ2ZIUGxLQzRZUmhVeDJMWlZ4VDh4NE9sM04zTFduSGk5ZFRtUUFtbm9EOGx0aG1ZYi91ZXBlUE1GcGtoMnNURzdHMFZwQXJtL3JablQxMzJlQlhxbUFMdGd1YnRYT0JqR000Q3FOZG5QMGRMY1lWK2t4WmF1V1V3Z1pyN2xnYkxtREU2ZHhFYjhyTDRwemxKZTZQb0x6WjVXRER6MS9kdDVrRzZnVnVQTmtwQ2h4SzBVdmxNOXd0VXZDWEFCQ0V4RUVzUWsvcFlFT1dibGdINTdReWZCbWQ2TTRwZXdDOGJBVDBrOWgrd0czb0xUdzFKOEdWaVVTdVVHQzJOeVBnWkJaZjBxTGsyUTJvY3czcWY1d0hoRDhpS3VwQ0NMbFR3dzJMTHdTWDBlWWRSVklWMXJOQUsyOGJHdHM2bTAxekxnUUNxWU5aYnAweC9TSUJUZzlkZ1NpMkJKb0RuMWtlUThJMEZKR1l2TVB1ZFZ0NlJMVGN2bFc0dnNVL1psZllPSyttSEpLOGFGQWxZcGhPajN6MFB4ZXgiLCJtYWMiOiI4MDU0YzQ4MWMzMWIwMTdmMzg5MzRkNzE0MmQwMmVlNmI3YjhmMjc2ZDVhZDE1OTIzOGViODJhMjU2MzkzMDYxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:48:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1776543500 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"89 characters\">http://127.0.0.1:8000/storage/expense_receipts/1753948061_Class%205%20math%20question.pdf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1776543500\", {\"maxDepth\":0})</script>\n"}}