{"__meta": {"id": "X67aaa9eb971064b4832b43b7b124f24e", "datetime": "2025-07-31 05:11:01", "utime": **********.055263, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:11:01] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.04777, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.182045, "end": **********.055289, "duration": 0.873244047164917, "duration_str": "873ms", "measures": [{"label": "Booting", "start": **********.182045, "relative_start": 0, "end": **********.884495, "relative_end": **********.884495, "duration": 0.7024500370025635, "duration_str": "702ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.884508, "relative_start": 0.702462911605835, "end": **********.055291, "relative_end": 1.9073486328125e-06, "duration": 0.17078304290771484, "duration_str": "171ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50614208, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.028124, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.036259999999999994, "accumulated_duration_str": "36.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.939983, "duration": 0.009699999999999999, "duration_str": "9.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 26.751}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.970284, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 26.751, "width_percent": 2.592}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.979725, "duration": 0.02443, "duration_str": "24.43ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 29.344, "width_percent": 67.375}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.009072, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 96.718, "width_percent": 3.282}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1227567772 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1227567772\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-321934888 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-321934888\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-520972623 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-520972623\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1117644470 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjZuL3JHeTBmMC9xcmE2TWxoV0t6Zmc9PSIsInZhbHVlIjoiSGdIMW52djF2WVZKQm5naG1sUkora3RXb3VIU3FVT3hoZU5CSlllejdmazBpWEVYcnZnRWlxazN6WjJSNlZyUVZCZmI0ZGxERDFSTmtpWGhxUkI2d1NjcmlZMU8wSy9XaHg3R0d0QXZ3R2VzMFZDVW4xcFd1TUY4dzJkVGFXeVlReFR5L1pMQTdwVG5XMFF1MEpqRUN0NW5TRDVaOHRpZTZsdHIxTXF4VGhWREYwbDB2bFZwdFdXQ1VWQm1lRytEVVJBZFgvWVZnbHNkdmlrMkNranBJbVNiaVdTN2FRYWYySXE4MzFjN0dtR0lmK2c0Q0MvaGtGVVEveFZBV1VqeGtRQnZ3R3lZMUxBblZhR1VRY25EMmd3c1E2ZWFqclR0cGdtbjhJNCtwcmRKMmdMQjhrR3hwS3lhU2VKMi83Qi9FWElwUXZvSjh6dkNZN08vNVRHWDM0dzhDcDlCZHlvMnF4WGpoY05lQnpzU0xadXRCMThXeGdrZ2M1WWNwQ29PaWxPNThKOURObWRQWWl6c2NKeEs4UXpGRHFoanJSRGhCSW42UTRWVUZrc3czNzhKTVJDeTc5OHVuM28xK21Sc0Y5bDlBUjZtR3hDOWVncWs1Wm1PYmJwdDg2Q3QwcWVoTjhqVDFmMm5DSFplc1NTTFVnNjZwSHlRUVZzdDB5TnQiLCJtYWMiOiIzNTA1YWZlMjViNTUwZWFmMjM1ODU2NTdiZTM2ZjE5NjEwMDgyYWZjZWYzMDI1ZTJjY2Y2ODJlYzE4YzI4NzVlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkJIcEJVTXlwYSt0UmxjamVtcS9kMUE9PSIsInZhbHVlIjoiMVY2cXpSTjNNZm02ZDYvL2VFeVNTK3IzUEg3SC9rMGFHT1FCU2labCs2SzdrWjEwMHBwYTA2a042Q01MM2hjS3puNWhwdVJsSldWRy9CUFBCZFM1KytNQlQ2SXYvbUVuYmYrdmI2NUltUWlYYVc2RTNaSUM3NWZRNkN0TVBiMFVZVDNqRGp2c3VFcW5PT3g3V3VNMkdwZmlGMStGeithZ09ONGNsbGxES2hMeEhxS09Mb0trcXF4a0Nia1RDQ0Z2bnpoVUtNMGM5NGN0MUxTaVQ3M0dodjhiKzRleGpKUXprSmphWHFTOVczRzNzNE5NbUF0Qlp3bnRxRDI2K1ZHRytJYjhkSEFHYWVnQmdYQnhrVHVqbVQ4U3JPL2htVUFVV3hCMm5qa0VPYnp4d2dJZmZqMjY1UE1pVDNndWk0RDUrcW5CUHk0UWJSS0sxTk5hN3BtNXp2QTd0VnhmTXNsTElSS1F3cXFkVnB3MTZvWlFuUjJYa3I3TEc0MW5ZNVlWRnFIbzRkcVNFcnNDYjJOOEp6amhoWFZ0WWRyTjk1ZThoL21FbHNpenp6ZW1GVFpqQTkwdUxScmFNczY2OElVemtEQXE2Mlk2NmlCRTM5NmRkM3U0aUdZdlo3MXp1eEQ3Q3VqZGZVNXdMcWN4SVB5ZDRScW9ud21jL2FOalQ4d1MiLCJtYWMiOiIwZWVhZjc3OTU3NDhkNGNjMmYzY2JlZmJjZTQ1ZWUyNTUzNTA2YTMzNzNmZTc4ZTA1ZmY2NzkzOWM4MTU5MWJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1117644470\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-523972250 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXcIUJMcydAyqtnEKWEUzIzoSEq1x7CzphaduhZT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-523972250\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-755940426 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:11:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1NREtURHhCTmZiSE56bFNXY0t2eUE9PSIsInZhbHVlIjoiUTgzU1ZuRXB6NkU0bkUwaEdRdFJURFdaWVFQWlFvclJEQUF4OFNvOHZqbzZLNm54NXZpRm8xRFJ1NjQ5M2VCSzNsdmxtWUNPKzExQkdsZWlaOEFZTlBGU1dBanlUeFFVTXkxRUpmUGhmMVc5RkZwZ25aWGV0UGRvU2NDck1neGNlcHdkWnhHZkg0SnRPV2szQUJXS0h1M1dJNVpWdFVlNXJtdllaZ0RSS0lPUEJEd3pTTWNWb1lzckQwYkhJUmpUT21vN25icnBpcGVPU0QwNmgvdnVVOFFoV2NjdytYWitYRVpOME5EbnNCTDFPY2k5VUdYZDNtUFYybFRlRUU2TUIyeG1PaTcvNGY4V0JEcXNlQ1VSK3FuMy9Bb2RVbnlyQVpCNXM0TFNmazJNU3p3aWVCK3RsbFdEaTl3UEwzL1czV2htTTVuTTU3d3l0dklsdlVyNDZ3b1Q1YzJYbzk0bkRrcU1MaDJTa2dLamRSRkE0NUg1MHFhUW02TGVyNHdtNWZKWXJJc0F3K1hNM2tCM2tSMXd6U09ycy9jNEtIbDFLWERoeEFyZVNEQk93NC9MWlVMRjFvSnFnTE9UamhmSXRWOXhNYU0yUjRuT0ZvaCtlNjhUQnlZYmVjakw1RkNzblFjVk5lRE1rUjI5TmRPODF2cVZtRS9RT004UElkV1EiLCJtYWMiOiI0MmFjMWU5M2YxNzBjZGFkOWE4MzI0OWFjNzg0YmU4NGY0N2MzNWZkNjE5NGQ0ODNiMTg1MDQ2YzdjNGM2YmI0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:11:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjRXdW9tSTViMWZYTkwrN1IrRUVwS3c9PSIsInZhbHVlIjoiQVcwY09pZlBVQk9HQmtHWkM3Smp4NnEvZk5yVndDOFB6b2lWZ0lnMlpJdEpzSEpJakg0a2VkVExzOEtKUCtmMjJrb1ZFU0VPbmlJNE5ld1ZuZE4yOXpBcDc5N0hHYXhZczBTOGlPRWJ5UlFVOTNSSVdhL0ZSVVgzczFRbmgybzluRWtXWC9OVWhXWmNLYUQ2OFVoRGRhanN4Zi9qcG9LZFBiMVNyNEdUd1pVYTQ3dFVGVnNqbUxwODZ5QWREc3FIZC9XRVhTQlJ5RUs1SThXSVhNcnQ2SENQQ0EyWnY2aGlMaER0MnpNdHVvbGQxbEpJMkhuL2I0YmlUbVdqbThVVlNGYWxmU1Jrbm9YbDcrbGxBS3NwR0ZPZXZNVDQva2VXSmM1Q0orbmdXVXFzTm1LalA0WE1oKzl6b3A3ZjdGbXh5N0dzd1ZWblVjSDkwbFVxSFBWVGxlSjZyR2k5UWJRZExDRFpEaG1YaXVqYmVBaU50ZXVPV2N4Q05mdHdKQ3pGb1pXazJyajZkQ1JqVi9xSjNZMHVwWWdKQzNkL0NORVlwMjdEVXhpZi9naUpQQTU0UVd3dlFHZUZTZEF4eGIxSllWd1NUVWtyOXVTeTFYZmx4b051UkRhaGlCMXdPRGJIM0FPRE9NMzRNcHNYU1hJWnI0bnBud1hWQ1Z5NnNXaU8iLCJtYWMiOiJjNzljODgyM2NjOWMyNDI1NzY2YzkzNWE4OWU1ZDg0NWVlNmQyZmFiMjFiYTMyNWI3NDNiNWU1OWM5NGQxYjQ3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:11:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1NREtURHhCTmZiSE56bFNXY0t2eUE9PSIsInZhbHVlIjoiUTgzU1ZuRXB6NkU0bkUwaEdRdFJURFdaWVFQWlFvclJEQUF4OFNvOHZqbzZLNm54NXZpRm8xRFJ1NjQ5M2VCSzNsdmxtWUNPKzExQkdsZWlaOEFZTlBGU1dBanlUeFFVTXkxRUpmUGhmMVc5RkZwZ25aWGV0UGRvU2NDck1neGNlcHdkWnhHZkg0SnRPV2szQUJXS0h1M1dJNVpWdFVlNXJtdllaZ0RSS0lPUEJEd3pTTWNWb1lzckQwYkhJUmpUT21vN25icnBpcGVPU0QwNmgvdnVVOFFoV2NjdytYWitYRVpOME5EbnNCTDFPY2k5VUdYZDNtUFYybFRlRUU2TUIyeG1PaTcvNGY4V0JEcXNlQ1VSK3FuMy9Bb2RVbnlyQVpCNXM0TFNmazJNU3p3aWVCK3RsbFdEaTl3UEwzL1czV2htTTVuTTU3d3l0dklsdlVyNDZ3b1Q1YzJYbzk0bkRrcU1MaDJTa2dLamRSRkE0NUg1MHFhUW02TGVyNHdtNWZKWXJJc0F3K1hNM2tCM2tSMXd6U09ycy9jNEtIbDFLWERoeEFyZVNEQk93NC9MWlVMRjFvSnFnTE9UamhmSXRWOXhNYU0yUjRuT0ZvaCtlNjhUQnlZYmVjakw1RkNzblFjVk5lRE1rUjI5TmRPODF2cVZtRS9RT004UElkV1EiLCJtYWMiOiI0MmFjMWU5M2YxNzBjZGFkOWE4MzI0OWFjNzg0YmU4NGY0N2MzNWZkNjE5NGQ0ODNiMTg1MDQ2YzdjNGM2YmI0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:11:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjRXdW9tSTViMWZYTkwrN1IrRUVwS3c9PSIsInZhbHVlIjoiQVcwY09pZlBVQk9HQmtHWkM3Smp4NnEvZk5yVndDOFB6b2lWZ0lnMlpJdEpzSEpJakg0a2VkVExzOEtKUCtmMjJrb1ZFU0VPbmlJNE5ld1ZuZE4yOXpBcDc5N0hHYXhZczBTOGlPRWJ5UlFVOTNSSVdhL0ZSVVgzczFRbmgybzluRWtXWC9OVWhXWmNLYUQ2OFVoRGRhanN4Zi9qcG9LZFBiMVNyNEdUd1pVYTQ3dFVGVnNqbUxwODZ5QWREc3FIZC9XRVhTQlJ5RUs1SThXSVhNcnQ2SENQQ0EyWnY2aGlMaER0MnpNdHVvbGQxbEpJMkhuL2I0YmlUbVdqbThVVlNGYWxmU1Jrbm9YbDcrbGxBS3NwR0ZPZXZNVDQva2VXSmM1Q0orbmdXVXFzTm1LalA0WE1oKzl6b3A3ZjdGbXh5N0dzd1ZWblVjSDkwbFVxSFBWVGxlSjZyR2k5UWJRZExDRFpEaG1YaXVqYmVBaU50ZXVPV2N4Q05mdHdKQ3pGb1pXazJyajZkQ1JqVi9xSjNZMHVwWWdKQzNkL0NORVlwMjdEVXhpZi9naUpQQTU0UVd3dlFHZUZTZEF4eGIxSllWd1NUVWtyOXVTeTFYZmx4b051UkRhaGlCMXdPRGJIM0FPRE9NMzRNcHNYU1hJWnI0bnBud1hWQ1Z5NnNXaU8iLCJtYWMiOiJjNzljODgyM2NjOWMyNDI1NzY2YzkzNWE4OWU1ZDg0NWVlNmQyZmFiMjFiYTMyNWI3NDNiNWU1OWM5NGQxYjQ3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:11:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755940426\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1651841080 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1651841080\", {\"maxDepth\":0})</script>\n"}}