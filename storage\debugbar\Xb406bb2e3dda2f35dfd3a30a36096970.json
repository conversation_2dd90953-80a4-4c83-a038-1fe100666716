{"__meta": {"id": "Xb406bb2e3dda2f35dfd3a30a36096970", "datetime": "2025-07-31 06:31:26", "utime": **********.335648, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:31:26] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.321832, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943483.665354, "end": **********.335696, "duration": 2.670341968536377, "duration_str": "2.67s", "measures": [{"label": "Booting", "start": 1753943483.665354, "relative_start": 0, "end": 1753943485.990921, "relative_end": 1753943485.990921, "duration": 2.3255670070648193, "duration_str": "2.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753943485.990971, "relative_start": 2.3256170749664307, "end": **********.3357, "relative_end": 4.0531158447265625e-06, "duration": 0.344728946685791, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50618648, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.278483, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.042519999999999995, "accumulated_duration_str": "42.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1334789, "duration": 0.005849999999999999, "duration_str": "5.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 13.758}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.1821911, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 13.758, "width_percent": 2.752}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.193546, "duration": 0.03347, "duration_str": "33.47ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 16.51, "width_percent": 78.716}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.239751, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 95.226, "width_percent": 4.774}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1671099011 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1671099011\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1627688317 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1627688317\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1864583602 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864583602\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1921069348 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImxvUUttWVdmbnIyQjJ1UWlmZUJ3VVE9PSIsInZhbHVlIjoiZ2pjWktHWis4Y05DdVZkZGpDaE0rQWEzVTQ1TjlENmNDZGhwa1FhVnVzS3JvUjU3d2NvTnowaFpaTzJpaCt6MVhqYnlUendncE44Q0RkVHhYb3E4cnFIbmtzbGtjMlhUTWNEbUJmTGFYekhaakFLYkFjNGsydXpmOFdPNmtLdHBvbW9qOWZ0ZzhZT01lYVFhQ0tmeDM1WTdMc3czbkJlMG04aXhVb0RnclorK21wL1hQSDl3cEhhN2g4S2FlQjlVeXJlc0dwalhBMllwaGVsSHdFWm94VW92RTc4TmFJQ0xFdHF2YlpqSFB4SWxBQU1nYlo4aXBoVytDMnZHcEZoOHZzSnJoZFlVZjZyVGFKRE1iWmR5UXY0Z3hmWFVWR1BNZlpMb2twcFNINC8vSkFQa1lERG4vdlpxVlF1YWZKd1dGT29pQXRTS3ZRRjArRkdnKzE0UXYydW51VTN6OHBqRDdJVGRLdmgvTXpSdjRPTzJTUk9MeDI4eVlBMk85dldtd2ExUmVCU2F6M2tyb1hrTkpRUXNOTUR0TWRYOHhZRG1OeHdFOVdXT0FqQjJtWngvY2hRK2liZG4yaWozNENvWXFtdnZCb3J4RWNGYXRVOG1IdmkrcERyblNqanRob1hWNUx5WjZOaUtnUng3UlV4bkhna1pJSDZIUjVZWXRFTUEiLCJtYWMiOiIxODkyNmNhZTg4NDNhNTUyOGU3MDI3MmVlODU3YjI5MjBlNDJlMjUxYjNiZDU2ODNhMDNiNjY2ZTVhMWY2MTU0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ijl0dUhzeXVuS2FVejhXUGc4MnBtTUE9PSIsInZhbHVlIjoiYkhpYmpVSUhKdlZkdERsb25EN3RSU3FiNHdEWWQ4QnpZU0dsNWUzbVBkT3VmL1pldWdRcjhVSFVodGhGUG53RDJwVk1qTEpodGhGa2JHNHI2M2RPYk11TEp3QnhyYXJoN205ZzV4dWJWdVltc05WMWlFdGNtbjVsc0tiMHVKbFBpQUFWa3RmU1o3dFNoc1VGd3RDQ1NkN2tkRWpHVys4U1F1YXhHdXQzZ3F5cVhXL0o5YndobW9INzJaUHZHSXJDWVpsYzJHYW1iWnVzRlRuWDh2YWhpeVc3bE9vektYOGFWRmpFek1nbm5WQktReExRUGRoeGNWNkRZeEdYdGZEMlhHNlB1OVJqRTFOQmZvcHRLcytTRjlsTGRqaVV2TDQxV2p2SW00T0xFWWdteE9jeitsRERpZnBDcHlvUUFNSXVWNVgrNG4zOHZsOFp6TCtTd1BUT0FFbHdGY0lhYUdFZm1NT3RQYktlYUx4L1lYNnIrc3dBK1BYVjZNYm9GaXhRcmFWOFZPQnR4NnhkbTZTWG9pWkhoZ1ZlL3ZRd0U3M1k2SkdqVEdSRjRxQzhmT0J4U3pYNUJmRUNDa1QrQVV6MktlWmZtOWU2aTlndDh3Nm8zNDY2ZCtyRE8xN2tUaEI3dHIzWm1jMGZVekR0eml1UXZ4N3BPaEZreitFRGgzczAiLCJtYWMiOiIxYTg4YmQ3MzUyNjQ0ODEwYjhkMzJkZTU0YTkxNmJlNjE2YzU0NTlkODUxYTE0MmQ5MGU1NjNlMjVjMmMyMjI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921069348\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1925840773 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925840773\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:31:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtWUmtpa0RtTnhtVGl5RjR6c1pBT1E9PSIsInZhbHVlIjoiNmJ2Z1lkTk1lSjRJbXFINStuOGUvcU1rNk13K1paTmhLbWNzU0pHYWdGVXpsZFdlVTlQM0hKMytLUE1zeWFqNDVTNG5IVGFUdXR5QkJ1WlU2cjU4b0FicW9NL2tFazRHS3hoWGZsM0ZvOEYvakdHcWZQWFN1OGpBdnI4MmtISDJCaEpHaUtFd0h0anp3cEcxRjdMbDZqa09tQ1kwM0xJaTV5d0VHckpSdVZMaDNVLzgzdERVbjBMcWZud3Q2djNsbU9XWERNWmJ0Nlo2US8zSU1PZVRUM3daeU5QY0dQdHNTMHlLYkZXcXFpMWtZQ0hOU3E2YVdRN3NuY0tHQTZQQ1ZiQzZ3TlA5Z1JxQWFuZ2s3eHd5K2FHNGpBK1VzbEZmU2loYVB2emFwc2J2dVFtM0puRHkyQlBrVjhEL21oM0g0cGxBdkZ6Mk9kN0ZiQjNRMnp2dmRoUVV1dndIckhYMVpKOGludEQrdXppR1Zra0o2WVFhckNXNnE0bWo5Um41ZHVPaVNzZUdYd1NGUUhSMDZXMnZWNXBJOFdrNWEyZE0ydWJLRUdmOE8vMHlxeFNCeTcyZWNrWjBTTERhd1N2VHR3c3I5Z0Z1d3FvVG1CZUJhU3hrbVR6eXZtUlpCelhqTWJCOVFYVFFpaW1maVNkRnMxeVc5UzBnQUpsaXN0UjYiLCJtYWMiOiI1ZTlhYjVkOWQyMTdiYjUwZTQ0ZjgyMjk5Zjg0ZTIwY2Y3YTA0ZWFhNzUwM2FiNmI2OWY4ZTQ3MDg1OTI5YjA1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IldpQnFReU40cERCbHM5MXMzdk9vY3c9PSIsInZhbHVlIjoiaHpQSjAySWZPQmpYZzNGZXRuT2NHT3JPVStoMXJlWkNzdWtHNktUNGkwUEljS1FxQndYbkxvMHExTG5ITFI2V3ltNERTUmpHWW1NZkhRU1NzRVh3RVVPOHpGcnpuRDZuM3dzOVM2UXRaU05JRVV4OUdXcHR5NS9BSXBETTd0MjVCK0VqMW9JeHB3QnFIY0dkWGpoNGpNN0huSWpDZm93NEhYMzkvdXRNWVIzSWp5aDBCRnVzWDRybzRNdXptTEpValVXN0RidGdGWE4zYlNZc1VKTCtBVmZiOEw0RDU3cDExd3FKY2lIVXZWazZCaEtlMEpzU2lTSGVBbkhIQ1B4am9jZ3hVNWNIekw2ZGxKLy9JZlBvaHBSWURSSGpidWU4aU9PdjFQRHJwM0xRVFd0U2Y4eDlLTTZCQmpwUTRMNDVlVHEzR255WEw4QjI3cTAxZ0U0dVJuVmdHYldwY1JPLzUrRGM4eTNqSjMwd0tjWlJweXlldXZsdk9rQWNvWHNqTlZUNkV2N2gyL09xSXo0MzFYYVZERnFaWEFiZjZZVVVkeEZmZU5ETlZaY2lKZVd1OWR6VVdVa0dCaGxYZVA5MERFeEpkSWZ2NVptVVNHT2lkcHBRUEZQZVo0RkVYSVgyTjdFK0JCaGZId0dPWFA2M2UwbGVJdmQ3OXg4NlJuZC8iLCJtYWMiOiIwZmM5MTgwNzI4MDhmNzg5MzkyMDc2YjhhMjU3MGNhZDE0YzQ1Y2NmZmZhYjgzOThhZWIyOWZjYzU3MzBkMjgwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtWUmtpa0RtTnhtVGl5RjR6c1pBT1E9PSIsInZhbHVlIjoiNmJ2Z1lkTk1lSjRJbXFINStuOGUvcU1rNk13K1paTmhLbWNzU0pHYWdGVXpsZFdlVTlQM0hKMytLUE1zeWFqNDVTNG5IVGFUdXR5QkJ1WlU2cjU4b0FicW9NL2tFazRHS3hoWGZsM0ZvOEYvakdHcWZQWFN1OGpBdnI4MmtISDJCaEpHaUtFd0h0anp3cEcxRjdMbDZqa09tQ1kwM0xJaTV5d0VHckpSdVZMaDNVLzgzdERVbjBMcWZud3Q2djNsbU9XWERNWmJ0Nlo2US8zSU1PZVRUM3daeU5QY0dQdHNTMHlLYkZXcXFpMWtZQ0hOU3E2YVdRN3NuY0tHQTZQQ1ZiQzZ3TlA5Z1JxQWFuZ2s3eHd5K2FHNGpBK1VzbEZmU2loYVB2emFwc2J2dVFtM0puRHkyQlBrVjhEL21oM0g0cGxBdkZ6Mk9kN0ZiQjNRMnp2dmRoUVV1dndIckhYMVpKOGludEQrdXppR1Zra0o2WVFhckNXNnE0bWo5Um41ZHVPaVNzZUdYd1NGUUhSMDZXMnZWNXBJOFdrNWEyZE0ydWJLRUdmOE8vMHlxeFNCeTcyZWNrWjBTTERhd1N2VHR3c3I5Z0Z1d3FvVG1CZUJhU3hrbVR6eXZtUlpCelhqTWJCOVFYVFFpaW1maVNkRnMxeVc5UzBnQUpsaXN0UjYiLCJtYWMiOiI1ZTlhYjVkOWQyMTdiYjUwZTQ0ZjgyMjk5Zjg0ZTIwY2Y3YTA0ZWFhNzUwM2FiNmI2OWY4ZTQ3MDg1OTI5YjA1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IldpQnFReU40cERCbHM5MXMzdk9vY3c9PSIsInZhbHVlIjoiaHpQSjAySWZPQmpYZzNGZXRuT2NHT3JPVStoMXJlWkNzdWtHNktUNGkwUEljS1FxQndYbkxvMHExTG5ITFI2V3ltNERTUmpHWW1NZkhRU1NzRVh3RVVPOHpGcnpuRDZuM3dzOVM2UXRaU05JRVV4OUdXcHR5NS9BSXBETTd0MjVCK0VqMW9JeHB3QnFIY0dkWGpoNGpNN0huSWpDZm93NEhYMzkvdXRNWVIzSWp5aDBCRnVzWDRybzRNdXptTEpValVXN0RidGdGWE4zYlNZc1VKTCtBVmZiOEw0RDU3cDExd3FKY2lIVXZWazZCaEtlMEpzU2lTSGVBbkhIQ1B4am9jZ3hVNWNIekw2ZGxKLy9JZlBvaHBSWURSSGpidWU4aU9PdjFQRHJwM0xRVFd0U2Y4eDlLTTZCQmpwUTRMNDVlVHEzR255WEw4QjI3cTAxZ0U0dVJuVmdHYldwY1JPLzUrRGM4eTNqSjMwd0tjWlJweXlldXZsdk9rQWNvWHNqTlZUNkV2N2gyL09xSXo0MzFYYVZERnFaWEFiZjZZVVVkeEZmZU5ETlZaY2lKZVd1OWR6VVdVa0dCaGxYZVA5MERFeEpkSWZ2NVptVVNHT2lkcHBRUEZQZVo0RkVYSVgyTjdFK0JCaGZId0dPWFA2M2UwbGVJdmQ3OXg4NlJuZC8iLCJtYWMiOiIwZmM5MTgwNzI4MDhmNzg5MzkyMDc2YjhhMjU3MGNhZDE0YzQ1Y2NmZmZhYjgzOThhZWIyOWZjYzU3MzBkMjgwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}