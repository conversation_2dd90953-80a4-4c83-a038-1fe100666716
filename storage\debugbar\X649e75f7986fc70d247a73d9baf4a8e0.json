{"__meta": {"id": "X649e75f7986fc70d247a73d9baf4a8e0", "datetime": "2025-07-31 06:00:52", "utime": **********.581185, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753941651.64731, "end": **********.581297, "duration": 0.9339869022369385, "duration_str": "934ms", "measures": [{"label": "Booting", "start": 1753941651.64731, "relative_start": 0, "end": **********.492085, "relative_end": **********.492085, "duration": 0.8447749614715576, "duration_str": "845ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.492104, "relative_start": 0.****************, "end": **********.581301, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "89.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VhhCzXM1AxHbznPyBWpiuXYliqxckdCkRV0ZFLfx", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1555599828 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1555599828\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-512314947 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-512314947\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2013332857 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2013332857\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1500216544 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1500216544\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1474741604 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1474741604\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-463360880 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:00:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVNWGxMOG5BYWpud2pKREg2Znd0UUE9PSIsInZhbHVlIjoiUTR4dXpSaVJaV0Mxb1hEVzZZS2U1MkxYWThuU1pNYmpnSFp4eWhpR0FWaklESXNYTzg4RjBBd1Y5K2NKUk56bEw0NmRXMGxvMUhDdXBObTFBSGJPdmh6ZDlSMzhZOFF4TThkNlJ3VThOcERFL0NRZFRBOWc2Z012c2dLS1dNbmdMTUM1ZVl1bUFxVlpLa2p5OW5HMzhKdFZ6R3JFK0RmL3VYMUIwZWltSmJWT05WNGJ6VU1mSjQrYVVYZC9aNk90dEowdDVob1Y1VFh4NGQzbTJGSDhtWll6Qi9ZY3RrVVFaZTZLY0hIcDZaTTZpTy9oVUd0MktYL3kyejJsR2J1MU1OMER4TXgzRXhsTVFIbitnMzM4Q2YvTTdBa2NJTnJ3SXVUOHV2WXpjcmFwK1l4M1ZKN0JKTDhwaFBTNGNFYWZHa0hkTHYzL2dTZ2ZNQzkrOHZCN0Z3K2F1bFZ5dDRrOHJVMTdDNkMwRCs2a3A4dmVYUDExb0ptWm0rODlJNE9BektSOGEwRW95TlB3aENDeUhEc0JiQVNBQ3ppaU5iZEZubGJjY3lKU1pacElmTGZlNzRGWTdhb1FmNHY4eHZBbFd4cmZKOHUyVjVBTkdydWRIOHNnMkFFK2dTbTBScUdIZ2IremtzOGE1QlVIOWFHWUk3cTZrMlU2WFJkZFlkYm8iLCJtYWMiOiJjZTlhMjEyOWNlYjM1NTY3NTVmYTRkMmJhMDE3MGViYmNiMzlkMzg3ZGZiODliMmE4NWUxYWVmYTllMWQ4ZjJkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:00:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im1aRXF0UnlxOGVJTHZZUGNETloxOGc9PSIsInZhbHVlIjoiQUlWSjluakR3LzlBZWplUnpCWnJURXRCQTJ4WFNIK0lTdUM3VVk1Z0srcTRacDFxSkIvUWVyRWNKdEw3dVRtYjc3YkJ2MjVTZi9acUJucG5PZDFySGhMMXFDdG5DZDFQWHJqN0paVCtVeHFKZTJuWWhydG5kVTQ5M1BEUHcwVTlIZC8rcit5M2xxczIvTG96RTFEOTM2dFI0M2NWdWdtUjhnb0t4eG44STZiTGFvdGxZYmZ3eDRramRUWVBTdjVyZ1hXS2w5SUFPQmlpY3FEYTloRnlPSTZwS2ZkVGM0SlRCY29zUUJmaEp6ZnE2VHhRMW5mL3dQclpUdUQxTW9jY012b21LNm1VMmhheHdKanF2a2V3Y2FES1pLWTg3Q3lVNkhzK3JkakN4UmhtcVNLZkxiakVvWFJ1eFRFWTZMZ2xvaUpnaDJ6RlhGUTJHd0FhRG1EZmxnQ0ZHdzV3NHUvTHdUZElISnA4bmthVnJTM1NCbStYYTBjUC9QQ010S1lnNjRCSnJ4dUU1ejU4ZkNJRFoyeGlhU3oxbjZmRnN5cnphZTUrYVJGK3hQaEpYYVQxSzlhU3VMbTBPMER1eWNjbUtwMVNuVVRMMExYUlA5NDNEN09UeTJVbXlIMFFXbFRkbU90TlNBMVEzSUN2RkQ4V0RyRFgwVWpsUFh6ZmRhU0EiLCJtYWMiOiJlYjBkMGNiZDBlNzY1OTVlOTkwZWE1MWRjYzA5OWQ1OGZmYzBlZTc3YTFhMTE1MjY4ZmNhMjUzODNlYTYxMWI2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:00:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVNWGxMOG5BYWpud2pKREg2Znd0UUE9PSIsInZhbHVlIjoiUTR4dXpSaVJaV0Mxb1hEVzZZS2U1MkxYWThuU1pNYmpnSFp4eWhpR0FWaklESXNYTzg4RjBBd1Y5K2NKUk56bEw0NmRXMGxvMUhDdXBObTFBSGJPdmh6ZDlSMzhZOFF4TThkNlJ3VThOcERFL0NRZFRBOWc2Z012c2dLS1dNbmdMTUM1ZVl1bUFxVlpLa2p5OW5HMzhKdFZ6R3JFK0RmL3VYMUIwZWltSmJWT05WNGJ6VU1mSjQrYVVYZC9aNk90dEowdDVob1Y1VFh4NGQzbTJGSDhtWll6Qi9ZY3RrVVFaZTZLY0hIcDZaTTZpTy9oVUd0MktYL3kyejJsR2J1MU1OMER4TXgzRXhsTVFIbitnMzM4Q2YvTTdBa2NJTnJ3SXVUOHV2WXpjcmFwK1l4M1ZKN0JKTDhwaFBTNGNFYWZHa0hkTHYzL2dTZ2ZNQzkrOHZCN0Z3K2F1bFZ5dDRrOHJVMTdDNkMwRCs2a3A4dmVYUDExb0ptWm0rODlJNE9BektSOGEwRW95TlB3aENDeUhEc0JiQVNBQ3ppaU5iZEZubGJjY3lKU1pacElmTGZlNzRGWTdhb1FmNHY4eHZBbFd4cmZKOHUyVjVBTkdydWRIOHNnMkFFK2dTbTBScUdIZ2IremtzOGE1QlVIOWFHWUk3cTZrMlU2WFJkZFlkYm8iLCJtYWMiOiJjZTlhMjEyOWNlYjM1NTY3NTVmYTRkMmJhMDE3MGViYmNiMzlkMzg3ZGZiODliMmE4NWUxYWVmYTllMWQ4ZjJkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:00:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im1aRXF0UnlxOGVJTHZZUGNETloxOGc9PSIsInZhbHVlIjoiQUlWSjluakR3LzlBZWplUnpCWnJURXRCQTJ4WFNIK0lTdUM3VVk1Z0srcTRacDFxSkIvUWVyRWNKdEw3dVRtYjc3YkJ2MjVTZi9acUJucG5PZDFySGhMMXFDdG5DZDFQWHJqN0paVCtVeHFKZTJuWWhydG5kVTQ5M1BEUHcwVTlIZC8rcit5M2xxczIvTG96RTFEOTM2dFI0M2NWdWdtUjhnb0t4eG44STZiTGFvdGxZYmZ3eDRramRUWVBTdjVyZ1hXS2w5SUFPQmlpY3FEYTloRnlPSTZwS2ZkVGM0SlRCY29zUUJmaEp6ZnE2VHhRMW5mL3dQclpUdUQxTW9jY012b21LNm1VMmhheHdKanF2a2V3Y2FES1pLWTg3Q3lVNkhzK3JkakN4UmhtcVNLZkxiakVvWFJ1eFRFWTZMZ2xvaUpnaDJ6RlhGUTJHd0FhRG1EZmxnQ0ZHdzV3NHUvTHdUZElISnA4bmthVnJTM1NCbStYYTBjUC9QQ010S1lnNjRCSnJ4dUU1ejU4ZkNJRFoyeGlhU3oxbjZmRnN5cnphZTUrYVJGK3hQaEpYYVQxSzlhU3VMbTBPMER1eWNjbUtwMVNuVVRMMExYUlA5NDNEN09UeTJVbXlIMFFXbFRkbU90TlNBMVEzSUN2RkQ4V0RyRFgwVWpsUFh6ZmRhU0EiLCJtYWMiOiJlYjBkMGNiZDBlNzY1OTVlOTkwZWE1MWRjYzA5OWQ1OGZmYzBlZTc3YTFhMTE1MjY4ZmNhMjUzODNlYTYxMWI2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:00:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-463360880\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-157023553 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VhhCzXM1AxHbznPyBWpiuXYliqxckdCkRV0ZFLfx</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-157023553\", {\"maxDepth\":0})</script>\n"}}