{"__meta": {"id": "X60ea91398c788811dc7e32f9538024b9", "datetime": "2025-07-31 05:35:14", "utime": **********.157768, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940112.862249, "end": **********.157814, "duration": 1.295565128326416, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": 1753940112.862249, "relative_start": 0, "end": **********.047493, "relative_end": **********.047493, "duration": 1.185244083404541, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.04752, "relative_start": 1.****************, "end": **********.157818, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "110ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "CKfIfTE19401gQ3CvXqeKzFTOdXgdmjXozHW3Fxz", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-305889849 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-305889849\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-407535759 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-407535759\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1621185551 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1621185551\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1284359124 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1284359124\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1750858052 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1750858052\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1789217574 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:35:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVvZnNsamUxcm9yU1o3VG80dm5BQUE9PSIsInZhbHVlIjoiV1pYT0k2NU5rbzdKekNsaE9Bb2lvZ1NodWZsdHoyZmdKY0ZQSlQwOWljNFo5RHRqUjBrUGlRWE5HQ0U1bUdBV0wwSVF0MHBkQStVQnJXaGJxait2eG9PS0hTYzd0bjFRODZPZW94d3Y5ZU9xRVRjN1ZJZTVEUnZ1M3ZZS0xFbzhGdjdBR0pJN3pvTTBqRkxpTWdwejlPMFYrZXlTRDFLMmc3V1VxNWJ0MG91ODhwUXlaRW1CQ0czbUJGUWNhYzhwMjYvcEN5NGJVWWt6cW5IVXJLaFNQV1IrQnFES1ZQQ201S0g3VUZtd0g1YjZsdmljVE84aHNHYnhnb3NYNUgyL2FqT01zTGpJK3pkUkJoRkwzcUVoempTNFJLd3AycVdiYkxDd1JHUGI1Mlp0MkZDTUZhMnozSGRVU1BRaW12engwSFlMZmxSTmp0T2NRUG9IQVVXNklqU2RMVERIbWV0S05YKzRIdU1ZOUtFS1A3N25LSU8zM0M1WWU3eUY5NGkvekcyUDk3c2pnU1h1QkRiK3RLenV4U2dPTndySGh0R1hoQmlETlVFWkxCZDlkRFp6UUxOL01GMTFOYk9jTTdRSlN2TnphRUZvNW8xREVvWVNQZzJIYUc0MlQxcUEzK2hkc2dRTFNNbGtod2dZeWo5NUN5V21oa0V0Q0xEUFdScEgiLCJtYWMiOiJiZDIwZDg1YzY2ODMyZjgwMmFkMzUzZDllN2JkMzc5ZDk4NGFlMzlmN2Y4NGU1YjljMGZkYWNmYTg2MjhjNjE2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:35:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik5sd0MyVmdVZWFDOEtYaXRsNE8yMHc9PSIsInZhbHVlIjoiMGFKOGtrRGZhbGFkRnNXV29KVVpKWnB1YkJnV2ZNRXVIM3h2QzhQZzdOME1IMUNZcG9PMlF2eTdVTnRQcnlDU1lZZ1NoeFFZWkw4MThGOTd6VnFOTjZHakxiNysxYWxJbVdwdXBad2xRY01yNWEybllMRks4alN6d2VIcnduOTJGbjJJS2VISkxzNlFBNFdRbUN0YXhNTzNLSWZZQlNnRUtqM0lwaGVOdVBPd09ibmZBTGVqL2hpK0dZME0wc3M4YS9LaFM2L0NDelFqUVgzZDZkYXc5cTR1UkxHNTVlaXFlWCttVmZTa2xJMUNoU1Fjd1BOOUhxeW1oUmJaaGVwbWxpckRKa1Q4Y2hKRVNHcElNTkd6VHdhSFh4d1hsNGdCNktYL01nZkdUQU1jY2o2bUd3czBlMXduNXdxSDRISlpIeSs2NVRWWGJCYnFXdVpWcGdZakZ1ZitPMmFJRnBEYnpRMnRRSU1oUXJSMXVmQ29Qc2s2VnJ1bVpZNHlteDVrc3dGRWhyN2h6QlpqTkpHYnJkdU1TYll5cnIwSEtCNmJ4cVRsc2xYRkIvdDlSWjlqUkFIaDYwVUhUd3RuN24xUDJyNklaWE0vZ3JxaFQzenVDdFJDamZqTVozbUQwTGN3NS9NMWsrdnZkWWFYbXBKY0VKYVU2VjBob0dhSEg3ZE4iLCJtYWMiOiJiYmI0NjdkNGJhN2QxYjA4OTRjOTkxNDJhMjlhZGYwYjY5OWNmZTkyNjA2OTI2MzUwYTRhOThhZTY0ZGRhNzFhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:35:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVvZnNsamUxcm9yU1o3VG80dm5BQUE9PSIsInZhbHVlIjoiV1pYT0k2NU5rbzdKekNsaE9Bb2lvZ1NodWZsdHoyZmdKY0ZQSlQwOWljNFo5RHRqUjBrUGlRWE5HQ0U1bUdBV0wwSVF0MHBkQStVQnJXaGJxait2eG9PS0hTYzd0bjFRODZPZW94d3Y5ZU9xRVRjN1ZJZTVEUnZ1M3ZZS0xFbzhGdjdBR0pJN3pvTTBqRkxpTWdwejlPMFYrZXlTRDFLMmc3V1VxNWJ0MG91ODhwUXlaRW1CQ0czbUJGUWNhYzhwMjYvcEN5NGJVWWt6cW5IVXJLaFNQV1IrQnFES1ZQQ201S0g3VUZtd0g1YjZsdmljVE84aHNHYnhnb3NYNUgyL2FqT01zTGpJK3pkUkJoRkwzcUVoempTNFJLd3AycVdiYkxDd1JHUGI1Mlp0MkZDTUZhMnozSGRVU1BRaW12engwSFlMZmxSTmp0T2NRUG9IQVVXNklqU2RMVERIbWV0S05YKzRIdU1ZOUtFS1A3N25LSU8zM0M1WWU3eUY5NGkvekcyUDk3c2pnU1h1QkRiK3RLenV4U2dPTndySGh0R1hoQmlETlVFWkxCZDlkRFp6UUxOL01GMTFOYk9jTTdRSlN2TnphRUZvNW8xREVvWVNQZzJIYUc0MlQxcUEzK2hkc2dRTFNNbGtod2dZeWo5NUN5V21oa0V0Q0xEUFdScEgiLCJtYWMiOiJiZDIwZDg1YzY2ODMyZjgwMmFkMzUzZDllN2JkMzc5ZDk4NGFlMzlmN2Y4NGU1YjljMGZkYWNmYTg2MjhjNjE2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:35:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik5sd0MyVmdVZWFDOEtYaXRsNE8yMHc9PSIsInZhbHVlIjoiMGFKOGtrRGZhbGFkRnNXV29KVVpKWnB1YkJnV2ZNRXVIM3h2QzhQZzdOME1IMUNZcG9PMlF2eTdVTnRQcnlDU1lZZ1NoeFFZWkw4MThGOTd6VnFOTjZHakxiNysxYWxJbVdwdXBad2xRY01yNWEybllMRks4alN6d2VIcnduOTJGbjJJS2VISkxzNlFBNFdRbUN0YXhNTzNLSWZZQlNnRUtqM0lwaGVOdVBPd09ibmZBTGVqL2hpK0dZME0wc3M4YS9LaFM2L0NDelFqUVgzZDZkYXc5cTR1UkxHNTVlaXFlWCttVmZTa2xJMUNoU1Fjd1BOOUhxeW1oUmJaaGVwbWxpckRKa1Q4Y2hKRVNHcElNTkd6VHdhSFh4d1hsNGdCNktYL01nZkdUQU1jY2o2bUd3czBlMXduNXdxSDRISlpIeSs2NVRWWGJCYnFXdVpWcGdZakZ1ZitPMmFJRnBEYnpRMnRRSU1oUXJSMXVmQ29Qc2s2VnJ1bVpZNHlteDVrc3dGRWhyN2h6QlpqTkpHYnJkdU1TYll5cnIwSEtCNmJ4cVRsc2xYRkIvdDlSWjlqUkFIaDYwVUhUd3RuN24xUDJyNklaWE0vZ3JxaFQzenVDdFJDamZqTVozbUQwTGN3NS9NMWsrdnZkWWFYbXBKY0VKYVU2VjBob0dhSEg3ZE4iLCJtYWMiOiJiYmI0NjdkNGJhN2QxYjA4OTRjOTkxNDJhMjlhZGYwYjY5OWNmZTkyNjA2OTI2MzUwYTRhOThhZTY0ZGRhNzFhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:35:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1789217574\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1521404547 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CKfIfTE19401gQ3CvXqeKzFTOdXgdmjXozHW3Fxz</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1521404547\", {\"maxDepth\":0})</script>\n"}}