{"__meta": {"id": "Xadab4b65eb35b91de250a2753e10e22b", "datetime": "2025-07-31 06:28:31", "utime": **********.406769, "method": "POST", "uri": "/pricing-plans", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:28:31] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/pricing-plans\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.374721, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943308.88062, "end": **********.406832, "duration": 2.526211977005005, "duration_str": "2.53s", "measures": [{"label": "Booting", "start": 1753943308.88062, "relative_start": 0, "end": **********.004304, "relative_end": **********.004304, "duration": 2.1236839294433594, "duration_str": "2.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.004347, "relative_start": 2.1237270832061768, "end": **********.406838, "relative_end": 5.9604644775390625e-06, "duration": 0.40249085426330566, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51158088, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pricing-plans", "middleware": "web, auth, XSS", "as": "pricing-plans.store", "controller": "App\\Http\\Controllers\\PricingPlanController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=62\" onclick=\"\">app/Http/Controllers/PricingPlanController.php:62-126</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.028419999999999997, "accumulated_duration_str": "28.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.188236, "duration": 0.01774, "duration_str": "17.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 62.421}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.247787, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 62.421, "width_percent": 4.258}, {"sql": "select count(*) as aggregate from `pricing_plans` where `name` = 'My Bot AI Flow'", "type": "query", "params": [], "bindings": ["My Bot AI Flow"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 983}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.305051, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "radhe_same", "start_percent": 66.678, "width_percent": 3.589}, {"sql": "insert into `pricing_plans` (`name`, `plan_type`, `description`, `price`, `status`, `duration`, `max_users`, `max_customers`, `max_vendors`, `max_clients`, `storage_limit`, `module_permissions`, `sort_order`, `updated_at`, `created_at`) values ('My Bot AI Flow', 'subaccount', '', '8900', 'active', 'yearly', '25', '25', '25', '25', '200', '{\\\"crm\\\":[\\\"create lead\\\",\\\"create deal\\\",\\\"create form builder\\\",\\\"create contract\\\",\\\"create pipeline\\\",\\\"create stage\\\",\\\"create source\\\",\\\"create label\\\",\\\"view crm dashboard\\\",\\\"view lead\\\",\\\"view deal\\\",\\\"view form builder\\\",\\\"view contract\\\",\\\"view pipeline\\\",\\\"view stage\\\",\\\"view source\\\",\\\"view label\\\",\\\"delete lead\\\",\\\"delete deal\\\",\\\"delete form builder\\\",\\\"delete contract\\\",\\\"delete pipeline\\\",\\\"delete stage\\\",\\\"delete source\\\",\\\"delete label\\\",\\\"manage lead\\\",\\\"edit lead\\\",\\\"manage deal\\\",\\\"edit deal\\\",\\\"manage form builder\\\",\\\"edit form builder\\\",\\\"manage contract\\\",\\\"edit contract\\\",\\\"manage pipeline\\\",\\\"edit pipeline\\\",\\\"manage stage\\\",\\\"edit stage\\\",\\\"manage source\\\",\\\"edit source\\\",\\\"manage label\\\",\\\"edit label\\\"],\\\"hrm\\\":[\\\"create employee\\\",\\\"create set salary\\\",\\\"create pay slip\\\",\\\"create leave\\\",\\\"create attendance\\\",\\\"create training\\\",\\\"create award\\\",\\\"create branch\\\",\\\"create department\\\",\\\"create designation\\\",\\\"create document type\\\",\\\"view hrm dashboard\\\",\\\"view employee\\\",\\\"view set salary\\\",\\\"view pay slip\\\",\\\"view leave\\\",\\\"view attendance\\\",\\\"view training\\\",\\\"view award\\\",\\\"view branch\\\",\\\"view department\\\",\\\"view designation\\\",\\\"view document type\\\",\\\"delete employee\\\",\\\"delete set salary\\\",\\\"delete pay slip\\\",\\\"delete leave\\\",\\\"delete attendance\\\",\\\"delete training\\\",\\\"delete award\\\",\\\"delete branch\\\",\\\"delete department\\\",\\\"delete designation\\\",\\\"delete document type\\\",\\\"manage employee\\\",\\\"edit employee\\\",\\\"manage set salary\\\",\\\"edit set salary\\\",\\\"manage pay slip\\\",\\\"edit pay slip\\\",\\\"manage leave\\\",\\\"edit leave\\\",\\\"manage attendance\\\",\\\"edit attendance\\\",\\\"manage training\\\",\\\"edit training\\\",\\\"manage award\\\",\\\"edit award\\\",\\\"manage branch\\\",\\\"edit branch\\\",\\\"manage department\\\",\\\"edit department\\\",\\\"manage designation\\\",\\\"edit designation\\\",\\\"manage document type\\\",\\\"edit document type\\\"],\\\"account\\\":[\\\"create customer\\\",\\\"create vender\\\",\\\"create invoice\\\",\\\"create bill\\\",\\\"create revenue\\\",\\\"create payment\\\",\\\"create proposal\\\",\\\"create goal\\\",\\\"create credit note\\\",\\\"create debit note\\\",\\\"create bank account\\\",\\\"create bank transfer\\\",\\\"create transaction\\\",\\\"create chart of account\\\",\\\"create journal entry\\\",\\\"create assets\\\",\\\"create constant custom field\\\",\\\"view account dashboard\\\",\\\"view customer\\\",\\\"view vender\\\",\\\"view invoice\\\",\\\"view bill\\\",\\\"view revenue\\\",\\\"view payment\\\",\\\"view proposal\\\",\\\"view goal\\\",\\\"view credit note\\\",\\\"view debit note\\\",\\\"view bank account\\\",\\\"view bank transfer\\\",\\\"view transaction\\\",\\\"view chart of account\\\",\\\"view journal entry\\\",\\\"view assets\\\",\\\"view constant custom field\\\",\\\"view report\\\",\\\"delete customer\\\",\\\"delete vender\\\",\\\"delete invoice\\\",\\\"delete bill\\\",\\\"delete revenue\\\",\\\"delete payment\\\",\\\"delete proposal\\\",\\\"delete goal\\\",\\\"delete credit note\\\",\\\"delete debit note\\\",\\\"delete bank account\\\",\\\"delete bank transfer\\\",\\\"delete transaction\\\",\\\"delete chart of account\\\",\\\"delete journal entry\\\",\\\"delete assets\\\",\\\"delete constant custom field\\\",\\\"manage customer\\\",\\\"edit customer\\\",\\\"manage vender\\\",\\\"edit vender\\\",\\\"manage invoice\\\",\\\"edit invoice\\\",\\\"manage bill\\\",\\\"edit bill\\\",\\\"manage revenue\\\",\\\"edit revenue\\\",\\\"manage payment\\\",\\\"edit payment\\\",\\\"manage proposal\\\",\\\"edit proposal\\\",\\\"manage goal\\\",\\\"edit goal\\\",\\\"manage credit note\\\",\\\"edit credit note\\\",\\\"manage debit note\\\",\\\"edit debit note\\\",\\\"manage bank account\\\",\\\"edit bank account\\\",\\\"manage bank transfer\\\",\\\"edit bank transfer\\\",\\\"manage transaction\\\",\\\"edit transaction\\\",\\\"manage chart of account\\\",\\\"edit chart of account\\\",\\\"manage journal entry\\\",\\\"edit journal entry\\\",\\\"manage assets\\\",\\\"edit assets\\\",\\\"manage constant custom field\\\",\\\"edit constant custom field\\\",\\\"manage report\\\"],\\\"project\\\":[\\\"create project\\\",\\\"create project task\\\",\\\"create timesheet\\\",\\\"create bug report\\\",\\\"create milestone\\\",\\\"create project stage\\\",\\\"create project task stage\\\",\\\"create project expense\\\",\\\"create activity\\\",\\\"create bug status\\\",\\\"view project dashboard\\\",\\\"view project\\\",\\\"view project task\\\",\\\"view timesheet\\\",\\\"view bug report\\\",\\\"view milestone\\\",\\\"view project stage\\\",\\\"view project task stage\\\",\\\"view project expense\\\",\\\"view activity\\\",\\\"view bug status\\\",\\\"delete project\\\",\\\"delete project task\\\",\\\"delete timesheet\\\",\\\"delete bug report\\\",\\\"delete milestone\\\",\\\"delete project stage\\\",\\\"delete project task stage\\\",\\\"delete project expense\\\",\\\"delete activity\\\",\\\"delete bug status\\\",\\\"manage project\\\",\\\"edit project\\\",\\\"manage project task\\\",\\\"edit project task\\\",\\\"manage timesheet\\\",\\\"edit timesheet\\\",\\\"manage bug report\\\",\\\"edit bug report\\\",\\\"manage milestone\\\",\\\"edit milestone\\\",\\\"manage project stage\\\",\\\"edit project stage\\\",\\\"manage project task stage\\\",\\\"edit project task stage\\\",\\\"manage project expense\\\",\\\"edit project expense\\\",\\\"manage activity\\\",\\\"edit activity\\\",\\\"manage bug status\\\",\\\"edit bug status\\\"],\\\"pos\\\":[\\\"create warehouse\\\",\\\"create purchase\\\",\\\"create quotation\\\",\\\"create pos\\\",\\\"create barcode\\\",\\\"create product\\\",\\\"create product category\\\",\\\"create product unit\\\",\\\"view pos dashboard\\\",\\\"view warehouse\\\",\\\"view purchase\\\",\\\"view quotation\\\",\\\"view pos\\\",\\\"view product\\\",\\\"view product category\\\",\\\"view product unit\\\",\\\"delete warehouse\\\",\\\"delete purchase\\\",\\\"delete quotation\\\",\\\"delete pos\\\",\\\"delete product\\\",\\\"delete product category\\\",\\\"delete product unit\\\",\\\"manage warehouse\\\",\\\"edit warehouse\\\",\\\"manage purchase\\\",\\\"edit purchase\\\",\\\"manage quotation\\\",\\\"edit quotation\\\",\\\"manage pos\\\",\\\"edit pos\\\",\\\"manage product\\\",\\\"edit product\\\",\\\"manage product category\\\",\\\"edit product category\\\",\\\"manage product unit\\\",\\\"edit product unit\\\"],\\\"support\\\":[\\\"create support\\\",\\\"view support dashboard\\\",\\\"view support\\\",\\\"delete support\\\",\\\"manage support\\\",\\\"edit support\\\",\\\"reply support\\\"],\\\"user_management\\\":[\\\"create user\\\",\\\"create client\\\",\\\"view user\\\",\\\"view client\\\",\\\"delete user\\\",\\\"delete client\\\",\\\"manage user\\\",\\\"edit user\\\",\\\"manage client\\\",\\\"edit client\\\"],\\\"booking\\\":[\\\"create booking\\\",\\\"create appointment\\\",\\\"create appointment booking\\\",\\\"create calendar event\\\",\\\"view booking dashboard\\\",\\\"view booking\\\",\\\"show booking\\\",\\\"view appointment\\\",\\\"show appointment\\\",\\\"view appointment booking\\\",\\\"show appointment booking\\\",\\\"view calendar event\\\",\\\"show calendar event\\\",\\\"delete booking\\\",\\\"delete appointment\\\",\\\"delete appointment booking\\\",\\\"delete calendar event\\\",\\\"manage booking\\\",\\\"edit booking\\\",\\\"manage appointment\\\",\\\"edit appointment\\\",\\\"manage appointment booking\\\",\\\"edit appointment booking\\\",\\\"manage calendar event\\\",\\\"edit calendar event\\\"],\\\"omx_flow\\\":[\\\"access omx flow\\\",\\\"whatsapp_flows\\\",\\\"whatsapp_orders\\\",\\\"campaigns\\\",\\\"templates\\\",\\\"chatbot\\\"],\\\"personal_tasks\\\":[\\\"create personal task\\\",\\\"create personal task comment\\\",\\\"create personal task file\\\",\\\"create personal task checklist\\\",\\\"view personal task\\\",\\\"delete personal task\\\",\\\"delete personal task comment\\\",\\\"delete personal task file\\\",\\\"delete personal task checklist\\\",\\\"manage personal task\\\",\\\"edit personal task\\\",\\\"edit personal task comment\\\",\\\"edit personal task checklist\\\",\\\"manage personal task time tracking\\\"],\\\"automatish\\\":[\\\"access automatish\\\"]}', '200', '2025-07-31 06:28:31', '2025-07-31 06:28:31')", "type": "query", "params": [], "bindings": ["My Bot AI Flow", "subaccount", "", "8900", "active", "yearly", "25", "25", "25", "25", "200", "{&quot;crm&quot;:[&quot;create lead&quot;,&quot;create deal&quot;,&quot;create form builder&quot;,&quot;create contract&quot;,&quot;create pipeline&quot;,&quot;create stage&quot;,&quot;create source&quot;,&quot;create label&quot;,&quot;view crm dashboard&quot;,&quot;view lead&quot;,&quot;view deal&quot;,&quot;view form builder&quot;,&quot;view contract&quot;,&quot;view pipeline&quot;,&quot;view stage&quot;,&quot;view source&quot;,&quot;view label&quot;,&quot;delete lead&quot;,&quot;delete deal&quot;,&quot;delete form builder&quot;,&quot;delete contract&quot;,&quot;delete pipeline&quot;,&quot;delete stage&quot;,&quot;delete source&quot;,&quot;delete label&quot;,&quot;manage lead&quot;,&quot;edit lead&quot;,&quot;manage deal&quot;,&quot;edit deal&quot;,&quot;manage form builder&quot;,&quot;edit form builder&quot;,&quot;manage contract&quot;,&quot;edit contract&quot;,&quot;manage pipeline&quot;,&quot;edit pipeline&quot;,&quot;manage stage&quot;,&quot;edit stage&quot;,&quot;manage source&quot;,&quot;edit source&quot;,&quot;manage label&quot;,&quot;edit label&quot;],&quot;hrm&quot;:[&quot;create employee&quot;,&quot;create set salary&quot;,&quot;create pay slip&quot;,&quot;create leave&quot;,&quot;create attendance&quot;,&quot;create training&quot;,&quot;create award&quot;,&quot;create branch&quot;,&quot;create department&quot;,&quot;create designation&quot;,&quot;create document type&quot;,&quot;view hrm dashboard&quot;,&quot;view employee&quot;,&quot;view set salary&quot;,&quot;view pay slip&quot;,&quot;view leave&quot;,&quot;view attendance&quot;,&quot;view training&quot;,&quot;view award&quot;,&quot;view branch&quot;,&quot;view department&quot;,&quot;view designation&quot;,&quot;view document type&quot;,&quot;delete employee&quot;,&quot;delete set salary&quot;,&quot;delete pay slip&quot;,&quot;delete leave&quot;,&quot;delete attendance&quot;,&quot;delete training&quot;,&quot;delete award&quot;,&quot;delete branch&quot;,&quot;delete department&quot;,&quot;delete designation&quot;,&quot;delete document type&quot;,&quot;manage employee&quot;,&quot;edit employee&quot;,&quot;manage set salary&quot;,&quot;edit set salary&quot;,&quot;manage pay slip&quot;,&quot;edit pay slip&quot;,&quot;manage leave&quot;,&quot;edit leave&quot;,&quot;manage attendance&quot;,&quot;edit attendance&quot;,&quot;manage training&quot;,&quot;edit training&quot;,&quot;manage award&quot;,&quot;edit award&quot;,&quot;manage branch&quot;,&quot;edit branch&quot;,&quot;manage department&quot;,&quot;edit department&quot;,&quot;manage designation&quot;,&quot;edit designation&quot;,&quot;manage document type&quot;,&quot;edit document type&quot;],&quot;account&quot;:[&quot;create customer&quot;,&quot;create vender&quot;,&quot;create invoice&quot;,&quot;create bill&quot;,&quot;create revenue&quot;,&quot;create payment&quot;,&quot;create proposal&quot;,&quot;create goal&quot;,&quot;create credit note&quot;,&quot;create debit note&quot;,&quot;create bank account&quot;,&quot;create bank transfer&quot;,&quot;create transaction&quot;,&quot;create chart of account&quot;,&quot;create journal entry&quot;,&quot;create assets&quot;,&quot;create constant custom field&quot;,&quot;view account dashboard&quot;,&quot;view customer&quot;,&quot;view vender&quot;,&quot;view invoice&quot;,&quot;view bill&quot;,&quot;view revenue&quot;,&quot;view payment&quot;,&quot;view proposal&quot;,&quot;view goal&quot;,&quot;view credit note&quot;,&quot;view debit note&quot;,&quot;view bank account&quot;,&quot;view bank transfer&quot;,&quot;view transaction&quot;,&quot;view chart of account&quot;,&quot;view journal entry&quot;,&quot;view assets&quot;,&quot;view constant custom field&quot;,&quot;view report&quot;,&quot;delete customer&quot;,&quot;delete vender&quot;,&quot;delete invoice&quot;,&quot;delete bill&quot;,&quot;delete revenue&quot;,&quot;delete payment&quot;,&quot;delete proposal&quot;,&quot;delete goal&quot;,&quot;delete credit note&quot;,&quot;delete debit note&quot;,&quot;delete bank account&quot;,&quot;delete bank transfer&quot;,&quot;delete transaction&quot;,&quot;delete chart of account&quot;,&quot;delete journal entry&quot;,&quot;delete assets&quot;,&quot;delete constant custom field&quot;,&quot;manage customer&quot;,&quot;edit customer&quot;,&quot;manage vender&quot;,&quot;edit vender&quot;,&quot;manage invoice&quot;,&quot;edit invoice&quot;,&quot;manage bill&quot;,&quot;edit bill&quot;,&quot;manage revenue&quot;,&quot;edit revenue&quot;,&quot;manage payment&quot;,&quot;edit payment&quot;,&quot;manage proposal&quot;,&quot;edit proposal&quot;,&quot;manage goal&quot;,&quot;edit goal&quot;,&quot;manage credit note&quot;,&quot;edit credit note&quot;,&quot;manage debit note&quot;,&quot;edit debit note&quot;,&quot;manage bank account&quot;,&quot;edit bank account&quot;,&quot;manage bank transfer&quot;,&quot;edit bank transfer&quot;,&quot;manage transaction&quot;,&quot;edit transaction&quot;,&quot;manage chart of account&quot;,&quot;edit chart of account&quot;,&quot;manage journal entry&quot;,&quot;edit journal entry&quot;,&quot;manage assets&quot;,&quot;edit assets&quot;,&quot;manage constant custom field&quot;,&quot;edit constant custom field&quot;,&quot;manage report&quot;],&quot;project&quot;:[&quot;create project&quot;,&quot;create project task&quot;,&quot;create timesheet&quot;,&quot;create bug report&quot;,&quot;create milestone&quot;,&quot;create project stage&quot;,&quot;create project task stage&quot;,&quot;create project expense&quot;,&quot;create activity&quot;,&quot;create bug status&quot;,&quot;view project dashboard&quot;,&quot;view project&quot;,&quot;view project task&quot;,&quot;view timesheet&quot;,&quot;view bug report&quot;,&quot;view milestone&quot;,&quot;view project stage&quot;,&quot;view project task stage&quot;,&quot;view project expense&quot;,&quot;view activity&quot;,&quot;view bug status&quot;,&quot;delete project&quot;,&quot;delete project task&quot;,&quot;delete timesheet&quot;,&quot;delete bug report&quot;,&quot;delete milestone&quot;,&quot;delete project stage&quot;,&quot;delete project task stage&quot;,&quot;delete project expense&quot;,&quot;delete activity&quot;,&quot;delete bug status&quot;,&quot;manage project&quot;,&quot;edit project&quot;,&quot;manage project task&quot;,&quot;edit project task&quot;,&quot;manage timesheet&quot;,&quot;edit timesheet&quot;,&quot;manage bug report&quot;,&quot;edit bug report&quot;,&quot;manage milestone&quot;,&quot;edit milestone&quot;,&quot;manage project stage&quot;,&quot;edit project stage&quot;,&quot;manage project task stage&quot;,&quot;edit project task stage&quot;,&quot;manage project expense&quot;,&quot;edit project expense&quot;,&quot;manage activity&quot;,&quot;edit activity&quot;,&quot;manage bug status&quot;,&quot;edit bug status&quot;],&quot;pos&quot;:[&quot;create warehouse&quot;,&quot;create purchase&quot;,&quot;create quotation&quot;,&quot;create pos&quot;,&quot;create barcode&quot;,&quot;create product&quot;,&quot;create product category&quot;,&quot;create product unit&quot;,&quot;view pos dashboard&quot;,&quot;view warehouse&quot;,&quot;view purchase&quot;,&quot;view quotation&quot;,&quot;view pos&quot;,&quot;view product&quot;,&quot;view product category&quot;,&quot;view product unit&quot;,&quot;delete warehouse&quot;,&quot;delete purchase&quot;,&quot;delete quotation&quot;,&quot;delete pos&quot;,&quot;delete product&quot;,&quot;delete product category&quot;,&quot;delete product unit&quot;,&quot;manage warehouse&quot;,&quot;edit warehouse&quot;,&quot;manage purchase&quot;,&quot;edit purchase&quot;,&quot;manage quotation&quot;,&quot;edit quotation&quot;,&quot;manage pos&quot;,&quot;edit pos&quot;,&quot;manage product&quot;,&quot;edit product&quot;,&quot;manage product category&quot;,&quot;edit product category&quot;,&quot;manage product unit&quot;,&quot;edit product unit&quot;],&quot;support&quot;:[&quot;create support&quot;,&quot;view support dashboard&quot;,&quot;view support&quot;,&quot;delete support&quot;,&quot;manage support&quot;,&quot;edit support&quot;,&quot;reply support&quot;],&quot;user_management&quot;:[&quot;create user&quot;,&quot;create client&quot;,&quot;view user&quot;,&quot;view client&quot;,&quot;delete user&quot;,&quot;delete client&quot;,&quot;manage user&quot;,&quot;edit user&quot;,&quot;manage client&quot;,&quot;edit client&quot;],&quot;booking&quot;:[&quot;create booking&quot;,&quot;create appointment&quot;,&quot;create appointment booking&quot;,&quot;create calendar event&quot;,&quot;view booking dashboard&quot;,&quot;view booking&quot;,&quot;show booking&quot;,&quot;view appointment&quot;,&quot;show appointment&quot;,&quot;view appointment booking&quot;,&quot;show appointment booking&quot;,&quot;view calendar event&quot;,&quot;show calendar event&quot;,&quot;delete booking&quot;,&quot;delete appointment&quot;,&quot;delete appointment booking&quot;,&quot;delete calendar event&quot;,&quot;manage booking&quot;,&quot;edit booking&quot;,&quot;manage appointment&quot;,&quot;edit appointment&quot;,&quot;manage appointment booking&quot;,&quot;edit appointment booking&quot;,&quot;manage calendar event&quot;,&quot;edit calendar event&quot;],&quot;omx_flow&quot;:[&quot;access omx flow&quot;,&quot;whatsapp_flows&quot;,&quot;whatsapp_orders&quot;,&quot;campaigns&quot;,&quot;templates&quot;,&quot;chatbot&quot;],&quot;personal_tasks&quot;:[&quot;create personal task&quot;,&quot;create personal task comment&quot;,&quot;create personal task file&quot;,&quot;create personal task checklist&quot;,&quot;view personal task&quot;,&quot;delete personal task&quot;,&quot;delete personal task comment&quot;,&quot;delete personal task file&quot;,&quot;delete personal task checklist&quot;,&quot;manage personal task&quot;,&quot;edit personal task&quot;,&quot;edit personal task comment&quot;,&quot;edit personal task checklist&quot;,&quot;manage personal task time tracking&quot;],&quot;automatish&quot;:[&quot;access automatish&quot;]}", "200", "2025-07-31 06:28:31", "2025-07-31 06:28:31"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 115}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.322563, "duration": 0.00845, "duration_str": "8.45ms", "memory": 0, "memory_str": null, "filename": "PricingPlanController.php:115", "source": "app/Http/Controllers/PricingPlanController.php:115", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=115", "ajax": false, "filename": "PricingPlanController.php", "line": "115"}, "connection": "radhe_same", "start_percent": 70.267, "width_percent": 29.733}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans/create\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7", "success": "Pricing plan created successfully.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pricing-plans", "status_code": "<pre class=sf-dump id=sf-dump-390707624 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-390707624\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-761016810 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-761016810\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">My Bot AI Flow</span>\"\n  \"<span class=sf-dump-key>plan_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">subaccount</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8900</span>\"\n  \"<span class=sf-dump-key>duration</span>\" => \"<span class=sf-dump-str title=\"6 characters\">yearly</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>max_users</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>max_customers</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>max_vendors</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>max_clients</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>storage_limit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>sort_order</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>modules</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>crm</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>hrm</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>account</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>project</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>pos</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>support</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>user_management</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>booking</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>omx_flow</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>personal_tasks</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>automatish</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>permissions</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>crm</span>\" => <span class=sf-dump-note>array:41</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">create lead</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">create deal</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"19 characters\">create form builder</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"15 characters\">create contract</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"15 characters\">create pipeline</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">create stage</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"13 characters\">create source</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"12 characters\">create label</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"18 characters\">view crm dashboard</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"9 characters\">view lead</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"9 characters\">view deal</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"17 characters\">view form builder</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"13 characters\">view contract</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"13 characters\">view pipeline</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">view stage</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"11 characters\">view source</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"10 characters\">view label</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">delete lead</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"11 characters\">delete deal</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"19 characters\">delete form builder</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"15 characters\">delete contract</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"15 characters\">delete pipeline</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"12 characters\">delete stage</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"13 characters\">delete source</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">delete label</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"11 characters\">manage lead</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"9 characters\">edit lead</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">manage deal</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"9 characters\">edit deal</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"19 characters\">manage form builder</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"17 characters\">edit form builder</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"15 characters\">manage contract</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"13 characters\">edit contract</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"15 characters\">manage pipeline</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"13 characters\">edit pipeline</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"12 characters\">manage stage</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"10 characters\">edit stage</span>\"\n      <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"13 characters\">manage source</span>\"\n      <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"11 characters\">edit source</span>\"\n      <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"12 characters\">manage label</span>\"\n      <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"10 characters\">edit label</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>hrm</span>\" => <span class=sf-dump-note>array:56</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">create employee</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">create set salary</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">create pay slip</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"12 characters\">create leave</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"17 characters\">create attendance</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"15 characters\">create training</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"12 characters\">create award</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"13 characters\">create branch</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"17 characters\">create department</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"18 characters\">create designation</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"20 characters\">create document type</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"18 characters\">view hrm dashboard</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"13 characters\">view employee</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"15 characters\">view set salary</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"13 characters\">view pay slip</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"10 characters\">view leave</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"15 characters\">view attendance</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">view training</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"10 characters\">view award</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"11 characters\">view branch</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"15 characters\">view department</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"16 characters\">view designation</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"18 characters\">view document type</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"15 characters\">delete employee</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"17 characters\">delete set salary</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"15 characters\">delete pay slip</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"12 characters\">delete leave</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"17 characters\">delete attendance</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"15 characters\">delete training</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">delete award</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"13 characters\">delete branch</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"17 characters\">delete department</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"18 characters\">delete designation</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"20 characters\">delete document type</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"13 characters\">edit employee</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n      <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"15 characters\">edit set salary</span>\"\n      <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"15 characters\">manage pay slip</span>\"\n      <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"13 characters\">edit pay slip</span>\"\n      <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n      <span class=sf-dump-index>41</span> => \"<span class=sf-dump-str title=\"10 characters\">edit leave</span>\"\n      <span class=sf-dump-index>42</span> => \"<span class=sf-dump-str title=\"17 characters\">manage attendance</span>\"\n      <span class=sf-dump-index>43</span> => \"<span class=sf-dump-str title=\"15 characters\">edit attendance</span>\"\n      <span class=sf-dump-index>44</span> => \"<span class=sf-dump-str title=\"15 characters\">manage training</span>\"\n      <span class=sf-dump-index>45</span> => \"<span class=sf-dump-str title=\"13 characters\">edit training</span>\"\n      <span class=sf-dump-index>46</span> => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n      <span class=sf-dump-index>47</span> => \"<span class=sf-dump-str title=\"10 characters\">edit award</span>\"\n      <span class=sf-dump-index>48</span> => \"<span class=sf-dump-str title=\"13 characters\">manage branch</span>\"\n      <span class=sf-dump-index>49</span> => \"<span class=sf-dump-str title=\"11 characters\">edit branch</span>\"\n      <span class=sf-dump-index>50</span> => \"<span class=sf-dump-str title=\"17 characters\">manage department</span>\"\n      <span class=sf-dump-index>51</span> => \"<span class=sf-dump-str title=\"15 characters\">edit department</span>\"\n      <span class=sf-dump-index>52</span> => \"<span class=sf-dump-str title=\"18 characters\">manage designation</span>\"\n      <span class=sf-dump-index>53</span> => \"<span class=sf-dump-str title=\"16 characters\">edit designation</span>\"\n      <span class=sf-dump-index>54</span> => \"<span class=sf-dump-str title=\"20 characters\">manage document type</span>\"\n      <span class=sf-dump-index>55</span> => \"<span class=sf-dump-str title=\"18 characters\">edit document type</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>account</span>\" => <span class=sf-dump-note>array:88</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">create customer</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">create vender</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"14 characters\">create invoice</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">create bill</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"14 characters\">create revenue</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">create payment</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"15 characters\">create proposal</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"11 characters\">create goal</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"18 characters\">create credit note</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"17 characters\">create debit note</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"19 characters\">create bank account</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"20 characters\">create bank transfer</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"18 characters\">create transaction</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"23 characters\">create chart of account</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"20 characters\">create journal entry</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"13 characters\">create assets</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"28 characters\">create constant custom field</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"22 characters\">view account dashboard</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"13 characters\">view customer</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"11 characters\">view vender</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"12 characters\">view invoice</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"9 characters\">view bill</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"12 characters\">view revenue</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"12 characters\">view payment</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"13 characters\">view proposal</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"9 characters\">view goal</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"16 characters\">view credit note</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"15 characters\">view debit note</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"17 characters\">view bank account</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"18 characters\">view bank transfer</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"16 characters\">view transaction</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"21 characters\">view chart of account</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"18 characters\">view journal entry</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"11 characters\">view assets</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"26 characters\">view constant custom field</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"11 characters\">view report</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"15 characters\">delete customer</span>\"\n      <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"13 characters\">delete vender</span>\"\n      <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"14 characters\">delete invoice</span>\"\n      <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"11 characters\">delete bill</span>\"\n      <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"14 characters\">delete revenue</span>\"\n      <span class=sf-dump-index>41</span> => \"<span class=sf-dump-str title=\"14 characters\">delete payment</span>\"\n      <span class=sf-dump-index>42</span> => \"<span class=sf-dump-str title=\"15 characters\">delete proposal</span>\"\n      <span class=sf-dump-index>43</span> => \"<span class=sf-dump-str title=\"11 characters\">delete goal</span>\"\n      <span class=sf-dump-index>44</span> => \"<span class=sf-dump-str title=\"18 characters\">delete credit note</span>\"\n      <span class=sf-dump-index>45</span> => \"<span class=sf-dump-str title=\"17 characters\">delete debit note</span>\"\n      <span class=sf-dump-index>46</span> => \"<span class=sf-dump-str title=\"19 characters\">delete bank account</span>\"\n      <span class=sf-dump-index>47</span> => \"<span class=sf-dump-str title=\"20 characters\">delete bank transfer</span>\"\n      <span class=sf-dump-index>48</span> => \"<span class=sf-dump-str title=\"18 characters\">delete transaction</span>\"\n      <span class=sf-dump-index>49</span> => \"<span class=sf-dump-str title=\"23 characters\">delete chart of account</span>\"\n      <span class=sf-dump-index>50</span> => \"<span class=sf-dump-str title=\"20 characters\">delete journal entry</span>\"\n      <span class=sf-dump-index>51</span> => \"<span class=sf-dump-str title=\"13 characters\">delete assets</span>\"\n      <span class=sf-dump-index>52</span> => \"<span class=sf-dump-str title=\"28 characters\">delete constant custom field</span>\"\n      <span class=sf-dump-index>53</span> => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n      <span class=sf-dump-index>54</span> => \"<span class=sf-dump-str title=\"13 characters\">edit customer</span>\"\n      <span class=sf-dump-index>55</span> => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n      <span class=sf-dump-index>56</span> => \"<span class=sf-dump-str title=\"11 characters\">edit vender</span>\"\n      <span class=sf-dump-index>57</span> => \"<span class=sf-dump-str title=\"14 characters\">manage invoice</span>\"\n      <span class=sf-dump-index>58</span> => \"<span class=sf-dump-str title=\"12 characters\">edit invoice</span>\"\n      <span class=sf-dump-index>59</span> => \"<span class=sf-dump-str title=\"11 characters\">manage bill</span>\"\n      <span class=sf-dump-index>60</span> => \"<span class=sf-dump-str title=\"9 characters\">edit bill</span>\"\n      <span class=sf-dump-index>61</span> => \"<span class=sf-dump-str title=\"14 characters\">manage revenue</span>\"\n      <span class=sf-dump-index>62</span> => \"<span class=sf-dump-str title=\"12 characters\">edit revenue</span>\"\n      <span class=sf-dump-index>63</span> => \"<span class=sf-dump-str title=\"14 characters\">manage payment</span>\"\n      <span class=sf-dump-index>64</span> => \"<span class=sf-dump-str title=\"12 characters\">edit payment</span>\"\n      <span class=sf-dump-index>65</span> => \"<span class=sf-dump-str title=\"15 characters\">manage proposal</span>\"\n      <span class=sf-dump-index>66</span> => \"<span class=sf-dump-str title=\"13 characters\">edit proposal</span>\"\n      <span class=sf-dump-index>67</span> => \"<span class=sf-dump-str title=\"11 characters\">manage goal</span>\"\n      <span class=sf-dump-index>68</span> => \"<span class=sf-dump-str title=\"9 characters\">edit goal</span>\"\n      <span class=sf-dump-index>69</span> => \"<span class=sf-dump-str title=\"18 characters\">manage credit note</span>\"\n      <span class=sf-dump-index>70</span> => \"<span class=sf-dump-str title=\"16 characters\">edit credit note</span>\"\n      <span class=sf-dump-index>71</span> => \"<span class=sf-dump-str title=\"17 characters\">manage debit note</span>\"\n      <span class=sf-dump-index>72</span> => \"<span class=sf-dump-str title=\"15 characters\">edit debit note</span>\"\n      <span class=sf-dump-index>73</span> => \"<span class=sf-dump-str title=\"19 characters\">manage bank account</span>\"\n      <span class=sf-dump-index>74</span> => \"<span class=sf-dump-str title=\"17 characters\">edit bank account</span>\"\n      <span class=sf-dump-index>75</span> => \"<span class=sf-dump-str title=\"20 characters\">manage bank transfer</span>\"\n      <span class=sf-dump-index>76</span> => \"<span class=sf-dump-str title=\"18 characters\">edit bank transfer</span>\"\n      <span class=sf-dump-index>77</span> => \"<span class=sf-dump-str title=\"18 characters\">manage transaction</span>\"\n      <span class=sf-dump-index>78</span> => \"<span class=sf-dump-str title=\"16 characters\">edit transaction</span>\"\n      <span class=sf-dump-index>79</span> => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n      <span class=sf-dump-index>80</span> => \"<span class=sf-dump-str title=\"21 characters\">edit chart of account</span>\"\n      <span class=sf-dump-index>81</span> => \"<span class=sf-dump-str title=\"20 characters\">manage journal entry</span>\"\n      <span class=sf-dump-index>82</span> => \"<span class=sf-dump-str title=\"18 characters\">edit journal entry</span>\"\n      <span class=sf-dump-index>83</span> => \"<span class=sf-dump-str title=\"13 characters\">manage assets</span>\"\n      <span class=sf-dump-index>84</span> => \"<span class=sf-dump-str title=\"11 characters\">edit assets</span>\"\n      <span class=sf-dump-index>85</span> => \"<span class=sf-dump-str title=\"28 characters\">manage constant custom field</span>\"\n      <span class=sf-dump-index>86</span> => \"<span class=sf-dump-str title=\"26 characters\">edit constant custom field</span>\"\n      <span class=sf-dump-index>87</span> => \"<span class=sf-dump-str title=\"13 characters\">manage report</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>project</span>\" => <span class=sf-dump-note>array:51</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">create project</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"19 characters\">create project task</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">create timesheet</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"17 characters\">create bug report</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">create milestone</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"20 characters\">create project stage</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"25 characters\">create project task stage</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"22 characters\">create project expense</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"15 characters\">create activity</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"17 characters\">create bug status</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"22 characters\">view project dashboard</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"12 characters\">view project</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">view project task</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"14 characters\">view timesheet</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"15 characters\">view bug report</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">view milestone</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"18 characters\">view project stage</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"23 characters\">view project task stage</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"20 characters\">view project expense</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"13 characters\">view activity</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"15 characters\">view bug status</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"14 characters\">delete project</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"19 characters\">delete project task</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"16 characters\">delete timesheet</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"17 characters\">delete bug report</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"16 characters\">delete milestone</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"20 characters\">delete project stage</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"25 characters\">delete project task stage</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"22 characters\">delete project expense</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"15 characters\">delete activity</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"17 characters\">delete bug status</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">manage project</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"12 characters\">edit project</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"19 characters\">manage project task</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"17 characters\">edit project task</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"16 characters\">manage timesheet</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"14 characters\">edit timesheet</span>\"\n      <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"17 characters\">manage bug report</span>\"\n      <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"15 characters\">edit bug report</span>\"\n      <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"16 characters\">manage milestone</span>\"\n      <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"14 characters\">edit milestone</span>\"\n      <span class=sf-dump-index>41</span> => \"<span class=sf-dump-str title=\"20 characters\">manage project stage</span>\"\n      <span class=sf-dump-index>42</span> => \"<span class=sf-dump-str title=\"18 characters\">edit project stage</span>\"\n      <span class=sf-dump-index>43</span> => \"<span class=sf-dump-str title=\"25 characters\">manage project task stage</span>\"\n      <span class=sf-dump-index>44</span> => \"<span class=sf-dump-str title=\"23 characters\">edit project task stage</span>\"\n      <span class=sf-dump-index>45</span> => \"<span class=sf-dump-str title=\"22 characters\">manage project expense</span>\"\n      <span class=sf-dump-index>46</span> => \"<span class=sf-dump-str title=\"20 characters\">edit project expense</span>\"\n      <span class=sf-dump-index>47</span> => \"<span class=sf-dump-str title=\"15 characters\">manage activity</span>\"\n      <span class=sf-dump-index>48</span> => \"<span class=sf-dump-str title=\"13 characters\">edit activity</span>\"\n      <span class=sf-dump-index>49</span> => \"<span class=sf-dump-str title=\"17 characters\">manage bug status</span>\"\n      <span class=sf-dump-index>50</span> => \"<span class=sf-dump-str title=\"15 characters\">edit bug status</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:37</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">create warehouse</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">create quotation</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">create pos</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"14 characters\">create barcode</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">create product</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"23 characters\">create product category</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"19 characters\">create product unit</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"18 characters\">view pos dashboard</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"14 characters\">view warehouse</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"13 characters\">view purchase</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"14 characters\">view quotation</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"8 characters\">view pos</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"12 characters\">view product</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"21 characters\">view product category</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"17 characters\">view product unit</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"16 characters\">delete warehouse</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"15 characters\">delete purchase</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"16 characters\">delete quotation</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"10 characters\">delete pos</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"14 characters\">delete product</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"23 characters\">delete product category</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"19 characters\">delete product unit</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"14 characters\">edit warehouse</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"15 characters\">manage purchase</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">edit purchase</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"16 characters\">manage quotation</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"14 characters\">edit quotation</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"8 characters\">edit pos</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">manage product</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"12 characters\">edit product</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"23 characters\">manage product category</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"21 characters\">edit product category</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"19 characters\">manage product unit</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"17 characters\">edit product unit</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>support</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">create support</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"22 characters\">view support dashboard</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">view support</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"14 characters\">delete support</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"14 characters\">manage support</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">edit support</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"13 characters\">reply support</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>user_management</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">create user</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">create client</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">view user</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">view client</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"11 characters\">delete user</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">delete client</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">edit user</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"11 characters\">edit client</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>booking</span>\" => <span class=sf-dump-note>array:25</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">create booking</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"18 characters\">create appointment</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"26 characters\">create appointment booking</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"21 characters\">create calendar event</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"22 characters\">view booking dashboard</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">view booking</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"12 characters\">show booking</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"16 characters\">view appointment</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">show appointment</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"24 characters\">view appointment booking</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"24 characters\">show appointment booking</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"19 characters\">view calendar event</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"19 characters\">show calendar event</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"14 characters\">delete booking</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"18 characters\">delete appointment</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"26 characters\">delete appointment booking</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"21 characters\">delete calendar event</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"14 characters\">manage booking</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"12 characters\">edit booking</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"18 characters\">manage appointment</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"16 characters\">edit appointment</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"26 characters\">manage appointment booking</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"24 characters\">edit appointment booking</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"21 characters\">manage calendar event</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"19 characters\">edit calendar event</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>omx_flow</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">access omx flow</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">whatsapp_flows</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">whatsapp_orders</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">campaigns</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">chatbot</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>personal_tasks</span>\" => <span class=sf-dump-note>array:14</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">create personal task</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"28 characters\">create personal task comment</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"25 characters\">create personal task file</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"30 characters\">create personal task checklist</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"18 characters\">view personal task</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"20 characters\">delete personal task</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"28 characters\">delete personal task comment</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"25 characters\">delete personal task file</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"30 characters\">delete personal task checklist</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"20 characters\">manage personal task</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"18 characters\">edit personal task</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"26 characters\">edit personal task comment</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"28 characters\">edit personal task checklist</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"34 characters\">manage personal task time tracking</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>automatish</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">access automatish</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">16234</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/pricing-plans/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Illlc2o2UlJIWlN0SjBDdkV6dFE0N2c9PSIsInZhbHVlIjoiRDlSQmx1SjFheFRLTjJBNEdNbGF5dE1vY3BQMlgrbXRLT1lyTmpza0R2Ny94ZUtEOWNUaEtWTElycTBRMHhMb1dCbldSbU0vQlVBUEpGekN5NVRBM1pDUEk4QUFVempBNG1jbkUxWGRUenV4eW5udnczWDhxVEZPajlzSW9adFMzOUJHeGFUUG9WWENJeWlzMVpwU3RoYWU2dlQybHdzY05xQXZuZ2pKVjdld21Fei91RmRiWG1EUTJwVlgvUzdhYjBIVXpBZW1VK2Q1Vmp4Q1FnZTljUVo1NitsK0l5QUQ3d0dWVGxheFA2Nk5ydndraURIcUtpYXZwMUVlNnZlTUEvenhQWUx2UlJBa1lSajB3MUo1ajU5R3J1NE4yakc5Z1dvQmVtSmlsVkVRZ3k3OGNjanF6QnA4OUpsVmd5VmwxVTlmSnVPajYzbXBJV1BYWlNlSGhLS1FxOURXSTQ0TW5HN0hPdklTbURTaVJ4aDJYcVVCaVFXL2lvN0p2M09Vcm4zbG5ibjVFcG5lSlp0UE4vdkd4dWhzL09Qa3RxcXQ3cW95Y2R0dTdNMjhGOW1kMTB2NS92T3pvMkVGcHVJVTk1cDlEQ1RCWEVWemdqR1RoTGdHUTkrRUtmZkFuSnI3aTRkZ1BXMmx0ZTkrdnM2clJ0OUJiTmdVZ014MUp2cFQiLCJtYWMiOiI4NjllMTdmNGM5NjQ5YTk4ZTcwMzY2Yzk0YmI0MmRiYmYwNTFjYTZmZmVjZGRlMWI2Nzg0YmVlYzBkMDEwMDcwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImNZdUlqbVYwSjZzNWxlVDFSTjRZWkE9PSIsInZhbHVlIjoiMEtuTnYvSzNtWUlSaGhlSGpjQXord2JPR3RmN0svVlVlM3VRTVFuWW1mVE9EcGNBUXV0aDBSWDdqSUpabFIvTm9SSHZiZi9HWGNydEN4UXNMeHZtek9qU29BLzlvemFhOHF6U284aHBqdVBHV2U4Y2IveUZNM2xVRU1KSWFOZXhqMGdFajRqTnhjZ1laUXVFenZFYzQ4ZWwzMVE5SVViTGpISEF2RkdjTmVXY3R6dGhXaFRKSDRmRkZzUmc5cllQMGR4b3NKZ0F3S1p1WTZnOGMyK1NuNWJMbERmZzFQbE1FT29vOWJqM1RHZVJ2T0luWVBWV2VIeGVOb3ZHVlJ6VFc5VVl0SmV1cWkyc1o2TXpCMUZsSHZabVlSVzZTSFhFRjNmMlk0ZERVS3gvSjN2c2ZPbDRXRk5JSUN0Q3JSRkZhNVdXUWkyWFd0VEtLeU9OWWJ4c2pIR2svU3RuTEQ2dE9lV3FSaGFMODdOSkNia21aQTdOVVQ3UUltWkU4QnFKWEQ0V1N4YWJ1L0N4K2VPMTlrMEc1M0FZajNnRWNzc2h6Zkp1YWl1YndaSVhRMmZGaVBtWVJRZDlGNi94ZnhsWDZJbG51TWtyeGRVby9aZXB5eWc2L2VzaUtFei9hQnlOMHltd3kyTEZMNlBFZEl4Q1VPQjRiejJHNlp6ZlhmM0QiLCJtYWMiOiIwOWViMWY4ODY1YThkMGQ1OTk2ZDc5MDM3NjJlZDQyZjYwZmQ1MzZkNzFjNGNmZDRhMjM3NjU4M2IzOWRjNmJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-325414364 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-325414364\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2129888799 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:28:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikh1ZGZmQzRQcTNyY0dhVkRDVDFIQVE9PSIsInZhbHVlIjoiY0hIdVdqSWpnbkdCTllibzBXaHlsU3RDdi9qNTJjcnZEYkVqK3lOZnBYSDlXV1lxK20zSVlyMUJnQUZkVmtocXAzalBRYndlMW91UlVTZE1XODhxUDFLak93dGpUQW94QSswLzJrSkowaHBsRHpPZXZzK1d4Zmdka2xOSWFrSVBmUC9uUkU1bysybVhnRTZYZVhGQVlhcWtDc2tKWHIrTGJScGNBdERwN0JhU1dOSW80VVYzbFpYWGdZbW9icERPQkZxcHRSbnFoMUU1ejdraDdJek5sckJIVVRCempSbkh3eTFtOXVUcTRobzdhMVdMT3ZwaHVHdjdLVXY4MHdtRFpWdHFsRDlVL1NMYk4xN0RheEdKNnVCcGNQNE95VS9PZ1FJUDlQL1VabFlmUkllTjEwMnorUDVjOVMvYmhRU0ZKRWhCRXlLTFRkM1VGZ3Vwckt4YzVuK2FwUHhGQ2Y2aFFta09GendGTUdWQ0hwM3Q1NVU0YmFnZEVsOWlMUUgvQzZkYThmRm9aUjE2aklYREk0YUVoSHp5WmJpcjdCQXdaUURGb1A0Y1BXVm92UEEvMndEdkcxS21oSzROYktSUnovbFdYcnVIMGVCTnJjQXYvWENaVzNwL2txdXI5K3M5ZkoxaDdkVlRMQmt2QkxMZG1YL0tUSmVQNmR2czJPY1EiLCJtYWMiOiI5NTY5Y2QwNGY5YjBhZDVmZDZmYjZiMzQ3YjgxZDE0MzE2YzkzN2E0ZTk1OTM4YzA5MGQyYWQzOGY4ZWZlNDg0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:28:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkVSS3V0VlVYVEE2VEZKblJPY1lEQXc9PSIsInZhbHVlIjoibUZlclhXa2dxRmtVd1pHQ1dJVWJSVEVQY3VrckRFVGh1b1JsMVpreGV5UC8yNFk3ZWNIdHNwdU1sMjZVTVp5Ri9LbUJpbElVZittSUp4VXB2N3VmdG5iSFliM2QrMVdYcHoxYmpsVUtFVmIrWmVVVjhUTFREQ3pnTWl5K0FlK0VySjZqM2tCUVNaZG83dXpERkdlWDU4SDlXZW1PN2FlWDBicUY1eFJxUzBKZkFGRDZxUWo5eUNaRkdtemY3OS9qRmYrVTVaUlhleXgrN1BYZjN0UlZRYTBJVzl1Zm1GMVNwVytaKzYzaVhGWWV6KzlvRzdDK2RncThIdWdkZjI2bnZwT1gxN3RHd1VocVgrbGhmanRvN3hBSzEzWEFMTlltc20ydlJFOTNkT0JmUldldG5ZYkYxdHZzRkhVaHk1VTZhQmZoeXl2U1ZrSlRWbHRXc3dpa2dsYXZVT1ExMEVSMnF2WURlNzRScmNHQVRaRFpkUlVvdmFWbEJac0RjTENFREdjZFVvak1tTFhQSGx0dHNoQ0tHc0ZtdWMwczV2aXdsWitqVmhvUXZnZUlLeVlpL0dkSUdzZ3FXVlgxV2pFZ3p6dno1T0JGM1U3b2l3MDVyTnlCZEJ4NmZudmQ3bGk4WnNDd1hDVGpiRjR0Uk52UlJCMnIxdWtQb0JGU1pZNzEiLCJtYWMiOiI3N2QwNWQxZGY1NjYxNzI0YjIxODNhYTgwOTA2MTk3OWU3MTEwNDBiNGYxZDFmYjc3YzBjMTk5MmJlODY3OGQ5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:28:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikh1ZGZmQzRQcTNyY0dhVkRDVDFIQVE9PSIsInZhbHVlIjoiY0hIdVdqSWpnbkdCTllibzBXaHlsU3RDdi9qNTJjcnZEYkVqK3lOZnBYSDlXV1lxK20zSVlyMUJnQUZkVmtocXAzalBRYndlMW91UlVTZE1XODhxUDFLak93dGpUQW94QSswLzJrSkowaHBsRHpPZXZzK1d4Zmdka2xOSWFrSVBmUC9uUkU1bysybVhnRTZYZVhGQVlhcWtDc2tKWHIrTGJScGNBdERwN0JhU1dOSW80VVYzbFpYWGdZbW9icERPQkZxcHRSbnFoMUU1ejdraDdJek5sckJIVVRCempSbkh3eTFtOXVUcTRobzdhMVdMT3ZwaHVHdjdLVXY4MHdtRFpWdHFsRDlVL1NMYk4xN0RheEdKNnVCcGNQNE95VS9PZ1FJUDlQL1VabFlmUkllTjEwMnorUDVjOVMvYmhRU0ZKRWhCRXlLTFRkM1VGZ3Vwckt4YzVuK2FwUHhGQ2Y2aFFta09GendGTUdWQ0hwM3Q1NVU0YmFnZEVsOWlMUUgvQzZkYThmRm9aUjE2aklYREk0YUVoSHp5WmJpcjdCQXdaUURGb1A0Y1BXVm92UEEvMndEdkcxS21oSzROYktSUnovbFdYcnVIMGVCTnJjQXYvWENaVzNwL2txdXI5K3M5ZkoxaDdkVlRMQmt2QkxMZG1YL0tUSmVQNmR2czJPY1EiLCJtYWMiOiI5NTY5Y2QwNGY5YjBhZDVmZDZmYjZiMzQ3YjgxZDE0MzE2YzkzN2E0ZTk1OTM4YzA5MGQyYWQzOGY4ZWZlNDg0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:28:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkVSS3V0VlVYVEE2VEZKblJPY1lEQXc9PSIsInZhbHVlIjoibUZlclhXa2dxRmtVd1pHQ1dJVWJSVEVQY3VrckRFVGh1b1JsMVpreGV5UC8yNFk3ZWNIdHNwdU1sMjZVTVp5Ri9LbUJpbElVZittSUp4VXB2N3VmdG5iSFliM2QrMVdYcHoxYmpsVUtFVmIrWmVVVjhUTFREQ3pnTWl5K0FlK0VySjZqM2tCUVNaZG83dXpERkdlWDU4SDlXZW1PN2FlWDBicUY1eFJxUzBKZkFGRDZxUWo5eUNaRkdtemY3OS9qRmYrVTVaUlhleXgrN1BYZjN0UlZRYTBJVzl1Zm1GMVNwVytaKzYzaVhGWWV6KzlvRzdDK2RncThIdWdkZjI2bnZwT1gxN3RHd1VocVgrbGhmanRvN3hBSzEzWEFMTlltc20ydlJFOTNkT0JmUldldG5ZYkYxdHZzRkhVaHk1VTZhQmZoeXl2U1ZrSlRWbHRXc3dpa2dsYXZVT1ExMEVSMnF2WURlNzRScmNHQVRaRFpkUlVvdmFWbEJac0RjTENFREdjZFVvak1tTFhQSGx0dHNoQ0tHc0ZtdWMwczV2aXdsWitqVmhvUXZnZUlLeVlpL0dkSUdzZ3FXVlgxV2pFZ3p6dno1T0JGM1U3b2l3MDVyTnlCZEJ4NmZudmQ3bGk4WnNDd1hDVGpiRjR0Uk52UlJCMnIxdWtQb0JGU1pZNzEiLCJtYWMiOiI3N2QwNWQxZGY1NjYxNzI0YjIxODNhYTgwOTA2MTk3OWU3MTEwNDBiNGYxZDFmYjc3YzBjMTk5MmJlODY3OGQ5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:28:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2129888799\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-528405791 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/pricing-plans/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Pricing plan created successfully.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-528405791\", {\"maxDepth\":0})</script>\n"}}