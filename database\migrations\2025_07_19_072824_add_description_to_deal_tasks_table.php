<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('deal_tasks', function (Blueprint $table) {
            // Check if the column doesn't already exist before adding it
            if (!Schema::hasColumn('deal_tasks', 'description')) {
                $table->text('description')->nullable()->after('status');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('deal_tasks', function (Blueprint $table) {
            // Check if the column exists before trying to drop it
            if (Schema::hasColumn('deal_tasks', 'description')) {
                $table->dropColumn('description');
            }
        });
    }
};
