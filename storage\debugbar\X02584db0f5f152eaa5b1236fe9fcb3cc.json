{"__meta": {"id": "X02584db0f5f152eaa5b1236fe9fcb3cc", "datetime": "2025-07-31 05:43:37", "utime": **********.671344, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:43:37] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.659939, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753940614.550005, "end": **********.671416, "duration": 3.121411085128784, "duration_str": "3.12s", "measures": [{"label": "Booting", "start": 1753940614.550005, "relative_start": 0, "end": **********.136335, "relative_end": **********.136335, "duration": 2.586329936981201, "duration_str": "2.59s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.136391, "relative_start": 2.58638596534729, "end": **********.671423, "relative_end": 6.9141387939453125e-06, "duration": 0.5350320339202881, "duration_str": "535ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48366280, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.027840000000000004, "accumulated_duration_str": "27.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2727818, "duration": 0.006900000000000001, "duration_str": "6.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 24.784}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.3235462, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 24.784, "width_percent": 4.167}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.337899, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 28.951, "width_percent": 3.879}, {"sql": "select * from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.349203, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 32.83, "width_percent": 3.807}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.391367, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 36.638, "width_percent": 3.915}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.417079, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 40.553, "width_percent": 4.705}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.432278, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 45.259, "width_percent": 5.711}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.449717, "duration": 0.01365, "duration_str": "13.65ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 50.97, "width_percent": 49.03}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-165989631 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InZ6eUdSQy9SeDM2VlpWa3VHNE5wcFE9PSIsInZhbHVlIjoiMzIya0VDbldzMnZRTDV6Y0k3UGFlTDJobmdOVVFBMHM0TnkxdlFkZFpiajhEOUt0YXhPbVhoNVF0WS9CZUt4SnJ6T0cvWnFZbUNCSVRFTzNXWGs1dVA2SlN0ZFczdzFYcnVWa3RrOXlDZGlSZ2RXTUhVUmVBU25vNFJ6VExZcGR3blRCU1lPRXhrT1BiTzgxVFduWnVZUi9aUTlaaCszTzlxd3ZoRFVnT1gvUXNjbTh3WWYwZ2plR1M2Vm1CNWdUbjZLSnpldURzZWlTczJQcHRHa2J5U2VteUpSQVJuQ2l0N2tSaDlBZktDWVB1V1Qrd2t3UW1sa3JXRE1MZ2N1K2FSMng3cDNOMDB6ZVI1N0hxYVhwZ0FKQTZiQ09qTUtXd3FiVi9iNVNIdUJTSXdQblRsd3FwcnAxWnJpeTExOW9xc3BkSTFSTlY0aUx4L00yYmN0Q2MvUVBNUG9qc3VIUjJwKzRPb3NUbmtwK004ckpRajVobU9sb2dJN21NR1lNWXdpdkVFSkp4ZG15MDhJU1k3ak5SQ1hINWZvWC9OTzM3RGg2QSs0a0daeVFmMWk5Z29RY0M2MGdIQnlEOUxnZGM1V2k0WDB5bC91aDRVcWs5MFhKamUycGtzV3R2bGREQWNsZDZPS3VpL1BGQXpreWJUeS9WVDNTdHZRQ1dSRkgiLCJtYWMiOiI5Yjc4NGEwZWM3ZjczNTRiZDkwNGI4MTdmODhmM2RiZTc4MzRhYzk4OTYyYjU0Y2IzMzZmNTI1M2E5OTFmNGNjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im1Yd0k0cE5QbVV4SGQxbnRrZ0JzTnc9PSIsInZhbHVlIjoiWVlpdGY1a0NhQy9Wdk54cVlEemhlVlVqNE8wU1BOb0tQTTB3N093RDZVTUozRWZKcEFTS01OcW04bjRabFVyMDV2emJvSWNETkxaWWROcEc5L0Q4SlVrRjdkV0tneW1TNmVXbmZ6dFJNc2xZOXhHNW4wSFB0TzdLaVRxTmdJVDdMTVA0V1JQRzhYOHVzTmExUVk3NmllN01uT0dQVHBicDFwQ1crR1l1U2FHbU1pWWswYS9iQXJyUUdxMkt1OVh0NEVBbWI4OHpVY3hkY0h3OTNhZ3pRTUlwSE11UUpmWi9xbzFDMU1GUXRyT1hXUVFRM25TRDRKNmdBY3BTUWFmRTdPbmt0TDkzUkg2a2k4MlpoVzFiOGJ2elhSQnFULy9UcWNjbkJqeHRuVUsvYTRGaWVXdzJXOXliWnRnSTdmaDVQa2N6Qm8xUUVYSTd6VHJjV00wZ3g3OEZkR2dxaXRFVXpXb25lUXl3a0R2RHp2dXV4V3NzME5oQ1ZYRTZDNU02eTkrRlY4NlhQWUtRc3FZU1NvZlFVUDJONFlmZkg3WS9JVUhtQTFBY2VVL0N0OFZ0T1FoZHZNY3NXZkt3c0ZpSVNkeHBldkhkT2N4eHpEait6TUtXY1dwWXhqZUY2MjVjZSsveXlUKzhCenU2MVBGcmlYOFBINVBKSWFZczlUZWQiLCJtYWMiOiI4YWU3MDA1MDNkYjdhYjNhNGY3ZDYzNTliNDMxMmYzY2VjM2JmNDZjYjE3MWM4ODk5MDdjNzZhY2Q0NDA3MDBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-165989631\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2003989574 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5LJND5bUclZnLzzClCMEaxKzZ5Yt8AAmGjprz0I</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003989574\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-69084005 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:43:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJPam8vRHB6VmxYdmxJSFhpTXFVUkE9PSIsInZhbHVlIjoiamZjQ2lGNFVGdjlJMklpcGRLTzJvUDlTWitKT0pOdlA4L2trUmo5VUdaRG5VS1QxczN5ZlV3ZkdvZHRnVWFUUWEzNG9oSUpVMExzdW56S3RHelNvaTVzS0NZL2lSL2JIMGl4dHJ3WlA0OEtZRFhLblZHeUJ3YnV4ZzVidXBJWndUQTdNY0dwRVJhSUg4Q0ZUakJlZDdUZ2NqdGgvWUllZXNYVzRyQkVDSnBiR2RsMGV4a0RPbmtOVjErakNtM1pvRFJxVkdMaFBxUjZ6MXRXeVQraEgrWmRYZWVRM0wxeDBwbkhUMkxNNE9uamVoQkdhRWUyeHNFSmVHK3FOZndSekk1Mktuakl4MThRaVh6b1pnTXgyRUxqb3Eza2xnUnFKdWNWZjJoeUFHVzBZOVNjdU9GdUtBWFJMQ2tmaGNLVDBpcktNNjdPaXRQMEc5OVdMUDRicGw5QTBLVmxuaFJnNFZZWDZSWmJOVmVXWXdib3JRUWp5UVF4Z3ROOUlEYkllSHZIMU1zZENZa21wUXFVUzFUZ3JKQVFUQlNaQnRXdFY4UFhiY3VFT2lSaUJoOFZUaVh6Tmxrcnd4dExTV0JXQzJoK0pxc0tXZXpNay9IVDRYcmtmNkp4cWJkRzRVQ0JnRDdwWEdybk1sK1B2c090ZkM2alViVVI1bUk0N2xRRFQiLCJtYWMiOiI5YjIxMGNkODZjYzgwODdjOTA5N2FjMjA2NTI5ZjQ0MzcwZjRmNDFiZWZhNGZhNmQ3YmNjYmY3YTcxNDIyM2FjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:43:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlpDY3B4UVE0OGZDZXdsYkF2cWVDRFE9PSIsInZhbHVlIjoicFhhTWpSajFkU2VHa2ZRd2NQaC9TQ0FHbjhlMHc5NFROckNhOHZrdlNMT1kwYStGdEVXd0NjZEVJUTdkb3pNaUE0S0krcFZtSkdmdzBaQU4vanVuZktrVUdVRDZiQlh4RHl2RTNlT1NISXBjNFAwUy8ybEt3cmg5YXRHQndyZERvL1R4RVYrOTd5OGpFVnJDZ0RUTWQ5UEhWODhYYUNTendzeG5wNTBIc1hRZnUvNkp2QU9LS0ZHaXFSdnJxZkNubytTYlBOTGpPLzYya0p1dUNzMXJPdVFzc09sOU5ld3VuQ28zNldva0dobEc2MHdEQlczb3I4ZlFQUFE2QTBZc0x5ME16ODFJTDJOOVZ6TjM1Z1U1dzQ4b2x1OThiSU9JVlB5NVdCUm9WOXdESTYzQlp3dVgvN0xlZ0lFa1BkQnl6SWhqSTRaSTF3ei94ajlYclNlb2xEYkhGUFV0Q3psNSsycjlrRS84T29tWkR2NHIzM0locFFhSGNacjhLQnd0c2MwZGJlMlN4bXA1ck1JZFl2SU9FUC9SOHk4dUpaUEhiWXJQSWw5bml6SXMrWktSZXdENzBSNEJJaTVTU0JZR21GWUZ6ZStFRHJaU050ZTJUWm5HSWVVM2pIUnlTRkVQL3h6QUR3UHh2TGgxUldpR3BCd0N0RnRQTG4ycGszTDIiLCJtYWMiOiJhYzk1MmYwNWZlNmY1YzdmOTk3ZTljZjQwZTQ3MTYyZDUxZjdkNTJiODMyZjFmYjYyN2I3OWMyNDZiYzUxMzhmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:43:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJPam8vRHB6VmxYdmxJSFhpTXFVUkE9PSIsInZhbHVlIjoiamZjQ2lGNFVGdjlJMklpcGRLTzJvUDlTWitKT0pOdlA4L2trUmo5VUdaRG5VS1QxczN5ZlV3ZkdvZHRnVWFUUWEzNG9oSUpVMExzdW56S3RHelNvaTVzS0NZL2lSL2JIMGl4dHJ3WlA0OEtZRFhLblZHeUJ3YnV4ZzVidXBJWndUQTdNY0dwRVJhSUg4Q0ZUakJlZDdUZ2NqdGgvWUllZXNYVzRyQkVDSnBiR2RsMGV4a0RPbmtOVjErakNtM1pvRFJxVkdMaFBxUjZ6MXRXeVQraEgrWmRYZWVRM0wxeDBwbkhUMkxNNE9uamVoQkdhRWUyeHNFSmVHK3FOZndSekk1Mktuakl4MThRaVh6b1pnTXgyRUxqb3Eza2xnUnFKdWNWZjJoeUFHVzBZOVNjdU9GdUtBWFJMQ2tmaGNLVDBpcktNNjdPaXRQMEc5OVdMUDRicGw5QTBLVmxuaFJnNFZZWDZSWmJOVmVXWXdib3JRUWp5UVF4Z3ROOUlEYkllSHZIMU1zZENZa21wUXFVUzFUZ3JKQVFUQlNaQnRXdFY4UFhiY3VFT2lSaUJoOFZUaVh6Tmxrcnd4dExTV0JXQzJoK0pxc0tXZXpNay9IVDRYcmtmNkp4cWJkRzRVQ0JnRDdwWEdybk1sK1B2c090ZkM2alViVVI1bUk0N2xRRFQiLCJtYWMiOiI5YjIxMGNkODZjYzgwODdjOTA5N2FjMjA2NTI5ZjQ0MzcwZjRmNDFiZWZhNGZhNmQ3YmNjYmY3YTcxNDIyM2FjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:43:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlpDY3B4UVE0OGZDZXdsYkF2cWVDRFE9PSIsInZhbHVlIjoicFhhTWpSajFkU2VHa2ZRd2NQaC9TQ0FHbjhlMHc5NFROckNhOHZrdlNMT1kwYStGdEVXd0NjZEVJUTdkb3pNaUE0S0krcFZtSkdmdzBaQU4vanVuZktrVUdVRDZiQlh4RHl2RTNlT1NISXBjNFAwUy8ybEt3cmg5YXRHQndyZERvL1R4RVYrOTd5OGpFVnJDZ0RUTWQ5UEhWODhYYUNTendzeG5wNTBIc1hRZnUvNkp2QU9LS0ZHaXFSdnJxZkNubytTYlBOTGpPLzYya0p1dUNzMXJPdVFzc09sOU5ld3VuQ28zNldva0dobEc2MHdEQlczb3I4ZlFQUFE2QTBZc0x5ME16ODFJTDJOOVZ6TjM1Z1U1dzQ4b2x1OThiSU9JVlB5NVdCUm9WOXdESTYzQlp3dVgvN0xlZ0lFa1BkQnl6SWhqSTRaSTF3ei94ajlYclNlb2xEYkhGUFV0Q3psNSsycjlrRS84T29tWkR2NHIzM0locFFhSGNacjhLQnd0c2MwZGJlMlN4bXA1ck1JZFl2SU9FUC9SOHk4dUpaUEhiWXJQSWw5bml6SXMrWktSZXdENzBSNEJJaTVTU0JZR21GWUZ6ZStFRHJaU050ZTJUWm5HSWVVM2pIUnlTRkVQL3h6QUR3UHh2TGgxUldpR3BCd0N0RnRQTG4ycGszTDIiLCJtYWMiOiJhYzk1MmYwNWZlNmY1YzdmOTk3ZTljZjQwZTQ3MTYyZDUxZjdkNTJiODMyZjFmYjYyN2I3OWMyNDZiYzUxMzhmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:43:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-69084005\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}