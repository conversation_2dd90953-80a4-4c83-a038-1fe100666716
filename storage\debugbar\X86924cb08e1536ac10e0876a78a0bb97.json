{"__meta": {"id": "X86924cb08e1536ac10e0876a78a0bb97", "datetime": "2025-07-31 05:13:50", "utime": **********.931041, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753938829.927894, "end": **********.931073, "duration": 1.0031788349151611, "duration_str": "1s", "measures": [{"label": "Booting", "start": 1753938829.927894, "relative_start": 0, "end": **********.847797, "relative_end": **********.847797, "duration": 0.9199028015136719, "duration_str": "920ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.847824, "relative_start": 0.****************, "end": **********.931077, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "83.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cbAw0naOAG3nIZ9JvBUJgDMTCg4w52EonV0uPfJJ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1405230155 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1405230155\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1630689172 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1630689172\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1405282600 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1405282600\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1498762659 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1498762659\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-742194692 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-742194692\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1962247722 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:13:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVOek5nNDQwdDdobzdYekd5V1VYcnc9PSIsInZhbHVlIjoiUmd6Uk5uTTF0YUpYUDlmS2RVa0RLWWhZa1U1ekdvdjY2OXFGTEFDckFIMjAxVzhUc2hFNDRuT0pkOVAyR2VMOU8vL0Frd3hySTRkZzN2RndSSXNMQWh5VzBLbXFxU296SC9FNzdiTGFiY3c5ZS9mR2U1RTAvcGFGSlcreHdvUm9hRjIvbFpnbEJTWjF4MDNveWZzTXBBVkcwS1lsRHF4UTF1eVhjMHlFSUkvUWUzUkFrVm9jelhscXFKekFDaGx6K28ralZVdVp3MVlmbGVxS2tUQmR1R2d0MHlJcFN2UWRhVm5HNHVwNnZ1cnMzTHNqSnZRTHNzSFgzNUxKMjlNOHZzUWU5SzFwUWZCRDRuTHdJMUxXOGNWR1VxWjFKVW1IU2lOTVZ0S05nNmQwQmp1eFRGcFJUdVM3WWpVOHM3VGVDd0RSSXJLRmVQTWE3TUhNTk1kUCtxdWJXRXQ1ay9RT2RyelFzSGNETnppMEF6bG9IWEYyNFgrMmJsMTFwUmczK0FQcVFoVHRTUEoyVXVCMG5SWThqRTVhbERRVkhCc1k1Z0MwUmlGQVFFQzdISndDN3lxMldJbGVYOWI3SXhXNys0anI3TDNndkhsdWxIUnJ1bDAxUXJ2a0M0clBiTEFPcHJZbGczZ3YwY0pTbE5Fb3ArZ2xGenB0UTBEYWJveWYiLCJtYWMiOiIyN2RlZGUyZThlY2M5YTIzNGFhYmZhY2Y1NmMxNjVhMTQxYWE4NGI4NDkwNTk3NDYyZTQ0YTE3YTEzZmUxY2U4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:13:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImtNc1FhOWRpQkd2WGFOR21TN0NIclE9PSIsInZhbHVlIjoibWhwQkMxNXlyV3paL3BhUUt0ZjZMVm1Penl3a1MrQkZ3bnpCcUw1UU9iZ3pRR2ZRcjh3VXlOUUhZL29BbEJiOGFBR1pjSWkwckxVeVhlMC9oOWFuVFlrdncyeXRHRVF2TjRPQm94RVpaRjFIWlJWRnVRWkExVDdQWG5hdkRPWHhhYXVKZGY4aS9CeitrMk00YUlSTkZDb1NTS0pQajBzQ2VpMDNWc2twTEJWUXN1N2xQMDB3N2dBTGZEUTc5bytZYnpCMU5yZGFQYTVpOGZHOVdGcU9zeU95Q1VWSTlEQnp1T05DOGhqMXh5RGJ4MkZqaDBOd2VlTitjYk9mOGgwWkxSRllhMHhTOG1mV2hNeEs4M0txZkFaTm5HS3JXRUVhVmJIcFFtZUczK1JsV3JNblZ3c3FTWFA4M3JoTEQ2dWIwSmtseHdEbS9VSzkxNlAzc0xaWmJDYjZMRFQ3VHRKZ0JoUFB6Rkw3ODdJWkk1aC9JeFFzdWRoeVpjWmtkcGtzTjQ0YVI4VHFtQU94TThVOHNIeVlDb2xYMUxTU0IvRjZRZzRpSE1OdkxmVnNuWjdaUGVka0tibkRzOXVidnl5SDZDNHUzYUV2VTJhU1ZpU2dnQmlpSTZFdDI2RE8vMXB4QVc0T21PVXVSTnphTHpybzM2SGpaUStia21oRGJGUkciLCJtYWMiOiJhMDQ4ZTM0YjIyOWFkNjdiMjJlOGFkYjFmOTFjZDBiMjY3NmRjYzViOGExNDczNTZlNWE0NjUyNjZiMjk0MDg2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:13:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVOek5nNDQwdDdobzdYekd5V1VYcnc9PSIsInZhbHVlIjoiUmd6Uk5uTTF0YUpYUDlmS2RVa0RLWWhZa1U1ekdvdjY2OXFGTEFDckFIMjAxVzhUc2hFNDRuT0pkOVAyR2VMOU8vL0Frd3hySTRkZzN2RndSSXNMQWh5VzBLbXFxU296SC9FNzdiTGFiY3c5ZS9mR2U1RTAvcGFGSlcreHdvUm9hRjIvbFpnbEJTWjF4MDNveWZzTXBBVkcwS1lsRHF4UTF1eVhjMHlFSUkvUWUzUkFrVm9jelhscXFKekFDaGx6K28ralZVdVp3MVlmbGVxS2tUQmR1R2d0MHlJcFN2UWRhVm5HNHVwNnZ1cnMzTHNqSnZRTHNzSFgzNUxKMjlNOHZzUWU5SzFwUWZCRDRuTHdJMUxXOGNWR1VxWjFKVW1IU2lOTVZ0S05nNmQwQmp1eFRGcFJUdVM3WWpVOHM3VGVDd0RSSXJLRmVQTWE3TUhNTk1kUCtxdWJXRXQ1ay9RT2RyelFzSGNETnppMEF6bG9IWEYyNFgrMmJsMTFwUmczK0FQcVFoVHRTUEoyVXVCMG5SWThqRTVhbERRVkhCc1k1Z0MwUmlGQVFFQzdISndDN3lxMldJbGVYOWI3SXhXNys0anI3TDNndkhsdWxIUnJ1bDAxUXJ2a0M0clBiTEFPcHJZbGczZ3YwY0pTbE5Fb3ArZ2xGenB0UTBEYWJveWYiLCJtYWMiOiIyN2RlZGUyZThlY2M5YTIzNGFhYmZhY2Y1NmMxNjVhMTQxYWE4NGI4NDkwNTk3NDYyZTQ0YTE3YTEzZmUxY2U4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:13:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImtNc1FhOWRpQkd2WGFOR21TN0NIclE9PSIsInZhbHVlIjoibWhwQkMxNXlyV3paL3BhUUt0ZjZMVm1Penl3a1MrQkZ3bnpCcUw1UU9iZ3pRR2ZRcjh3VXlOUUhZL29BbEJiOGFBR1pjSWkwckxVeVhlMC9oOWFuVFlrdncyeXRHRVF2TjRPQm94RVpaRjFIWlJWRnVRWkExVDdQWG5hdkRPWHhhYXVKZGY4aS9CeitrMk00YUlSTkZDb1NTS0pQajBzQ2VpMDNWc2twTEJWUXN1N2xQMDB3N2dBTGZEUTc5bytZYnpCMU5yZGFQYTVpOGZHOVdGcU9zeU95Q1VWSTlEQnp1T05DOGhqMXh5RGJ4MkZqaDBOd2VlTitjYk9mOGgwWkxSRllhMHhTOG1mV2hNeEs4M0txZkFaTm5HS3JXRUVhVmJIcFFtZUczK1JsV3JNblZ3c3FTWFA4M3JoTEQ2dWIwSmtseHdEbS9VSzkxNlAzc0xaWmJDYjZMRFQ3VHRKZ0JoUFB6Rkw3ODdJWkk1aC9JeFFzdWRoeVpjWmtkcGtzTjQ0YVI4VHFtQU94TThVOHNIeVlDb2xYMUxTU0IvRjZRZzRpSE1OdkxmVnNuWjdaUGVka0tibkRzOXVidnl5SDZDNHUzYUV2VTJhU1ZpU2dnQmlpSTZFdDI2RE8vMXB4QVc0T21PVXVSTnphTHpybzM2SGpaUStia21oRGJGUkciLCJtYWMiOiJhMDQ4ZTM0YjIyOWFkNjdiMjJlOGFkYjFmOTFjZDBiMjY3NmRjYzViOGExNDczNTZlNWE0NjUyNjZiMjk0MDg2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:13:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1962247722\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1624667658 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cbAw0naOAG3nIZ9JvBUJgDMTCg4w52EonV0uPfJJ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1624667658\", {\"maxDepth\":0})</script>\n"}}