{"__meta": {"id": "X08c44116cc5da148b42d5bc314cc69f2", "datetime": "2025-07-31 05:19:36", "utime": **********.45085, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939175.310488, "end": **********.450878, "duration": 1.1403899192810059, "duration_str": "1.14s", "measures": [{"label": "Booting", "start": 1753939175.310488, "relative_start": 0, "end": **********.344981, "relative_end": **********.344981, "duration": 1.0344929695129395, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.344999, "relative_start": 1.****************, "end": **********.450881, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rGZEaZYAerpatpvunUDqehePhERAB47H6uDh6riB", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-919500769 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-919500769\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1369346341 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1369346341\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1936991878 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1936991878\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-759697890 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-759697890\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1559012724 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1559012724\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1896677160 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:19:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFScnFhQkFlYUVFeWQzcktHMXNPbHc9PSIsInZhbHVlIjoiTXBzN3U1L0REbXRRYm1sZ2kxRFpnbEVVMjBrMmhvZ3M3aHM3YmJHblZ1ck9xcExyakM3YlVEOC9aTHl5MzJmSi8vT2FMcnpQYndNSGJ3azRJbysyaHc5QmxwVDRrNk5uNUtRc1lmQjRhTGVyU3BBdlN0Qmo3dkdkczl0SENKdEkxWFRCMG5zbWd4RUlLd1ZuUW1pNExsSTBJWkZMdWlSb3RhYVF3WVJVR0dSdjlucEFBaFpZcS9IdHZFK0t4bmU5WUc0Y2FoV3JWUGNBWUpuL3AzNGFKWFllY241ZGxTUHQvMXdYNE9QU1gzUmltNUJBZ3BrV25LbllCM01BOHB2OW5nRDJYQkZZWUZhRTFOaWFlM1FRb1Jvcks4ZkVJa2l1YTdjN3BRZGdoR0xYQ2wvV3Zwcm82VkFpZnRqdmVuVjNEUEhqUE84TXplZitMUjJPWlVYZzBETk9sdlIxMFIxR25jbTEzS20yWlowZDAwdlo3a25RSU5DVFptT2U0NHROLy9qZWFDcW9ReXdFUXFoN0laTWRpbnB0aWEvNS9aMVpyalFidTV5REVtSmNBQVhudVQzVmVMTm4yeDdvTmFQTnRDdFNVVHlkc0c3WEU5K2ZLT3ZqVjkyZFAzRDBLdGtmNnVlTFR3WXczanZMNHUrbXdhM0lJREJsMklxQ3QzZ04iLCJtYWMiOiJlYjYxOWRhODgwZGFmMzU1YmIwMGYyY2UwMzQzYjA3ODkyYjY1NzY1YmFkNTgxMTFlYWQxYmM0YzVhMGJmNzI5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:19:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InJEQkpyRnVodDNmSFJoQmJNSlVBM1E9PSIsInZhbHVlIjoiQ0tsYUoybDJ5dTVQMm5SMkI5aTBlblVkZEZRQlVhb3dCRld2SWhqNG1vK1R4SXF5NTNiYkdrYk1YWVQwNTFDMFA3ZFd5b3hYMVJyQU9KdTlOZTlmaGxzTTJnTzRFcU54eXVQTWV0SndoR3gvQTNjZ3FLZE02SnhLem9PSU00dmNZRXdUdU9ramUzUE44MFMycGxCcE1kNCtybThxUGs0UVBuMXNldXBzQTkvNHI0SHYwS1B3b0YxQnJ3M0VsMGFDK1JwQzA0ci9nN1ZIWnVSazhJcEF5dGhja0I4MFBDQTNQeWNZWHhwR2paOGdmd3NFMC9KS3J2SXpOSEkrbEtHTjFJVGRVRGhqNEFFeThqdC9MWGZiNkVWMzNsdlhvWjFhOWpxUTB2V0prMXF0by85WHBOMVBtcjhPV2djb2VhaTBlQk9ZVGVMNTZjc2FGQjRtYmJ5UHZ3cGtBdmo4LzVhaEVHNW9ZYXF1OS9QaXhuaWxSb2l5dTFhUFdvQ3ZZZEhLWUVXdlYyVDV4dVV3SVRKeTlxVkFJdmRzQVBTSDdsdkh0OHd1OFZGV1ZDeXJVenkybGlPNWV4SGtNdjFhQUxjc0l1M3FkbHNyajBsNU9jK1lGelhwSXlNZUVxN25lRmVLbjFqQ2xtaTFkMnNjbGkxYVUvMStQWDB2L3paTUNFV0siLCJtYWMiOiJkOGRjMmM3NWFmODVlYjlhNWE0Zjc4MWZkMWRmMTI5YjI1M2YyZGQ1MjMxOGMzMWIxNjJjMDFiZWQ0NjQ4ZmIxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:19:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFScnFhQkFlYUVFeWQzcktHMXNPbHc9PSIsInZhbHVlIjoiTXBzN3U1L0REbXRRYm1sZ2kxRFpnbEVVMjBrMmhvZ3M3aHM3YmJHblZ1ck9xcExyakM3YlVEOC9aTHl5MzJmSi8vT2FMcnpQYndNSGJ3azRJbysyaHc5QmxwVDRrNk5uNUtRc1lmQjRhTGVyU3BBdlN0Qmo3dkdkczl0SENKdEkxWFRCMG5zbWd4RUlLd1ZuUW1pNExsSTBJWkZMdWlSb3RhYVF3WVJVR0dSdjlucEFBaFpZcS9IdHZFK0t4bmU5WUc0Y2FoV3JWUGNBWUpuL3AzNGFKWFllY241ZGxTUHQvMXdYNE9QU1gzUmltNUJBZ3BrV25LbllCM01BOHB2OW5nRDJYQkZZWUZhRTFOaWFlM1FRb1Jvcks4ZkVJa2l1YTdjN3BRZGdoR0xYQ2wvV3Zwcm82VkFpZnRqdmVuVjNEUEhqUE84TXplZitMUjJPWlVYZzBETk9sdlIxMFIxR25jbTEzS20yWlowZDAwdlo3a25RSU5DVFptT2U0NHROLy9qZWFDcW9ReXdFUXFoN0laTWRpbnB0aWEvNS9aMVpyalFidTV5REVtSmNBQVhudVQzVmVMTm4yeDdvTmFQTnRDdFNVVHlkc0c3WEU5K2ZLT3ZqVjkyZFAzRDBLdGtmNnVlTFR3WXczanZMNHUrbXdhM0lJREJsMklxQ3QzZ04iLCJtYWMiOiJlYjYxOWRhODgwZGFmMzU1YmIwMGYyY2UwMzQzYjA3ODkyYjY1NzY1YmFkNTgxMTFlYWQxYmM0YzVhMGJmNzI5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:19:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InJEQkpyRnVodDNmSFJoQmJNSlVBM1E9PSIsInZhbHVlIjoiQ0tsYUoybDJ5dTVQMm5SMkI5aTBlblVkZEZRQlVhb3dCRld2SWhqNG1vK1R4SXF5NTNiYkdrYk1YWVQwNTFDMFA3ZFd5b3hYMVJyQU9KdTlOZTlmaGxzTTJnTzRFcU54eXVQTWV0SndoR3gvQTNjZ3FLZE02SnhLem9PSU00dmNZRXdUdU9ramUzUE44MFMycGxCcE1kNCtybThxUGs0UVBuMXNldXBzQTkvNHI0SHYwS1B3b0YxQnJ3M0VsMGFDK1JwQzA0ci9nN1ZIWnVSazhJcEF5dGhja0I4MFBDQTNQeWNZWHhwR2paOGdmd3NFMC9KS3J2SXpOSEkrbEtHTjFJVGRVRGhqNEFFeThqdC9MWGZiNkVWMzNsdlhvWjFhOWpxUTB2V0prMXF0by85WHBOMVBtcjhPV2djb2VhaTBlQk9ZVGVMNTZjc2FGQjRtYmJ5UHZ3cGtBdmo4LzVhaEVHNW9ZYXF1OS9QaXhuaWxSb2l5dTFhUFdvQ3ZZZEhLWUVXdlYyVDV4dVV3SVRKeTlxVkFJdmRzQVBTSDdsdkh0OHd1OFZGV1ZDeXJVenkybGlPNWV4SGtNdjFhQUxjc0l1M3FkbHNyajBsNU9jK1lGelhwSXlNZUVxN25lRmVLbjFqQ2xtaTFkMnNjbGkxYVUvMStQWDB2L3paTUNFV0siLCJtYWMiOiJkOGRjMmM3NWFmODVlYjlhNWE0Zjc4MWZkMWRmMTI5YjI1M2YyZGQ1MjMxOGMzMWIxNjJjMDFiZWQ0NjQ4ZmIxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:19:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1896677160\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-166129854 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rGZEaZYAerpatpvunUDqehePhERAB47H6uDh6riB</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-166129854\", {\"maxDepth\":0})</script>\n"}}