{"__meta": {"id": "Xa721bb289d7dd14ff3fe84fc87cff275", "datetime": "2025-07-31 06:24:50", "utime": **********.261477, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943087.822994, "end": **********.261553, "duration": 2.438559055328369, "duration_str": "2.44s", "measures": [{"label": "Booting", "start": 1753943087.822994, "relative_start": 0, "end": **********.076611, "relative_end": **********.076611, "duration": 2.25361704826355, "duration_str": "2.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.076647, "relative_start": 2.***************, "end": **********.261558, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "185ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "UYp5KUwWvGBtCN1JKQwoGOJQ2hI1kiQRgvleMMfh", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-90778045 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-90778045\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-534955430 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-534955430\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1434990382 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1434990382\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1746273450 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1746273450\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1100176163 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1100176163\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-184847555 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:24:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IncrK2wzdDB4OWg0NWFLR2czN2R1OHc9PSIsInZhbHVlIjoiNHFPYkU2eXhoWmpKRUlocEFZNlhYUDJsR3BpdVhQSzBta2JCNjNhQ0lYeFpiQnVibXJhWXp5QUd4RVdaSnR3SEdnV1Z0cTlVN0JNckpOak5GN0RMc2VKVGl2TjlocllWbFV1dkptWmpSNlV4QnRzeHQxSWVjTFhGa3IrMTV0NGY4eHFVb1MxUDRRNXFNZ2pEakR1SGxCVXY3UWltK3RKcHl6SldydE92MmJQQjVtQnhXbFJtbStEajAzUnduL3NVclJoQTBsV2F6d2oveUI2M1pGTzlPVUNrbWRVMGVUZGdWMk0xKzRiMm54MHZvaEtGQ29nRGxQSGhUZFdhRXpGYlcvcGpYTU5tNmdKNVRnb1RIdlB5WlRiUUVHRGE3SmYzWlZ2NldOdlJQMytKZytKT2NwWEowOGxVM3Rid2dHNlN3anZldFFWdXpjYnJuVlVXSDRBai92UkVaYjg1aXdjWVdEZExyZXpqYVNBTEluMTBWMzhxc1dyUUhNRWZkZ3J3enZ6MHU1TWFvU0xuTjR6cmR5cWIxelZpSEpuNU05ZVVNMHVSbTdwWitQeEFkZm5QVVpDbFpwUEIwN3B4UUNIWm04WlVMUnhCMUtGZ0c1VmdySkVxeE54OS85ZFI0Z3R4bWNUdkVZSlgrK3QrNGM3cTFxUURhMjdKMjE5c0dzcTEiLCJtYWMiOiJlZjU4N2QyMjRmYzViZmUyZDg4MzgzODY0OGZhZWIxN2U2ZDk0NGI1ZmE2YTc1YzFlYTcyNjRlYTliNmQ0MTAxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:24:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkZTVjlNcW1BaXRZZ2pGUGZuRmdYblE9PSIsInZhbHVlIjoiblFaODkzeTBHSW90alY5Qk5yejllbTJFT1ZZNlJrd1lmNjdxbGVVNjcvdjVnUFJPRElKSFlQQ3UzbVc3eHZ1T1VFRFVCVlBaa0s4aE9EUlc4YTVwR2pRN0VseGpWcFo3WVVYbWs3OWZSdW5hZU96Y0ZRbXcvd3VrdXlKa2dTUkZOYnN6WTcwN2U3Y0pXKzhDVlNWVDY3SXJmM1JCU3YvSlJtVEZMRXZLVEYydU5pRVRTT0lPenJEUmtvTlpJSlNITWFSMXNCTkhoTDhDMFUyZ1M4elhaZzhLUFFBYW10REhxK050OExjaC82cDcraXBjMklka3R3eVBTSE45cDZDYTVLZ2M0Tk1mWUdzUVQrSjZ2eTgrbzBQdzlmNUlKMml5V2lBR2Z3YkhOZWtHdnlLRUluOXV5RHNYekdhdFQycDkvVktPVW1LNnVWaWI3RitHeHRHc284Z25GdExWM0hzYjRRK2pZdXF0TFVPUHRrbHlWcEpzdVQrQlRqWXJrNXRjUXhaeDdoR1o5TzBiRDRZQ1RJNGFzaE9YNUVLeEovRlVQckZ0OXZxODA2V2UxV21YeStHbXZja1FlR1dLMmU3WnErNnBKZ1F4V2RrcTRETDhSb2YwM3BieVlwUFkxRXAwK09uTFJ0OFZwNng0djFMQm01VENYUUFlKzRyaWpvKy8iLCJtYWMiOiI4ZGM4NWNlMzk3MzE3ZmVlNjBjM2JmYzFkMzY1MDMwZThiMDhjNTkwYzlkNDBiMDRjMTBhYTE3ZmViZWI0ZmYxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:24:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IncrK2wzdDB4OWg0NWFLR2czN2R1OHc9PSIsInZhbHVlIjoiNHFPYkU2eXhoWmpKRUlocEFZNlhYUDJsR3BpdVhQSzBta2JCNjNhQ0lYeFpiQnVibXJhWXp5QUd4RVdaSnR3SEdnV1Z0cTlVN0JNckpOak5GN0RMc2VKVGl2TjlocllWbFV1dkptWmpSNlV4QnRzeHQxSWVjTFhGa3IrMTV0NGY4eHFVb1MxUDRRNXFNZ2pEakR1SGxCVXY3UWltK3RKcHl6SldydE92MmJQQjVtQnhXbFJtbStEajAzUnduL3NVclJoQTBsV2F6d2oveUI2M1pGTzlPVUNrbWRVMGVUZGdWMk0xKzRiMm54MHZvaEtGQ29nRGxQSGhUZFdhRXpGYlcvcGpYTU5tNmdKNVRnb1RIdlB5WlRiUUVHRGE3SmYzWlZ2NldOdlJQMytKZytKT2NwWEowOGxVM3Rid2dHNlN3anZldFFWdXpjYnJuVlVXSDRBai92UkVaYjg1aXdjWVdEZExyZXpqYVNBTEluMTBWMzhxc1dyUUhNRWZkZ3J3enZ6MHU1TWFvU0xuTjR6cmR5cWIxelZpSEpuNU05ZVVNMHVSbTdwWitQeEFkZm5QVVpDbFpwUEIwN3B4UUNIWm04WlVMUnhCMUtGZ0c1VmdySkVxeE54OS85ZFI0Z3R4bWNUdkVZSlgrK3QrNGM3cTFxUURhMjdKMjE5c0dzcTEiLCJtYWMiOiJlZjU4N2QyMjRmYzViZmUyZDg4MzgzODY0OGZhZWIxN2U2ZDk0NGI1ZmE2YTc1YzFlYTcyNjRlYTliNmQ0MTAxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:24:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkZTVjlNcW1BaXRZZ2pGUGZuRmdYblE9PSIsInZhbHVlIjoiblFaODkzeTBHSW90alY5Qk5yejllbTJFT1ZZNlJrd1lmNjdxbGVVNjcvdjVnUFJPRElKSFlQQ3UzbVc3eHZ1T1VFRFVCVlBaa0s4aE9EUlc4YTVwR2pRN0VseGpWcFo3WVVYbWs3OWZSdW5hZU96Y0ZRbXcvd3VrdXlKa2dTUkZOYnN6WTcwN2U3Y0pXKzhDVlNWVDY3SXJmM1JCU3YvSlJtVEZMRXZLVEYydU5pRVRTT0lPenJEUmtvTlpJSlNITWFSMXNCTkhoTDhDMFUyZ1M4elhaZzhLUFFBYW10REhxK050OExjaC82cDcraXBjMklka3R3eVBTSE45cDZDYTVLZ2M0Tk1mWUdzUVQrSjZ2eTgrbzBQdzlmNUlKMml5V2lBR2Z3YkhOZWtHdnlLRUluOXV5RHNYekdhdFQycDkvVktPVW1LNnVWaWI3RitHeHRHc284Z25GdExWM0hzYjRRK2pZdXF0TFVPUHRrbHlWcEpzdVQrQlRqWXJrNXRjUXhaeDdoR1o5TzBiRDRZQ1RJNGFzaE9YNUVLeEovRlVQckZ0OXZxODA2V2UxV21YeStHbXZja1FlR1dLMmU3WnErNnBKZ1F4V2RrcTRETDhSb2YwM3BieVlwUFkxRXAwK09uTFJ0OFZwNng0djFMQm01VENYUUFlKzRyaWpvKy8iLCJtYWMiOiI4ZGM4NWNlMzk3MzE3ZmVlNjBjM2JmYzFkMzY1MDMwZThiMDhjNTkwYzlkNDBiMDRjMTBhYTE3ZmViZWI0ZmYxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:24:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184847555\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-833047626 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UYp5KUwWvGBtCN1JKQwoGOJQ2hI1kiQRgvleMMfh</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-833047626\", {\"maxDepth\":0})</script>\n"}}