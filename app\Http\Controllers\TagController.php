<?php

namespace App\Http\Controllers;

use App\Models\Deal;
use App\Models\Tag;
use App\Models\Lead;
use Illuminate\Http\Request;

class TagController extends Controller
{
    public function __construct()
    {
        $this->middleware(
            [
                'auth',
                'XSS',
            ]
        );
    }

    /**
     * Display a listing of the tags.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(\Auth::user()->can('manage label'))
        {
            $tags = Tag::all(); // Show all tags from tags table

            return view('tags.index')->with('tags', $tags);
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Show the form for creating a new tag.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if(\Auth::user()->can('create label'))
        {
            return view('tags.create');
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    /**
     * Store a newly created tag in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if(\Auth::user()->can('create label'))
        {

            $validator = \Validator::make(
                $request->all(), [
                                   'name' => 'required|max:20',
                               ]
            );

            if($validator->fails())
            {
                $messages = $validator->getMessageBag();

                return redirect()->route('tags.index')->with('error', $messages->first());
            }

            $tag = new Tag();
            $tag->name = $request->name;
            $tag->created_by = \Auth::user()->ownerId();
            $tag->save();

            return redirect()->route('tags.index')->with('success', __('Tag successfully created!'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Display the specified tag.
     *
     * @param \App\Models\Tag $tag
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Tag $tag)
    {
        return redirect()->route('tags.index');
    }

    /**
     * Show the form for editing the specified tag.
     *
     * @param \App\Models\Tag $tag
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(Tag $tag)
    {
        if(\Auth::user()->can('edit label'))
        {
            if($tag->created_by == \Auth::user()->ownerId())
            {
                return view('tags.edit', compact('tag'));
            }
            else
            {
                return response()->json(['error' => __('Permission Denied.')], 401);
            }
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    /**
     * Update the specified tag in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Tag $tag
     *
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Tag $tag)
    {
        if(\Auth::user()->can('edit label'))
        {
            if($tag->created_by == \Auth::user()->ownerId())
            {
                $validator = \Validator::make(
                    $request->all(), [
                                       'name' => 'required|max:20',
                                   ]
                );

                if($validator->fails())
                {
                    $messages = $validator->getMessageBag();

                    return redirect()->route('tags.index')->with('error', $messages->first());
                }

                $tag->name = $request->name;
                $tag->save();

                return redirect()->route('tags.index')->with('success', __('Tag successfully updated!'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Remove the specified tag from storage.
     *
     * @param \App\Models\Tag $tag
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Tag $tag)
    {
        if(\Auth::user()->can('delete label'))
        {
            if($tag->created_by == \Auth::user()->ownerId())
            {
                // Remove this tag ID from all leads that have it assigned
                $leads = Lead::where('created_by', \Auth::user()->ownerId())
                           ->whereNotNull('labels')
                           ->where('labels', '!=', '')
                           ->get();

                foreach($leads as $lead) {
                    if($lead->labels) {
                        $tagIds = explode(',', $lead->labels);
                        // Remove the deleted tag ID from the array
                        $tagIds = array_filter($tagIds, function($id) use ($tag) {
                            return $id != $tag->id;
                        });
                        // Update the lead with the new tags string
                        $lead->labels = !empty($tagIds) ? implode(',', $tagIds) : null;
                        $lead->save();
                    }
                }

                // Remove this tag ID from all deals that have it assigned
                $deals = Deal::where('created_by', \Auth::user()->ownerId())
                           ->whereNotNull('labels')
                           ->where('labels', '!=', '')
                           ->get();

                foreach($deals as $deal) {
                    if($deal->labels) {
                        $tagIds = explode(',', $deal->labels);
                        // Remove the deleted tag ID from the array
                        $tagIds = array_filter($tagIds, function($id) use ($tag) {
                            return $id != $tag->id;
                        });
                        // Update the deal with the new tags string
                        $deal->labels = !empty($tagIds) ? implode(',', $tagIds) : null;
                        $deal->save();
                    }
                }

                $tag->delete();

                return redirect()->route('tags.index')->with('success', __('Tag successfully deleted!'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }
}
