{"__meta": {"id": "Xd895be070c46b6e89be662f1023552a6", "datetime": "2025-07-31 06:29:13", "utime": **********.579732, "method": "GET", "uri": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943351.40987, "end": **********.579781, "duration": 2.1699111461639404, "duration_str": "2.17s", "measures": [{"label": "Booting", "start": 1753943351.40987, "relative_start": 0, "end": **********.393257, "relative_end": **********.393257, "duration": 1.9833869934082031, "duration_str": "1.98s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.393291, "relative_start": 1.****************, "end": **********.579786, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "186ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Q84JAToxWViZRp0VkttX7mY5wOxJxSHM5sI0rdge", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "status_code": "<pre class=sf-dump id=sf-dump-2127041452 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2127041452\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1447806140 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1447806140\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-286524082 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-286524082\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-216436297 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-216436297\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1596883266 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1596883266\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:29:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1RakJtRU4xWTNiT3hvakdMeGt2dlE9PSIsInZhbHVlIjoiNE44ZDM4UDVRSzMrNzJVVnVRenJTdm5FZEphMm5ScldOWW5BRjZaeEJMYU80aUE2ajVucjdVZjZvdjZEVmxMd0JkTEhTUHh3dktMRDRNZ0x0R0RFdXE4QU1VM1c0UEh0emh6UFVsaDBETmc1UXZCekJISGx4bCtibTg1WFlBRExMcmdLQTJtZjZGR3UzZUlXb29BeUYvcHkxR0ZCRVhYTktBY0FVaUhXQjEwRUFGa0ErdHZjb0pTTEgvZ1czMjZBTmdZSEJMUGdzK0tEVlV4S3dHaXR0YjAyT1I0TU5pOEs0WmwzZVNqelZwanQzUlk2VE5hL1BsWjVBZU5Yb3p0K1ZjTm5ZcVpWbXlUQVgvK2lEV3NOV0ZsVno1UlBmU2hqdy92UnFIelFDamFrOUxBRU91VmNpWHFTc0ZVYUlDdGZ1TmFEV2RxeDlERnBxMnF5b2M0cDdFcDRoQk9yb0I1amdRUEI3QUZING1YaHgwTkNUZ2lScTAzZy95ai9CamFEbHZPN3l1blpzSFhkU1h4MlA3YWdLVDV3cUhOUGY4MFJESE12SFMwYmMxdzcrSm96UXVuK2gvS2VhUnMyemRrekFKaXY3NUVDUytQVWRiK3hHazltRzNUek5rZGJ5TkFMTFFVeWdNdWpBenB2YUtabjRqSVV6TVFPMDlKWk5wUFciLCJtYWMiOiI4NGQ3YzNmNTQ0YjU5YjdiMDRhZTkwZjEzMDZmNmI0MDMyMzBkOGYyNDNiM2Y0ODE0MmQyMWExODUyMTYyYjhhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImovK3pGVFJEbXliTXc3NWpHNFhuRnc9PSIsInZhbHVlIjoiVWdLajR1Qk10THQ0T0YrM1l5cmNEQmdvdCs4TXRYRkVLdzNueDRxN3hEZldjTEhTWXl2OTZ5VjlEcGludjJrZHMzN3Y5Qk1TOWhCdVM5UVpaMlQzQXA2RDNvNlpQdkNaSmZRMUZ5dXFvbmJaNXZJWmFyeDY1dnhvai91QkhUOFZETEgva0xZUWhMQjZMT2g3V3JKV2JBRHE2WHdwQzkyU2lLcmZwengxSXhOanVnVW5ReitWNmhtYnV5OGNGYXdNeGlVQ1lRaDFodk5KYm9nWUUwdGp0WUlGMWdEWS94MlRsRE03S0tRQ1JZcTlMN2VmNlJtQXVEc3NpWDFXVlZpT1dBbDZDdzE5QUgwSnRnWER6ZDErM1B6Y0s4MTcvcUlzNjJ5ZzhUcWlZWTFnNUhtZXpSbWc1bHFiSllVK1QvNUxsdnNpVWgrbEJRWGdGa0ZRQmhjVW9mZDdvSnV6SmZvK2N6cnVIWGVpRnhoa1ZMUS9nR2dQb3FySi9SS0NOdmR2S3Nob3BBUU01WmRrbXl4ODhNUU5HN1NaMnRRYmJnZ2Z2dWRvSzE3RGg3TnZMcE8zLzd4NkVFUUd2dEk3Vnh3WHVJeUk1TUJBN0NJTjBUWFB5THpHNTdCRi9pbnpMSGppMkZvc29JWEgyOUUrd3lVUzFtU2RCSlJvelNQSkphVmwiLCJtYWMiOiIxMjM5Njg2NDZiMzY5YTFlNTMzZGQzMjY0YjNkODYxN2FjZTA5ZDgwYTRmZmRlMmQwYTFlM2Q1MDE3N2FhZWMwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1RakJtRU4xWTNiT3hvakdMeGt2dlE9PSIsInZhbHVlIjoiNE44ZDM4UDVRSzMrNzJVVnVRenJTdm5FZEphMm5ScldOWW5BRjZaeEJMYU80aUE2ajVucjdVZjZvdjZEVmxMd0JkTEhTUHh3dktMRDRNZ0x0R0RFdXE4QU1VM1c0UEh0emh6UFVsaDBETmc1UXZCekJISGx4bCtibTg1WFlBRExMcmdLQTJtZjZGR3UzZUlXb29BeUYvcHkxR0ZCRVhYTktBY0FVaUhXQjEwRUFGa0ErdHZjb0pTTEgvZ1czMjZBTmdZSEJMUGdzK0tEVlV4S3dHaXR0YjAyT1I0TU5pOEs0WmwzZVNqelZwanQzUlk2VE5hL1BsWjVBZU5Yb3p0K1ZjTm5ZcVpWbXlUQVgvK2lEV3NOV0ZsVno1UlBmU2hqdy92UnFIelFDamFrOUxBRU91VmNpWHFTc0ZVYUlDdGZ1TmFEV2RxeDlERnBxMnF5b2M0cDdFcDRoQk9yb0I1amdRUEI3QUZING1YaHgwTkNUZ2lScTAzZy95ai9CamFEbHZPN3l1blpzSFhkU1h4MlA3YWdLVDV3cUhOUGY4MFJESE12SFMwYmMxdzcrSm96UXVuK2gvS2VhUnMyemRrekFKaXY3NUVDUytQVWRiK3hHazltRzNUek5rZGJ5TkFMTFFVeWdNdWpBenB2YUtabjRqSVV6TVFPMDlKWk5wUFciLCJtYWMiOiI4NGQ3YzNmNTQ0YjU5YjdiMDRhZTkwZjEzMDZmNmI0MDMyMzBkOGYyNDNiM2Y0ODE0MmQyMWExODUyMTYyYjhhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImovK3pGVFJEbXliTXc3NWpHNFhuRnc9PSIsInZhbHVlIjoiVWdLajR1Qk10THQ0T0YrM1l5cmNEQmdvdCs4TXRYRkVLdzNueDRxN3hEZldjTEhTWXl2OTZ5VjlEcGludjJrZHMzN3Y5Qk1TOWhCdVM5UVpaMlQzQXA2RDNvNlpQdkNaSmZRMUZ5dXFvbmJaNXZJWmFyeDY1dnhvai91QkhUOFZETEgva0xZUWhMQjZMT2g3V3JKV2JBRHE2WHdwQzkyU2lLcmZwengxSXhOanVnVW5ReitWNmhtYnV5OGNGYXdNeGlVQ1lRaDFodk5KYm9nWUUwdGp0WUlGMWdEWS94MlRsRE03S0tRQ1JZcTlMN2VmNlJtQXVEc3NpWDFXVlZpT1dBbDZDdzE5QUgwSnRnWER6ZDErM1B6Y0s4MTcvcUlzNjJ5ZzhUcWlZWTFnNUhtZXpSbWc1bHFiSllVK1QvNUxsdnNpVWgrbEJRWGdGa0ZRQmhjVW9mZDdvSnV6SmZvK2N6cnVIWGVpRnhoa1ZMUS9nR2dQb3FySi9SS0NOdmR2S3Nob3BBUU01WmRrbXl4ODhNUU5HN1NaMnRRYmJnZ2Z2dWRvSzE3RGg3TnZMcE8zLzd4NkVFUUd2dEk3Vnh3WHVJeUk1TUJBN0NJTjBUWFB5THpHNTdCRi9pbnpMSGppMkZvc29JWEgyOUUrd3lVUzFtU2RCSlJvelNQSkphVmwiLCJtYWMiOiIxMjM5Njg2NDZiMzY5YTFlNTMzZGQzMjY0YjNkODYxN2FjZTA5ZDgwYTRmZmRlMmQwYTFlM2Q1MDE3N2FhZWMwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1046055349 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Q84JAToxWViZRp0VkttX7mY5wOxJxSHM5sI0rdge</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046055349\", {\"maxDepth\":0})</script>\n"}}