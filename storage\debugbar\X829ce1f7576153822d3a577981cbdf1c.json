{"__meta": {"id": "X829ce1f7576153822d3a577981cbdf1c", "datetime": "2025-07-31 06:30:47", "utime": **********.14957, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:30:47] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.142916, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943445.221243, "end": **********.149618, "duration": 1.928375005722046, "duration_str": "1.93s", "measures": [{"label": "Booting", "start": 1753943445.221243, "relative_start": 0, "end": 1753943446.937005, "relative_end": 1753943446.937005, "duration": 1.7157621383666992, "duration_str": "1.72s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753943446.93703, "relative_start": 1.7157871723175049, "end": **********.149622, "relative_end": 4.0531158447265625e-06, "duration": 0.21259188652038574, "duration_str": "213ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45918336, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00871, "accumulated_duration_str": "8.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.052354, "duration": 0.00597, "duration_str": "5.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 68.542}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.091248, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 68.542, "width_percent": 12.4}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.103102, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 80.941, "width_percent": 8.84}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.112275, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 89.782, "width_percent": 10.218}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-728065927 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-728065927\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1450660507 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1450660507\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-200719057 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-200719057\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1275951127 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkFMTjBZL1M5OGJRbWRMdzNUV1RnSlE9PSIsInZhbHVlIjoiRDNrU1J1NmZMSHp6Nldadk5BR2dsMEVKNGVFeTFleXVCd1M3Nzk1ekhMZms2Tkw5WWt5SjVPSUM4QXZFS0hMS0p0MDRWaC9aVUwxV0NuYTlReEJFYmhIcy9ubEhzQ29aK2RMd2FkRWdhNGxqWnJhei91YWhrZlpqQ09xRFFMMlFNSU5XQjNrc1pYN05sSTdqMFNTYVJLZ3NTNldBdDFVZnlWOGdjTXNDWWZTekxBL3AwYTBSeUxuVHRSTlUwRitNQ2ZpdVplcXIvQUdEdHJwbDZtSDRFZWxqNzhRUUwvQS9rN2FrdEJkcDdQUGFpdjJyZFZYckhqVE9vRVJueVJnWjZOQXlSUGtOY3VjbnZZRHArT2ZDWHFQeUlMUlkvSlRUMzhYQXhGWVMrSXVDc1JQT0w4WGVjbXJqYW93clZJcU8xUEtjdSs0cDlaR2Y0eStoY09McTRCVkNvbldZWWxQc1J0cFZ4Rm9tUjFtS2lnY2svL2RmOTFGdVUxdVV6M0VjeXJiVkE1K01BNmdjLzRIbDM5d2tJUS92NEFpOGNSLzlvMzF2WGxTanZ6VGlXdFA1bGc1amxNSVlHV3NnNG1MVWlZcGhrNVNHL2xnRDVWMkJ3MGRZYkt3R0xNdDJ1SnljbWV6Smt6S0xHQlVzbkdCU0pUNnNVTWxlazBrOUNNRXgiLCJtYWMiOiJiNmQ0MjUyNjMzYTdiMDNhMjVmMmNiZTU2YWY3ZDFiZjVkYTY4ZmExZGY2MDViYWQ5YTNiYzdjNDc0NWFiNzgyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InpNL2FYSlZ5dU5mcnJzU3F4ckpzTEE9PSIsInZhbHVlIjoiaGR4dU96VDdTSEFLczcreHp4dG9Kb0JqZTRiK3NsVGExaHNXWU5rNlBrOEZSYVQzSHJLNmpaanR0Q3dONEJZLzA3VVZJNDBHSlZVVWZ4L2d2UmxESUpsNEN0dWt0VWJ2YmNveTZYWkl1azZIeHZwcDd6Z0dYM20rZkF6ZmN0Ui9HS08zYUltaUZBZDZmMzN4cWpRM3E5cVZkM0k5OUQvM0cxanNidTQxMW1selZ3YncrVUVySzZudW04aVM1a2h2ZXJ2R2xhWEVLc0FmTEJWWXVoTlJucTFMUmZrd2pJTFV6cmRUY05La2JsVkNFbFZZUnhlMEdOZVRpNjFIanFaUVkzREFoQTFLZG9NbVVuOWxnOEFyRnpzckVUaG1QcGJiSi9BMTVFSW9XZ2l5SzlXakZ5VVp0MWJwZTV5WllZL3c1dmc2Uzk0N2NUV0hoTURDcUNzYk54S3ZCblpkbVBFQWxIdlFyZEY5RnlyUFEvR3YzOElxVkN2ZjVJU0ErQ29TYTI1VUlDRkxmU1J2eXRiV1ZadEhFdk90NUUvZnpzc0VNREJ1dzJua2E1cFVXVWpUai9lQUpwNjBCM1BtVTMrNkRaYXUzZkpnTmlFQU5TY2JLUWplM2ptM1JzVzVJbEhtKzNHZ1NDN1RkcSthbFJFeGNtbXIzSklJajk3ZmNqNkoiLCJtYWMiOiJmMmUwZmZiMDkyNGRiOGMwZWQ0MDZhYjBkMmY1MDMzZTA5NzNmMTYyNmI5OTFlNmQ0YzQ0ZTc4MmU0YjBkZTAzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1275951127\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1236295298 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1236295298\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1580757520 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:30:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllLTHJBcURVd09kdzlvMUVKK1o1dFE9PSIsInZhbHVlIjoiNTJvbVF3bCs2blRwT1FaZ1NLUTZXdk1PTm1GRUZVZ3ExZGg5bTdlZDFNMTRkeWgzbTQzcUpQVTJwbVBLWTlHd0pCRUtiVmJYUWZFaW1kbVVzOTNoZWFzL0Jyb2FTblNncUx4Rm5tejRBQ2dJQ0h6eG5NYmhUMGRqdklOSHNnUVBDcnJ0OFVLWktjTHA5RWxvTXZFVUhUOC9NVS9xZW45YkVqNzN4MXhraFg1ZUpCaUFDMTdzcEZwYkhwWitndTlkWTdORnBwRyttZHFKN3lwdHYvNjVJSEppU2phN0l4RDBSMFVMV2NrNGtGUnVlUEVqTW8vNXRRVFloTDRIVGh0RFR4TkVpZWZnRkpScFBxT0oyNzdKdVlQZ2FPWXFpZzFBSnY5aUhhQVRUeVkwZ25HaTVrQ3RYTFlaRWMvRFE1Rk4xaGRtR3JLeHJLSngwS240QlRyUm5HUkdiR0duaUg0ZzcwUjlyMnpDa0YyVDdIVHRxZDk1anNLTjJnelFNWlVOSkF6RTJxR3ZTUGNYeUd6NTFqQVJITXFITHhSd1k2SHl4UEc0SnpqZXAydWlNWWpkRHZWOEpEV1owbXNFa1NsU0ViUkM2bUZJQTFNcGJVeWJjTUg5cFc1RFg2b0xyUmlNeDFQV0k0QnhKd2RTVjhXVDBvd1BPT0wrSHdhT1I5VUUiLCJtYWMiOiI5ZmM2NWFhMWM2NjMxMzlkOGIxNmYyNmQwOTFmNzBhZjQ5MTg0NWIzZmU2MTU5MDhiNDdiM2Q3NTI4NWJjMWZiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:30:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkdsaE53N3Jqd0lDVGIxc09BK09Qb1E9PSIsInZhbHVlIjoiSStYMmJLVUNlY3Ryc09sM3dURys1eU1lVDh0YkFDRWFBR2NxWkVPdUxvRWZpTmwvT1djUlRCakdKQ2hmZmsvK0tOV25neGhPV3QvVXpWZG9ydnp6SWs2NlkvRnV0bE1MTkdGQWUvSkdQTTh2anp5WTJUWldRSUhjVVczZzhRVjE0VkIveVM2KzVwdnl2N2JleHVJeWJuYmpSMllIeWpodjZybFFXRG9mMFBYTURsVWlkd2hRQ1JMVGdGUkRveWhRaENHOVJ5RnROK01VajVyRFp3OHkxTGpIOGhWN0hFYjFVbllQb1A2bktTTGNsY1NBdzlJeFJubW9KRE9SWXV1RGRuQ1luNGdNRWRsbFpzaUlzZGxNRWV0L0gwQmtvV2M4RzBOUThOZDZ3VitSdGowcnVIV3dXb1phN1dMYjhzOW8xbFYvMTNhRE9jVklLWXlKY28wV0pXdE1FV1VWaEp4UXN5UDA1d3QzTDZHMk5IcnlMRzdya2dmd3M1S2tnelRFM2xUa2pENzZhNmJOVzhQdzVINzVJVHhwQzFBcFcyYW5GQmNHQ1MxTHdXS0xubXRBeWRzNE1Nbk9hTVEwVFREUG8yRW5lTjUzZXVuRWlIVm1Lcjd4NmhvQlpqWnQ2V3luV3J0VmprSEE4S0ZPbmVsR0RXNXhXYnhIYmpBS2paQXciLCJtYWMiOiJkNjNiYjNmMTliZWEzM2E5MzA3MzBmMGZhMGU5OTZmMzc4NDM0ODVmMmIzNjFlZTc0NDk1OWIwODYzMGY2NWQ4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:30:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllLTHJBcURVd09kdzlvMUVKK1o1dFE9PSIsInZhbHVlIjoiNTJvbVF3bCs2blRwT1FaZ1NLUTZXdk1PTm1GRUZVZ3ExZGg5bTdlZDFNMTRkeWgzbTQzcUpQVTJwbVBLWTlHd0pCRUtiVmJYUWZFaW1kbVVzOTNoZWFzL0Jyb2FTblNncUx4Rm5tejRBQ2dJQ0h6eG5NYmhUMGRqdklOSHNnUVBDcnJ0OFVLWktjTHA5RWxvTXZFVUhUOC9NVS9xZW45YkVqNzN4MXhraFg1ZUpCaUFDMTdzcEZwYkhwWitndTlkWTdORnBwRyttZHFKN3lwdHYvNjVJSEppU2phN0l4RDBSMFVMV2NrNGtGUnVlUEVqTW8vNXRRVFloTDRIVGh0RFR4TkVpZWZnRkpScFBxT0oyNzdKdVlQZ2FPWXFpZzFBSnY5aUhhQVRUeVkwZ25HaTVrQ3RYTFlaRWMvRFE1Rk4xaGRtR3JLeHJLSngwS240QlRyUm5HUkdiR0duaUg0ZzcwUjlyMnpDa0YyVDdIVHRxZDk1anNLTjJnelFNWlVOSkF6RTJxR3ZTUGNYeUd6NTFqQVJITXFITHhSd1k2SHl4UEc0SnpqZXAydWlNWWpkRHZWOEpEV1owbXNFa1NsU0ViUkM2bUZJQTFNcGJVeWJjTUg5cFc1RFg2b0xyUmlNeDFQV0k0QnhKd2RTVjhXVDBvd1BPT0wrSHdhT1I5VUUiLCJtYWMiOiI5ZmM2NWFhMWM2NjMxMzlkOGIxNmYyNmQwOTFmNzBhZjQ5MTg0NWIzZmU2MTU5MDhiNDdiM2Q3NTI4NWJjMWZiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:30:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkdsaE53N3Jqd0lDVGIxc09BK09Qb1E9PSIsInZhbHVlIjoiSStYMmJLVUNlY3Ryc09sM3dURys1eU1lVDh0YkFDRWFBR2NxWkVPdUxvRWZpTmwvT1djUlRCakdKQ2hmZmsvK0tOV25neGhPV3QvVXpWZG9ydnp6SWs2NlkvRnV0bE1MTkdGQWUvSkdQTTh2anp5WTJUWldRSUhjVVczZzhRVjE0VkIveVM2KzVwdnl2N2JleHVJeWJuYmpSMllIeWpodjZybFFXRG9mMFBYTURsVWlkd2hRQ1JMVGdGUkRveWhRaENHOVJ5RnROK01VajVyRFp3OHkxTGpIOGhWN0hFYjFVbllQb1A2bktTTGNsY1NBdzlJeFJubW9KRE9SWXV1RGRuQ1luNGdNRWRsbFpzaUlzZGxNRWV0L0gwQmtvV2M4RzBOUThOZDZ3VitSdGowcnVIV3dXb1phN1dMYjhzOW8xbFYvMTNhRE9jVklLWXlKY28wV0pXdE1FV1VWaEp4UXN5UDA1d3QzTDZHMk5IcnlMRzdya2dmd3M1S2tnelRFM2xUa2pENzZhNmJOVzhQdzVINzVJVHhwQzFBcFcyYW5GQmNHQ1MxTHdXS0xubXRBeWRzNE1Nbk9hTVEwVFREUG8yRW5lTjUzZXVuRWlIVm1Lcjd4NmhvQlpqWnQ2V3luV3J0VmprSEE4S0ZPbmVsR0RXNXhXYnhIYmpBS2paQXciLCJtYWMiOiJkNjNiYjNmMTliZWEzM2E5MzA3MzBmMGZhMGU5OTZmMzc4NDM0ODVmMmIzNjFlZTc0NDk1OWIwODYzMGY2NWQ4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:30:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1580757520\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1495367346 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495367346\", {\"maxDepth\":0})</script>\n"}}