{"__meta": {"id": "X613bf1447f4dff8d8c5449ca3405e709", "datetime": "2025-07-31 06:14:09", "utime": **********.119884, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:14:09] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.108795, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753942447.461871, "end": **********.119938, "duration": 1.658066987991333, "duration_str": "1.66s", "measures": [{"label": "Booting", "start": 1753942447.461871, "relative_start": 0, "end": **********.85489, "relative_end": **********.85489, "duration": 1.393019199371338, "duration_str": "1.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.854914, "relative_start": 1.393043041229248, "end": **********.119943, "relative_end": 5.0067901611328125e-06, "duration": 0.2650289535522461, "duration_str": "265ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50612688, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.082051, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.050390000000000004, "accumulated_duration_str": "50.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9604049, "duration": 0.02287, "duration_str": "22.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 45.386}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.0110881, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 45.386, "width_percent": 2.758}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0223591, "duration": 0.02452, "duration_str": "24.52ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 48.144, "width_percent": 48.66}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.053947, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 96.805, "width_percent": 3.195}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-467683877 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-467683877\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-512044289 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-512044289\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-742575607 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-742575607\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-712532189 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im9xT1BBOElDL0pZcTJvZ2ZBOWhoaFE9PSIsInZhbHVlIjoiS0JtS25XNFF3UlQyVFNoVUVrd2xjbDd1cmU5ajVpaVY1THM4ekJ5UVU2eGlnQkp3OGdvSVNhaGhjbnFGZG9qVFlSVklGTTB1b1BnelVZYkRtV2ZsTm1sRDFGOU45QTh2OUtIcWRmeUNYb0Y5aSsxS3ROT3diMkpITHByT3N0UUFBbUtZZGlTTURKMTFaWktrRU9zN2pBUFM1UVJFeS9DRDYxUUpFaENZMGg3TFZFK3pQbFNwUk9MTnpZNlFjMm1Yc0ppZnIremZpR3Q1Vkk3VWJUMTgrQkltOVh1SGhUZGx3cVRlWHVQbkJoNGRlVmpKd1U4Q0N6SFNrR0NrRmtqOTdCaG9ybW9jQXBhN1lPSkg5VXJMY2ErcXE3S283RktZdFJPTUluZ3ZCTW85Y0hvQ3ZaZFVqZ0ducDJYQ1hDc2crVE9ZZzhvRjdpWXNuS2xmck45UUNDOFBaTzM1ZjlTTU92eHNmbzZjM2hLOU8zTTIvRURmVktpMkVjeVBKSnZEQ0VLdnZSRVNnRmJXQmRubzRJL3A3RTY0dDU4ZHRYajNwV0MrSW9sYWhUcHR1UnBrM1dXNTFnL3F5dVNCTThCNzBZeGhDeFRkV3R2Rm14WWxialNkMWU2YllSR1o3dWJDVi8wUHpHamdqL1ZoMm05TlNJV3RqMW5Tajc2R1RWSGgiLCJtYWMiOiIzMDRlNTVhMDZhMWEzZGQ2Y2M2MGJkYzRhMjQwMzFkNmM2ODY4ZDk1NjBiNGM3MjQ4YzEwMmM2ZDMyNWRmODNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imw0dkVKc1VrOTFydnloUjNyakY3K0E9PSIsInZhbHVlIjoiZ2Z1RlBOOEN0d3RsU2pPNkJ4amR5L0dmQ3VmaEc0ZlNzUXQ0aUdnZkpRa2Y2ZGRqeG9McytuRTdSMzlJaE1qNk5LL3p1dHFielNNMmR2c0R6eTRGZjVqSWpMMXlXR3d2WVpsNk5mOVlkVEhybC9CanNNV3J4NnFjeEwyN00wNVczVktoQlZUVXlmRUhKYU1mTDErQ09jUFNNbE45TUFKRmx3bHZqeDRqdm5LcHc3M001M3BrZEsvMXYvaU9EQ2F6eWF6SU8zUHQxMXZHcnB1N3YzVm1Rek1qVTFXQlFES0czVS92TFNWYXZHQXo5RkZDdHVsa1FkUGNBNVBKanVBY1g5Qkx1Wjl0YU1qckIxRmtLYmgyYkxkRTB0c3VCQTBqTjNEOElmQ3pWL28yVUltVVJmWTlaMXF2YW8rRmdVSTg5TjhXMHlpYkRMUVhLby9EYUEzVVRZN2RtNXJab2RaRHRkdDVjVHhCQUxJcHpQakxWNTJSWmN0S3N0TjlEM0xyc01BQWp5MHF2dmVqTGZqK1NyS01kYVdja1F6RER1VmMxeGUyTmRUUTFCdkFzOHlETkZidlN0NmlKK0xjMlgvSWZOS1JsdzRxdlIxMkp6SHlZMldnK2ViU2cyMnkxeStFQXFkYkFrd010SytZdDluOUU0ZUIrQVdSZ2YrUzRTT04iLCJtYWMiOiJjMGExYTAzZjJjZGZhOGI1NmEyNDhlZWQzOTVhMmMxYjc4NzMxNzY1OGYwMGU1ZTQ5NmRhMmI3MTM5NjBlOGQxIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IjIwN0N5R21vMDJLeDBpd3NRZUJXemc9PSIsInZhbHVlIjoiUExVTk02THdMOE1VZ0ppdFQ3LzJPMkVYVnBxcHZDWVJqV3QxY0oyclNIOHFoL3llRHJlblpiaU5KeTlQNEphVGVpRFdCU1l5RDROQzhyc1p4RFRZUDloaVNtTGxLbGlhN28vL0hZL2FsVW05UGtrNEhxSnQvQ1NGMS9yRkNNOXpocnM1T0VYTVZmQlhhZFE5NXcwdUF3R0JtRi90dHBadDdDemNFUjJJNmVQSTU3WUFzeWFPUUlERlBVV3cvSy9PS3Y4NmtIMmh2NmJXUDBwRjA5YVIvYmcrZjA1Z1E5WExYbmMzaG9DWnVFQ0MwNGEybEZhSTFsNndTbWRKcStmdEVvOXdXTVJQSDJVcHFicjZWQTBtZ1A1S2VBaXQxTGxTaUg2Wi9VanJBdE1DSEdJTVFZNCtxbVoyRHphV1hiVzZYekNHM0MraDQvU0NrWkZqYVh0a21LSFVNQ1JZVTFIaUhtNVgxbTV4QzFKZGViTFMvMmxzZTRhT25JM3dJY2Erdk5MdmIwd3VjNTJ4eXRPb0FMMGR4anJNemEweTJya3Z3d09FazBHc0o5VW02Z2IzYWpGWllBS1pEVlVNYk44eFZoU1JzUkJodWVKcmh5TmwzQnV5RW9td2RXVnpQbEdQRGNDMVFIRWJSV0kvQ0gwb2sveUFVSTdKVG8ralQycjIiLCJtYWMiOiI3OTkzNDZkYjgzMjk1MWE1Yzg2ZTc2OGFlNWI2YWRiNDIxNTg0Njk4YWU5MTQyZWY0YzFjN2ExODQ5ZTRlYjViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-712532189\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1029655320 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5LJND5bUclZnLzzClCMEaxKzZ5Yt8AAmGjprz0I</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sUbnPperdA1I3PaRxBpMGV4YsR5AVGq0ERh2LBdJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1029655320\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1927409260 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:14:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJnWGp6bnEzekRaUlBUODVzY2w5WFE9PSIsInZhbHVlIjoiR2UwdU41b1BaaUFndE9xbE5jd0pOajhDZmo2UFR3eXNHd0xzL1FabEF2bFpkVi9MMnBJZllyaEQzQzF5NS9Xb2R1MU1oN29PekZNQWE0OHJJK3RHUUcrRU85OG5vUHUzcjdVNVlIOGE0Umw1V1lkaS9KekIwWnRLWFdhRy92eVFFbGVIdWkvc2M4TERNU0hnWWYrWktNTEppUEIrUm53MGhMU0xGTnhncXF5dzJNbDdEeFFOTjFxeTFzdjBkOVQ4UUV5ZEgzTnd0bGFMOWFNWlpzMk80L0VtUlo3TThhTlNOTXYvckdhbzRJNmJLaUt6MWlJTG05ZnhSdmEwbklSV1hoSkdBRXkxVUNyZHRKR0ZSVjc4QUViZWNrTFBVRThRNjlMbmQzQzFjdUdRUEI2TjlTU0R2dEk5QWV4dzhqSktBcnFqWmRFcGVndzVoZ1lrUGNoNGR5dDhhazdSWG1HeTBJR04xUXQ3cjZyL3VMYjM3UWpzZ1loNlhMMUdvRUtHcUFMTytIUXVJc1Fta1dKTk1aTzVSbkYzOGRYV29naTRtRTZ0NWc5elZKTUVvYUllQStZeE00UW9aNVh0R3BlSmZDcmZVUG1uOGhCcC9XcDcwZ0ltMEM0K0JLOVNWWnVoRDZyVVVsci9wU3hUUEZ4enlYQWJ6R0ZGZVpBaWhuRjMiLCJtYWMiOiJiMmFkZTVhZDc1YmFhNDE4YzE2ODRhMDJiMDczZjQ5MTcxZDhjZjdiOWZmMTY3NmI3MDIwYTUxMTM5ZWViMzFhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:14:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IlhmWU9pS2NxOW9ybWwyaGRONm9nRkE9PSIsInZhbHVlIjoiRWR0NitJQXliM3FubDJ5M1N0WTBhMHdhTEFLUnpjc1NvR0R3eFhybi9mSnZjdjMxTDg3UUsyNUR4d3VyRkhiTHFzZUcwRUIyY3hOay9yaVBVNkdqSUI5SHhWRFYwV0dFVWcxQVpRY2dPblhZckgxRGFqcE5naXBlaFZLRFRYZnUyaGhKMHhmQUs0N09lQ3hxbHVhWWZKM0IvQUxXcG5BZW14enVxRHB3MnBGWm54ZDlXKy9ZZ0lQblVEMVlKZjVoRGZrb0NWUytxcE1SQksyOXFJNGs5Zk1LRklJb0w0Y0tUc2NvazdMaUdPYWRpWEpzaUJsWmFUdjZ2dWxONFhJVHJkK0YwSjROdDltdUpENFBtb1FXUkdiOWJBc2tzSk1MaHBacm9rbXVRMVVwR21seEwrakJOTjF2NklRQjVySGxGL0x4MjNLaGdJSjJLanlKNTZLTGVOYU1JcjJlakVWYUFSY2liWmtQSCthc1E0TDhOOGRMM1g0U3NmWHg5K2MrV25DQm1ueEE5RThjejdEeEVHY2syTVZDT0dTVXVVWXoxSy93aDZocFJHUE5NUGRhSmF0ZFRVM1dBVUlVOFJwQlUzakxoR1RMdStndDVhVEY3S3JndlNyTTdycFQrVkVxNERKcSs2WmVKUUtEZFQ1TFRQdmV4VWJWVHh3VVFlNEEiLCJtYWMiOiI4MzQzMDI5ZGEwNTVkMWE3ZGQ3MTlkNmU2YTA2NDBmZjYxYTAxYzYxMjE2NzZkZDA3YjVmYTU0OTYzOTJhNWM1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:14:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJnWGp6bnEzekRaUlBUODVzY2w5WFE9PSIsInZhbHVlIjoiR2UwdU41b1BaaUFndE9xbE5jd0pOajhDZmo2UFR3eXNHd0xzL1FabEF2bFpkVi9MMnBJZllyaEQzQzF5NS9Xb2R1MU1oN29PekZNQWE0OHJJK3RHUUcrRU85OG5vUHUzcjdVNVlIOGE0Umw1V1lkaS9KekIwWnRLWFdhRy92eVFFbGVIdWkvc2M4TERNU0hnWWYrWktNTEppUEIrUm53MGhMU0xGTnhncXF5dzJNbDdEeFFOTjFxeTFzdjBkOVQ4UUV5ZEgzTnd0bGFMOWFNWlpzMk80L0VtUlo3TThhTlNOTXYvckdhbzRJNmJLaUt6MWlJTG05ZnhSdmEwbklSV1hoSkdBRXkxVUNyZHRKR0ZSVjc4QUViZWNrTFBVRThRNjlMbmQzQzFjdUdRUEI2TjlTU0R2dEk5QWV4dzhqSktBcnFqWmRFcGVndzVoZ1lrUGNoNGR5dDhhazdSWG1HeTBJR04xUXQ3cjZyL3VMYjM3UWpzZ1loNlhMMUdvRUtHcUFMTytIUXVJc1Fta1dKTk1aTzVSbkYzOGRYV29naTRtRTZ0NWc5elZKTUVvYUllQStZeE00UW9aNVh0R3BlSmZDcmZVUG1uOGhCcC9XcDcwZ0ltMEM0K0JLOVNWWnVoRDZyVVVsci9wU3hUUEZ4enlYQWJ6R0ZGZVpBaWhuRjMiLCJtYWMiOiJiMmFkZTVhZDc1YmFhNDE4YzE2ODRhMDJiMDczZjQ5MTcxZDhjZjdiOWZmMTY3NmI3MDIwYTUxMTM5ZWViMzFhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:14:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IlhmWU9pS2NxOW9ybWwyaGRONm9nRkE9PSIsInZhbHVlIjoiRWR0NitJQXliM3FubDJ5M1N0WTBhMHdhTEFLUnpjc1NvR0R3eFhybi9mSnZjdjMxTDg3UUsyNUR4d3VyRkhiTHFzZUcwRUIyY3hOay9yaVBVNkdqSUI5SHhWRFYwV0dFVWcxQVpRY2dPblhZckgxRGFqcE5naXBlaFZLRFRYZnUyaGhKMHhmQUs0N09lQ3hxbHVhWWZKM0IvQUxXcG5BZW14enVxRHB3MnBGWm54ZDlXKy9ZZ0lQblVEMVlKZjVoRGZrb0NWUytxcE1SQksyOXFJNGs5Zk1LRklJb0w0Y0tUc2NvazdMaUdPYWRpWEpzaUJsWmFUdjZ2dWxONFhJVHJkK0YwSjROdDltdUpENFBtb1FXUkdiOWJBc2tzSk1MaHBacm9rbXVRMVVwR21seEwrakJOTjF2NklRQjVySGxGL0x4MjNLaGdJSjJLanlKNTZLTGVOYU1JcjJlakVWYUFSY2liWmtQSCthc1E0TDhOOGRMM1g0U3NmWHg5K2MrV25DQm1ueEE5RThjejdEeEVHY2syTVZDT0dTVXVVWXoxSy93aDZocFJHUE5NUGRhSmF0ZFRVM1dBVUlVOFJwQlUzakxoR1RMdStndDVhVEY3S3JndlNyTTdycFQrVkVxNERKcSs2WmVKUUtEZFQ1TFRQdmV4VWJWVHh3VVFlNEEiLCJtYWMiOiI4MzQzMDI5ZGEwNTVkMWE3ZGQ3MTlkNmU2YTA2NDBmZjYxYTAxYzYxMjE2NzZkZDA3YjVmYTU0OTYzOTJhNWM1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:14:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927409260\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}