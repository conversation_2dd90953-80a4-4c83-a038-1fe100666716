{"__meta": {"id": "X5b80b059a15169636a919581670acc13", "datetime": "2025-07-31 05:38:07", "utime": **********.905832, "method": "GET", "uri": "/users/79/login-with-company", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940285.941453, "end": **********.90589, "duration": 1.9644370079040527, "duration_str": "1.96s", "measures": [{"label": "Booting", "start": 1753940285.941453, "relative_start": 0, "end": **********.707579, "relative_end": **********.707579, "duration": 1.7661259174346924, "duration_str": "1.77s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.707628, "relative_start": 1.7661750316619873, "end": **********.905895, "relative_end": 5.0067901611328125e-06, "duration": 0.19826698303222656, "duration_str": "198ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44693512, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1450\" onclick=\"\">app/Http/Controllers/UserController.php:1450-1474</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.03428, "accumulated_duration_str": "34.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.79622, "duration": 0.03226, "duration_str": "32.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 94.107}, {"sql": "select * from `users` where `users`.`id` = '79' limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\UserController.php", "line": 1452}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.846554, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "UserController.php:1452", "source": "app/Http/Controllers/UserController.php:1452", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1452", "ajax": false, "filename": "UserController.php", "line": "1452"}, "connection": "radhe_same", "start_percent": 94.107, "width_percent": 5.893}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/users/79/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/79/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1662585281 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1662585281\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1175456049 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik5XeVBFSXcvNW5OODE1T3BVRWowSkE9PSIsInZhbHVlIjoiWFRXOHBLTkh5czQzd2lsamR2NUlqcXhuTmZ5MUZpSzBicXBnUzJOMkdLMEQ4dEVrbS9IYTB6STlrVWxHMEhTdjVLN3Z6NTBqekFpMlhWYmZyN0gzZkZrU2tET09EbXYxenhjaW1QTkw1M0RGU2Q1UnkwNUhwbVZzdTd0MjQvYXlwZTZTR0UxQVVqMDhIVkFhZkJvV1JWYXpqbDlNRDFLQzF5R0dMME5nL0NGMUJXYmxjaXVDTFRLWno3TXBTTTh5M3AwQUl1Q21kcDVrSmt6dmcrYStEVVhnMTFqZzdzSCthUUVTRTVXU3R6dEtybHVZRFlJSFpIallVWDVhNTBTVGc1RVArVThFT3VHK3Z3aERHQVNEL3Q1b0FrRi9tTkdNcUNkRDhyci9WMVpGMjNUWHc1OXMvSnZJMHMzMnVyalI4a1RCbUxsQ2VvMG5IWWJsMTZoTEpzWE8wUGswZ2c4eUdVMEpPdHhtSk41QUd5N0Zob24yZjFrWHJWVFRhKzJrTlBtQ08zUStCR1BpQmRiamdneDN1ci91Mm00WXZOUXN3UGJya0pURGFudU5QQ2ZpY3RzTCtTaU4vUUhyeE1iZHdxUE9JMjBJcGNKSkNvT2RRUUVUQlZjTWdva2pHbVhxcHN6WmQ1eHMwd0YrTC9IZkJTRkJ6UVR1Rjd5YzNicjUiLCJtYWMiOiIwYzNiYzkwZWYwOGE3MDQ5MjI3OTM3YjRlZDM3MzMyMGVlNTQyMmU1M2M5OWFmYzBlZDk1MzM1ZjNjOTNmOGM1IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Imd4TWpha3Z4WnBXOGliTTB4MlZhenc9PSIsInZhbHVlIjoiTlFDQTZueFd1dGZnNGc2VWxOT0pHSGZ1NkNtbjZPTExFR2VKMml0TDM2aVd2YVorYUQwZTlMVE1sVDhMRUhWOW9xQmNlY3NaUGo0WWEvNy80THR2bU9zSFFkUGxIMldOMmFCYnVkRGt3M2Q2UmIyRlEwbnQ2SkhSbnl4MFd0TjJTUTNDdnNoWXdNbDNXRUYyWTN5bzhIU3FyZ2pQTHYxcjNRZmp5TlRCcWlZNTJQN3dpaGF5eUM4cllLN1d6TVdMOTlBWXd3ZXlRUW9uWThiMUJkcjQweU9ZYU1velZaYXpMNzhieElHaExBQTh5dFN0WnpRVUU4cDRtdFZqZ296anlwQ2k4SVRiakluaGtCT2JkL053dm15VzRib2QzaU5ueGZLcm51bkFqZkdZOFYwcWo4ZzNBSWxKRnpYOVlBMkY3SDdNbDh6cVM0WlFhOW00RU5MOWtONFh2OG8zNzhUdXlJWUZwa3EwNmdVMk5nNmtxcXZSRXljanVUeXFNdnB5R2JnMnJBeGtmUUp5YU53VTRRWVA3a0dXOWZkdVBYYjdJUDlHM0x0VmN1ZllEZGc0RkV5Y1N1RzRRM3EreWtjZHBGTkpickI2eGJyeHA5VXF2V1ZRa2JsRDdGd1RHMHIxb0thZmNLQXFlR2pYeHJiTm9udEp6cy9WRC8yS0ZGTG0iLCJtYWMiOiIwMzMyOGE4NjAxMjgwMTg2ZjU0M2JhODc2YmJmMzNmOGMzNThhZGM2MWI1NjIwYjdiZDA1NTA4NDY0YjljZWYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1175456049\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-372767181 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lOWuOQi5K5zalKq6lxC9bmXS2G5Yh3URND5pGg9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-372767181\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-830984854 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:38:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlF0YkkwOW5ybHZMV0FscmlUK3BUdHc9PSIsInZhbHVlIjoiOUtYdlNReEpDWCt6eVhZdjE1VHkvc2VtWFVteXY2cFJEQk5acDhySElqVlJLdXc0ZGhSTStvbUp4dUJVV3EvSExIQ0srRWtocnNxWWdJNUQyUTN4blhRMzUyZHl4eDJYZk5JcHdxQWtYcXpYWXlHUHY0cVluRTlFd0pZMGE0eW92aVBwYis0YzFCS09hRmtpRFNSUjVaZ0IzbTdMdjJVZUQrUS84cHpTSnVvK08xVmpGM1hUWkdaWWFHTTBJWFJNakxQNW95a2hTNXltZXJCUUU2Z3h1ejMyazFNcWdEZ0QydSt4b0hvczdqZXNDSTJpZDRFQ2RZdVd0UFNjWWZFNkJLQWJHaDV6TmE3NUNHbVVENmlyVFhTM0hLOGdkOU8wWTcrUE1ody9CZFpHVTZ3ek5pNHcrampkTWxwTmJTUlcvMTJQUFJvZk1lL2xVWTk1SXkvSENBUXhXbGtIZ3J2NHBnT1B5d3F2WE9obGdRekdKS3lERFNFTVo5ZURGMFgrT1dGRktURk5KV3NmTjVNV05vOEszZVkyK3QwajY0UllwUzIzN213TmxZaE5RNDl4WlpZS1FGZmJkd0ZITk9pY0JWU1FaVGNnc0N5RjRkUTEvQWxvaG8yNkVBNHBqQTgzRmlRbkZscHhqYk9mN0Z3bDhabDZWV2JiTS9ZUzNjaDUiLCJtYWMiOiI1ZDRjNmU0MzM2YmZiZGE0N2ZmODE0MmRlNzU0MTg5YzEyZjcxMGJhNjg3YzNkYmVlZmZhOGNkOGYyOWFjOGFiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:38:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlJpb21WSE5XaS9vZzJ1M3QvUDc4anc9PSIsInZhbHVlIjoiNU9Gc2g0ckgvdmFjd1Iwa2ZQTGFNaTZYc1YwNVdINll0UDFGS293WHc4ZTFQeEJuRUJPVGVpTVFzTDYza1lQd1NnMC9uaHN5RDZKQURoRjAvM3dHSS9LN0JzYzduUTI3cFJCVC9ZYkNNVy9jZU5ySzNuYm93UGR5bmc3WFhDaEJEb3E0OSt5eExMcURZci91eXNDQnRraEc1YUh3ZEh1NExmRCtBMlBZRERaNTlCWVlkKzVuL3FCdFRsUFhaRGoxdVpLVUkwcFAzOTRaZ0JHRDliTWFmczkrbFk2ZzBocHRBcVVkOFp5Q3JBZ2drTDNDNitOWFRrNEtONHVDcm92enNkSjViNzRqZnZ3TS9uN1VDbVNwRFhxS3dvak9YOTR2eWJPMXdjVkpHY0ovUlF0d0NkUHFZTlVSS28xZk04RkhLeHEvYVhxQTF3NC9JT3BuOWdOeEtBcEVLTlZNUXV5bExDMEJVcW5rUEF4akJnRTE2OFk3aEY2ZDllZFpENlkxcTA4MmpCdXhsYnFFQ0JsYlErMXFZZnluQ09ORmFZdTJvdjJMSVJudnBMMVNrR0Y2ZWQzNmpmMkpCMkt1TzI3dnlFaDgvZ2cvTDR0cUcxZ1hBSmhmc2V0OHhKOFNVUW5VYitBS3BNVzR2dDcvQ05IMmhWenhUVnRFMS9ZTDdDZ3YiLCJtYWMiOiIxODQzZTE5M2YyNDhmYTZhMjI0YTdhNjU3NDg1Yjc2MTZlYTZiOTcxYzBhNWRlOGUxODIwZDJiNjljMTZjNmVlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:38:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlF0YkkwOW5ybHZMV0FscmlUK3BUdHc9PSIsInZhbHVlIjoiOUtYdlNReEpDWCt6eVhZdjE1VHkvc2VtWFVteXY2cFJEQk5acDhySElqVlJLdXc0ZGhSTStvbUp4dUJVV3EvSExIQ0srRWtocnNxWWdJNUQyUTN4blhRMzUyZHl4eDJYZk5JcHdxQWtYcXpYWXlHUHY0cVluRTlFd0pZMGE0eW92aVBwYis0YzFCS09hRmtpRFNSUjVaZ0IzbTdMdjJVZUQrUS84cHpTSnVvK08xVmpGM1hUWkdaWWFHTTBJWFJNakxQNW95a2hTNXltZXJCUUU2Z3h1ejMyazFNcWdEZ0QydSt4b0hvczdqZXNDSTJpZDRFQ2RZdVd0UFNjWWZFNkJLQWJHaDV6TmE3NUNHbVVENmlyVFhTM0hLOGdkOU8wWTcrUE1ody9CZFpHVTZ3ek5pNHcrampkTWxwTmJTUlcvMTJQUFJvZk1lL2xVWTk1SXkvSENBUXhXbGtIZ3J2NHBnT1B5d3F2WE9obGdRekdKS3lERFNFTVo5ZURGMFgrT1dGRktURk5KV3NmTjVNV05vOEszZVkyK3QwajY0UllwUzIzN213TmxZaE5RNDl4WlpZS1FGZmJkd0ZITk9pY0JWU1FaVGNnc0N5RjRkUTEvQWxvaG8yNkVBNHBqQTgzRmlRbkZscHhqYk9mN0Z3bDhabDZWV2JiTS9ZUzNjaDUiLCJtYWMiOiI1ZDRjNmU0MzM2YmZiZGE0N2ZmODE0MmRlNzU0MTg5YzEyZjcxMGJhNjg3YzNkYmVlZmZhOGNkOGYyOWFjOGFiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:38:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlJpb21WSE5XaS9vZzJ1M3QvUDc4anc9PSIsInZhbHVlIjoiNU9Gc2g0ckgvdmFjd1Iwa2ZQTGFNaTZYc1YwNVdINll0UDFGS293WHc4ZTFQeEJuRUJPVGVpTVFzTDYza1lQd1NnMC9uaHN5RDZKQURoRjAvM3dHSS9LN0JzYzduUTI3cFJCVC9ZYkNNVy9jZU5ySzNuYm93UGR5bmc3WFhDaEJEb3E0OSt5eExMcURZci91eXNDQnRraEc1YUh3ZEh1NExmRCtBMlBZRERaNTlCWVlkKzVuL3FCdFRsUFhaRGoxdVpLVUkwcFAzOTRaZ0JHRDliTWFmczkrbFk2ZzBocHRBcVVkOFp5Q3JBZ2drTDNDNitOWFRrNEtONHVDcm92enNkSjViNzRqZnZ3TS9uN1VDbVNwRFhxS3dvak9YOTR2eWJPMXdjVkpHY0ovUlF0d0NkUHFZTlVSS28xZk04RkhLeHEvYVhxQTF3NC9JT3BuOWdOeEtBcEVLTlZNUXV5bExDMEJVcW5rUEF4akJnRTE2OFk3aEY2ZDllZFpENlkxcTA4MmpCdXhsYnFFQ0JsYlErMXFZZnluQ09ORmFZdTJvdjJMSVJudnBMMVNrR0Y2ZWQzNmpmMkpCMkt1TzI3dnlFaDgvZ2cvTDR0cUcxZ1hBSmhmc2V0OHhKOFNVUW5VYitBS3BNVzR2dDcvQ05IMmhWenhUVnRFMS9ZTDdDZ3YiLCJtYWMiOiIxODQzZTE5M2YyNDhmYTZhMjI0YTdhNjU3NDg1Yjc2MTZlYTZiOTcxYzBhNWRlOGUxODIwZDJiNjljMTZjNmVlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:38:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-830984854\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://127.0.0.1:8000/users/79/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}