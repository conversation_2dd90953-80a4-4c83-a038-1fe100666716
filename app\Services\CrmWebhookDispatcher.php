<?php

namespace App\Services;

use App\Services\ModuleWebhookService;
use App\Services\WebhookLogger;
use App\Constants\CrmWebhookActions;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;

/**
 * CRM Webhook Event Dispatcher
 * 
 * This service provides a centralized way to dispatch webhook events
 * for CRM actions. It can be called from any controller to trigger
 * webhooks to all integrated modules.
 */
class CrmWebhookDispatcher
{
    /**
     * @var ModuleWebhookService
     */
    private $webhookService;

    /**
     * @var WebhookLogger
     */
    private $logger;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->webhookService = new ModuleWebhookService();
        $this->logger = new WebhookLogger();
    }
    
    /**
     * Dispatch a webhook event
     * 
     * @param string $action The CRM action type
     * @param mixed $entity The entity (model instance) or data array
     * @param array $additionalData Additional data to include
     * @param int|null $userId User ID (defaults to current authenticated user)
     * @return array
     */
    public function dispatch($action, $entity, $additionalData = [], $userId = null)
    {
        try {
            // Get user ID if not provided
            if ($userId === null && Auth::check()) {
                $userId = Auth::id();
            }

            // Build the webhook data
            $data = $this->buildWebhookData($entity, $additionalData, $userId);

            // Extract entity info for logging
            $entityType = is_object($entity) ? class_basename(get_class($entity)) : null;
            $entityId = is_object($entity) && isset($entity->id) ? $entity->id : null;

            // Log dispatch start
            $this->logger->logDispatchStart($action, $userId, $entityType, $entityId);

            // Send to all modules
            $results = $this->webhookService->sendToAllModules($action, $data, $userId);

            // Log dispatch completion
            $this->logger->logDispatchComplete($action, $results, $userId);

            return $results;

        } catch (\Exception $e) {
            $this->logger->logError("Error dispatching CRM webhook: {$e->getMessage()}", [
                'action' => $action,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Dispatch lead-related webhook events
     */
    public function dispatchLeadCreated($lead, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::LEAD_CREATED, $lead, $additionalData);
    }
    
    public function dispatchLeadUpdated($lead, $changes = [], $additionalData = [])
    {
        $data = array_merge($additionalData, ['changes' => $changes]);
        return $this->dispatch(CrmWebhookActions::LEAD_UPDATED, $lead, $data);
    }
    
    public function dispatchLeadDeleted($lead, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::LEAD_DELETED, $lead, $additionalData);
    }
    
    public function dispatchLeadStageChanged($lead, $oldStageId, $newStageId, $additionalData = [])
    {
        $data = array_merge($additionalData, [
            'old_stage_id' => $oldStageId,
            'new_stage_id' => $newStageId
        ]);
        return $this->dispatch(CrmWebhookActions::LEAD_STAGE_CHANGED, $lead, $data);
    }
    
    public function dispatchLeadConvertedToDeal($lead, $deal, $additionalData = [])
    {
        $userId = Auth::check() ? Auth::id() : null;
        $data = array_merge($additionalData, ['deal' => $this->buildWebhookData($deal, [], $userId)]);
        return $this->dispatch(CrmWebhookActions::LEAD_CONVERTED_TO_DEAL, $lead, $data);
    }
    
    /**
     * Dispatch deal-related webhook events
     */
    public function dispatchDealCreated($deal, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::DEAL_CREATED, $deal, $additionalData);
    }
    
    public function dispatchDealUpdated($deal, $changes = [], $additionalData = [])
    {
        $data = array_merge($additionalData, ['changes' => $changes]);
        return $this->dispatch(CrmWebhookActions::DEAL_UPDATED, $deal, $data);
    }
    
    public function dispatchDealDeleted($deal, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::DEAL_DELETED, $deal, $additionalData);
    }
    
    public function dispatchDealStageChanged($deal, $oldStageId, $newStageId, $additionalData = [])
    {
        $data = array_merge($additionalData, [
            'old_stage_id' => $oldStageId,
            'new_stage_id' => $newStageId
        ]);
        return $this->dispatch(CrmWebhookActions::DEAL_STAGE_CHANGED, $deal, $data);
    }
    
    public function dispatchDealStatusChanged($deal, $oldStatus, $newStatus, $additionalData = [])
    {
        $data = array_merge($additionalData, [
            'old_status' => $oldStatus,
            'new_status' => $newStatus
        ]);
        return $this->dispatch(CrmWebhookActions::DEAL_STATUS_CHANGED, $deal, $data);
    }

    public function dispatchDealAssigned($deal, $assignedUserId, $previousUserId = null, $additionalData = [])
    {
        $data = array_merge($additionalData, [
            'assigned_user_id' => $assignedUserId,
            'previous_user_id' => $previousUserId
        ]);
        return $this->dispatch(CrmWebhookActions::DEAL_ASSIGNED, $deal, $data);
    }

    public function dispatchDealUnassigned($deal, $unassignedUserId, $additionalData = [])
    {
        $data = array_merge($additionalData, [
            'unassigned_user_id' => $unassignedUserId
        ]);
        return $this->dispatch(CrmWebhookActions::DEAL_UNASSIGNED, $deal, $data);
    }

    public function dispatchDealDeadlineApproaching($deal, $daysUntilDeadline, $additionalData = [])
    {
        $data = array_merge($additionalData, [
            'days_until_deadline' => $daysUntilDeadline,
            'deadline_date' => $deal->deadline ?? null
        ]);
        return $this->dispatch(CrmWebhookActions::DEAL_DEADLINE_APPROACHING, $deal, $data);
    }

    public function dispatchDealInactiveForDays($deal, $inactiveDays, $additionalData = [])
    {
        $data = array_merge($additionalData, [
            'inactive_days' => $inactiveDays,
            'last_activity_date' => $deal->updated_at ?? null
        ]);
        return $this->dispatch(CrmWebhookActions::DEAL_INACTIVE_FOR_DAYS, $deal, $data);
    }

    public function dispatchDealNotesAdded($deal, $note, $additionalData = [])
    {
        $data = array_merge($additionalData, [
            'note' => $note,
            'note_id' => is_object($note) && isset($note->id) ? $note->id : null
        ]);
        return $this->dispatch(CrmWebhookActions::DEAL_NOTES_ADDED, $deal, $data);
    }

    public function dispatchDealDocumentsUploaded($deal, $document, $additionalData = [])
    {
        $data = array_merge($additionalData, [
            'document' => $document,
            'document_id' => is_object($document) && isset($document->id) ? $document->id : null,
            'document_name' => is_object($document) && isset($document->name) ? $document->name : null,
            'document_type' => is_object($document) && isset($document->type) ? $document->type : null
        ]);
        return $this->dispatch(CrmWebhookActions::DEAL_DOCUMENTS_UPLOADED, $deal, $data);
    }

    public function dispatchDealWon($deal, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::DEAL_WON, $deal, $additionalData);
    }

    public function dispatchDealLost($deal, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::DEAL_LOST, $deal, $additionalData);
    }

    /**
     * Dispatch task-related webhook events
     */
    public function dispatchLeadTaskCreated($task, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::LEAD_TASK_CREATED, $task, $additionalData);
    }
    
    public function dispatchLeadTaskCompleted($task, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::LEAD_TASK_COMPLETED, $task, $additionalData);
    }
    
    public function dispatchDealTaskCreated($task, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::DEAL_TASK_CREATED, $task, $additionalData);
    }
    
    public function dispatchDealTaskCompleted($task, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::DEAL_TASK_COMPLETED, $task, $additionalData);
    }
    
    /**
     * Dispatch file-related webhook events
     */
    public function dispatchLeadFileUploaded($file, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::LEAD_FILE_UPLOADED, $file, $additionalData);
    }

    public function dispatchDealFileUploaded($file, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::DEAL_FILE_UPLOADED, $file, $additionalData);
    }

    /**
     * Dispatch booking & appointment-related webhook events
     */
    public function dispatchAppointmentScheduled($appointment, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::APPOINTMENT_SCHEDULED, $appointment, $additionalData);
    }

    public function dispatchAppointmentRescheduled($appointment, $oldDateTime, $newDateTime, $additionalData = [])
    {
        $data = array_merge($additionalData, [
            'old_date_time' => $oldDateTime,
            'new_date_time' => $newDateTime
        ]);
        return $this->dispatch(CrmWebhookActions::APPOINTMENT_RESCHEDULED, $appointment, $data);
    }

    public function dispatchAppointmentCancelled($appointment, $reason = null, $additionalData = [])
    {
        $data = array_merge($additionalData, [
            'cancellation_reason' => $reason
        ]);
        return $this->dispatch(CrmWebhookActions::APPOINTMENT_CANCELLED, $appointment, $data);
    }

    public function dispatchAppointmentReminderTimeReached($appointment, $reminderType, $additionalData = [])
    {
        $data = array_merge($additionalData, [
            'reminder_type' => $reminderType
        ]);
        return $this->dispatch(CrmWebhookActions::APPOINTMENT_REMINDER_TIME_REACHED, $appointment, $data);
    }

    public function dispatchEventCreated($event, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::EVENT_CREATED, $event, $additionalData);
    }

    public function dispatchBookingFormSubmitted($booking, $formData = [], $additionalData = [])
    {
        $data = array_merge($additionalData, [
            'form_data' => $formData
        ]);
        return $this->dispatch(CrmWebhookActions::BOOKING_FORM_SUBMITTED, $booking, $data);
    }

    public function dispatchBookingCreated($booking, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::BOOKING_CREATED, $booking, $additionalData);
    }

    public function dispatchAppointmentBookingCreated($appointmentBooking, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::APPOINTMENT_BOOKING_CREATED, $appointmentBooking, $additionalData);
    }

    public function dispatchDateOverrideAdded($dateOverride, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::DATE_OVERRIDE_ADDED, $dateOverride, $additionalData);
    }

    public function dispatchRecurringAppointmentCreated($recurringAppointment, $additionalData = [])
    {
        return $this->dispatch(CrmWebhookActions::RECURRING_APPOINTMENT_CREATED, $recurringAppointment, $additionalData);
    }

    public function dispatchAppointmentLocationChanged($appointment, $oldLocation, $newLocation, $additionalData = [])
    {
        $data = array_merge($additionalData, [
            'old_location' => $oldLocation,
            'new_location' => $newLocation
        ]);
        return $this->dispatch(CrmWebhookActions::APPOINTMENT_LOCATION_CHANGED, $appointment, $data);
    }
    
    /**
     * Build webhook data from entity
     *
     * @param mixed $entity
     * @param array $additionalData
     * @param int|null $userId
     * @return array
     */
    private function buildWebhookData($entity, $additionalData = [], $userId = null)
    {
        $data = [];

        if (is_array($entity)) {
            $data = $entity;
        } elseif ($entity instanceof Model) {
            $data = $entity->toArray();

            // Add related data for common models - with proper error handling
            try {
                if (method_exists($entity, 'stage')) {
                    $stage = $entity->stage;
                    if ($stage) {
                        $data['stage'] = $stage->toArray();
                    }
                }

                if (method_exists($entity, 'pipeline')) {
                    $pipeline = $entity->pipeline;
                    if ($pipeline) {
                        $data['pipeline'] = $pipeline->toArray();
                    }
                }

                if (method_exists($entity, 'labels')) {
                    $labels = $entity->labels();
                    if ($labels && $labels !== false && is_iterable($labels)) {
                        $data['labels'] = is_array($labels) ? $labels : $labels->toArray();
                    } else {
                        $data['labels'] = [];
                    }
                }

                if (method_exists($entity, 'sources')) {
                    $sources = $entity->sources();
                    if ($sources && $sources !== false && is_iterable($sources)) {
                        $data['sources'] = is_array($sources) ? $sources : $sources->toArray();
                    } else {
                        $data['sources'] = [];
                    }
                }

                if (method_exists($entity, 'products')) {
                    $products = $entity->products();
                    if ($products && $products !== false && is_iterable($products)) {
                        $data['products'] = is_array($products) ? $products : $products->toArray();
                    } else {
                        $data['products'] = [];
                    }
                }
            } catch (\Exception $e) {
                // Log the error but don't fail the webhook
                \Log::warning('Error building webhook data for related models', [
                    'entity_class' => get_class($entity),
                    'error' => $e->getMessage()
                ]);
            }
        } else {
            $data = ['value' => $entity];
        }

        // Add current user information to every payload
        $userInfo = null;
        if ($userId) {
            $user = User::find($userId);
            if ($user) {
                $userInfo = [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'name' => $user->name,
                    'type' => $user->type ?? null,
                ];
            }
        } elseif (Auth::check()) {
            $user = Auth::user();
            $userInfo = [
                'user_id' => $user->id,
                'email' => $user->email,
                'name' => $user->name,
                'type' => $user->type ?? null,
            ];
        }

        // Merge all data together
        $finalData = array_merge($data, $additionalData);
        if ($userInfo) {
            $finalData['triggered_by'] = $userInfo;
        }

        return $finalData;
    }
    
    /**
     * Send webhook to specific module only
     * 
     * @param string $moduleName
     * @param string $action
     * @param mixed $entity
     * @param array $additionalData
     * @param int|null $userId
     * @return array|null
     */
    public function dispatchToModule($moduleName, $action, $entity, $additionalData = [], $userId = null)
    {
        if ($userId === null && Auth::check()) {
            $userId = Auth::id();
        }
        
        $data = $this->buildWebhookData($entity, $additionalData, $userId);
        
        return $this->webhookService->sendToModuleByName($moduleName, $action, $data, $userId);
    }
}
