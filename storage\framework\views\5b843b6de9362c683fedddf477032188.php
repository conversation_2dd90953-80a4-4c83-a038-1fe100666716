
<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Manage Lead')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-page'); ?>
<style>
    .apexcharts-yaxis
    {
        transform: translate(5px, 0px) !important;
    }
    .tab-btn {
        border: 1px solid #0f766e;
        background: #fff;
        color: #0f766e !important;
        padding: 10px 28px;
        border-radius: 14px;
        font-weight: 500;
        font-size: 1rem;
        transition: background 0.2s, color 0.2s, box-shadow 0.2s, border 0.2s;
        box-shadow: 0 2px 6px rgba(0,0,0,0.04);
        outline: none;
    }
    .tab-btn.active, .tab-btn:focus {
        background: var(--bs-primary);
        color: #fff !important;
        border: 1px solid transparent;
        box-shadow: 0 4px 12px rgba(6,95,70,0.10);
    }
    .tab-btn:not(.active):hover {
        background: var(--bs-primary);
        color: #fff !important;
        border-color: #43b37a;
    }
    .tab-section { display: block; }
    .tab-section.d-none { display: none !important; }
    @media (max-width: 767px) {
        .tab-btn { width: 100%; margin-bottom: 8px; }
        .tab-menu-responsive { flex-direction: column !important; align-items: stretch !important; }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Lead Report')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <a href="javascript:void(0)" onclick="downloadLeadReportPDF()" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="<?php echo e(__('Download PDF')); ?>" data-original-title="<?php echo e(__('Download PDF')); ?>">
            <span class="btn-inner--icon"><i class="ti ti-download"></i></span>
        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row" id="printableArea">
        <div class="col-12">
            <input type="hidden" value="<?php echo e(__('Lead Report')); ?>" id="filename">
            <!-- Horizontal Tab Menu at the very top -->
            <div class="d-flex flex-wrap justify-content-center gap-2 tab-menu-responsive mb-4 mt-2">
                <button class="tab-btn active bg-primary bg-gradient text-white" data-tab="general-report"><?php echo e(__('General Report')); ?></button>
                <button class="tab-btn bg-primary bg-gradient" data-tab="sources-report"><?php echo e(__('Sources Report')); ?></button>
                <button class="tab-btn bg-primary bg-gradient" data-tab="staff-report"><?php echo e(__('Staff Report')); ?></button>
                <button class="tab-btn bg-primary bg-gradient" data-tab="pipeline-report"><?php echo e(__('Total Leads')); ?></button>
                <button class="tab-btn bg-primary bg-gradient" data-tab="sales-forecast-report"><?php echo e(__('Sales Forecast')); ?></button>
            </div>
            <!-- Tab Content Sections -->
            <div class="tab-section" id="tab-general-report">
                <!-- General Report Section -->
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-chart-line" style="color: #0CAF60;"></i><?php echo e(__(' This Week Leads Conversions')); ?></h5>
                        <button type="button" class="btn btn-sm btn-primary chart-settings-btn" data-chart="leads-this-week" data-bs-toggle="modal" data-bs-target="#chartSettingsModal">
                            <i class="ti ti-settings"></i>
                        </button>
                    </div>
                    <div class="card-body pt-0 ">
                        <div id="leads-this-week" class="chart-container" data-color="primary" data-height="280">
                            <canvas id="leadsChartCanvas"></canvas>
                        </div>
                    </div>
                </div>
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-exchange-alt" style="color: #0CAF60;"></i> <?php echo e(__(' Sources Conversion')); ?></h5>
                        <button type="button" class="btn btn-sm btn-primary chart-settings-btn" data-chart="leads-sources-report" data-bs-toggle="modal" data-bs-target="#chartSettingsModal">
                            <i class="ti ti-settings"></i>
                        </button>
                    </div>
                    <div class="card-body pt-0">
                        <div id="leads-sources-report" class="chart-container" data-color="primary" data-height="280">
                            <canvas id="sourcesChartCanvas"></canvas>
                        </div>
                    </div>
                </div>
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="row w-100">
                            <div class="col-9 d-flex align-items-center">
                                <h5 class="mb-0"><i class="fas fa-signal" style="color: #0CAF60;"></i><?php echo e(__(' Monthly')); ?></h5>
                            </div>
                            <div class="col-3 d-flex align-items-center justify-content-end gap-2">
                                <select name="month" class="form-control selectpicker" id="selectmonth" data-none-selected-text="Nothing selected" >
                                    <option value=" "><?php echo e(__('Select Month')); ?></option>
                                    <option value="1"><?php echo e(__('January')); ?></option>
                                    <option value="2"><?php echo e(__('February')); ?></option>
                                    <option value="3"><?php echo e(__('March')); ?></option>
                                    <option value="4"><?php echo e(__('April')); ?></option>
                                    <option value="5"><?php echo e(__('May')); ?></option>
                                    <option value="6"><?php echo e(__('June')); ?></option>
                                    <option value="7"><?php echo e(__('July')); ?></option>
                                    <option value="8"><?php echo e(__('August')); ?></option>
                                    <option value="9"><?php echo e(__('September')); ?></option>
                                    <option value="10"><?php echo e(__('October')); ?></option>
                                    <option value="11"><?php echo e(__('November')); ?></option>
                                    <option value="12"><?php echo e(__('December')); ?></option>
                                </select>
                                <button type="button" class="btn btn-sm btn-primary chart-settings-btn" data-chart="leads-monthly" data-bs-toggle="modal" data-bs-target="#chartSettingsModal">
                                    <i class="ti ti-settings"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mt-3">
                            <div id="leads-monthly" class="chart-container" data-color="primary" data-height="280">
                                <canvas id="monthlyChartCanvas"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-section d-none" id="tab-sources-report">
                <!-- Sources Report Section -->
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-globe" style="color: #0CAF60;"></i><?php echo e(__(' Sources Report')); ?></h5>
                        <button type="button" class="btn btn-sm btn-primary chart-settings-btn" data-chart="leads-sources-pie-chart" data-bs-toggle="modal" data-bs-target="#chartSettingsModal">
                            <i class="ti ti-settings"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div id="leads-sources-pie-chart" class="chart-container" data-color="primary" data-height="280">
                                    <canvas id="sourcesPieChartCanvas"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th><?php echo e(__('Source')); ?></th>
                                                <th><?php echo e(__('Count')); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $leadsourceName; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $source): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($source); ?></td>
                                                    <td><?php echo e($leadsourceeData[$key]); ?></td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-section d-none" id="tab-staff-report">
                <!-- Staff Report Section -->
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-user-tie" style="color: #0CAF60;"></i> <?php echo e(__(' Staff Report')); ?></h5>
                        <button type="button" class="btn btn-sm btn-primary chart-settings-btn" data-chart="leads-staff-report" data-bs-toggle="modal" data-bs-target="#chartSettingsModal">
                            <i class="ti ti-settings"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <?php echo e(Form::label('From Date', __('From Date'),['class'=>'col-form-label'])); ?>

                                <?php echo e(Form::date('From Date',null, array('class' => 'form-control from_date','id'=>'data_picker1',))); ?>

                                <span id="fromDate" style="color: red;"></span>
                            </div>
                            <div class="col-md-4">
                                <?php echo e(Form::label('To Date', __('To Date'),['class'=>'col-form-label'])); ?>

                                <?php echo e(Form::date('To Date',null, array('class' => 'form-control to_date','id'=>'data_picker2',))); ?>

                                <span id="toDate"  style="color: red;"></span>
                            </div>
                            <div class="col-md-4" id="filter_type" style="padding-top : 38px;">
                                <button  class="btn btn-primary label-margin generate_button" ><?php echo e(__('Generate')); ?></button>
                            </div>
                        </div>
                        <div id="leads-staff-report" class="chart-container mt-3" data-color="primary" data-height="280">
                            <canvas id="staffChartCanvas"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-section d-none" id="tab-pipeline-report">
                <!-- Pipeline Report Section -->
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-address-card" style="color: #0CAF60;"></i><?php echo e(__(' Total Leads')); ?></h5>
                        <button type="button" class="btn btn-sm btn-primary chart-settings-btn" data-chart="leads-piplines-report" data-bs-toggle="modal" data-bs-target="#chartSettingsModal">
                            <i class="ti ti-settings"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div id="leads-piplines-report" class="chart-container" data-color="primary" data-height="280">
                                <canvas id="pipelineChartCanvas"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-section d-none" id="tab-sales-forecast-report">
                <!-- Sales Forecast Report Section -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5> <i class="fas fa-chart-bar" style="color: #0CAF60;"></i><?php echo e(__(' Sales Forecast')); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <ul class="nav nav-pills mb-3" id="forecast-tab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="summary-tab" data-bs-toggle="pill" data-bs-target="#summary" type="button" role="tab" aria-controls="summary" aria-selected="true"><?php echo e(__('Summary')); ?></button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="sources-tab" data-bs-toggle="pill" data-bs-target="#sources" type="button" role="tab" aria-controls="sources" aria-selected="false"><?php echo e(__('By Source')); ?></button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="products-tab" data-bs-toggle="pill" data-bs-target="#products" type="button" role="tab" aria-controls="products" aria-selected="false"><?php echo e(__('By Product')); ?></button>
                                    </li>
                                </ul>
                                <div class="tab-content" id="forecast-tabContent">
                                    <div class="tab-pane fade show active" id="summary" role="tabpanel" aria-labelledby="summary-tab">
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead class="thead-light">
                                                    <tr>
                                                        <th><?php echo e(__('Time Period')); ?></th>
                                                        <th><?php echo e(__('Total Value')); ?></th>
                                                        <th><?php echo e(__('Weighted Value')); ?></th>
                                                        <th><?php echo e(__('Deal Count')); ?></th>
                                                        <th><?php echo e(__('Avg. Deal Size')); ?></th>
                                                    </tr>
                                                </thead>
                                                <tbody id="forecast-summary">
                                                    <!-- Data will be loaded via AJAX -->
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="mt-4">
                                            <div id="forecast-chart" style="min-height: 300px;"></div>
                                        </div>
                                    </div>
                                    <!-- Sources Tab -->
                                    <div class="tab-pane fade" id="sources" role="tabpanel" aria-labelledby="sources-tab">
                                        <div class="table-responsive">
                                            <table class="table table-bordered" id="sources-table">
                                                <thead class="thead-light">
                                                    <tr>
                                                        <th><?php echo e(__('Source')); ?></th>
                                                        <th><?php echo e(__('Total Deals')); ?></th>
                                                        <th><?php echo e(__('Forecasted Value')); ?></th>
                                                        <th><?php echo e(__('Avg Deal')); ?></th>
                                                        <th><?php echo e(__('Conversion Rate')); ?></th>
                                                    </tr>
                                                </thead>
                                                <tbody id="forecast-sources">
                                                    <!-- Data will be loaded via AJAX -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <!-- Products Tab -->
                                    <div class="tab-pane fade" id="products" role="tabpanel" aria-labelledby="products-tab">
                                        <div class="table-responsive">
                                            <table class="table table-bordered" id="products-table">
                                                <thead class="thead-light">
                                                    <tr>
                                                        <th><?php echo e(__('Product Name')); ?></th>
                                                        <th><?php echo e(__('Total Deals')); ?></th>
                                                        <th><?php echo e(__('Forecasted Value')); ?></th>
                                                        <th><?php echo e(__('Avg Deal')); ?></th>
                                                        <th><?php echo e(__('Top Source')); ?></th>
                                                    </tr>
                                                </thead>
                                                <tbody id="forecast-products">
                                                    <!-- Data will be loaded via AJAX -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<!-- Chart Settings Modal -->
<div class="modal fade" id="chartSettingsModal" tabindex="-1" aria-labelledby="chartSettingsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="chartSettingsModalLabel"><?php echo e(__('Graph Settings')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><?php echo e(__('Customize Chart Display')); ?></h6>
                        <form id="chartSettingsForm">
                            <?php echo csrf_field(); ?>
                            <div class="mb-3">
                                <label for="leads-this-week-chart-type" class="form-label"><i class="fas fa-chart-line" style="color: #0CAF60;"></i><?php echo e(__(' This Week Leads Conversions')); ?></label>
                                <select class="form-select" name="leads-this-week" id="leads-this-week-chart-type" data-chart-id="leads-this-week">
                                    <option value="line">Line</option>
                                    <option value="bar">Bar</option>
                                    <option value="pie">Pie</option>
                                    <option value="doughnut">Doughnut</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="leads-sources-report-chart-type" class="form-label"><i class="fas fa-exchange-alt" style="color: #0CAF60;"></i><?php echo e(__(' Sources Conversion')); ?></label>
                                <select class="form-select" name="leads-sources-report" id="leads-sources-report-chart-type" data-chart-id="leads-sources-report">
                                    <option value="bar">Bar</option>
                                    <option value="line">Line</option>
                                    <option value="pie">Pie</option>
                                    <option value="doughnut">Doughnut</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="leads-monthly-chart-type" class="form-label"><i class="fas fa-signal" style="color: #0CAF60;"></i><?php echo e(__(' Monthly Sources Report')); ?></label>
                                <select class="form-select" name="leads-monthly" id="leads-monthly-chart-type" data-chart-id="leads-monthly">
                                    <option value="bar">Bar</option>
                                    <option value="line">Line</option>
                                    <option value="pie">Pie</option>
                                    <option value="doughnut">Doughnut</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="leads-sources-pie-chart-chart-type" class="form-label"><i class="fas fa-globe" style="color: #0CAF60;"></i><?php echo e(__(' Sources Report')); ?></label>
                                <select class="form-select" name="leads-sources-pie-chart" id="leads-sources-pie-chart-chart-type" data-chart-id="leads-sources-pie-chart">
                                    <option value="pie">Pie</option>
                                    <option value="doughnut">Doughnut</option>
                                    <option value="bar">Bar</option>
                                    <option value="line">Line</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="leads-staff-report-chart-type" class="form-label"> <i class="fas fa-user-tie" style="color: #0CAF60;"></i><?php echo e(__(' Staff Report')); ?></label>
                                <select class="form-select" name="leads-staff-report" id="leads-staff-report-chart-type" data-chart-id="leads-staff-report">
                                    <option value="bar">Bar</option>
                                    <option value="line">Line</option>
                                    <option value="pie">Pie</option>
                                    <option value="doughnut">Doughnut</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="leads-piplines-report-chart-type" class="form-label" ><i class="fas fa-address-card" style="color: #0CAF60;"></i><?php echo e(__(' Total Leads')); ?></label>
                                <select class="form-select" name="leads-piplines-report" id="leads-piplines-report-chart-type" data-chart-id="leads-piplines-report">
                                    <option value="bar">Bar</option>
                                    <option value="line">Line</option>
                                    <option value="pie">Pie</option>
                                    <option value="doughnut">Doughnut</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <h6><?php echo e(__('Preview')); ?></h6>
                        <div id="chartPreview" style="height: 300px; width: 100%;">
                            <canvas id="previewChartCanvas"></canvas>
                        </div>
                        <div id="tablePreview" class="table-responsive" style="display: none;"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                <button type="button" class="btn btn-primary" id="saveChartSettings"><?php echo e(__('Save Changes')); ?></button>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('script-page'); ?>
    <script type="text/javascript" src="<?php echo e(asset('js/html2pdf.bundle.min.js')); ?>"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        console.log('Lead report script loaded successfully');

        // Global variables for chart management
        window.chartInstances = {};
        window.previewChart = null;

        $(document).ready(function() {
            // #region Chart Settings
            const chartConfig = {
                'leads-this-week': { canvasId: 'leadsChartCanvas', defaultType: 'line' },
                'leads-sources-report': { canvasId: 'sourcesChartCanvas', defaultType: 'bar' },
                'leads-monthly': { canvasId: 'monthlyChartCanvas', defaultType: 'bar' },
                'leads-sources-pie-chart': { canvasId: 'sourcesPieChartCanvas', defaultType: 'pie' },
                'leads-staff-report': { canvasId: 'staffChartCanvas', defaultType: 'bar' },
                'leads-piplines-report': { canvasId: 'pipelineChartCanvas', defaultType: 'bar' }
            };

            // Load settings from localStorage or set defaults
            function getChartSettings() {
                const savedSettings = localStorage.getItem('chartSettings');
                if (savedSettings) {
                    return JSON.parse(savedSettings);
                }
                // Default settings
                let defaultSettings = {};
                for (const chartId in chartConfig) {
                    defaultSettings[chartId] = chartConfig[chartId].defaultType;
                }
                return defaultSettings;
            }

            // Save settings to localStorage
            function saveChartSettings(settings) {
                localStorage.setItem('chartSettings', JSON.stringify(settings));
            }

            // Render all charts based on current settings
            function renderAllCharts() {
                const settings = getChartSettings();
                for (const chartId in settings) {
                    const type = settings[chartId];
                    const { canvasId } = chartConfig[chartId];
                    renderChart(chartId, canvasId, type);
                }
            }

            // Render a single chart
            async function renderChart(chartId, canvasId, type) {
                try {
                    const canvas = document.getElementById(canvasId);
                    if (!canvas) {
                        console.warn('Canvas not found:', canvasId);
                        return;
                    }

                    // Destroy existing chart instance if it exists
                    if (window.chartInstances[chartId]) {
                        console.log('Destroying existing chart:', chartId);
                        window.chartInstances[chartId].destroy();
                        delete window.chartInstances[chartId];
                    }

                    // Also check for any Chart.js instances on this canvas
                    if (Chart.getChart(canvas)) {
                        console.log('Destroying Chart.js instance on canvas:', canvasId);
                        Chart.getChart(canvas).destroy();
                    }

                    const data = await fetchChartData(chartId);
                    const ctx = canvas.getContext('2d');
                    window.chartInstances[chartId] = new Chart(ctx, {
                        type: type,
                        data: data,
                        options: getChartOptions(chartId)
                    });
                    console.log('Chart rendered successfully:', chartId);

                } catch (error) {
                    console.error(`Error rendering chart ${chartId}:`, error);
                    // Display an error message on the canvas
                    const canvas = document.getElementById(canvasId);
                    if (canvas) {
                        const ctx = canvas.getContext('2d');
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                        ctx.fillStyle = '#666';
                        ctx.font = '14px Arial';
                        ctx.fillText('Could not load chart data.', 10, 50);
                    }
                }
            }

            // Fetch data for a specific chart
            async function fetchChartData(chartId) {
                // This is a mock implementation. Replace with your actual API call.
                // You might need to adjust the endpoint based on your routes.
                // const response = await fetch(`/api/chart-data/${chartId}`);
                // if (!response.ok) throw new Error('Network response was not ok');
                // return await response.json();

                // Mock data for demonstration
                return {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun','August','Sep','Oct','Nov','Dec'],
                    datasets: [{
                        label: chartId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                        data: [12, 19, 3, 5, 2, 3].map(() => Math.floor(Math.random() * 20)),
                        backgroundColor: '#2E7D32',
                        borderColor: '#2E7D32',
                        borderWidth: 1
                    }]
                };
            }

            // Get chart options
            function getChartOptions(title) {
                return {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: true, position: 'top' },
                        title: { display: true, text: title.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) }
                    }
                };
            }
            
            // When the modal is shown, populate the form and set up the preview
            $('#chartSettingsModal').on('shown.bs.modal', function () {
                const settings = getChartSettings();
                // Populate the dropdowns with saved settings
                $('#chartSettingsForm select').each(function() {
                    const chartId = $(this).data('chart-id');
                    if (settings[chartId]) {
                        $(this).val(settings[chartId]);
                    }
                });

                // Detach any previous change handlers to avoid multiple triggers
                $('#chartSettingsForm select').off('change');

                // Attach a new change handler to update the preview
                $('#chartSettingsForm select').on('change', function() {
                    const selectedType = $(this).val();
                    const chartTitle = $(this).closest('.mb-3').find('label').text();
                    updatePreview(selectedType, chartTitle);
                });

                // Trigger the change event on the first dropdown to show an initial preview
                $('#chartSettingsForm select').first().trigger('change');
            });

            // Update the preview chart in the modal
            function updatePreview(type, title) {
                const canvas = document.getElementById('previewChartCanvas');
                if (!canvas) return;
                const ctx = canvas.getContext('2d');

                if (previewChart) {
                    previewChart.destroy();
                }

                const sampleData = {
                    labels: ['Sample A', 'Sample B', 'Sample C', 'Sample D'],
                    datasets: [{
                        label: 'Preview',
                        data: [12, 19, 3, 5].map(() => Math.floor(Math.random() * 20)),
                        backgroundColor: '#2E7D32',
                        borderColor: '#2E7D32',
                        borderWidth: 1
                    }]
                };

                previewChart = new Chart(ctx, {
                    type: type,
                    data: sampleData,
                    options: getChartOptions(title)
                });
            }

            // Handle the save button click
            $('#saveChartSettings').on('click', function() {
                const newSettings = {};
                $('#chartSettingsForm select').each(function() {
                    const chartId = $(this).data('chart-id');
                    newSettings[chartId] = $(this).val();
                });
                saveChartSettings(newSettings);
                renderAllCharts();
                $('#chartSettingsModal').modal('hide');
                // You could add a toast message here for user feedback
            });

            // Initial render of all charts
            renderAllCharts();
            // #endregion

            // Tab switching logic
            $('.tab-btn').on('click', function() {
                $('.tab-btn').removeClass('active bg-primary bg-gradient text-white');
                $('.tab-btn').css({'color':'#0f766e','background':'#fff'});
                $(this).addClass('active bg-primary bg-gradient text-white');
                $(this).css({'color':'#fff'});
                $('.tab-section').addClass('d-none');
                $('#tab-' + $(this).data('tab')).removeClass('d-none');
            });
            // Show the first tab by default
            $('.tab-btn[data-tab="general-report"]').trigger('click');
            $('.tab-btn').hover(
                function() {
                    if (!$(this).hasClass('active')) {
                        $(this).addClass('bg-primary bg-gradient text-white');
                        $(this).css({'color':'#fff'});
                    }
                },
                function() {
                    if (!$(this).hasClass('active')) {
                        $(this).removeClass('bg-primary bg-gradient text-white');
                        $(this).css({'color':'#0f766e','background':'#fff'});
                    }
                }
            );

            // Graph Settings Modal logic
            let currentChartForSettings = null;
            $('.chart-settings-btn').on('click', function() {
                currentChartForSettings = $(this).data('chart');
                // Set the correct tab in the modal
                $('#chartSettingsForm select').each(function() {
                    const chartId = $(this).data('chart-id');
                    if(chartId === currentChartForSettings) {
                        $(this).closest('.mb-3').show();
                    } else {
                        $(this).closest('.mb-3').hide();
                    }
                });
                // Set the dropdown to the current chart's type
                const settings = getChartSettings();
                $('#chartSettingsForm select').each(function() {
                    const chartId = $(this).data('chart-id');
                    if(chartId === currentChartForSettings && settings[chartId]) {
                        $(this).val(settings[chartId]);
                    }
                });
                // Show preview for the current chart
                $('#chartSettingsForm select[data-chart-id="'+currentChartForSettings+'"]').trigger('change');
            });

            // Monthly chart: always show Jan-Dec on x-axis
            function getAllMonthsData(rawData) {
                const months = [
                    'January','February','March','April','May','June','July','August','September','October','November','December'
                ];
                let data = new Array(12).fill(0);
                if(rawData && rawData.labels && rawData.datasets && rawData.datasets[0]) {
                    rawData.labels.forEach(function(label, idx) {
                        const monthIdx = months.findIndex(m => m.toLowerCase() === label.toLowerCase());
                        if(monthIdx !== -1) {
                            data[monthIdx] = rawData.datasets[0].data[idx];
                        }
                    });
                }
                return {
                    labels: months,
                    datasets: [{
                        ...rawData.datasets[0],
                        data: data
                    }]
                };
            }

            // Patch renderChart to use getAllMonthsData for monthly chart
            const originalRenderChart = window.renderChart;
            window.renderChart = async function(chartId, canvasId, type) {
                const canvas = document.getElementById(canvasId);
                if (!canvas) return;
                if (window.chartInstances[chartId]) {
                    window.chartInstances[chartId].destroy();
                }
                try {
                    let data = await fetchChartData(chartId);
                    if(chartId === 'leads-monthly') {
                        data = getAllMonthsData(data);
                    }
                    const ctx = canvas.getContext('2d');
                    window.chartInstances[chartId] = new Chart(ctx, {
                        type: type,
                        data: data,
                        options: getChartOptions(chartId)
                    });
                } catch (error) {
                    const ctx = canvas.getContext('2d');
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    ctx.fillText('Could not load chart data.', 10, 50);
                }
            };
        });

        // PDF Download Function
        function downloadLeadReportPDF() {
            console.log('PDF download function called');

            // Check if html2pdf is available
            if (typeof html2pdf === 'undefined') {
                console.error('html2pdf library not loaded');
                alert('PDF library not loaded. Please refresh the page and try again.');
                return;
            }

            try {
                // Show loading message
                var originalButton = event.target.closest('a');
                var originalText = originalButton.innerHTML;
                originalButton.innerHTML = '<i class="ti ti-loader"></i> Generating...';
                originalButton.disabled = true;

                // Wait for charts to be fully rendered
                setTimeout(function() {
                    try {
                        var element = document.getElementById('printableArea');
                        if (!element) {
                            console.error('Printable area element not found');
                            alert('Could not find content to convert to PDF');
                            originalButton.innerHTML = originalText;
                            originalButton.disabled = false;
                            return;
                        }

                        // Clone the element to avoid modifying the original
                        var clonedElement = element.cloneNode(true);

                        // Remove ALL canvas elements and replace with placeholders
                        var allCanvases = clonedElement.querySelectorAll('canvas');
                        console.log('Found', allCanvases.length, 'canvases to replace');

                        allCanvases.forEach(function(canvas, index) {
                            var placeholder = document.createElement('div');
                            placeholder.style.cssText = 'padding: 30px; text-align: center; background: #f8f9fa; border: 2px solid #007bff; border-radius: 8px; margin: 15px 0; min-height: 200px; display: flex; align-items: center; justify-content: center;';
                            placeholder.innerHTML = '<div><i class="ti ti-chart-line" style="font-size: 24px; color: #007bff; margin-bottom: 10px;"></i><br><strong style="color: #333;">Chart ' + (index + 1) + '</strong><br><small style="color: #666;">Data visualization available in web interface</small></div>';
                            canvas.parentNode.replaceChild(placeholder, canvas);
                            console.log('Replaced canvas', index, 'with placeholder');
                        });

                        // Remove any remaining problematic elements
                        var apexCharts = clonedElement.querySelectorAll('.apexcharts-canvas, .apexcharts-svg, .apexcharts-inner');
                        apexCharts.forEach(function(chart) {
                            var placeholder = document.createElement('div');
                            placeholder.style.cssText = 'padding: 30px; text-align: center; background: #f8f9fa; border: 2px solid #28a745; border-radius: 8px; margin: 15px 0; min-height: 200px; display: flex; align-items: center; justify-content: center;';
                            placeholder.innerHTML = '<div><i class="ti ti-chart-bar" style="font-size: 24px; color: #28a745; margin-bottom: 10px;"></i><br><strong style="color: #333;">Interactive Chart</strong><br><small style="color: #666;">Data visualization available in web interface</small></div>';
                            chart.parentNode.replaceChild(placeholder, chart);
                        });

                        // Add the cloned element to body temporarily
                        clonedElement.style.position = 'absolute';
                        clonedElement.style.left = '-9999px';
                        clonedElement.style.top = '0';
                        clonedElement.style.width = '800px';
                        document.body.appendChild(clonedElement);

                        var filename = 'Lead_Report_' + new Date().toISOString().slice(0,19).replace(/:/g, '-');
                        console.log('Generating PDF with filename:', filename);

                        var opt = {
                            margin: [0.5, 0, 0.5, 0],
                            filename: filename,
                            image: {type: 'jpeg', quality: 0.95},
                            html2canvas: {
                                scale: 1.2,
                                dpi: 72,
                                letterRendering: true,
                                useCORS: true,
                                allowTaint: true,
                                backgroundColor: '#ffffff',
                                ignoreElements: function(element) {
                                    return element.tagName === 'CANVAS' ||
                                           element.classList.contains('apexcharts-canvas') ||
                                           element.style.display === 'none' ||
                                           element.style.visibility === 'hidden';
                                }
                            },
                            jsPDF: {unit: 'in', format: 'A4'}
                        };

                        console.log('Starting PDF generation...');

                        html2pdf().set(opt).from(clonedElement).save().then(function() {
                            console.log('PDF generated successfully');
                            // Clean up
                            document.body.removeChild(clonedElement);
                            // Restore button
                            originalButton.innerHTML = originalText;
                            originalButton.disabled = false;
                        }).catch(function(error) {
                            console.error('PDF generation failed:', error);
                            alert('Failed to generate PDF: ' + error.message);
                            // Clean up
                            if (document.body.contains(clonedElement)) {
                                document.body.removeChild(clonedElement);
                            }
                            // Restore button
                            originalButton.innerHTML = originalText;
                            originalButton.disabled = false;
                        });

                    } catch (innerError) {
                        console.error('Error in PDF generation:', innerError);
                        alert('Error generating PDF: ' + innerError.message);
                        // Restore button
                        originalButton.innerHTML = originalText;
                        originalButton.disabled = false;
                    }
                }, 2000); // Wait 2 seconds for charts to fully load

            } catch (error) {
                console.error('Error in PDF function:', error);
                alert('Error generating PDF: ' + error.message);
                // Restore button if available
                if (originalButton) {
                    originalButton.innerHTML = originalText;
                    originalButton.disabled = false;
                }
            }
        }
        $(document).ready(function() {
            console.log('Lead report script loaded successfully');
            console.log('Document ready, jQuery version:', $.fn.jquery);
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/report/lead.blade.php ENDPATH**/ ?>