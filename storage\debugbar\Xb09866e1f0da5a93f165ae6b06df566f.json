{"__meta": {"id": "Xb09866e1f0da5a93f165ae6b06df566f", "datetime": "2025-07-31 05:10:23", "utime": **********.79993, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753938622.621314, "end": **********.799983, "duration": 1.1786689758300781, "duration_str": "1.18s", "measures": [{"label": "Booting", "start": 1753938622.621314, "relative_start": 0, "end": **********.723197, "relative_end": **********.723197, "duration": 1.1018829345703125, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.72322, "relative_start": 1.****************, "end": **********.799987, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "76.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "szixauebrW0oUHDAYviyT9mL1semKAUgJ3froVL8", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-666650331 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-666650331\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1532915118 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1532915118\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-560642667 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-560642667\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1493195391 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1493195391\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-561191746 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-561191746\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-455408209 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:10:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVYaFJMT3E0a2tZU1RQZjZhOGQwUmc9PSIsInZhbHVlIjoib1BrbHBvQ1B2Z05ZdDNSSWxaN2NuQldsaWt0THlZMVNoZ2pPNENVN2hQZUFYZjBrWW5xTTZFYzIvRzhhaEZyUEpQMS9DazZFNDJxZkJFdk9QdWswY0JQS0NkYUNNTC83UzBsQk83M29kNVBSbmFEQ0plUWJGNEZhTy9BamJSb3oxd2dDaFdlZ1lXcU9wd2lubTB4c2pET2NWQnBESFBzNUVjcGRDMm1KOGVwNzlKZkdxcFpWdkJqUTZnWDN3UXVUeXR6TFRZb3IwNXBkczJFZzNadzlxSGpqR21GNXJmb3JpOHRiZlliMDA1SytXb2dqajhzUFlaN04xWnkwRG92US9Gc0FqRENIUlBYNlBZSEVBVGVuYlVRdXNuc3lnM2ZwRUR1R3YrSm9GM2RMQ25GbkF5ZEkraFU1R2dLMnl5SGhJOERVUS9QSnJZM21xeDgxUmJKVXp5VDVoZThXN1BlRXVpT3VLSkhrS04xeHVYRUZSVEFlWnNJWjRmUUtZUC9OZ244QnBxYVFNVGVUYm9YRDUxTGVGczQ1WVdHZkR3TWVnM1FZemxMUVpOTzdpSjcvT2RWaWp1NjdtbnhqOVNhRDlVampRNW56TnhoTGxzTTlLeEdjNXBJMkRMODZOTGZRNFJjZjRCMG9aSE03RW1mdVJDRWhlMDVEbVZ5RVJibEwiLCJtYWMiOiI5Mjk0MmUwNTZmZDcyYzI1MDQ1NjE4NTliNDYyODlhMjlmYTNmYWQ3ZDRiODhiNmJiYTdiYjk0YjRlODNiNGU0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:10:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikhab2NSTHJFZFU1amQ5QmNVNVowZEE9PSIsInZhbHVlIjoiaHFGSklxOHYzVVdPMk1pZ3dyZVN2ZGxwNDE1czBUVjB5cWc5YU0xejgyb2swUGpUanRlNVc1MnlJNmJmRkxUNE5DQ0JudDYwbmRhMTdHMTFLMGdJSTIrZEU5Q2pqRHFGVjlxekVEOEsrMTQ5RnVERmYzNU4xQ0FaUkRLbG5wSHFQUSt2YXo3cVh0cTJjMXEvWm11K1JKaENTbjdxZEdiZERveGZmUmFmZ1M2cDdEMlVGS2FsZDUvUnJMZDhjQzE4MXZkcmhvejhUdXJ3YmdlYXNYWmlnRGxrQmxCN2pXaXlYMVVIUGZCM25CQnhSa1g0UHZlUEtxcXVmTGxkRkNPZitDUFJCbTQ3YjIrUG5MeFFXbWVoS0J5UVhVNlJKYVFXVTdEWm1HanFCUU9CaXE1WmJoVW1sTms2OVBiNW9uNThDbkV4dS9QYUdITE0reDlwaEhzQ1poZjdrSUZSc3k3VUtOaEFsSlBoVXUrYkpMOVF1angzWTJQNEtkQS82QWlGeTQyMnRKSmMvVm1JL1dhM3FaWEpUMTM4b0pBWDJmMHl3c2t4Z2ZRSDIrMkRxTlZPdUEvUE51U09FMEFvMmp2VTFUdStUdHh6T3RnbklYMTdwd05DbkRmL3Z3NUgySXEycElBN01DcTRNMTJ0WTFDSmttY3pEZWpZSkdFSUlzSUoiLCJtYWMiOiIzY2UwNzc4MzViMTZmZWE3NGMzNzRmNmIzNTljMjIxYWNhMzdmYjA5NDU2YzhmZTMxOGM2MDYzODNkMzI3ZmRjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:10:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVYaFJMT3E0a2tZU1RQZjZhOGQwUmc9PSIsInZhbHVlIjoib1BrbHBvQ1B2Z05ZdDNSSWxaN2NuQldsaWt0THlZMVNoZ2pPNENVN2hQZUFYZjBrWW5xTTZFYzIvRzhhaEZyUEpQMS9DazZFNDJxZkJFdk9QdWswY0JQS0NkYUNNTC83UzBsQk83M29kNVBSbmFEQ0plUWJGNEZhTy9BamJSb3oxd2dDaFdlZ1lXcU9wd2lubTB4c2pET2NWQnBESFBzNUVjcGRDMm1KOGVwNzlKZkdxcFpWdkJqUTZnWDN3UXVUeXR6TFRZb3IwNXBkczJFZzNadzlxSGpqR21GNXJmb3JpOHRiZlliMDA1SytXb2dqajhzUFlaN04xWnkwRG92US9Gc0FqRENIUlBYNlBZSEVBVGVuYlVRdXNuc3lnM2ZwRUR1R3YrSm9GM2RMQ25GbkF5ZEkraFU1R2dLMnl5SGhJOERVUS9QSnJZM21xeDgxUmJKVXp5VDVoZThXN1BlRXVpT3VLSkhrS04xeHVYRUZSVEFlWnNJWjRmUUtZUC9OZ244QnBxYVFNVGVUYm9YRDUxTGVGczQ1WVdHZkR3TWVnM1FZemxMUVpOTzdpSjcvT2RWaWp1NjdtbnhqOVNhRDlVampRNW56TnhoTGxzTTlLeEdjNXBJMkRMODZOTGZRNFJjZjRCMG9aSE03RW1mdVJDRWhlMDVEbVZ5RVJibEwiLCJtYWMiOiI5Mjk0MmUwNTZmZDcyYzI1MDQ1NjE4NTliNDYyODlhMjlmYTNmYWQ3ZDRiODhiNmJiYTdiYjk0YjRlODNiNGU0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:10:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikhab2NSTHJFZFU1amQ5QmNVNVowZEE9PSIsInZhbHVlIjoiaHFGSklxOHYzVVdPMk1pZ3dyZVN2ZGxwNDE1czBUVjB5cWc5YU0xejgyb2swUGpUanRlNVc1MnlJNmJmRkxUNE5DQ0JudDYwbmRhMTdHMTFLMGdJSTIrZEU5Q2pqRHFGVjlxekVEOEsrMTQ5RnVERmYzNU4xQ0FaUkRLbG5wSHFQUSt2YXo3cVh0cTJjMXEvWm11K1JKaENTbjdxZEdiZERveGZmUmFmZ1M2cDdEMlVGS2FsZDUvUnJMZDhjQzE4MXZkcmhvejhUdXJ3YmdlYXNYWmlnRGxrQmxCN2pXaXlYMVVIUGZCM25CQnhSa1g0UHZlUEtxcXVmTGxkRkNPZitDUFJCbTQ3YjIrUG5MeFFXbWVoS0J5UVhVNlJKYVFXVTdEWm1HanFCUU9CaXE1WmJoVW1sTms2OVBiNW9uNThDbkV4dS9QYUdITE0reDlwaEhzQ1poZjdrSUZSc3k3VUtOaEFsSlBoVXUrYkpMOVF1angzWTJQNEtkQS82QWlGeTQyMnRKSmMvVm1JL1dhM3FaWEpUMTM4b0pBWDJmMHl3c2t4Z2ZRSDIrMkRxTlZPdUEvUE51U09FMEFvMmp2VTFUdStUdHh6T3RnbklYMTdwd05DbkRmL3Z3NUgySXEycElBN01DcTRNMTJ0WTFDSmttY3pEZWpZSkdFSUlzSUoiLCJtYWMiOiIzY2UwNzc4MzViMTZmZWE3NGMzNzRmNmIzNTljMjIxYWNhMzdmYjA5NDU2YzhmZTMxOGM2MDYzODNkMzI3ZmRjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:10:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-455408209\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-254225707 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">szixauebrW0oUHDAYviyT9mL1semKAUgJ3froVL8</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-254225707\", {\"maxDepth\":0})</script>\n"}}