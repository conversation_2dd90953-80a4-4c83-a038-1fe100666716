{"__meta": {"id": "X4daa5ec8f8a64615fc2a2520552e5b50", "datetime": "2025-07-31 05:11:00", "utime": **********.166729, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753938659.38373, "end": **********.166756, "duration": 0.7830259799957275, "duration_str": "783ms", "measures": [{"label": "Booting", "start": 1753938659.38373, "relative_start": 0, "end": **********.095251, "relative_end": **********.095251, "duration": 0.7115211486816406, "duration_str": "712ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.095348, "relative_start": 0.****************, "end": **********.166759, "relative_end": 3.0994415283203125e-06, "duration": 0.*************, "duration_str": "71.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PmHhaJY7Q6Ye0vDFZLwt06n34NYkuWkq82PWORHy", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1752304910 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1752304910\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1981692816 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1981692816\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1131627719 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1131627719\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-647672264 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-647672264\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2101025757 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2101025757\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-850723006 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:11:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InkvYS9IUThEUWw1Yjl6eE9HRWl3T0E9PSIsInZhbHVlIjoiRVp2NTNEbHZMdXBnZUp5RU1ta2MzTHlHREgrNnJLN0t4aVFxVnJ3dDZranBCTzd3YU9kcWN6Y1ZlamUrSjNXc21XN0JoWGU0dk81eEdLeUpjWUhjdndzOS9sNGc2bWhiWklkWXp2TTA2K1RxRzBMTFhWV21tU3NFb3VMdGxucVVEcG96RHZMSHhoZ00rOEc5WTVBcXFTdTdEdk15RFBaR3FwdkhlUlVHdUZ3ZXFCRVBiMDUwU0xoaUgzaUI0OWozSitYNTcxT2YzTFNBL3JTbXdIb3VSaTJNSzg4QXEyWkxONjUzNE83MzUvN2ZmNGJVUnZ5M2xnYS9RMTd5d3JXc0dSQmJaRXlGcEh2eWNSRy9yYVFzTUJLU1F4aUQ5SW1DeWJPVC9mR3QybTd1d0ZRWWt3WC9Pc2EyODZkckhsVzdaTitpRGJCQm5GdXZJWXdQZkJlUy9rRWNTdkpaQzhITVdZNE5QcnVzNUZwMjZHVTVJamEvd1RtVEhNVjBSSVk1U0ZicnJXallLVXdSTXM5VHQ1KzdsUDA3S1RPM3R0bmE3R2Z3RXJ3T2RRVGRwTGltRG9sakZQcFNHZUg0V1VqQzROTGRyVnFNUEwraStpcUN0S1k2Z2ZWc0V3LzM0aS8ySVlRRE9Rc2YyYTczMFlHai9zaFpCZTBvNnc1ZkY5T1UiLCJtYWMiOiJhMzFhNWE0NDFkNzEyM2FiNDdiMTU0YzcyYjIwZjQ2ZTU4Y2UzYTc4NGQ1MTY2NzMwMDU0YzM3NjFhZjMyNGViIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:11:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik4xck5pS1JmcEFRTFZpNnBWMzhUZFE9PSIsInZhbHVlIjoiWDkzVGU2M0V6V21lTmF6SUtNMUUreGJGU2dKdjJLVytnRUFHWGhuQWFpcE5DcHh0TFhHTUR5cXdGTVo5cVcyOE8ybVFLSkl1cG1kUnlrbnBqbkR3WHVCejg4c3NpWHA4blEzQmx4cEhiRm5zZVRhOTVCcldvYnIvVWxLOTVTMXYvZlA1NlhadlBIRkw2eGNtREwxU012T1FJWDNZak95NnJ1S1ZmMXFheStRVGwrNXJ3anpQMXFsTjZBNjg5OTBUeEl3cGl6ZlQ0NkpFdEJ0UUNzN1o1ZWI2SWtRYjlIZzFrM1RaanVyckdHQ29CM1JMYVIrR1E3M1U0L2Y5ZEhSeWNhUmtpVE81ZTN1MjhYRW9YcTMxbWJrbjVBbGU3SUs3ckJzanFFWGszZFdycW0xNzFaT2pGVUY3Qmd0eld2dm9XT2xIZitReU80UDVFc2FtVEc4MzVEclQvU3ZhUzhIVVVCaGQ3ZUQwdGVucmZDcFpXOWU4SEFXSks1bkdHQWltY3krdmh3Ym1NMHFJQjZWRkJZaW9GMXIrYUo4RHV6QnI0N3cvbXhYNVhUaXMvNFFDZERmc2FEY0lCYnJFOVBOaElTTGFMUUtKUFZMYzNSam1CQ0NBRHM5WWVETHJCT1p5ZjZIa2JEQWllSXNJQ1ZLNC90NGJhZjR5c1huRzR4eWUiLCJtYWMiOiI1ZDA1MGFkODA4N2M2YjdiNmEwMWIxMGQzZTNjZGM2ODM2MGUwMzk4YjkxNjQyMTFmNmUwZmQwN2JlNzkyYzRjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:11:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InkvYS9IUThEUWw1Yjl6eE9HRWl3T0E9PSIsInZhbHVlIjoiRVp2NTNEbHZMdXBnZUp5RU1ta2MzTHlHREgrNnJLN0t4aVFxVnJ3dDZranBCTzd3YU9kcWN6Y1ZlamUrSjNXc21XN0JoWGU0dk81eEdLeUpjWUhjdndzOS9sNGc2bWhiWklkWXp2TTA2K1RxRzBMTFhWV21tU3NFb3VMdGxucVVEcG96RHZMSHhoZ00rOEc5WTVBcXFTdTdEdk15RFBaR3FwdkhlUlVHdUZ3ZXFCRVBiMDUwU0xoaUgzaUI0OWozSitYNTcxT2YzTFNBL3JTbXdIb3VSaTJNSzg4QXEyWkxONjUzNE83MzUvN2ZmNGJVUnZ5M2xnYS9RMTd5d3JXc0dSQmJaRXlGcEh2eWNSRy9yYVFzTUJLU1F4aUQ5SW1DeWJPVC9mR3QybTd1d0ZRWWt3WC9Pc2EyODZkckhsVzdaTitpRGJCQm5GdXZJWXdQZkJlUy9rRWNTdkpaQzhITVdZNE5QcnVzNUZwMjZHVTVJamEvd1RtVEhNVjBSSVk1U0ZicnJXallLVXdSTXM5VHQ1KzdsUDA3S1RPM3R0bmE3R2Z3RXJ3T2RRVGRwTGltRG9sakZQcFNHZUg0V1VqQzROTGRyVnFNUEwraStpcUN0S1k2Z2ZWc0V3LzM0aS8ySVlRRE9Rc2YyYTczMFlHai9zaFpCZTBvNnc1ZkY5T1UiLCJtYWMiOiJhMzFhNWE0NDFkNzEyM2FiNDdiMTU0YzcyYjIwZjQ2ZTU4Y2UzYTc4NGQ1MTY2NzMwMDU0YzM3NjFhZjMyNGViIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:11:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik4xck5pS1JmcEFRTFZpNnBWMzhUZFE9PSIsInZhbHVlIjoiWDkzVGU2M0V6V21lTmF6SUtNMUUreGJGU2dKdjJLVytnRUFHWGhuQWFpcE5DcHh0TFhHTUR5cXdGTVo5cVcyOE8ybVFLSkl1cG1kUnlrbnBqbkR3WHVCejg4c3NpWHA4blEzQmx4cEhiRm5zZVRhOTVCcldvYnIvVWxLOTVTMXYvZlA1NlhadlBIRkw2eGNtREwxU012T1FJWDNZak95NnJ1S1ZmMXFheStRVGwrNXJ3anpQMXFsTjZBNjg5OTBUeEl3cGl6ZlQ0NkpFdEJ0UUNzN1o1ZWI2SWtRYjlIZzFrM1RaanVyckdHQ29CM1JMYVIrR1E3M1U0L2Y5ZEhSeWNhUmtpVE81ZTN1MjhYRW9YcTMxbWJrbjVBbGU3SUs3ckJzanFFWGszZFdycW0xNzFaT2pGVUY3Qmd0eld2dm9XT2xIZitReU80UDVFc2FtVEc4MzVEclQvU3ZhUzhIVVVCaGQ3ZUQwdGVucmZDcFpXOWU4SEFXSks1bkdHQWltY3krdmh3Ym1NMHFJQjZWRkJZaW9GMXIrYUo4RHV6QnI0N3cvbXhYNVhUaXMvNFFDZERmc2FEY0lCYnJFOVBOaElTTGFMUUtKUFZMYzNSam1CQ0NBRHM5WWVETHJCT1p5ZjZIa2JEQWllSXNJQ1ZLNC90NGJhZjR5c1huRzR4eWUiLCJtYWMiOiI1ZDA1MGFkODA4N2M2YjdiNmEwMWIxMGQzZTNjZGM2ODM2MGUwMzk4YjkxNjQyMTFmNmUwZmQwN2JlNzkyYzRjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:11:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-850723006\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1261648020 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PmHhaJY7Q6Ye0vDFZLwt06n34NYkuWkq82PWORHy</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261648020\", {\"maxDepth\":0})</script>\n"}}