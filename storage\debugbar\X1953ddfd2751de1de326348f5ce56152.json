{"__meta": {"id": "X1953ddfd2751de1de326348f5ce56152", "datetime": "2025-07-31 05:35:39", "utime": **********.62366, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:35:39] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.601728, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753940137.798249, "end": **********.623756, "duration": 1.8255069255828857, "duration_str": "1.83s", "measures": [{"label": "Booting", "start": 1753940137.798249, "relative_start": 0, "end": **********.349955, "relative_end": **********.349955, "duration": 1.551706075668335, "duration_str": "1.55s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.349977, "relative_start": 1.5517280101776123, "end": **********.623769, "relative_end": 1.3113021850585938e-05, "duration": 0.273792028427124, "duration_str": "274ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50614672, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.552727, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03309, "accumulated_duration_str": "33.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4377701, "duration": 0.00542, "duration_str": "5.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 16.38}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.476064, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 16.38, "width_percent": 4.835}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4868429, "duration": 0.02428, "duration_str": "24.28ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 21.215, "width_percent": 73.376}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.520253, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 94.591, "width_percent": 5.409}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans/10/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-375688307 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-375688307\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1962050783 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1962050783\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2133520852 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133520852\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-616027841 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pricing-plans/10/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImZvUU9sd29FNnI0SUtrek5hbm15OWc9PSIsInZhbHVlIjoiMng5Z0tZUlkvOHlwQVpySVBYMitRaUttOW1GNWdjNC9zQ0JyUnpFZjdpb3c5NUtKZGlOTXZ6amd3aktCaGk4L25oN2REemRETFYxbzhiZjhCM2dvTHdUaCtqQWxlcU5LTVFVTHdjTXprYkR2NDh4eGVURWFlclpsUUVMMjJ5TjhsU3dGSmpyRXNLcnRhUVpMeVpTQUFDOFFRSmlJbm5Rc2lnQmpuVm4ycU96dlhuNG95cExyTmZ0TXFpMU5RN2ZlS21SNEFiZ2pRZng2UUNQcDk0YnF2eHdnZllMTEFsMGFyTG5KRGo2bFJrdlY0NTI4Rmc2N3N4QkRBNUNMZFkxalBwc3l0dHd3Q3Rta3pac2ZHSkxGb2JXREphQnNzNGZpQlZSWndKSkdDQUpmZld2aytpZkZQVWRNY3V0MnFhU3Zta2Ywd1pFWWI5cFhKT0o1WmttOE1YU3ZVdDlsMUYzSjF1R01BS1NpWGZkNnpmZENaWTRhU25XK1p2SzVHa2U2S203ckVnVjVCNmJDZk1xbG50dk1XRjM0MS9DcEF0dFF0WTZsWkdXQldiUi82OHplTlNFRVFWZ2ZnUzk0L2hSUHlyVnNvWW5FZXU2SFE1bm1ORHU3MzVYTllFN1JLRVkydWJ4RGZJZ2RVTnNiY00rSThFMHlaNkJ1cnNraDNjMHIiLCJtYWMiOiJhYTU2YzEzOTBhOTk4OTI2ODhmMjBkMDY5MTEyMGNhNmRkYjRhYmJjNTNkMTA3MDRhOTFmZmRkMDA4ZWE0OTdkIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Iklva0I2cFNadFJlR3YxZitYdERJNUE9PSIsInZhbHVlIjoiR2RkNzhyYmFCMUZabU1kUGdtcEptbmVSWDJNWllGRnN5eFNQdzN1TjNreS9jSGxOMmVOMHhXb0lWZFdObHhldnp5c3dGVFJib0Vxd0k4SDk2MENKbXJaRFZIR1FDSkpyRGdPajNSd0h3VzhaKzRyNW1FSDFQQmFNM2VOanE1ZjVuRUJzUkh3ZE81MVdKR0pwNEtRRGh6bEdKMHhpK3NHN0IvSk9WL0F1UTdDK2NJb2hUTjZCVkoveENmZlBDdVBvb1ZLWFZYUktXWnhLak9ZS0t3MzVYS2k5eU9GMEdYZjk5bWo5dVBhaU11d0UyblVBbXpCWE9ZRVd2WWhsOHJyalYrN1hMcS9hdUIvNDBFSi92a0s3VUFwV3diaDUzSU5BNGhEa2xiOU15RFo2NlZ5S1dxQXlNalFTNFhKeTZQVmEyM09wQU12K1JnWUxqa3drTWV4emdNYjFWZlVZbGJzZjljUTZONytaWm05QW80cUtQMVV3d0Q3czFpaDNscTJVY2syblJMVVRLZ2IwUm5LS09lbk5TcW9WZ0dCYUwwaW1paEM1YWo5UDFJejBtRWlvcjNtVjk2L2lNN0RWdVEyZkkxZW5sMSs5SlhKcjllaDBXUkRTOXZWOURiNUxPME9sQ3gzVE9mV0tVS1YwbXVFelUzYXg0dHRKMzRLRkRQc1ciLCJtYWMiOiJhMGYzN2VlNWQ2N2UzZjE2OGM3OGQzZjVhYWRlMTA2OThjNmViZTNiODQ4YzI1ZDlmMjNjNzE2YjZkOTNlMTI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-616027841\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2014008751 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lOWuOQi5K5zalKq6lxC9bmXS2G5Yh3URND5pGg9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014008751\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-284841988 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:35:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVyMDI0SEhpZ1BVWFJFbW5qaHRXTHc9PSIsInZhbHVlIjoiTGdGbXI1YnNEL3A5aEtZTXpVQTZYa3BxTzBTdjhmZ01MSEg2cDFzYUw3TjZaSjYzU0NCa3FsWnVHeXBkSFJoSUU2cCt2Zy9xYUlxbjExNFVwbEtyNi94dFB5blhSZTcybmNiOGRQSGZ1VEFyandNd3ZkUmxTRUJUQmhYbzFjSTMvNUNhZUdNOGNpTlRZTWhqckp6VmYwbmI3eTlxaU12bEdNdnN4NjZQTmxTMmZNUmxSY3JIck1abkE5WHFTdnkzQ2RSSUk0cVJaUWl3czZqdllaT2Z3b3Vtdm1Xd0FDL01wVGJvZ0paZjJUQmVKbTVTTGVUSzcrSzRsdEU2dFY1a0tRNnFFcXdvS2pBUmxFbWlDRHMySUJPaHQ3ZDJTR2VlZDVQRW9FaWFocmh3MlBOZW1kdGNGeExqamg3T0swQ21SUlAyVmVnMFpLdUJKSDgvRFN1TzcxQk1BcUVBTzBUeUp1dXpaTXBBZmtmZEdZTnFZU09uQUVpMWRzbWcxem9pU0x5bysrZXN0N1lFM3dObjA1a0Y5YmErZ0lVYkoyQVNuVFNITDE5YTg5VCtCcG5LcEtSTTV2dk1NN2diNEpMdGJmcTR1a2o5MW5xK3hqVEUrWnNiU1N6Mm5WZ1NPMUptL3cyTVNOeWUxc1R0c1BFaDdMUC9YcmRDVVluSERjRWciLCJtYWMiOiJiYWM3ZjY5Yjg2ZmZkYjA0Y2VjNDgyMmE3NzA0YjliZDIxODRhMzAxNTNmMDA4ZmQ5MmY5MDk0YmJlODFiNzhkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:35:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InBGR3JiYnQvOXRjTk5yT2ZNMDE3dEE9PSIsInZhbHVlIjoiWHFwOFYwem0zNThOWlY5WmR1UTRZSHRSN2JQUFlXUURoWW1wNk1mVnVTb3dJVDduU2xTU2JhTUJGWTNFdVVzU0t2TUl0a0Q1ODdQeEJjVnVVVjVKbEhuZUtxVGlyeitQaHdoTFJNNHBGdzZxWWVlbmZjWGcxN1hIT3BXWmFBU2x5ek9DYUNlS2JGT0NKeTZWQXUvTG9NaitLS1lWd0F1cktyNWpoMEt2VUZlK3lDOTZaQzFMZWh0bzJrc1dZbWJPaXFKSmtyQUxDYVJqRFdzTTltZmVKT0J5aHBNd2IwQUVteXE4VHZPRHFQWTBtMk9zRlBGdHBvbW9zTldmUWlyR3h2ODc0STdlT2EzQXVzaXhwK1V0MDQwVDVyWUFTVkg2Tit0L3N4VTYybDBQaEZKT04wd0ppQTR4MU9tNXZwQkJCVHV4TmNSdXZUOEE2MUV6ZFdlRVI5UWxrekM3VHIyVHNQYjg1MEFIeDArSER4Q2lyNXRVV3VzaFdveElxbjFCSGFZSitEZ3dsSHROOEdFWkN0NGZLZGdjVXlOSTArVXR2N3h6NS9SQlF6andGdU1sN2xTb2tPK1lZeFJocDNWRHk1TTc4NVNZaUlWMzBhTTJ3ZUdLd2M4RUpncU8wd2JHTGNBSWFKaXU0MVhMNE14RjRBUEk3M2h4NzJpZEdSUHkiLCJtYWMiOiI3NjI3OGVjY2U3Yzk2YmVmZjc4OGU3MTZiODFjNDZiOGE5ZTAzMTlmZmY5Mjk5Nzg2N2VkOWU0NzEyODgyNTJiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:35:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVyMDI0SEhpZ1BVWFJFbW5qaHRXTHc9PSIsInZhbHVlIjoiTGdGbXI1YnNEL3A5aEtZTXpVQTZYa3BxTzBTdjhmZ01MSEg2cDFzYUw3TjZaSjYzU0NCa3FsWnVHeXBkSFJoSUU2cCt2Zy9xYUlxbjExNFVwbEtyNi94dFB5blhSZTcybmNiOGRQSGZ1VEFyandNd3ZkUmxTRUJUQmhYbzFjSTMvNUNhZUdNOGNpTlRZTWhqckp6VmYwbmI3eTlxaU12bEdNdnN4NjZQTmxTMmZNUmxSY3JIck1abkE5WHFTdnkzQ2RSSUk0cVJaUWl3czZqdllaT2Z3b3Vtdm1Xd0FDL01wVGJvZ0paZjJUQmVKbTVTTGVUSzcrSzRsdEU2dFY1a0tRNnFFcXdvS2pBUmxFbWlDRHMySUJPaHQ3ZDJTR2VlZDVQRW9FaWFocmh3MlBOZW1kdGNGeExqamg3T0swQ21SUlAyVmVnMFpLdUJKSDgvRFN1TzcxQk1BcUVBTzBUeUp1dXpaTXBBZmtmZEdZTnFZU09uQUVpMWRzbWcxem9pU0x5bysrZXN0N1lFM3dObjA1a0Y5YmErZ0lVYkoyQVNuVFNITDE5YTg5VCtCcG5LcEtSTTV2dk1NN2diNEpMdGJmcTR1a2o5MW5xK3hqVEUrWnNiU1N6Mm5WZ1NPMUptL3cyTVNOeWUxc1R0c1BFaDdMUC9YcmRDVVluSERjRWciLCJtYWMiOiJiYWM3ZjY5Yjg2ZmZkYjA0Y2VjNDgyMmE3NzA0YjliZDIxODRhMzAxNTNmMDA4ZmQ5MmY5MDk0YmJlODFiNzhkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:35:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InBGR3JiYnQvOXRjTk5yT2ZNMDE3dEE9PSIsInZhbHVlIjoiWHFwOFYwem0zNThOWlY5WmR1UTRZSHRSN2JQUFlXUURoWW1wNk1mVnVTb3dJVDduU2xTU2JhTUJGWTNFdVVzU0t2TUl0a0Q1ODdQeEJjVnVVVjVKbEhuZUtxVGlyeitQaHdoTFJNNHBGdzZxWWVlbmZjWGcxN1hIT3BXWmFBU2x5ek9DYUNlS2JGT0NKeTZWQXUvTG9NaitLS1lWd0F1cktyNWpoMEt2VUZlK3lDOTZaQzFMZWh0bzJrc1dZbWJPaXFKSmtyQUxDYVJqRFdzTTltZmVKT0J5aHBNd2IwQUVteXE4VHZPRHFQWTBtMk9zRlBGdHBvbW9zTldmUWlyR3h2ODc0STdlT2EzQXVzaXhwK1V0MDQwVDVyWUFTVkg2Tit0L3N4VTYybDBQaEZKT04wd0ppQTR4MU9tNXZwQkJCVHV4TmNSdXZUOEE2MUV6ZFdlRVI5UWxrekM3VHIyVHNQYjg1MEFIeDArSER4Q2lyNXRVV3VzaFdveElxbjFCSGFZSitEZ3dsSHROOEdFWkN0NGZLZGdjVXlOSTArVXR2N3h6NS9SQlF6andGdU1sN2xTb2tPK1lZeFJocDNWRHk1TTc4NVNZaUlWMzBhTTJ3ZUdLd2M4RUpncU8wd2JHTGNBSWFKaXU0MVhMNE14RjRBUEk3M2h4NzJpZEdSUHkiLCJtYWMiOiI3NjI3OGVjY2U3Yzk2YmVmZjc4OGU3MTZiODFjNDZiOGE5ZTAzMTlmZmY5Mjk5Nzg2N2VkOWU0NzEyODgyNTJiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:35:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-284841988\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-611315745 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pricing-plans/10/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-611315745\", {\"maxDepth\":0})</script>\n"}}