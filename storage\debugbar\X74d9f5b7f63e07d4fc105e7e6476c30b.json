{"__meta": {"id": "X74d9f5b7f63e07d4fc105e7e6476c30b", "datetime": "2025-07-31 06:03:05", "utime": **********.923378, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753941783.426646, "end": **********.923416, "duration": 2.496769905090332, "duration_str": "2.5s", "measures": [{"label": "Booting", "start": 1753941783.426646, "relative_start": 0, "end": **********.792855, "relative_end": **********.792855, "duration": 2.366209030151367, "duration_str": "2.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.792904, "relative_start": 2.***************, "end": **********.923419, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "131ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VWaqd6oSgWIfJKUM10SbfhCbTTGQlMzV4L7ZtItr", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-744990755 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-744990755\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-162108408 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-162108408\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1497390227 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1497390227\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-430441525 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-430441525\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-511144706 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-511144706\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1435590209 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:03:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9zaGJMenhsa01aQ1hxNFpDSnZobHc9PSIsInZhbHVlIjoiSlpWODVVVldlODM0UWZCRndQeVUwRi9qTnlPYUJjYnF5TGpENXFBbmdrWTg4eW0wZm5IRDAwcEpiYjNkcDFXbTFqSWpwWjZUMzc3czFOTDNxaGFCMzZJeDdyUWxiQThGRDFqMkZyMUVDYkN5cUtVeFJWUDJlbmVsbW5CYWNHSE9vZHBVRTdFWGZtRjNQUTdKbG5mRUQxdTIxMU12RUREMTJTTkpxQ3pNSkZoQWlxcnkrbnRZTUFVMDMwcTUxQk1pTkpGRHVnSmN1TEZ4K2VDZmk2S3hsSjVaalRqWk9XTk1sVVdYdFFjZnpIVFBlQWZCZzJoQndLa1FkdTdDOUg2OFNNbnRmNC9vTGdhTTN0ZWo0Y1FYQ1pPZjQ5TVUwbEpXZGhMUjZlOHFPK3ZscjZ4UTVjWElIam82ZmZMRjNjZi9IbTdLRmlwTmp3eGN2WFNYZnpLNDNQNk1QSnpCOWpsWkpYeER2QjhnUXJDR3pCN0VmUjJnekxVV1lKc2I4NVJjNEVGeDJiTENodmR3b1VOU0tNazd1elNRNzVwQTlLVm5URmtFemFLeHc4T3dXMDl1V1NnKy9wbXdNYlIzQVFEU002ZTRPTXFaZ3dsUnFpbHFPNDk3UmY2aURXUDRNcWpYZ2c5Vi8yVnVXLzJDTTdQS0F0TzcrSFkzOSt6Zml2b0YiLCJtYWMiOiIyYmU2YTllMWEwMWUyMGM2ODE5N2NiNWFkYWM2YTBlYTI5NmMwODAyNWQ1OTUyMTc5MDk5OGM1NDliNjhmMGIxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:03:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik5ISXM2REx3QU1vckRQU0lPaEJFM2c9PSIsInZhbHVlIjoiWFhwcXhmZVVpL3JsOWYxem1TUnVPaENkaHJFUy8yYUEzQVlsV0NKTFQrQ2V0WnpnY0FSTE9zMU5YVlJ6V09HenkyUmx3alRzVSszUkVYRjI5RFI3WUVCVGZpZDc2bUxuVHBNZkVIYzFPS0tHZWlTcW41T0M5andjUnJ5V1U3a3VzclhNL1dldTlBNVBaakJKWmpBYk9jZXA4aFRjMWpWL3prbCtLMjF6Y3ZnNmxGMGJCTnQvT0lPamFBUUtzRU5rOGhuUkJBS1ZLNnhWNm9NbEFXNlJCVU0vYXFaVU1hU3kvYU91VWNvblZSL3F4bjQzRXJncVlOQi9DTENpYjY1Z2xMM09jNHV1RktIV3RjbUF2RUl5NytRYld3WklPUjMwQTNLYUF2ajZvWHlZL3RPRWhJdUtEUGJBaXR2Y1VGbWtmK1FrTHhMYk02K2ZCRE5SVW51a1Vsemt1dVRrSUg0L0F2amhlTnloN2JFb2M1SGVQak9qL1UrTTFQeVRvZ3YwRGI1Mk9yMmw4NWNSZG8xM0srSnVYZjJ2aUE5ZDNqbHdVVms2bWtXL3lDbnJIdkZsNEllR1FybUoxVlhCVkpaMmpabFVXRTZIUHp0dU11ZFZPMzVOVFVEZ1phZXRLcitwendUVU5GaXlsWXViUE1oSGJsVnVnSm55V2RsZno0dk0iLCJtYWMiOiI4MzcwZGFiMjBiNWM2ZmJmOTM0MzJjNjFjMjY1ZjlhZjM5OWRjYTA0ZDZiMTNiNGU3ODEwNDU0MGJkOTMzZDM0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:03:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9zaGJMenhsa01aQ1hxNFpDSnZobHc9PSIsInZhbHVlIjoiSlpWODVVVldlODM0UWZCRndQeVUwRi9qTnlPYUJjYnF5TGpENXFBbmdrWTg4eW0wZm5IRDAwcEpiYjNkcDFXbTFqSWpwWjZUMzc3czFOTDNxaGFCMzZJeDdyUWxiQThGRDFqMkZyMUVDYkN5cUtVeFJWUDJlbmVsbW5CYWNHSE9vZHBVRTdFWGZtRjNQUTdKbG5mRUQxdTIxMU12RUREMTJTTkpxQ3pNSkZoQWlxcnkrbnRZTUFVMDMwcTUxQk1pTkpGRHVnSmN1TEZ4K2VDZmk2S3hsSjVaalRqWk9XTk1sVVdYdFFjZnpIVFBlQWZCZzJoQndLa1FkdTdDOUg2OFNNbnRmNC9vTGdhTTN0ZWo0Y1FYQ1pPZjQ5TVUwbEpXZGhMUjZlOHFPK3ZscjZ4UTVjWElIam82ZmZMRjNjZi9IbTdLRmlwTmp3eGN2WFNYZnpLNDNQNk1QSnpCOWpsWkpYeER2QjhnUXJDR3pCN0VmUjJnekxVV1lKc2I4NVJjNEVGeDJiTENodmR3b1VOU0tNazd1elNRNzVwQTlLVm5URmtFemFLeHc4T3dXMDl1V1NnKy9wbXdNYlIzQVFEU002ZTRPTXFaZ3dsUnFpbHFPNDk3UmY2aURXUDRNcWpYZ2c5Vi8yVnVXLzJDTTdQS0F0TzcrSFkzOSt6Zml2b0YiLCJtYWMiOiIyYmU2YTllMWEwMWUyMGM2ODE5N2NiNWFkYWM2YTBlYTI5NmMwODAyNWQ1OTUyMTc5MDk5OGM1NDliNjhmMGIxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:03:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik5ISXM2REx3QU1vckRQU0lPaEJFM2c9PSIsInZhbHVlIjoiWFhwcXhmZVVpL3JsOWYxem1TUnVPaENkaHJFUy8yYUEzQVlsV0NKTFQrQ2V0WnpnY0FSTE9zMU5YVlJ6V09HenkyUmx3alRzVSszUkVYRjI5RFI3WUVCVGZpZDc2bUxuVHBNZkVIYzFPS0tHZWlTcW41T0M5andjUnJ5V1U3a3VzclhNL1dldTlBNVBaakJKWmpBYk9jZXA4aFRjMWpWL3prbCtLMjF6Y3ZnNmxGMGJCTnQvT0lPamFBUUtzRU5rOGhuUkJBS1ZLNnhWNm9NbEFXNlJCVU0vYXFaVU1hU3kvYU91VWNvblZSL3F4bjQzRXJncVlOQi9DTENpYjY1Z2xMM09jNHV1RktIV3RjbUF2RUl5NytRYld3WklPUjMwQTNLYUF2ajZvWHlZL3RPRWhJdUtEUGJBaXR2Y1VGbWtmK1FrTHhMYk02K2ZCRE5SVW51a1Vsemt1dVRrSUg0L0F2amhlTnloN2JFb2M1SGVQak9qL1UrTTFQeVRvZ3YwRGI1Mk9yMmw4NWNSZG8xM0srSnVYZjJ2aUE5ZDNqbHdVVms2bWtXL3lDbnJIdkZsNEllR1FybUoxVlhCVkpaMmpabFVXRTZIUHp0dU11ZFZPMzVOVFVEZ1phZXRLcitwendUVU5GaXlsWXViUE1oSGJsVnVnSm55V2RsZno0dk0iLCJtYWMiOiI4MzcwZGFiMjBiNWM2ZmJmOTM0MzJjNjFjMjY1ZjlhZjM5OWRjYTA0ZDZiMTNiNGU3ODEwNDU0MGJkOTMzZDM0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:03:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1435590209\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1076491907 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VWaqd6oSgWIfJKUM10SbfhCbTTGQlMzV4L7ZtItr</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076491907\", {\"maxDepth\":0})</script>\n"}}