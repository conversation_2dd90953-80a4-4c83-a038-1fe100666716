{"__meta": {"id": "Xe80781eafd5925cde24b2b45625ed782", "datetime": "2025-07-31 05:33:54", "utime": **********.263213, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940033.03655, "end": **********.263272, "duration": 1.226722002029419, "duration_str": "1.23s", "measures": [{"label": "Booting", "start": 1753940033.03655, "relative_start": 0, "end": **********.1671, "relative_end": **********.1671, "duration": 1.1305499076843262, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.167132, "relative_start": 1.****************, "end": **********.263277, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "96.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "y5Lr9MywSharKNzFQgn7pIp7V8Ykro1CZH5mSYQ9", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-692969165 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-692969165\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-224658550 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-224658550\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-352982827 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-352982827\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1480507686 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1480507686\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-548109694 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-548109694\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:33:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjF1QzJwV1VkUWc3cU5YYTVZb1VUNEE9PSIsInZhbHVlIjoiMGJjbEZEZDRDc3FhUjN4NG14R0szZ3FBMUR3MU53MlNCeHFYT1NCdkluRjhRcGJ1U0NndXVmV2tFa213bmR5RDh6NjN2ZVJqaHo4cGEyQ0Z5ZjM3bDNBdkhnNXlZRzVYcm9jSnpnTmNRaXB4eDF2Qjc0aUl5bXJZMUdTc2lKWlJlTGo1RzdVN0JSS0pMd0Z1K0k2T1FHaDFMcVhnd1JhbmN0VktydnJ3TGZzR1BIbU83eXYrVUdmbXdPZXJObHljZUMvaThQTW14eE1FVjlFNXRPYmJib293Tm04OWVaL1kxTWtXNzBwRzRkU2hXVm5zM0pDVzVsNGxJTnNuZDJRUGQyOG9pdFVyZ3pJSVB4ZG1lb3MwbldiS25zaDU1MTg3YVdCdDBsL1NJOVhKQkFZL2Y1anhzRFRYODJseFE4ZHlHNlRvRFZCaWNGdDM3TW50aXdwZ2ZtcGxIS1pta2ZmdHlmWHJtaXc0SkcyREVsL2tSQUQ2SW1SSk9IeUVySzJENHh1VWlib1RFbUFsaElaaE9YWTBPcDVGdmt0N3ViUzJ5YTRrRTJkWE1aTy9wWVpuUlNJY3VaeGFYZ0F1UzA3bW1sQlh6Y0F1T3RXYlRVNHF2S2ZjNjBuK2RwdVJMNmE5YURIYnpqL0VZU3llNXNlOXJzVkJUSFVTNkw1WVhScnciLCJtYWMiOiIzOThhY2RmNGMwZDBlZGJhMGZjM2VmNTcxYmYzODA0ZTM4YWEyZTliMWJlMDdkMmViYzk3M2I0MWE3MmNmMWRkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:33:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik1Ud2t0ZFBuV1BpZldTbmxDWTdKNVE9PSIsInZhbHVlIjoid0tBTE1MeUtReDNKUjlQSXRuQ2ZPT042OXFpWWFPR3JsSDd2NWVvOHhsQjNPaVd4N2ZrQ2hzb29YM0pDeXE0Rjd4ekZPYnlVZ1duaGxDRWhaWkJBT1VpaGJwbm5FYkpxM0lONDdQVjVCWENsci9BdVZ2YVV4VTFYMjJ5WTVOQlFoZmx3SFhvQUFyNGVHSEtiSkcrWE50a2xOQ0ZoL0lwblRtSnYrSkI0cGN5NEt1K0kyaCtPbTdXMTlIUmxHUjhHOGV0L21sbUcxWi9YTXFweVl1UW9KeFVINytwai9DcHRReGcvdzFwMHBaQWpDbkF5M2JFYWh2UFh5QnVtaEw1bEQxTTRUa3ZNRXFXRUlQa0FLcGlxOU80ellqZURyTWpMZ1VXTEtrV2x2T2VaOVdCM2ZlOFN5dDFPRE5hR1I5WVUrRlNmTlpzNE1ReTJEc2dQUnc4UW12RGtMem1EWFR1cnNjdnNONnZKN0lkOEE0L2VWZjVzUVpJNXcrOHA3OHl6Zm1Qa083cDdzeXJoZlNwRG4ydWh6TGs1UUhBcENNT0pBek03L2dwUU5oczZrRlp6TGI1b3IwNE9ZNVdiMEwxMnhkV2RpcGpyVFRETi9reGZ0QXJtT2ZyTjZWS1NEdlhrSlVTdUVsN0svbFppVlFYY0VrT0pzc2dGSU9XQWVXMUkiLCJtYWMiOiJjMzVhZDY2M2M5NWFjNzU3YTcwODA0YmU5MDAyY2RhMzQ1MDcxMWE3OGRlYTU5NWYwYjY5ZTlhYjA4MTAxM2Y5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:33:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjF1QzJwV1VkUWc3cU5YYTVZb1VUNEE9PSIsInZhbHVlIjoiMGJjbEZEZDRDc3FhUjN4NG14R0szZ3FBMUR3MU53MlNCeHFYT1NCdkluRjhRcGJ1U0NndXVmV2tFa213bmR5RDh6NjN2ZVJqaHo4cGEyQ0Z5ZjM3bDNBdkhnNXlZRzVYcm9jSnpnTmNRaXB4eDF2Qjc0aUl5bXJZMUdTc2lKWlJlTGo1RzdVN0JSS0pMd0Z1K0k2T1FHaDFMcVhnd1JhbmN0VktydnJ3TGZzR1BIbU83eXYrVUdmbXdPZXJObHljZUMvaThQTW14eE1FVjlFNXRPYmJib293Tm04OWVaL1kxTWtXNzBwRzRkU2hXVm5zM0pDVzVsNGxJTnNuZDJRUGQyOG9pdFVyZ3pJSVB4ZG1lb3MwbldiS25zaDU1MTg3YVdCdDBsL1NJOVhKQkFZL2Y1anhzRFRYODJseFE4ZHlHNlRvRFZCaWNGdDM3TW50aXdwZ2ZtcGxIS1pta2ZmdHlmWHJtaXc0SkcyREVsL2tSQUQ2SW1SSk9IeUVySzJENHh1VWlib1RFbUFsaElaaE9YWTBPcDVGdmt0N3ViUzJ5YTRrRTJkWE1aTy9wWVpuUlNJY3VaeGFYZ0F1UzA3bW1sQlh6Y0F1T3RXYlRVNHF2S2ZjNjBuK2RwdVJMNmE5YURIYnpqL0VZU3llNXNlOXJzVkJUSFVTNkw1WVhScnciLCJtYWMiOiIzOThhY2RmNGMwZDBlZGJhMGZjM2VmNTcxYmYzODA0ZTM4YWEyZTliMWJlMDdkMmViYzk3M2I0MWE3MmNmMWRkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:33:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik1Ud2t0ZFBuV1BpZldTbmxDWTdKNVE9PSIsInZhbHVlIjoid0tBTE1MeUtReDNKUjlQSXRuQ2ZPT042OXFpWWFPR3JsSDd2NWVvOHhsQjNPaVd4N2ZrQ2hzb29YM0pDeXE0Rjd4ekZPYnlVZ1duaGxDRWhaWkJBT1VpaGJwbm5FYkpxM0lONDdQVjVCWENsci9BdVZ2YVV4VTFYMjJ5WTVOQlFoZmx3SFhvQUFyNGVHSEtiSkcrWE50a2xOQ0ZoL0lwblRtSnYrSkI0cGN5NEt1K0kyaCtPbTdXMTlIUmxHUjhHOGV0L21sbUcxWi9YTXFweVl1UW9KeFVINytwai9DcHRReGcvdzFwMHBaQWpDbkF5M2JFYWh2UFh5QnVtaEw1bEQxTTRUa3ZNRXFXRUlQa0FLcGlxOU80ellqZURyTWpMZ1VXTEtrV2x2T2VaOVdCM2ZlOFN5dDFPRE5hR1I5WVUrRlNmTlpzNE1ReTJEc2dQUnc4UW12RGtMem1EWFR1cnNjdnNONnZKN0lkOEE0L2VWZjVzUVpJNXcrOHA3OHl6Zm1Qa083cDdzeXJoZlNwRG4ydWh6TGs1UUhBcENNT0pBek03L2dwUU5oczZrRlp6TGI1b3IwNE9ZNVdiMEwxMnhkV2RpcGpyVFRETi9reGZ0QXJtT2ZyTjZWS1NEdlhrSlVTdUVsN0svbFppVlFYY0VrT0pzc2dGSU9XQWVXMUkiLCJtYWMiOiJjMzVhZDY2M2M5NWFjNzU3YTcwODA0YmU5MDAyY2RhMzQ1MDcxMWE3OGRlYTU5NWYwYjY5ZTlhYjA4MTAxM2Y5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:33:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">y5Lr9MywSharKNzFQgn7pIp7V8Ykro1CZH5mSYQ9</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}