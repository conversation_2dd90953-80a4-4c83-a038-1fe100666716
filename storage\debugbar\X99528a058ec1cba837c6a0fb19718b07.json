{"__meta": {"id": "X99528a058ec1cba837c6a0fb19718b07", "datetime": "2025-07-31 05:34:56", "utime": 1753940096.008802, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:34:55] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.996376, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753940094.311155, "end": 1753940096.008866, "duration": 1.6977109909057617, "duration_str": "1.7s", "measures": [{"label": "Booting", "start": 1753940094.311155, "relative_start": 0, "end": **********.734184, "relative_end": **********.734184, "duration": 1.4230289459228516, "duration_str": "1.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.734251, "relative_start": 1.423095941543579, "end": 1753940096.008871, "relative_end": 5.0067901611328125e-06, "duration": 0.27462005615234375, "duration_str": "275ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50614672, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.969055, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.034519999999999995, "accumulated_duration_str": "34.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8580651, "duration": 0.00824, "duration_str": "8.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 23.87}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.9045599, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 23.87, "width_percent": 4.722}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9171271, "duration": 0.02275, "duration_str": "22.75ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 28.592, "width_percent": 65.904}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.946208, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 94.496, "width_percent": 5.504}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-493712939 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-493712939\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1178859551 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1178859551\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-226201670 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226201670\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-264590009 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik1XZk45Y0tlYVA0K3hDZG9ybHF0YXc9PSIsInZhbHVlIjoiczE1VVdDV3h5QWNvMStrbmhWcDFTeFozeHgzNjJuaHlyRVdNT0hGMm9nRVJLSEp0cWN5R2N2UFRTQU5aS2pPRVpJc3hLb2t1bk1NbnlmRGh1NklJWjFGbm90VnF6TDMxeGQvSkdJRGN1WUU0dVc0cnJ5ZnJOckhseVYrK2IzYjVjMSt0ZkdyYlh0RGJPd3BKeTZxNkw3UWRVWm9HKzFxRlpNWUZrTEFscEY0QnpWZHJoVm1teE5pOGVXSDdiTkZ1QURjMUg3bFRUWXBRUzBSdk52ZzRQZUZJMmYwK3NSWnF1RktEY2ZuVkZZNE9xbFZNY1IzMXZuZDQ0Z1lLQlJiaXVYa3NFNE1lVFJKVGdhRVUxZnBVQjlBOFJmMVJrdDVnbFRmL0pBL2RYaGFYQVMyMHBTMmNMalBVRTlFVTNtdGZHTjBxd2NYdGpxZjFzUUY2WU1heE50UVZZSG9JblVlL3QzS1k4TlluYXFiNE10QlVqaHZ5WGN1M3RsQXduazdDR2pBZ0U2TkxjU0tNUGU2aXFTcGhPZ2ZSaE43aEFWdG1LVUptejV3RkVVMysyOGluSG9KZ0dmSXF2Sm5KbzloRmVKamRBQnNKUTFzaHdPMEhGYWxiWkJZQlkrd3JMekdTQm1sdzFrSndpWXB1MWFhWWRNbUFpempiYlBySzJoY1AiLCJtYWMiOiIzMjc4YTM0Y2YwMDE1OWE2MzJhYmU4MjhmYTUyYmQ0OWQzNGRlMzU3MWQ3ZDI0YjU4OWI0MmMwMGZkNjI1MGU2IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlBMaFB2a0xpb3NNTEp6VVlDeHRJdVE9PSIsInZhbHVlIjoiT1dxVGw0U2NnQzk1QXpEWm5zb1VYVE5tTG5CM2w1QVJlbXIwellFSTFRVFpaR3ZzNFpXVWJQeC8yTWYyY3FVc0UxQ2FoUzcrcUpqdlhzMHcraVA1a2thK0dJNGQwdytWTkpzSDZ0TGlldDYxU01EWjZZdEpreVRUOWcvNVVoY3RWN011aW41UlQwL24yWXd1S09BYlZIZ0ptSlUyUUNqUS9yMlRJdXI5ZmdCbFl1SWNITFhqckJaOW04Ry83NWQyVHlSMmtTa2h6RUpVYzcyN0dvQ0dXb0RvQXg4N3JJMllwb2xFMmwxcXcvakFyWXEvU0xsYVhBbGYrZ0RGSERVWmwrd2tiMW8vNDJMRTh3b25qOHJ0bGNxN2xPWFVUaFU4czFaempNL3N0dlBMZW9FRjZ0RVlKOVB6czJYQm9kb1V3ZHUwenpEZDR0c2Q4N0hsbkIvazVWVVNvK0g2Q2J1OG5wTFUwWFovY1RtK0dLTmlHcmdFOWRFKzRUUm5sdnpJcHdWZDMyWHNnWGNRMXNPQ0t5RjBnSVB3ZWltejFpM25kMGpKa0hOMlgyUmNveHhmK2J6YTJvS0RkOXFPOStJZU1WWDhNUG8zYTJFbEVNN2J5RkVOV29aZTlPa3o3blVqaS9LZytkTUFhY2NwMFNpeExKdFBwY0VBTXdKOGVqTWgiLCJtYWMiOiIxZTcwNjY5MDEzNDUzZjE1ZjY4NTRjNDRlNzg3ZTRiOGFhNTFkNGNkNDA2YjBmZDlmN2QxNjliM2E5ODY4MThkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-264590009\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-633427096 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lOWuOQi5K5zalKq6lxC9bmXS2G5Yh3URND5pGg9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-633427096\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1487034806 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:34:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxLcGxEN2d0VlQwZmpvcE1JdDZkR3c9PSIsInZhbHVlIjoiUTcxTW4xSllMV2dBRnZtYndSU0J0UUNuT3lpTEkyOCtTdENTMm1pNEVXazNtZ0dBczlSMy9WQWVzcjFvbkVvR1BoOHo5T05kYm1yNEtQUE9NdVA2Z1BXb0xqMVlVb3NJU3pJcXd6SGo0b3kyUXpGQ0h1OWdLNk1KYlVaL2tGejczUGhZc2Q1SDBCMHhQbTdQZWlHTEdKc0ZaZG5aMThuSDVrZzhva3Q3TnVkcDdPQ3l5RktlOHBJMVFPaW0welhSMFdTc2pZVHluVG54RWVUYWdGNC92VW95NlRwR284ZG5nNStiY2w5M3NXdnNMaFpsNTJPTGJLVkg4VmlxMUZrSmhSS1lPY1JxUGFuK3RuQ09kYk1zNG5aRmJaMG5YOGtST05YU0gzUG1MblY5SVJWWFMrS2F1cVBoa2xYQzl4WDVCVHkxbGErcktBNW84Z1NHQ09NRDV3QktSam1md1hBeUdWTmU1Y2ZtcHI5aC9ST1BtUlo0d0picWFwaGRtTzBPd0JBN1ZvaGp3eVlPTmdRVGRGRTdobkVzWEk1UktvdTA3dE03WWRXWW9yeVBua3RGWlhDaERtZU5CcnVJTDNuUmkrUURrQjNaTjdnRGVEZ2Rpemh0NEhDSG9CbnV2NWdYUkhZTkR5enJ6UGUwK1VtQS9NNm1KU3RLak4ycVRacnciLCJtYWMiOiIwOTM0MjA4NGFhZGEwOTAwNDAyNjc0NjExY2Y2M2QyZjI5ZTEwODFkZjVmMWZhYjkzOWFiZWY3MDhiNmEyNTZhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:55 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InBvSkdsbW9IUVplQlpzRy9WQnRFdkE9PSIsInZhbHVlIjoiVVgvN3RPa1pkeFppM2laREZsM2NhaGlaRXpUYTdUcmtpTUFhbGMwdnBHeW5BbmxpeWI1dnpQNi9CRmtwbmx4MWMvakFrL2hpWHA2c1ZNMGpGMENQbm0zYnhOK2w5cWQ4b1JzeXJnMnBmMFIzcUplaXNJZWZoS25TUjNrVENTMDc5dGszRE5FUjkzdyt1ZnNEaFczV1hCTzMwdnJrYjl6RHBtNW1xMGhML1pyRCtZblhMbGprbEhuYWZOQ1QvSk1HOFJ4c0dib0F6K2VPeFlmMmxJUWROQVZrbmxkd25uNkZMbGtrNi93YjBRdmhjOHg0U09yeEZiTmtWT2paV1JwNktQMjBUSVRqQmhzSXdseWxwVXkwVWJyM3JSQmI1QTVtY2VCd3dpOGcveHJndnVEbnpuRzR2L1VtY2t5Z1o4YjF5dStKMmNqRG1XbGF3YjBpc200LzkwS3d5OXJPSlJ3bkZOQlgwZHZFeUN0WFpQekFqamw3elpKZGFVblo5SGFLY2FuT2Z3UVlTaEZoNmxDOVF3d01RcmQ2bVV6d3lWYjVHb3dKUHJRdWpRb2FPVTYySytyTE9VenJ5MG04cTRVcUFveVlwamNac2pVNVAwMTdFU0hHYXNmN1BubkpobUszY09hTWFwM25vRGloNHB6WEdVdW4vdnhGaVFmNjhHaHgiLCJtYWMiOiI5OWE2ODYxMTQ5Y2QxYWU4YjYyNzU1ZjI5YzI0NTI3Mzc1NjhjZGZiMGQ3YjY1NWNiZmIxNDc5NmI2YjAxNGIxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:55 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxLcGxEN2d0VlQwZmpvcE1JdDZkR3c9PSIsInZhbHVlIjoiUTcxTW4xSllMV2dBRnZtYndSU0J0UUNuT3lpTEkyOCtTdENTMm1pNEVXazNtZ0dBczlSMy9WQWVzcjFvbkVvR1BoOHo5T05kYm1yNEtQUE9NdVA2Z1BXb0xqMVlVb3NJU3pJcXd6SGo0b3kyUXpGQ0h1OWdLNk1KYlVaL2tGejczUGhZc2Q1SDBCMHhQbTdQZWlHTEdKc0ZaZG5aMThuSDVrZzhva3Q3TnVkcDdPQ3l5RktlOHBJMVFPaW0welhSMFdTc2pZVHluVG54RWVUYWdGNC92VW95NlRwR284ZG5nNStiY2w5M3NXdnNMaFpsNTJPTGJLVkg4VmlxMUZrSmhSS1lPY1JxUGFuK3RuQ09kYk1zNG5aRmJaMG5YOGtST05YU0gzUG1MblY5SVJWWFMrS2F1cVBoa2xYQzl4WDVCVHkxbGErcktBNW84Z1NHQ09NRDV3QktSam1md1hBeUdWTmU1Y2ZtcHI5aC9ST1BtUlo0d0picWFwaGRtTzBPd0JBN1ZvaGp3eVlPTmdRVGRGRTdobkVzWEk1UktvdTA3dE03WWRXWW9yeVBua3RGWlhDaERtZU5CcnVJTDNuUmkrUURrQjNaTjdnRGVEZ2Rpemh0NEhDSG9CbnV2NWdYUkhZTkR5enJ6UGUwK1VtQS9NNm1KU3RLak4ycVRacnciLCJtYWMiOiIwOTM0MjA4NGFhZGEwOTAwNDAyNjc0NjExY2Y2M2QyZjI5ZTEwODFkZjVmMWZhYjkzOWFiZWY3MDhiNmEyNTZhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InBvSkdsbW9IUVplQlpzRy9WQnRFdkE9PSIsInZhbHVlIjoiVVgvN3RPa1pkeFppM2laREZsM2NhaGlaRXpUYTdUcmtpTUFhbGMwdnBHeW5BbmxpeWI1dnpQNi9CRmtwbmx4MWMvakFrL2hpWHA2c1ZNMGpGMENQbm0zYnhOK2w5cWQ4b1JzeXJnMnBmMFIzcUplaXNJZWZoS25TUjNrVENTMDc5dGszRE5FUjkzdyt1ZnNEaFczV1hCTzMwdnJrYjl6RHBtNW1xMGhML1pyRCtZblhMbGprbEhuYWZOQ1QvSk1HOFJ4c0dib0F6K2VPeFlmMmxJUWROQVZrbmxkd25uNkZMbGtrNi93YjBRdmhjOHg0U09yeEZiTmtWT2paV1JwNktQMjBUSVRqQmhzSXdseWxwVXkwVWJyM3JSQmI1QTVtY2VCd3dpOGcveHJndnVEbnpuRzR2L1VtY2t5Z1o4YjF5dStKMmNqRG1XbGF3YjBpc200LzkwS3d5OXJPSlJ3bkZOQlgwZHZFeUN0WFpQekFqamw3elpKZGFVblo5SGFLY2FuT2Z3UVlTaEZoNmxDOVF3d01RcmQ2bVV6d3lWYjVHb3dKUHJRdWpRb2FPVTYySytyTE9VenJ5MG04cTRVcUFveVlwamNac2pVNVAwMTdFU0hHYXNmN1BubkpobUszY09hTWFwM25vRGloNHB6WEdVdW4vdnhGaVFmNjhHaHgiLCJtYWMiOiI5OWE2ODYxMTQ5Y2QxYWU4YjYyNzU1ZjI5YzI0NTI3Mzc1NjhjZGZiMGQ3YjY1NWNiZmIxNDc5NmI2YjAxNGIxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487034806\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-591232777 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-591232777\", {\"maxDepth\":0})</script>\n"}}