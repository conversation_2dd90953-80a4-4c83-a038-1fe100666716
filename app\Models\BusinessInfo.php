<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BusinessInfo extends Model
{
    protected $table = 'business_infos';

    protected $fillable = [
        'created_by',
        'company_name',
        'company_email',
        'company_phone',
        'country',
        'currency',
        'business_gst',
        'business_state',
        'business_logo',
        'agree_gst_change',
        'street_address',
        'city',
        'pincode',
        'state_prov_region',
        'address_country',
        'time_zone',
        'contact_person_name',
        'job_position',
        'contact_email',
        'contact_phone',
        'business_type',
        'industry',
        'website',
        'business_description',
        'established_date',
        'registration_number',
        'tax_id'
    ];

    protected $casts = [
        'agree_gst_change' => 'boolean',
        'established_date' => 'date',
    ];

    /**
     * Relationship with User model
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get business information for a specific user
     */
    public static function getByUser($userId)
    {
        return self::where('created_by', $userId)->first();
    }

    /**
     * Update or create business information for a user
     */
    public static function updateOrCreateForUser($userId, $data)
    {
        // Filter out empty values (except for boolean fields)
        $filteredData = [];
        foreach ($data as $key => $value) {
            if ($key === 'agree_gst_change' || (!empty($value) && trim($value) !== '')) {
                $filteredData[$key] = $value;
            }
        }

        // Add the user ID
        $filteredData['created_by'] = $userId;

        // Use updateOrCreate to handle the record
        return self::updateOrCreate(
            ['created_by' => $userId],
            $filteredData
        );
    }

    /**
     * Get a specific business info field for a user
     */
    public static function getField($userId, $fieldName)
    {
        $businessInfo = self::where('created_by', $userId)->first();
        return $businessInfo ? $businessInfo->$fieldName : null;
    }

    /**
     * Set a specific business info field for a user
     */
    public static function setField($userId, $fieldName, $value)
    {
        $businessInfo = self::updateOrCreate(
            ['created_by' => $userId],
            [$fieldName => $value]
        );

        return $businessInfo ? true : false;
    }

    /**
     * Get the list of fillable fields
     */
    public static function getBusinessFields()
    {
        return (new self())->getFillable();
    }
}
