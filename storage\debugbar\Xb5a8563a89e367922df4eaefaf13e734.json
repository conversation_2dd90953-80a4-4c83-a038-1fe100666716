{"__meta": {"id": "Xb5a8563a89e367922df4eaefaf13e734", "datetime": "2025-07-31 05:33:38", "utime": 1753940018.01695, "method": "GET", "uri": "/login-with-company/exit", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940015.69785, "end": 1753940018.016975, "duration": 2.319124937057495, "duration_str": "2.32s", "measures": [{"label": "Booting", "start": 1753940015.69785, "relative_start": 0, "end": **********.843288, "relative_end": **********.843288, "duration": 2.1454379558563232, "duration_str": "2.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.843347, "relative_start": 2.1454970836639404, "end": 1753940018.016978, "relative_end": 3.0994415283203125e-06, "duration": 0.173630952835083, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44673544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login-with-company/exit", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@ExitCompany", "namespace": null, "prefix": "", "where": [], "as": "exit.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1476\" onclick=\"\">app/Http/Controllers/UserController.php:1476-1480</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01614, "accumulated_duration_str": "16.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.955968, "duration": 0.01442, "duration_str": "14.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 89.343}, {"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Services/ImpersonateManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\lab404\\laravel-impersonate\\src\\Services\\ImpersonateManager.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Services/ImpersonateManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\lab404\\laravel-impersonate\\src\\Services\\ImpersonateManager.php", "line": 137}, {"index": 19, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Models/Impersonate.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\lab404\\laravel-impersonate\\src\\Models\\Impersonate.php", "line": 64}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\UserController.php", "line": 1478}], "start": **********.9861782, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 89.343, "width_percent": 10.657}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login-with-company/exit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login-with-company/exit", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1183019145 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1183019145\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1197418461 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImRvbWt5aHNHTVJDdmhSMGJWQ2ZJYlE9PSIsInZhbHVlIjoiK1hUR0hhOXcvV1JsY2NrbmJ5aVVBZWtZdGt6a2w1ZFRtZWduMFJ1em9md1AyMlRBYUxSY3o5U0J4SmVNTDVFTnFtaWN5N3F2cCttWDRnbWVpSTdyTG4xOUtDWGFFWXFncEJLQ0dYcTVJTEhkYmJOblBKOVhuQmx4b0t3S0hkb013RmxNcS9SL2I0S3RaV24zcXFtNSthN3R1bXNXZW8rbHo3bERZSU4xRTlLNGdnTGtwak05RkNXalJFVyswSm43NjlBeDh3U2d1YU1iOGRhK0JEQ1gxVU9nUWRWeHZLYlo2MncrRVFsZkRDUllFeXByM3RES1JMTmdqSmg0KzY5OGhmbjZhdS80SVA1ZXlQVnlNQVpMem0vbDNzMlFZSTJXR0J0OWxiQTZZY2dsUXNvQ1JPT3VNcG5LSmRBNW5wRml6RXQ0ejFVMk95TGRabXpUZ2plY2JESUZiZHVkZFc3NDZwc0ZyeFRPbi9FcityU2FnMHJLN1dTZllzUGl0Nmd3VjJCV055TFd0alVtWDBQWlBYQk5weVZNR2lUS1AvSFc4MXd6VFJYbVl0VHFQNENyQmk5WVBGVVFHMXg5dnhIZ3ZLTUpjSTlnM2JmKzJhWi9lU3FsaVVmUHpwOWhIZ05WNmVkVDgzN0diKzlmM091STJGeTZsbUpKTGRWZTh4TVkiLCJtYWMiOiI0NDkzYzc1NmI3ZjhiZTRiYzEyZGFkMmZiYmI4OGY2YmJiMDM0NzgxN2Y3MzAwZTQ5ODI4MmIyODQ5MmU5N2I3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkV5NWpXdkZTUzZNUS9pYjMyYzNNeEE9PSIsInZhbHVlIjoidzNFWFo2WG8rNm1Gb0NOWExjRGh3TnhEVkRJQ3ZxMVhwc2Fmb09kWFNFYlMxakJOcnpuVzBRNDcxdVo1NUlHTFUzU3FDdTNiMlUrWHZ0ZkRYUjJxSFpXVU1VNVFKZWZJeENDeWoxeEJkOHVwNExmcUxKdHd2K3RPd3FDN0N6ajdSNmFDWU5JSHRtVmIyenRqNll5TmZpdTZkQW5MUExnbFZrNzdlYXFXYmFKRzJnS2orRFpZR0puRnpuMExLUFhub2diVm5BVExuUm9CR29tck8wcVh1dW9PU2RIRXdpYWpWNmhhTEZCd09SQk5ocWVuRmFoNkEvZGZDMjZFNEVFUm52czVXakhMeUIzTDRMTWc2bGlsd1Q5VHRuemNyb3RIU051THpZU2JueUZya1h1bzMraWd5OXI0VCt4ZzQzb0ZXMnF1VEtZVVlXT0NzRU43RlhCc0YrQVRXRVpuU1cyVm10N0REV005Ymd1V0lUV3lOUzlReExkbVc0M3p4K1oyMFRpZForOHhsZmh4bU5OMmlYV0laVDQ2NHJxMkhxQnhySDkvZlc3UW1la3B6MkdYd1ZwdFZDN2VBbEtTY0RGTzF1cWdBRGduTCtkLzN2c0EzNWg4SkRhclcvRGFSZXRVSGtWRjYzWkZrNGlPRFNFaW5ueVBHTjlDQ2NxeC9DNjAiLCJtYWMiOiJhMjU3ZTMwYmZlZmVmZWY1ZGI5OGQxNDI5MDIwZDJlMDk2NThhZjdjNTFjZGI1NGZjMmU4ZWExMDEwOGExNDVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1197418461\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1986214095 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">f24xnIHkwms8v0rbgfC16W0wpwmuAXK5XTVisj1V</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1986214095\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1745513573 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:33:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxrQlJTTHZZcjhnLzhtQm15NGdIZkE9PSIsInZhbHVlIjoiQUZ3SVl6OXd1c3lMbXVLYmhtR3NKUGZmRXRGY3hzNDZ6U0oxd2dYMlkzMkptRXpUS3k0S1ZzUG9CZ05POTJUVUgxK05JNWE4Z1JzcEtwT0ZzSVNPd3p5SEZ1c09Tb3BwZEdlVzMweXpBaUJCalNNV3JhR2dXcDRyN09BdkhEaDFkVEtBSW01U3k2SDRUVkpYWHpBcVpFUXY3RmFMRFBobU51aWhBNzdiQnI1Tm04M3hOQUowV3l4WTEyUTVueXZxK1FMZVRVMGlRblFzMlI2ZmRoRDhMeEFIWjc0eFhwRmZWa0tmdXVzUDRTZFlPcExpNVh3SXdWNTFkSnU5S2ROZ05LVUlPa01PQmpkb3RrNndUL0t2a0xFMTJML1RMbzllK2UwY2dYSGdFajExN0JoL1B5TUxqcG4xT0IwZVQ4V3VjM0hwWE9kcVNsK3YyMVVaRWxwUFJSeDhDMisvZGg0cjZUWW9aemZ2ZTBCV3kwQjUxNE9hZFU1UlVNK3hReEZXY0tZWkEwUVJ0SlVEY2tmMkk5MEdVTUFOY2VVQVNEZXBZNzJtMDQzeVlOcUdveXJTVU1pSjhpUEJXbklTclFuNWVXQjFma3lWVVN1QVB5QVpVT1NpTXZTYjRjMEhaMW9Lc1Q1RkJJKzhjbndEMkNHNHZrSmFXQWt0dlRja3lwOWQiLCJtYWMiOiI1ZTFkODAzZTIwY2E5MzgwZTNiYjVmMzQ3OTJkOWE3OGQ1ZTQ3NmY3YjkxNGQwY2YxNTJiZWYwMzc0Y2FhZDUyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:33:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik5JeVZiTHkxQzFwRU5zYU1UYXdyYmc9PSIsInZhbHVlIjoibFNLUzdmQ2VBMXdNdHhEdDBIbkpwMlA0OGlLc09UNE9ha0dYVkNYcUJVb283MXRBVkR5RGwvTWZycENmSjJaU2E1UHg5Sko2N2dwamdSYmE1WTJxdmRZazhheTJ2ekVQVHcxRlhXM0xBVm80NGxzMFhPWUlTZzBPNnowTlc0cWdDRnRvNVVoZmRLU3ZDaHFpaGlJZ3YxV1pDMmNtVnBwQkhXNC8zUG51VkFUUkRXT2ZLWXJuMkM0MjRiN1RJbWtRR1dsNU1ad1JlZjNtMWRmeGdmZWEwU1padXJPSlhmRitkL0ZDaCt5QzRHOW9TU2ZScUk5UG9zWGM2N1RNamZlRFdqWWR2SzFDRDhIK1dKaDB5SVF3UWJXb21aS0d5cU1BMVBqUnhyZklYbHB6WmU3RTAzZ29Mc2RlVmUxUGFHOTZxNFpVWnZMQ0JJaGZtUVc5cHJXUXFDUi9zZVJkYTh3SGxFMzZvMFJ1aUtiemh0MkdtVHRTL2R2aWZKaDBrWGpTYXlqR3MxdlhwS1dYWXNZUjhseFpWN1R1ckl3U3NzUXFHODNCQndJZHlPd0NmSUQyUjhHLy9zdHljNDU5QU1LekxVaG9vSGJEK3dscFNIZ1RYVFFldHl1bDZadWJsVytQdHAwQnFuOTF1aVdKWFBuMVlNMHRacjZsN0Y3MTZzd0siLCJtYWMiOiJlZWI1YmMwYWQ0NmNkNzA5OGY5NGMxYTU3ZjVlMjcwOWVmYzMyMzJiZGI4YzY2Njg1Y2QwZWM0MWNmYmUwOTE3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:33:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxrQlJTTHZZcjhnLzhtQm15NGdIZkE9PSIsInZhbHVlIjoiQUZ3SVl6OXd1c3lMbXVLYmhtR3NKUGZmRXRGY3hzNDZ6U0oxd2dYMlkzMkptRXpUS3k0S1ZzUG9CZ05POTJUVUgxK05JNWE4Z1JzcEtwT0ZzSVNPd3p5SEZ1c09Tb3BwZEdlVzMweXpBaUJCalNNV3JhR2dXcDRyN09BdkhEaDFkVEtBSW01U3k2SDRUVkpYWHpBcVpFUXY3RmFMRFBobU51aWhBNzdiQnI1Tm04M3hOQUowV3l4WTEyUTVueXZxK1FMZVRVMGlRblFzMlI2ZmRoRDhMeEFIWjc0eFhwRmZWa0tmdXVzUDRTZFlPcExpNVh3SXdWNTFkSnU5S2ROZ05LVUlPa01PQmpkb3RrNndUL0t2a0xFMTJML1RMbzllK2UwY2dYSGdFajExN0JoL1B5TUxqcG4xT0IwZVQ4V3VjM0hwWE9kcVNsK3YyMVVaRWxwUFJSeDhDMisvZGg0cjZUWW9aemZ2ZTBCV3kwQjUxNE9hZFU1UlVNK3hReEZXY0tZWkEwUVJ0SlVEY2tmMkk5MEdVTUFOY2VVQVNEZXBZNzJtMDQzeVlOcUdveXJTVU1pSjhpUEJXbklTclFuNWVXQjFma3lWVVN1QVB5QVpVT1NpTXZTYjRjMEhaMW9Lc1Q1RkJJKzhjbndEMkNHNHZrSmFXQWt0dlRja3lwOWQiLCJtYWMiOiI1ZTFkODAzZTIwY2E5MzgwZTNiYjVmMzQ3OTJkOWE3OGQ1ZTQ3NmY3YjkxNGQwY2YxNTJiZWYwMzc0Y2FhZDUyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:33:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik5JeVZiTHkxQzFwRU5zYU1UYXdyYmc9PSIsInZhbHVlIjoibFNLUzdmQ2VBMXdNdHhEdDBIbkpwMlA0OGlLc09UNE9ha0dYVkNYcUJVb283MXRBVkR5RGwvTWZycENmSjJaU2E1UHg5Sko2N2dwamdSYmE1WTJxdmRZazhheTJ2ekVQVHcxRlhXM0xBVm80NGxzMFhPWUlTZzBPNnowTlc0cWdDRnRvNVVoZmRLU3ZDaHFpaGlJZ3YxV1pDMmNtVnBwQkhXNC8zUG51VkFUUkRXT2ZLWXJuMkM0MjRiN1RJbWtRR1dsNU1ad1JlZjNtMWRmeGdmZWEwU1padXJPSlhmRitkL0ZDaCt5QzRHOW9TU2ZScUk5UG9zWGM2N1RNamZlRFdqWWR2SzFDRDhIK1dKaDB5SVF3UWJXb21aS0d5cU1BMVBqUnhyZklYbHB6WmU3RTAzZ29Mc2RlVmUxUGFHOTZxNFpVWnZMQ0JJaGZtUVc5cHJXUXFDUi9zZVJkYTh3SGxFMzZvMFJ1aUtiemh0MkdtVHRTL2R2aWZKaDBrWGpTYXlqR3MxdlhwS1dYWXNZUjhseFpWN1R1ckl3U3NzUXFHODNCQndJZHlPd0NmSUQyUjhHLy9zdHljNDU5QU1LekxVaG9vSGJEK3dscFNIZ1RYVFFldHl1bDZadWJsVytQdHAwQnFuOTF1aVdKWFBuMVlNMHRacjZsN0Y3MTZzd0siLCJtYWMiOiJlZWI1YmMwYWQ0NmNkNzA5OGY5NGMxYTU3ZjVlMjcwOWVmYzMyMzJiZGI4YzY2Njg1Y2QwZWM0MWNmYmUwOTE3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:33:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1745513573\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://127.0.0.1:8000/login-with-company/exit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}