{"__meta": {"id": "Xdaecdf27ec743999f21b9c31d3062d3d", "datetime": "2025-07-31 05:34:52", "utime": **********.66893, "method": "GET", "uri": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940090.811784, "end": **********.669001, "duration": 1.8572170734405518, "duration_str": "1.86s", "measures": [{"label": "Booting", "start": 1753940090.811784, "relative_start": 0, "end": **********.506419, "relative_end": **********.506419, "duration": 1.6946349143981934, "duration_str": "1.69s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.506447, "relative_start": 1.****************, "end": **********.669009, "relative_end": 7.867813110351562e-06, "duration": 0.*****************, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Rdcyjr4hyT0TZYutCjfH7nNcrI6vwxQMaLTEwubv", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "status_code": "<pre class=sf-dump id=sf-dump-1946520306 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1946520306\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-249425372 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-249425372\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1062094648 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1062094648\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1738384415 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1738384415\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1780257071 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1780257071\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1132053173 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:34:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVHZU5KSEVQdXVydnJYVmhkeU5Eb3c9PSIsInZhbHVlIjoibjJFblZlcnhGS0Zhc0Z5VEJ5bE5CSVpHK2pQeE9FWWRCVGpGWGlGcnNMNUd5bTExSEJCcGRqY3kxTXY0a25BZDVWRFlBVmgyQUExL2YxdUI0L1dBODRVc0tmVEx5L2FoTTcyT08yT2ZDMTVZQS92Vm5rWGhxKzhwTU5vbEViWDEwcTQydUwzSWlKelR5YUp3OUR4dVJEdk1kQVRTbTlRSXFPM1ZtS1lydlBOQlVxTGZWL2ZRRFVBa0RWZ0N6UGNoZlJSSDF0SkdZVDlicU5VZW51dFdnMGx5YzFCYmUrUERzTEZUQ3ozVlBhSGk2MUY2dXdYQko3cTZINWdpaWdLamNUTVRtUkIwMlUxenpLZlhSRktITUJrdlBuMDVXNU91N1FoYlVRVlA5anZPOTdYeEo3NHhSeVNiVkZaNGpxejhxM2FqZ0oyM1lrSlpQRnFINStQOTZoeFNzRzIvTTZaM2JFY092K3hUUEpEaXp3NzZpVk5DenlVdmZXVDhRRVRaeXQwVk0rNVJKOTZ5Y1BuRlhHckJ3MnluTHFZTGF5bU0rUVY0T045TDJiMHYzY0ZFa0tWdHhidmMvSU8vblY4U0lLMGU5Uy9CU1YrR1UwU09mcnA3YTFMMzkwQzdnUFVRcWdic0NaelIydENBdFlSY0JHWVl0bzFaUXRJVk9ESDUiLCJtYWMiOiI5YzQ1NWU3Y2NjYmVkZmNiY2M4NzQ1YWUzZjA2NjRjYjllNzhlNTJmMThkODJjOTNjNjIxOTc0Y2E1ZmI4NjEzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InNlbGRoZEU0L3BpdGtlai9INkZFVVE9PSIsInZhbHVlIjoiRk1MMVhEV2o3ZWt5SEhWWXNEbUJGbFNoazdMVDhpUStKQzZ0NUNaa1l2V0oxTFpDRnVDSDUyTkcvL05DYzE0a0VnWW44QnZHNlZWdmU2MURlQnY5Q0x4aUNTWFoxYnR2TzVrR05KZ2dSamRaR2Y1dkNWbkhkWGtJOHF4U1hKdUQ4RjdFRXlibTdLQWZpVVdWb0paYnJQaWJyaFEzNGVpSXZZNHlDNGRsZVZ4cUlhajAybUlqNjUxeVZ5a0Q0R1BPWE5UdFl3UHJ6Qk9JVXlNVDFoWUtDOHpvZW5oYlBIcHN6ZzRJeUlSSTh3bHgvdDgvdUxuOXh4dDRyN2VLc2lwWlVPaUQwWUJlSzZoQUJkamoyMXJiUEdod0dxSy9RSURZZ2NoSXFBazdzV2duMTI0OGZscHZSaERTWDlKQlJsa0w0Vk03OHZUV2JwYUpyZGJPYlM2Y2hRb25PR0N0eUdveWQ0WWVzd3RkY29pY0dGYWVxNmIzRkg3RlpMcU5YRHNKL2lkeEJwaktnbFdyMkJVSEVHMWZGbGk4M3FTZTFiTVZGYjF2UDJYbnNOZVdjTjl5ZS9kR0pDTjF3MVloalpuMlpEdUcvZ3NLVUNDMEVtd2VQRHppUXdlaUdtb3RHVVhKVS91MVpDNmQ4ZHVjdWZGSGJJak9PVzNpYlcyZnFSSi8iLCJtYWMiOiIyOGM0YjRjOWJiZDQ1ODRkMDI2NjI3ZTBkMDdlMTc0YzFmYjAyNTQwNTliNjU4OTEzMDAyNGI1MmJjMzlmMDZiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVHZU5KSEVQdXVydnJYVmhkeU5Eb3c9PSIsInZhbHVlIjoibjJFblZlcnhGS0Zhc0Z5VEJ5bE5CSVpHK2pQeE9FWWRCVGpGWGlGcnNMNUd5bTExSEJCcGRqY3kxTXY0a25BZDVWRFlBVmgyQUExL2YxdUI0L1dBODRVc0tmVEx5L2FoTTcyT08yT2ZDMTVZQS92Vm5rWGhxKzhwTU5vbEViWDEwcTQydUwzSWlKelR5YUp3OUR4dVJEdk1kQVRTbTlRSXFPM1ZtS1lydlBOQlVxTGZWL2ZRRFVBa0RWZ0N6UGNoZlJSSDF0SkdZVDlicU5VZW51dFdnMGx5YzFCYmUrUERzTEZUQ3ozVlBhSGk2MUY2dXdYQko3cTZINWdpaWdLamNUTVRtUkIwMlUxenpLZlhSRktITUJrdlBuMDVXNU91N1FoYlVRVlA5anZPOTdYeEo3NHhSeVNiVkZaNGpxejhxM2FqZ0oyM1lrSlpQRnFINStQOTZoeFNzRzIvTTZaM2JFY092K3hUUEpEaXp3NzZpVk5DenlVdmZXVDhRRVRaeXQwVk0rNVJKOTZ5Y1BuRlhHckJ3MnluTHFZTGF5bU0rUVY0T045TDJiMHYzY0ZFa0tWdHhidmMvSU8vblY4U0lLMGU5Uy9CU1YrR1UwU09mcnA3YTFMMzkwQzdnUFVRcWdic0NaelIydENBdFlSY0JHWVl0bzFaUXRJVk9ESDUiLCJtYWMiOiI5YzQ1NWU3Y2NjYmVkZmNiY2M4NzQ1YWUzZjA2NjRjYjllNzhlNTJmMThkODJjOTNjNjIxOTc0Y2E1ZmI4NjEzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InNlbGRoZEU0L3BpdGtlai9INkZFVVE9PSIsInZhbHVlIjoiRk1MMVhEV2o3ZWt5SEhWWXNEbUJGbFNoazdMVDhpUStKQzZ0NUNaa1l2V0oxTFpDRnVDSDUyTkcvL05DYzE0a0VnWW44QnZHNlZWdmU2MURlQnY5Q0x4aUNTWFoxYnR2TzVrR05KZ2dSamRaR2Y1dkNWbkhkWGtJOHF4U1hKdUQ4RjdFRXlibTdLQWZpVVdWb0paYnJQaWJyaFEzNGVpSXZZNHlDNGRsZVZ4cUlhajAybUlqNjUxeVZ5a0Q0R1BPWE5UdFl3UHJ6Qk9JVXlNVDFoWUtDOHpvZW5oYlBIcHN6ZzRJeUlSSTh3bHgvdDgvdUxuOXh4dDRyN2VLc2lwWlVPaUQwWUJlSzZoQUJkamoyMXJiUEdod0dxSy9RSURZZ2NoSXFBazdzV2duMTI0OGZscHZSaERTWDlKQlJsa0w0Vk03OHZUV2JwYUpyZGJPYlM2Y2hRb25PR0N0eUdveWQ0WWVzd3RkY29pY0dGYWVxNmIzRkg3RlpMcU5YRHNKL2lkeEJwaktnbFdyMkJVSEVHMWZGbGk4M3FTZTFiTVZGYjF2UDJYbnNOZVdjTjl5ZS9kR0pDTjF3MVloalpuMlpEdUcvZ3NLVUNDMEVtd2VQRHppUXdlaUdtb3RHVVhKVS91MVpDNmQ4ZHVjdWZGSGJJak9PVzNpYlcyZnFSSi8iLCJtYWMiOiIyOGM0YjRjOWJiZDQ1ODRkMDI2NjI3ZTBkMDdlMTc0YzFmYjAyNTQwNTliNjU4OTEzMDAyNGI1MmJjMzlmMDZiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1132053173\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1941273551 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rdcyjr4hyT0TZYutCjfH7nNcrI6vwxQMaLTEwubv</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1941273551\", {\"maxDepth\":0})</script>\n"}}