<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class InstallmentPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'installment_plan_id',
        'payment_id',
        'installment_number',
        'due_date',
        'paid_date',
        'amount',
        'paid_amount',
        'penalty_amount',
        'status',
        'payment_method',
        'transaction_id',
        'receipt_url',
        'notes',
        'processed_by',
    ];

    protected $casts = [
        'due_date' => 'date',
        'paid_date' => 'date',
        'amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'penalty_amount' => 'decimal:2',
    ];

    /**
     * Generate unique payment ID
     */
    public static function generatePaymentId()
    {
        do {
            $id = 'PAY-' . date('Y') . '-' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (self::where('payment_id', $id)->exists());

        return $id;
    }

    /**
     * Get the installment plan that owns this payment
     */
    public function installmentPlan()
    {
        return $this->belongsTo(InstallmentPlan::class);
    }

    /**
     * Get the user who processed this payment
     */
    public function processor()
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClass()
    {
        return match($this->status) {
            'paid' => 'bg-success',
            'pending' => 'bg-warning',
            'overdue' => 'bg-danger',
            'partial' => 'bg-info',
            default => 'bg-secondary'
        };
    }

    /**
     * Get formatted status
     */
    public function getFormattedStatus()
    {
        return ucfirst($this->status);
    }

    /**
     * Check if payment is overdue
     */
    public function isOverdue()
    {
        return $this->due_date->isPast() && in_array($this->status, ['pending', 'partial']);
    }

    /**
     * Get remaining amount to be paid
     */
    public function getRemainingAmount()
    {
        return $this->amount - $this->paid_amount;
    }

    /**
     * Check if payment is fully paid
     */
    public function isFullyPaid()
    {
        return $this->paid_amount >= $this->amount;
    }

    /**
     * Mark payment as paid
     */
    public function markAsPaid($amount = null, $paymentMethod = null, $transactionId = null, $processedBy = null)
    {
        $this->paid_amount = $amount ?? $this->amount;
        $this->paid_date = now();
        $this->status = $this->isFullyPaid() ? 'paid' : 'partial';
        
        if ($paymentMethod) {
            $this->payment_method = $paymentMethod;
        }
        
        if ($transactionId) {
            $this->transaction_id = $transactionId;
        }
        
        if ($processedBy) {
            $this->processed_by = $processedBy;
        }
        
        $this->save();
        
        // Update installment plan
        $this->updateInstallmentPlan();
    }

    /**
     * Update the parent installment plan based on this payment
     */
    public function updateInstallmentPlan()
    {
        $plan = $this->installmentPlan;
        
        if (!$plan) {
            return;
        }
        
        // Recalculate paid installments and amount
        $paidPayments = $plan->payments()->where('status', 'paid')->count();
        $totalPaidAmount = $plan->payments()->where('status', 'paid')->sum('paid_amount') + 
                          $plan->payments()->where('status', 'partial')->sum('paid_amount');
        
        $plan->paid_installments = $paidPayments;
        $plan->paid_amount = $plan->down_payment + $totalPaidAmount;
        $plan->pending_amount = $plan->total_amount - $plan->paid_amount;
        
        // Update next installment date
        $nextPendingPayment = $plan->payments()
            ->whereIn('status', ['pending', 'partial', 'overdue'])
            ->orderBy('due_date')
            ->first();
            
        $plan->next_installment_date = $nextPendingPayment ? $nextPendingPayment->due_date : null;
        
        // Update plan status
        $plan->updateStatus();
    }

    /**
     * Scope for overdue payments
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereIn('status', ['pending', 'partial']);
    }

    /**
     * Scope for pending payments
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for paid payments
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope for due today
     */
    public function scopeDueToday($query)
    {
        return $query->whereDate('due_date', today())
                    ->whereIn('status', ['pending', 'partial']);
    }

    /**
     * Scope for due this week
     */
    public function scopeDueThisWeek($query)
    {
        return $query->whereBetween('due_date', [now()->startOfWeek(), now()->endOfWeek()])
                    ->whereIn('status', ['pending', 'partial']);
    }

    /**
     * Scope for due this month
     */
    public function scopeDueThisMonth($query)
    {
        return $query->whereBetween('due_date', [now()->startOfMonth(), now()->endOfMonth()])
                    ->whereIn('status', ['pending', 'partial']);
    }
}
