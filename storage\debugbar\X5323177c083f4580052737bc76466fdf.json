{"__meta": {"id": "X5323177c083f4580052737bc76466fdf", "datetime": "2025-07-31 06:32:08", "utime": 1753943528.403919, "method": "PUT", "uri": "/system-admin/companies/79", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[06:32:08] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 79,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2039 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": 1753943528.313405, "xdebug_link": null, "collector": "log"}, {"message": "[06:32:08] LOG.info: OMX Flow sync result for company {\n    \"user_id\": 79,\n    \"user_email\": \"parichaysing<PERSON><EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"Parichay Singha AI\",\n        \"company_description\": \"Company created via pricing plan integration\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753943528.3146, "xdebug_link": null, "collector": "log"}, {"message": "[06:32:08] LOG.info: Module permissions synced for company: 79 {\n    \"company_id\": 79,\n    \"module_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"account\": [\n            \"create customer\",\n            \"create vender\",\n            \"create invoice\",\n            \"create bill\",\n            \"create revenue\",\n            \"create payment\",\n            \"create proposal\",\n            \"create goal\",\n            \"create credit note\",\n            \"create debit note\",\n            \"create bank account\",\n            \"create bank transfer\",\n            \"create transaction\",\n            \"create chart of account\",\n            \"create journal entry\",\n            \"create assets\",\n            \"create constant custom field\",\n            \"view account dashboard\",\n            \"view customer\",\n            \"view vender\",\n            \"view invoice\",\n            \"view bill\",\n            \"view revenue\",\n            \"view payment\",\n            \"view proposal\",\n            \"view goal\",\n            \"view credit note\",\n            \"view debit note\",\n            \"view bank account\",\n            \"view bank transfer\",\n            \"view transaction\",\n            \"view chart of account\",\n            \"view journal entry\",\n            \"view assets\",\n            \"view constant custom field\",\n            \"view report\",\n            \"delete customer\",\n            \"delete vender\",\n            \"delete invoice\",\n            \"delete bill\",\n            \"delete revenue\",\n            \"delete payment\",\n            \"delete proposal\",\n            \"delete goal\",\n            \"delete credit note\",\n            \"delete debit note\",\n            \"delete bank account\",\n            \"delete bank transfer\",\n            \"delete transaction\",\n            \"delete chart of account\",\n            \"delete journal entry\",\n            \"delete assets\",\n            \"delete constant custom field\",\n            \"manage customer\",\n            \"edit customer\",\n            \"manage vender\",\n            \"edit vender\",\n            \"manage invoice\",\n            \"edit invoice\",\n            \"manage bill\",\n            \"edit bill\",\n            \"manage revenue\",\n            \"edit revenue\",\n            \"manage payment\",\n            \"edit payment\",\n            \"manage proposal\",\n            \"edit proposal\",\n            \"manage goal\",\n            \"edit goal\",\n            \"manage credit note\",\n            \"edit credit note\",\n            \"manage debit note\",\n            \"edit debit note\",\n            \"manage bank account\",\n            \"edit bank account\",\n            \"manage bank transfer\",\n            \"edit bank transfer\",\n            \"manage transaction\",\n            \"edit transaction\",\n            \"manage chart of account\",\n            \"edit chart of account\",\n            \"manage journal entry\",\n            \"edit journal entry\",\n            \"manage assets\",\n            \"edit assets\",\n            \"manage constant custom field\",\n            \"edit constant custom field\",\n            \"manage report\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753943528.316867, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943523.515771, "end": 1753943528.404081, "duration": 4.888310194015503, "duration_str": "4.89s", "measures": [{"label": "Booting", "start": 1753943523.515771, "relative_start": 0, "end": **********.696945, "relative_end": **********.696945, "duration": 2.1811740398406982, "duration_str": "2.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.696972, "relative_start": 2.1812009811401367, "end": 1753943528.40409, "relative_end": 8.821487426757812e-06, "duration": 2.707118034362793, "duration_str": "2.71s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52140536, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT system-admin/companies/{id}", "middleware": "web, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\SystemAdminController@updateCompany", "namespace": null, "prefix": "/system-admin", "where": [], "as": "system-admin.companies.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FSystemAdminController.php&line=550\" onclick=\"\">app/Http/Controllers/SystemAdminController.php:550-615</a>"}, "queries": {"nb_statements": 15, "nb_failed_statements": 0, "accumulated_duration": 0.05566, "accumulated_duration_str": "55.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8398051, "duration": 0.03193, "duration_str": "31.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 57.366}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.913344, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 57.366, "width_percent": 2.533}, {"sql": "select * from `users` where `type` = 'company' and `id` = '79' and `created_by` = 7 limit 1", "type": "query", "params": [], "bindings": ["company", "79", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/SystemAdminController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\SystemAdminController.php", "line": 559}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9290411, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "SystemAdminController.php:559", "source": "app/Http/Controllers/SystemAdminController.php:559", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FSystemAdminController.php&line=559", "ajax": false, "filename": "SystemAdminController.php", "line": "559"}, "connection": "radhe_same", "start_percent": 59.899, "width_percent": 3.36}, {"sql": "select count(*) as aggregate from `users` where `email` = 'parich<PERSON>ing<PERSON><EMAIL>' and `id` <> '79'", "type": "query", "params": [], "bindings": ["parich<PERSON>ingha<PERSON>@gmail.com", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 983}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.038534, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "radhe_same", "start_percent": 63.259, "width_percent": 2.893}, {"sql": "select count(*) as aggregate from `pricing_plans` where `id` = '11'", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.047092, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "radhe_same", "start_percent": 66.152, "width_percent": 1.815}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = '11' limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/SystemAdminController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\SystemAdminController.php", "line": 580}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.057088, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "SystemAdminController.php:580", "source": "app/Http/Controllers/SystemAdminController.php:580", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FSystemAdminController.php&line=580", "ajax": false, "filename": "SystemAdminController.php", "line": "580"}, "connection": "radhe_same", "start_percent": 67.966, "width_percent": 1.994}, {"sql": "update `users` set `plan` = '11', `users`.`updated_at` = '2025-07-31 06:32:06' where `id` = 79", "type": "query", "params": [], "bindings": ["11", "2025-07-31 06:32:06", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemAdminController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\SystemAdminController.php", "line": 603}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0705302, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "SystemAdminController.php:603", "source": "app/Http/Controllers/SystemAdminController.php:603", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FSystemAdminController.php&line=603", "ajax": false, "filename": "SystemAdminController.php", "line": "603"}, "connection": "radhe_same", "start_percent": 69.96, "width_percent": 7.438}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 453}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/SystemAdminController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\SystemAdminController.php", "line": 607}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.081668, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "User.php:453", "source": "app/Models/User.php:453", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=453", "ajax": false, "filename": "User.php", "line": "453"}, "connection": "radhe_same", "start_percent": 77.398, "width_percent": 2.192}, {"sql": "update `users` set `plan_expire_date` = '2026-07-31', `users`.`updated_at` = '2025-07-31 06:32:06' where `id` = 79", "type": "query", "params": [], "bindings": ["2026-07-31", "2025-07-31 06:32:06", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 474}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemAdminController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\SystemAdminController.php", "line": 607}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.095328, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "User.php:474", "source": "app/Models/User.php:474", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=474", "ajax": false, "filename": "User.php", "line": "474"}, "connection": "radhe_same", "start_percent": 79.59, "width_percent": 4.905}, {"sql": "select * from `users` where `created_by` = 0 and `type` != 'super admin' and `type` != 'company' and `type` != 'client'", "type": "query", "params": [], "bindings": ["0", "super admin", "company", "client"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 488}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/SystemAdminController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\SystemAdminController.php", "line": 607}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.108713, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "User.php:488", "source": "app/Models/User.php:488", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=488", "ajax": false, "filename": "User.php", "line": "488"}, "connection": "radhe_same", "start_percent": 84.495, "width_percent": 2.875}, {"sql": "select * from `users` where `created_by` = 0 and `type` = 'client'", "type": "query", "params": [], "bindings": ["0", "client"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 489}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/SystemAdminController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\SystemAdminController.php", "line": 607}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.116915, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "User.php:489", "source": "app/Models/User.php:489", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=489", "ajax": false, "filename": "User.php", "line": "489"}, "connection": "radhe_same", "start_percent": 87.37, "width_percent": 4.366}, {"sql": "select * from `customers` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 490}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/SystemAdminController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\SystemAdminController.php", "line": 607}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.132428, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "User.php:490", "source": "app/Models/User.php:490", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=490", "ajax": false, "filename": "User.php", "line": "490"}, "connection": "radhe_same", "start_percent": 91.736, "width_percent": 2.174}, {"sql": "select * from `venders` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 491}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/SystemAdminController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\SystemAdminController.php", "line": 607}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.146959, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "User.php:491", "source": "app/Models/User.php:491", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=491", "ajax": false, "filename": "User.php", "line": "491"}, "connection": "radhe_same", "start_percent": 93.909, "width_percent": 2.264}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemAdminController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\SystemAdminController.php", "line": 777}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/SystemAdminController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\SystemAdminController.php", "line": 747}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/SystemAdminController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\SystemAdminController.php", "line": 610}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.161464, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "SystemAdminController.php:777", "source": "app/Http/Controllers/SystemAdminController.php:777", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FSystemAdminController.php&line=777", "ajax": false, "filename": "SystemAdminController.php", "line": "777"}, "connection": "radhe_same", "start_percent": 96.173, "width_percent": 1.725}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/SystemAdminController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\SystemAdminController.php", "line": 792}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/SystemAdminController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\SystemAdminController.php", "line": 747}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/SystemAdminController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\SystemAdminController.php", "line": 610}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.174745, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 97.898, "width_percent": 2.102}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "App\\Models\\ModuleIntegration": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FModuleIntegration.php&line=1", "ajax": false, "filename": "ModuleIntegration.php", "line": "?"}}}, "count": 6, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies/79/edit\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7", "success": "Company updated successfully.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/system-admin/companies/79", "status_code": "<pre class=sf-dump id=sf-dump-1736787923 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1736787923\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Parich<PERSON> Singha AI</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"26 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>plan_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-148900962 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">692</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundary5LQn6oOGFVHABWoG</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/system-admin/companies/79/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IndJWk9Wd1RIcFQvc0ZXdHZKZjN2M3c9PSIsInZhbHVlIjoiYWd1SW14Y0J3amVobk1lOENCNXk4clBCZnFOZzYwYVYwaitzQ0dRcERyTjY2WUN4YjloU3NCSFRhM3VEM1VIZ054Y3F6MjA4TmZhNDBKYnU2RnhkRkdvenNGQy9PRDQ4aWpvN0F6Sm4zZ2Z3NUo2R1cvZ1ZKSVdLa3VUUUFPRHZnQ3RtOGMxQStTTGZPSkZ5ZWdac1lvWE4yQjdZZm9ONmNWaURYNFl2MDB4Q0FNUVFPTFNYaEMyMndoUnNxYVM3ejQyZWlYV3Y4ZzFWYVlHM3ZDWkQyY3N5cUxKSVQxOWhyR0htRVFvRnBIbSt4YmxKTkxOamkvelJQUVQydk8xTy8wNVNhdmwyY0gwN0VQMkI0QWFLc2t5dDFDRGRiZlIrSHFNTGxNU1JsQ2hlTGlBWFQ4czI4NFkrVXNUZTNFYTIvVzZuMWgyM2lWclFpL1FiTlFqZWZ4NUJoWTlzYzJjNjlGZk1tYkpLK1pqVjdtcTVBQ2FDWkFuMnZ5dGN4Ymx0QWxNSTJ0c2JhNGFOKzJwRlpPYnh4ZVhJWE9LMlBaVVM0eGdpQW1rdFM0WUZoellRYTh4ZmhpbWhVMzhLcmFnczZaQkZGYnVUVXpaZE1NeDlGSGFVdVE5eVhNbU9KaHg5STVjK3poTjVjdlVGekhlWDE1NERkeWptNXR1SWNqNVEiLCJtYWMiOiI4MjkwNGM3ZTk2YTk0NjNjNjIxMTRlYTc2M2U3ZWJjM2UxYmIyYjRjODA0ZGMzMGM0ZTdiNzA3YWQ4M2I3YTE0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImlidUNFelpZdHRqSVZnWWhlVkRnZWc9PSIsInZhbHVlIjoiMmxXNzNGbGVsUkRVaCtMcmVPWTU1TlRzQ0RJV0Y0bDRsR29WU2VFaFl1d3U2Um8zRGIwbk5odHVXYVJCMmJ0SjIzVTJGbDBSSSsweitpbk1hSUh6bWdlOGl6V3JHZ3RXaGNWR3BRRXRCS2Z1UG84M1pPYk5LamJNQUxEb05hL1Ivdkk2b1BEOC8zVi8wUUx6MzFTN0ZJcUZzUm1rZzdGNWZ2UG1ycUNvd1Z4WnRPM3RpNGxVcXJRK2dqclN2K2QySW1WTVI4R0dzQUkwc2xkakZJMDhPRGJ3eGZCSlRvaUp6RVhtUXE0TEhLM0o3QzhIbDJJa0R5ZmpXNUR6bnkrbkVmSzA5d1JZK0hCdXlWUDVTN3pvVWM5WkdvcGhRSnBRQmFDK21MUWV3NUxCekdjSzZrZ1kyVFNMU3RUSzRPcm9BNml5cTd5T3p1SWhNSSsxWlNOZzR1cXh0bVd6YlVxenNhTHdhSFdoVlQ4RFd6SmRiK2JsYW5QbE9nZzZ4RU1CZ08wMWZCK3p1SXBUaStSbnRYbVpmNDBSbkxZUkF1MXRnQkliZ05iZnZqTzRieXpTQ0xHQTNDME5mbUQvTXowVXRjSkxWZHZmQUZTODRmcFBzUmJ4cG15K1NVU1RSY1Vra085Tlo2blU2bWxkb3BnMHhERXhNTndWSnNQVjhvYmIiLCJtYWMiOiIwZWFiNzJjMzA5N2FiZTVmMDk1ZmZjODJiOTNkMmM2NWM2ODllZDFhYWY0NzA0ZmJkZThiMGNjNzEzY2YzM2Q0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-148900962\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-9084233 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9084233\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1451221754 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:32:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imhya2RrODU0THpISnQrY1FjU1g4eFE9PSIsInZhbHVlIjoicjNPWm1KVkpUT1ZWbzIrd1JTVGFPa0NKNWRLdkhockp0N3c4SzI5WEJrOGY3VFo5Ym1Sb2diRGZBUUtRQjBVV09hWmUvdjNMckUxS3VFQmhCK2s3UU9rUmtuZSt1ZmxiUTlmNTc3NHY4eWxCcXJVTitKQXBsaVA1U3lGenNmdEdmcUFENDVjNjcrVGJsVUZUQ2Jjc2dxN1JaUDlnWUdmamEyR1Q5NmR6SzAwL3VvYm9vSmFrK1VaeEh0RG10NGxQNnZLYndGSVlqejZMaXJna1RIdUMzUzcyT0JjZjRhdDg3eTNRZ2JzcDlLb1pnV1BkVmlXQk5idUY1ZW4rU2dsL3dKd1J5b0Fha1lBYytpZEJLVk8vNE8wQjJTekxEYmJKQnM3aEluSmFKQmlaenN4TnVvcUxYRU9EQ3NmMy9XTjl0TU04RTBWa0NDSHJYZ2dKTklVbSt2YTcvRHBOR1pJVnRidHJnNEkrb1M3WUtqYUJkRHlkb0k2RFROUU1LVkswWmo2cExvakpEWjlCcjNlNTZXT2plcWJFMHowcHMyUVF3ekVjQmxzSG44bHhnZTYxL2tLRVplRWg4SzlSWlJ4NlB2MEJLMS9Hd1hwbXRLMTNpbzI1dkEyaWV6USt3Ym9CamJqd2x6NDlrdU1RbGlBSWVNRkNuTVV1ZEZTKzYxZnYiLCJtYWMiOiI4ODQyOWU1ODAzMDBiNzc2YWRjM2I5MDIzNzRlMjMxNmFlNTk4NDBhMDJlOGFlNmQ0MTEzZmJjYzI0NzBhYWI5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImJ4c0daWGYyZHlyaFZXRDcwQURaSVE9PSIsInZhbHVlIjoiN1pKcXVrZUs0TGx1dlhxUHMyQ2g1Wk85a3RhSk5XYkFGWVl1MzUwNVYrdXRHK3cwbjR1Q2NsSGp2VTlPTUx0V1pxczdBWUczbXZlR09wbDI0eTRwdkNncC85Y1VnVTQvQ3pORmhzeWNBMzVaT0JzVzEwSlp4eDFNYXd5Zm53TUJuWnczS1A0TlhJclN6NjVONUpRK1Z5VGhHNHVRazJJK2hUd3ZlVjRkSzY2YWZuNjBNL3JaaVlUVTREc2c1dHp6cjBnY0NMaDFnUmFTb0sycWM3NUo4QkdNclZhb0ZQeEM2bGM3VUQ0TXFNWTlhZmU1OGRZcTFpdk9ORkl6YlFxVWpTS0gzaFhlNGRwZEZUKzJlTm5lbkllS1c1YUZFYWdCRkNjbHZMd0YrY3BoaHN1UkQ5amFUem9wVDV4RUhHU3ViVjNJcmNoSEtTYzB2a3ZBMmcwQ1NNV1gyMGxobkFhQ09zSDVIZlA3WVFKZGhqbnNLYVBJQVltWEFjdGsxOFB5QzQ3TjhxNVhRdTBhTHBlU2U1TEdjTkh1SlFzQ3NYOWVpQnQvSU5ma0t3OUZxdU1YYVNodk1PRVd0eDRsSEJPdmEzS2RlYndiTmY5d0VlRG1DTzV3N0ZhaGc5THVrS3Z0RCtzYWVyZ2NyMUE4dGdMdTZPazA5TXNjOHJQcjdvdkciLCJtYWMiOiJiMzFjOTBmYzlkYWI0NmE5MTg1ZjEyNmYzNDNmMDAxYTk1MGVmZGEzN2U5YzkxOWY0ZWZlYjQ1YzMwOWIzNWMwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imhya2RrODU0THpISnQrY1FjU1g4eFE9PSIsInZhbHVlIjoicjNPWm1KVkpUT1ZWbzIrd1JTVGFPa0NKNWRLdkhockp0N3c4SzI5WEJrOGY3VFo5Ym1Sb2diRGZBUUtRQjBVV09hWmUvdjNMckUxS3VFQmhCK2s3UU9rUmtuZSt1ZmxiUTlmNTc3NHY4eWxCcXJVTitKQXBsaVA1U3lGenNmdEdmcUFENDVjNjcrVGJsVUZUQ2Jjc2dxN1JaUDlnWUdmamEyR1Q5NmR6SzAwL3VvYm9vSmFrK1VaeEh0RG10NGxQNnZLYndGSVlqejZMaXJna1RIdUMzUzcyT0JjZjRhdDg3eTNRZ2JzcDlLb1pnV1BkVmlXQk5idUY1ZW4rU2dsL3dKd1J5b0Fha1lBYytpZEJLVk8vNE8wQjJTekxEYmJKQnM3aEluSmFKQmlaenN4TnVvcUxYRU9EQ3NmMy9XTjl0TU04RTBWa0NDSHJYZ2dKTklVbSt2YTcvRHBOR1pJVnRidHJnNEkrb1M3WUtqYUJkRHlkb0k2RFROUU1LVkswWmo2cExvakpEWjlCcjNlNTZXT2plcWJFMHowcHMyUVF3ekVjQmxzSG44bHhnZTYxL2tLRVplRWg4SzlSWlJ4NlB2MEJLMS9Hd1hwbXRLMTNpbzI1dkEyaWV6USt3Ym9CamJqd2x6NDlrdU1RbGlBSWVNRkNuTVV1ZEZTKzYxZnYiLCJtYWMiOiI4ODQyOWU1ODAzMDBiNzc2YWRjM2I5MDIzNzRlMjMxNmFlNTk4NDBhMDJlOGFlNmQ0MTEzZmJjYzI0NzBhYWI5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImJ4c0daWGYyZHlyaFZXRDcwQURaSVE9PSIsInZhbHVlIjoiN1pKcXVrZUs0TGx1dlhxUHMyQ2g1Wk85a3RhSk5XYkFGWVl1MzUwNVYrdXRHK3cwbjR1Q2NsSGp2VTlPTUx0V1pxczdBWUczbXZlR09wbDI0eTRwdkNncC85Y1VnVTQvQ3pORmhzeWNBMzVaT0JzVzEwSlp4eDFNYXd5Zm53TUJuWnczS1A0TlhJclN6NjVONUpRK1Z5VGhHNHVRazJJK2hUd3ZlVjRkSzY2YWZuNjBNL3JaaVlUVTREc2c1dHp6cjBnY0NMaDFnUmFTb0sycWM3NUo4QkdNclZhb0ZQeEM2bGM3VUQ0TXFNWTlhZmU1OGRZcTFpdk9ORkl6YlFxVWpTS0gzaFhlNGRwZEZUKzJlTm5lbkllS1c1YUZFYWdCRkNjbHZMd0YrY3BoaHN1UkQ5amFUem9wVDV4RUhHU3ViVjNJcmNoSEtTYzB2a3ZBMmcwQ1NNV1gyMGxobkFhQ09zSDVIZlA3WVFKZGhqbnNLYVBJQVltWEFjdGsxOFB5QzQ3TjhxNVhRdTBhTHBlU2U1TEdjTkh1SlFzQ3NYOWVpQnQvSU5ma0t3OUZxdU1YYVNodk1PRVd0eDRsSEJPdmEzS2RlYndiTmY5d0VlRG1DTzV3N0ZhaGc5THVrS3Z0RCtzYWVyZ2NyMUE4dGdMdTZPazA5TXNjOHJQcjdvdkciLCJtYWMiOiJiMzFjOTBmYzlkYWI0NmE5MTg1ZjEyNmYzNDNmMDAxYTk1MGVmZGEzN2U5YzkxOWY0ZWZlYjQ1YzMwOWIzNWMwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1451221754\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1273729620 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/system-admin/companies/79/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Company updated successfully.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273729620\", {\"maxDepth\":0})</script>\n"}}