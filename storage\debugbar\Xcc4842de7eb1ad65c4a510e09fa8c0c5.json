{"__meta": {"id": "Xcc4842de7eb1ad65c4a510e09fa8c0c5", "datetime": "2025-07-31 06:31:12", "utime": **********.492297, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943470.422022, "end": **********.492353, "duration": 2.070330858230591, "duration_str": "2.07s", "measures": [{"label": "Booting", "start": 1753943470.422022, "relative_start": 0, "end": **********.333674, "relative_end": **********.333674, "duration": 1.911651849746704, "duration_str": "1.91s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.333703, "relative_start": 1.****************, "end": **********.49236, "relative_end": 7.152557373046875e-06, "duration": 0.*****************, "duration_str": "159ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "RIRqxDQlg0K44fzxCxuFHprA9w9zs49sC4Zz0XKF", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1161144986 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1161144986\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1230717746 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1230717746\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1476604792 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1476604792\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1372221972 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1372221972\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1154939325 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1154939325\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1674453481 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:31:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InV4ZjF3V1BRQ2hXKy9SQnBrQkdJaXc9PSIsInZhbHVlIjoiVlpzbUgyZ3RoUjgwSUVZZDBhS3ZYZlJzVDhjVVdmanU1OWovcjU3aEVLaGVXelEwT3JyYms1UzJMMmFHeW56a1BPcWdBUzlMYXRBL3RxVHJuSDhFd2EraG1XZGNnV2NWcXdEdVQ5ZXlTUVFXYXlYa0t3STE5eHIxVTRwV3hoWlEyRUJFaE9kYzJlVEVqbS9EbTN3dEZPamxScnh1cEdLUFcvN2NvVFNjR0VrR2FRZFpHdFBjMjhSVndMdjdWQmVQQXBib2lqam9kd3ZzcFpDRkdLYWE3M1lxODJhdmVQd1dvM3Z4WCtYZHpqTTBqb2tDTUlRb3VWb3YrZmt4L0tlbExTL2tpaWxtV2Y4TEg2R21pdnBMVWtJRS9CYVh0ZFpNZWgyeEhvcFE4RHN6Z2FoczJIejl4QWdaVUU0NFViSDVFMlgrYUhvbHFIMWVvWENERDhjQkV3RmxLNFQxeXpNeHFrN3JRY3U1MU9XZG1hS0pQd1BCc2JqbnRKZUw0M3FWbnF2dXpJZThHYitZVTQ0Slo2ZHdZamlGZS9zQ1NDNHVYMjRmZjA4eHZuZmt6cEQrYXg2VW40ekFnd01xYUp3QXdHNjB1VHMvejNDbUZhZ2N4ak00eW9tYkkxMG1OUlQyV3NvZFhIbEZPUlpPamhKUFhGNkNZR3BsTVhQQjJMRjgiLCJtYWMiOiI3OTk0NTZjODJjYmFlYzk1NjVlYTVjZWZlZTNjNTc0MjFmNWYwNWJmOGY1ZDZhOWJhN2Y4YTc4MWU5OTUzODlhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im15TUFPY210cG9xcWlqSkhZYVNUakE9PSIsInZhbHVlIjoieXFTQTBSaUtFNWh1SjZnWE9jMXY3dlhkdFlzMnd3cmZyMnVsTG0xeVdmblZ5YVlPeXZzYURISGh6ZEZnajFLcFMwd3FaN3RaeG1JN2Flei9jakN5dnJJc3g5U2d0RVNsTWxTdGlaVjdlTjZ6TUVjR2RHQTczUFNhVzROSnMyQlk1UVcwVGkrRzVJWm1pcnJzUFhlTUp0dEE0bzlOVkxWb3lkcUVBR2FBbHAwbGFEQ2VZYlQ3MlYzaWV4YUU1WC9jak42N1AzcGl4bWcyeW9CQ3BQQjlocTRUMzNXaUlOaWQvL0ZqT0ZZZnNnRUJ6YW5HTXEyckdZdFNHT0NsaVpybUFjUXdPNUsrY1hsZXZOQVpLdjJsZExwLy9qODRRcnpFUkdZWXJqOHh2eHp0TWRvSU93RmRaRU9HTUZEakRJTWxqUFpnNVBuVkhrWVNCSG5qbjhHc0ZvNTlaeU0zZlpDUXYvMUR0UFkyNDdrRzlLbm04NmMzTm95Q1dpQ2dmUk9SS2R3a1pLM1pFSFBPNmlkVndCSURtR1pxbkxvV2F1TkE4dVg3cnI2T3l6UmdkSHI2bTJkVEVwWFdZcGU3QnFDeUR3NnhiTTg0ZmlNTFJUZ1I5QmUyVzBqWG9tMXJBT1Rna3NMTzJNRnZmMms2SGlhbCtnMVozWU1lbGZFRGdibXEiLCJtYWMiOiJiYzJmYjg2NmYwYThiNDQyOGQ1ZjM0MzFhM2JkYmMyYzBhOGI2ZGUwOTAxZDdmY2EwNTQ5NDRiMDU1YjcwNWViIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InV4ZjF3V1BRQ2hXKy9SQnBrQkdJaXc9PSIsInZhbHVlIjoiVlpzbUgyZ3RoUjgwSUVZZDBhS3ZYZlJzVDhjVVdmanU1OWovcjU3aEVLaGVXelEwT3JyYms1UzJMMmFHeW56a1BPcWdBUzlMYXRBL3RxVHJuSDhFd2EraG1XZGNnV2NWcXdEdVQ5ZXlTUVFXYXlYa0t3STE5eHIxVTRwV3hoWlEyRUJFaE9kYzJlVEVqbS9EbTN3dEZPamxScnh1cEdLUFcvN2NvVFNjR0VrR2FRZFpHdFBjMjhSVndMdjdWQmVQQXBib2lqam9kd3ZzcFpDRkdLYWE3M1lxODJhdmVQd1dvM3Z4WCtYZHpqTTBqb2tDTUlRb3VWb3YrZmt4L0tlbExTL2tpaWxtV2Y4TEg2R21pdnBMVWtJRS9CYVh0ZFpNZWgyeEhvcFE4RHN6Z2FoczJIejl4QWdaVUU0NFViSDVFMlgrYUhvbHFIMWVvWENERDhjQkV3RmxLNFQxeXpNeHFrN3JRY3U1MU9XZG1hS0pQd1BCc2JqbnRKZUw0M3FWbnF2dXpJZThHYitZVTQ0Slo2ZHdZamlGZS9zQ1NDNHVYMjRmZjA4eHZuZmt6cEQrYXg2VW40ekFnd01xYUp3QXdHNjB1VHMvejNDbUZhZ2N4ak00eW9tYkkxMG1OUlQyV3NvZFhIbEZPUlpPamhKUFhGNkNZR3BsTVhQQjJMRjgiLCJtYWMiOiI3OTk0NTZjODJjYmFlYzk1NjVlYTVjZWZlZTNjNTc0MjFmNWYwNWJmOGY1ZDZhOWJhN2Y4YTc4MWU5OTUzODlhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im15TUFPY210cG9xcWlqSkhZYVNUakE9PSIsInZhbHVlIjoieXFTQTBSaUtFNWh1SjZnWE9jMXY3dlhkdFlzMnd3cmZyMnVsTG0xeVdmblZ5YVlPeXZzYURISGh6ZEZnajFLcFMwd3FaN3RaeG1JN2Flei9jakN5dnJJc3g5U2d0RVNsTWxTdGlaVjdlTjZ6TUVjR2RHQTczUFNhVzROSnMyQlk1UVcwVGkrRzVJWm1pcnJzUFhlTUp0dEE0bzlOVkxWb3lkcUVBR2FBbHAwbGFEQ2VZYlQ3MlYzaWV4YUU1WC9jak42N1AzcGl4bWcyeW9CQ3BQQjlocTRUMzNXaUlOaWQvL0ZqT0ZZZnNnRUJ6YW5HTXEyckdZdFNHT0NsaVpybUFjUXdPNUsrY1hsZXZOQVpLdjJsZExwLy9qODRRcnpFUkdZWXJqOHh2eHp0TWRvSU93RmRaRU9HTUZEakRJTWxqUFpnNVBuVkhrWVNCSG5qbjhHc0ZvNTlaeU0zZlpDUXYvMUR0UFkyNDdrRzlLbm04NmMzTm95Q1dpQ2dmUk9SS2R3a1pLM1pFSFBPNmlkVndCSURtR1pxbkxvV2F1TkE4dVg3cnI2T3l6UmdkSHI2bTJkVEVwWFdZcGU3QnFDeUR3NnhiTTg0ZmlNTFJUZ1I5QmUyVzBqWG9tMXJBT1Rna3NMTzJNRnZmMms2SGlhbCtnMVozWU1lbGZFRGdibXEiLCJtYWMiOiJiYzJmYjg2NmYwYThiNDQyOGQ1ZjM0MzFhM2JkYmMyYzBhOGI2ZGUwOTAxZDdmY2EwNTQ5NDRiMDU1YjcwNWViIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1674453481\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIRqxDQlg0K44fzxCxuFHprA9w9zs49sC4Zz0XKF</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}