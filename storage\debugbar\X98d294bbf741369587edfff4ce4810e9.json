{"__meta": {"id": "X98d294bbf741369587edfff4ce4810e9", "datetime": "2025-07-31 06:32:27", "utime": **********.401668, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943544.97281, "end": **********.401718, "duration": 2.428907871246338, "duration_str": "2.43s", "measures": [{"label": "Booting", "start": 1753943544.97281, "relative_start": 0, "end": **********.128961, "relative_end": **********.128961, "duration": 2.156151056289673, "duration_str": "2.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.129005, "relative_start": 2.****************, "end": **********.401723, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "273ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "e3DzKB5CJCB8sem4Hof4FoKiU3DupGOfWQL9RI6S", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-606877552 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-606877552\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-781365372 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-781365372\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1449097615 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1449097615\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1264703271 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1264703271\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-389234213 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-389234213\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-275183177 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:32:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9ZdEo1d0o0djVTOGdQRVJzL0RMVlE9PSIsInZhbHVlIjoiSkFFazZ5aCtGK2ZWRXRSRC9XdlA4bWZZZys3dVZKaHZxdDROTjZZUHhUUk5lRWZ0MFlxMUJNZTllZzNhOFdrZlVsQWQ4c2Q5a1RsTmlHUWJqSXRVK3NiR2Rrb2NwbEJCTC9QUUd2QklEaGwwZzBNR3RsTm5ySzhySG02cHlEK21TS01rdGwyUSsyaUE4blZVV00rcW5ZUUNUbXRkVGNjRmhoSVRVNTdiTzlKQ2VWdHZkN1ZKdTRGQ3hkWU84RlFTeVMxNXVTRm1PVzRibVVkNWRhaHJoVnhuTHlvTXdqR0ExaURoZk13RXloWm0xSVptQVlLMzczWnZ1VlM2eVZrODBNTHNkWVBYb3JOYmVRSlY0cUNoUG9aSUZFU2htai9TbFNSYUVkSTkvdlZnTlQ2RjR3N0NITTFBNHZLNG9zanRic3FiQTAzbm8rZlZOYmYxWFJJY3QxU3dicGl6Vjk3aVVwbFJ0UlhXY2cweC91UmJoL09jWk54UVJLSHFIZXl5ei9aS1V0aW5sMmh5eCtOb0xzNVJMTTdRVUg2S3lDcnB1UVVvT2tpR2s0VEQwaHBaOWJGVHJDRTMyeDJCdDZSeGJjcTBmK0tzUHlUdWFtMjQ1TzByc0J1QzZySjQ5K1l6U3g0VXVxa3BQc3lUd3dtVFFDcmpUZXFoLzIydEJSN2wiLCJtYWMiOiJjYzQ2NmM4NDk0NWE3NWI0NmE2MTQzNGUyMzcxNjhmYmUwYjk5NGJiMzA5OWJhNjFiN2I4NTUyOGNkZTVmMzUwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImF4bndzemtjOFBoT3FlT0sySDFYTmc9PSIsInZhbHVlIjoiYjBEVWpPTDJJd0ZUYURtbXVtbzk3UFJFYXR4bWFUTW10NkQ3KzdiMHFGdW1IbzlKbnl5TndnVTJIaE51VDlxRG5ScDVuSnlRNWdQTllTekZGS1kxOUU4TWc5NkVjb2JxU1VMbW8zYlhFN3hMK0U1SHI3VTFVSXhCL1luaTZBVzIvajJnaUlXelRoOEZISjJ6N1pTOHdwWVNVRnhQMk93QWNGZ2NOK1hIMEdBWGlUeVZDRWdyT1B5VnVXOVhmalp3U1pOZ0RJaXNpaS9vei9XWHBJVW1ZbTVyZ2ZzOUdlbjM4dnorUWpTTE9NdUR4ZnRiQk9SNTh0MU1IZFhjbmVwd2Z5MkVaUjNtUzBkdElHeHJtQXZsREFaYVNsY0JobDlJWFF6WGVZMTZCMFUvZTR3cFFUNjlSNzBia25wckZ6V2FsZG5ubU5ldnJ0R0hQM0ZodkZpWis3dXN5QU1pSkxzVHZQU1ZFVWNma0w5aHdVTUQ0NjZIZ2pROEU0NzVsMlhtdmFLbGNSQ0VhNS9zaGJKQTFyY1pyNm15SnpmYUFwTUorYnVTM21QUU5SQzRycTI4N0xlRzlTanUwTTBPWWsydUF6TkhTZC8rbnN6TVVwMjM4MWpWajVMWWpnTUVCa2xhQllyRVl5RlVza3NxRWhkUHc5Ty9kczhZVzVyYWE3MDkiLCJtYWMiOiIwN2UxY2NhZDUwZjgzYTdiMjIyZmNiYTYwYTVjYmM2MDFjZTM2OGRkYjhhYzRiZjk1OTMyOTkyMDUxM2RkMWMwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9ZdEo1d0o0djVTOGdQRVJzL0RMVlE9PSIsInZhbHVlIjoiSkFFazZ5aCtGK2ZWRXRSRC9XdlA4bWZZZys3dVZKaHZxdDROTjZZUHhUUk5lRWZ0MFlxMUJNZTllZzNhOFdrZlVsQWQ4c2Q5a1RsTmlHUWJqSXRVK3NiR2Rrb2NwbEJCTC9QUUd2QklEaGwwZzBNR3RsTm5ySzhySG02cHlEK21TS01rdGwyUSsyaUE4blZVV00rcW5ZUUNUbXRkVGNjRmhoSVRVNTdiTzlKQ2VWdHZkN1ZKdTRGQ3hkWU84RlFTeVMxNXVTRm1PVzRibVVkNWRhaHJoVnhuTHlvTXdqR0ExaURoZk13RXloWm0xSVptQVlLMzczWnZ1VlM2eVZrODBNTHNkWVBYb3JOYmVRSlY0cUNoUG9aSUZFU2htai9TbFNSYUVkSTkvdlZnTlQ2RjR3N0NITTFBNHZLNG9zanRic3FiQTAzbm8rZlZOYmYxWFJJY3QxU3dicGl6Vjk3aVVwbFJ0UlhXY2cweC91UmJoL09jWk54UVJLSHFIZXl5ei9aS1V0aW5sMmh5eCtOb0xzNVJMTTdRVUg2S3lDcnB1UVVvT2tpR2s0VEQwaHBaOWJGVHJDRTMyeDJCdDZSeGJjcTBmK0tzUHlUdWFtMjQ1TzByc0J1QzZySjQ5K1l6U3g0VXVxa3BQc3lUd3dtVFFDcmpUZXFoLzIydEJSN2wiLCJtYWMiOiJjYzQ2NmM4NDk0NWE3NWI0NmE2MTQzNGUyMzcxNjhmYmUwYjk5NGJiMzA5OWJhNjFiN2I4NTUyOGNkZTVmMzUwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImF4bndzemtjOFBoT3FlT0sySDFYTmc9PSIsInZhbHVlIjoiYjBEVWpPTDJJd0ZUYURtbXVtbzk3UFJFYXR4bWFUTW10NkQ3KzdiMHFGdW1IbzlKbnl5TndnVTJIaE51VDlxRG5ScDVuSnlRNWdQTllTekZGS1kxOUU4TWc5NkVjb2JxU1VMbW8zYlhFN3hMK0U1SHI3VTFVSXhCL1luaTZBVzIvajJnaUlXelRoOEZISjJ6N1pTOHdwWVNVRnhQMk93QWNGZ2NOK1hIMEdBWGlUeVZDRWdyT1B5VnVXOVhmalp3U1pOZ0RJaXNpaS9vei9XWHBJVW1ZbTVyZ2ZzOUdlbjM4dnorUWpTTE9NdUR4ZnRiQk9SNTh0MU1IZFhjbmVwd2Z5MkVaUjNtUzBkdElHeHJtQXZsREFaYVNsY0JobDlJWFF6WGVZMTZCMFUvZTR3cFFUNjlSNzBia25wckZ6V2FsZG5ubU5ldnJ0R0hQM0ZodkZpWis3dXN5QU1pSkxzVHZQU1ZFVWNma0w5aHdVTUQ0NjZIZ2pROEU0NzVsMlhtdmFLbGNSQ0VhNS9zaGJKQTFyY1pyNm15SnpmYUFwTUorYnVTM21QUU5SQzRycTI4N0xlRzlTanUwTTBPWWsydUF6TkhTZC8rbnN6TVVwMjM4MWpWajVMWWpnTUVCa2xhQllyRVl5RlVza3NxRWhkUHc5Ty9kczhZVzVyYWE3MDkiLCJtYWMiOiIwN2UxY2NhZDUwZjgzYTdiMjIyZmNiYTYwYTVjYmM2MDFjZTM2OGRkYjhhYzRiZjk1OTMyOTkyMDUxM2RkMWMwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-275183177\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-163411333 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">e3DzKB5CJCB8sem4Hof4FoKiU3DupGOfWQL9RI6S</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-163411333\", {\"maxDepth\":0})</script>\n"}}