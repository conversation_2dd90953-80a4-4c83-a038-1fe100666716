{"__meta": {"id": "X302147d1547f4cdc6b20f62eb8830fc3", "datetime": "2025-07-31 05:34:50", "utime": **********.777021, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940088.858737, "end": **********.77706, "duration": 1.918323040008545, "duration_str": "1.92s", "measures": [{"label": "Booting", "start": 1753940088.858737, "relative_start": 0, "end": **********.632488, "relative_end": **********.632488, "duration": 1.7737510204315186, "duration_str": "1.77s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.632516, "relative_start": 1.****************, "end": **********.777064, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "145ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "P3fQoFOC2TyltMn6dAKMfJYOugyVeO5kKykWfpt3", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-2098501786 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2098501786\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2121663427 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2121663427\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-893122970 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-893122970\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-627641976 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-627641976\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-288829101 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-288829101\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1950308926 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:34:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikxndm4xUngzSTQwUXEwbGdjdnZVWFE9PSIsInZhbHVlIjoiZ3l0aVkwRUdXZTFpQnNJUm1rQVRkTjlURDZWb2VrSjZNbklUanhsVENnZjJkQXBKY1pqWk1hdGQ0YW8wQXNFK1NjbmNnTGpiRzdhVTBkS3BQbzVBNTNyQ1ZMaFAzdWhINDRuRFJEdmtPRlFBam9HTEx6VlVycWpDb0kzSjhWTmtyT1F2RGorbTFRVlNDWDRhaXZ6bUhFT1FjT1hHSlBYK3ZpUnF4SnFocEdJM1FTR0REUUowM09zZi8zVnhOcXJQQnFRU3ZVVnBnWUxNd0prRVVZeFd0MTZGdXZJSy9oWHhZbndHYzYwZDlNRCtUVjlJVlV1eWNCUzFvYkliYVgyd2NvUnhabHY5UGZNeTVyYUZSV2Q0YjBVWmNTUm1MaUJ6OTRDQUVmOUxLR1M4WmZiWXFEYnlpVEZSamswMjdaNGRxRWtXMk9Ka2k2YWRhMUtTWlNkRDBrWE9Bc2g2elUvUjN1TzlQS0Z2Q3lRNHRtZUNTR1JUTWRRMXlKYnhlQmdDbEFlWGdNWmM0MStTRzduTCtlYVhVMG9QajR2VzJjMnBra1ZzeFdqTEg4anpHd0xJd1ZNL3hNaU9MeXl3cVIwKzJzai9ZcTd5UkFHcnBBbmpKakZrdlFqbUdUSFZuSkVoVlF5S0NwTStxUzdBWmo2WkFDK1hZbmV0Z2o5RDNPaUEiLCJtYWMiOiJkNjBjNDRmNTcyMGFiZTc3MTVmYmQ5ZDIxMjMwOTQ1N2Y3YjlhNTlhOTA1MDQ3ZWI3ZGMwYzk5ZGIwMDgxNGFkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjR1bzVNdUN1dWRkUGZmRHY3Q3lqL2c9PSIsInZhbHVlIjoiUVN3S2c1TmVRMTMvdTk2OElhdU5HcHg3eS9aZlZLY3BaZ3UzNWJ0cXRZZndwU1hPd2NHR2c0bS82UnkrQWJjK3R2T0NaejNDOGtNdDhJSmdEditTQlRnVG9CUTNjTHNwdVVtOVJUejRaNy9ENjN4a2dOek41a01HZG9XUXZldGY4UXArbitpYWFqQnM5aU9PeEN6ei9jTUFRRHZYUjJON1N1WEtBUjhXdEdNditNZmsyVkxNOGE2SnRHVThtZ25jNjd6MXM1T09LN0RrcEdIUlNaWEtnc0dOU25vZ3h5WEYxREtYSUdPRjZFTHRMS0xOSXVkY2xoM0x4UDIwQjROZE96VW54bllvZ0Z2NkdHT0tKQVk4Vjc4akN2TVpyNDcvazY5MkRleUdYQVQrVmhtSHRyMVNxeXpSK1pBdkdtclUzay9iSXpTcVVvWCtiT05XMTRiSm0vaThROUgwZWFqWm9IYXZuSHBIcHI3NUJpYWVxZEZWdm1BZlJuQVo2L0RtUjAxZEhPSkcwY2d0dWgrZ25WSDg1MFZjbEw4VUpTaFZ1TUlaNkJKTEdWaVpzZzZqY3FBeFBranMrb2hmM21IR0k5emkzT2dJSjZKNHpyTG9QTHJnYmVmOXRQNDl3UUpOb2FpUlFtYmxSTjQ4TWFBQVhZOG0vZ3pVMlA3SjRrQUkiLCJtYWMiOiI4MDM4OThkMDA3NjcxNDNiNmM2MjY5MmQ0NGVmNmY2N2YzZGY3NGRiMTk1MjYxMmQ0MjBlODQwNTdlZjlkYjdjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikxndm4xUngzSTQwUXEwbGdjdnZVWFE9PSIsInZhbHVlIjoiZ3l0aVkwRUdXZTFpQnNJUm1rQVRkTjlURDZWb2VrSjZNbklUanhsVENnZjJkQXBKY1pqWk1hdGQ0YW8wQXNFK1NjbmNnTGpiRzdhVTBkS3BQbzVBNTNyQ1ZMaFAzdWhINDRuRFJEdmtPRlFBam9HTEx6VlVycWpDb0kzSjhWTmtyT1F2RGorbTFRVlNDWDRhaXZ6bUhFT1FjT1hHSlBYK3ZpUnF4SnFocEdJM1FTR0REUUowM09zZi8zVnhOcXJQQnFRU3ZVVnBnWUxNd0prRVVZeFd0MTZGdXZJSy9oWHhZbndHYzYwZDlNRCtUVjlJVlV1eWNCUzFvYkliYVgyd2NvUnhabHY5UGZNeTVyYUZSV2Q0YjBVWmNTUm1MaUJ6OTRDQUVmOUxLR1M4WmZiWXFEYnlpVEZSamswMjdaNGRxRWtXMk9Ka2k2YWRhMUtTWlNkRDBrWE9Bc2g2elUvUjN1TzlQS0Z2Q3lRNHRtZUNTR1JUTWRRMXlKYnhlQmdDbEFlWGdNWmM0MStTRzduTCtlYVhVMG9QajR2VzJjMnBra1ZzeFdqTEg4anpHd0xJd1ZNL3hNaU9MeXl3cVIwKzJzai9ZcTd5UkFHcnBBbmpKakZrdlFqbUdUSFZuSkVoVlF5S0NwTStxUzdBWmo2WkFDK1hZbmV0Z2o5RDNPaUEiLCJtYWMiOiJkNjBjNDRmNTcyMGFiZTc3MTVmYmQ5ZDIxMjMwOTQ1N2Y3YjlhNTlhOTA1MDQ3ZWI3ZGMwYzk5ZGIwMDgxNGFkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjR1bzVNdUN1dWRkUGZmRHY3Q3lqL2c9PSIsInZhbHVlIjoiUVN3S2c1TmVRMTMvdTk2OElhdU5HcHg3eS9aZlZLY3BaZ3UzNWJ0cXRZZndwU1hPd2NHR2c0bS82UnkrQWJjK3R2T0NaejNDOGtNdDhJSmdEditTQlRnVG9CUTNjTHNwdVVtOVJUejRaNy9ENjN4a2dOek41a01HZG9XUXZldGY4UXArbitpYWFqQnM5aU9PeEN6ei9jTUFRRHZYUjJON1N1WEtBUjhXdEdNditNZmsyVkxNOGE2SnRHVThtZ25jNjd6MXM1T09LN0RrcEdIUlNaWEtnc0dOU25vZ3h5WEYxREtYSUdPRjZFTHRMS0xOSXVkY2xoM0x4UDIwQjROZE96VW54bllvZ0Z2NkdHT0tKQVk4Vjc4akN2TVpyNDcvazY5MkRleUdYQVQrVmhtSHRyMVNxeXpSK1pBdkdtclUzay9iSXpTcVVvWCtiT05XMTRiSm0vaThROUgwZWFqWm9IYXZuSHBIcHI3NUJpYWVxZEZWdm1BZlJuQVo2L0RtUjAxZEhPSkcwY2d0dWgrZ25WSDg1MFZjbEw4VUpTaFZ1TUlaNkJKTEdWaVpzZzZqY3FBeFBranMrb2hmM21IR0k5emkzT2dJSjZKNHpyTG9QTHJnYmVmOXRQNDl3UUpOb2FpUlFtYmxSTjQ4TWFBQVhZOG0vZ3pVMlA3SjRrQUkiLCJtYWMiOiI4MDM4OThkMDA3NjcxNDNiNmM2MjY5MmQ0NGVmNmY2N2YzZGY3NGRiMTk1MjYxMmQ0MjBlODQwNTdlZjlkYjdjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1950308926\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1910889055 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">P3fQoFOC2TyltMn6dAKMfJYOugyVeO5kKykWfpt3</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1910889055\", {\"maxDepth\":0})</script>\n"}}