{"__meta": {"id": "Xafc0e44b8d2c3f90360ccb3cabe8a64f", "datetime": "2025-07-31 06:17:09", "utime": **********.477512, "method": "GET", "uri": "/users/79/login-with-company", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753942627.456451, "end": **********.477567, "duration": 2.021116018295288, "duration_str": "2.02s", "measures": [{"label": "Booting", "start": 1753942627.456451, "relative_start": 0, "end": **********.271372, "relative_end": **********.271372, "duration": 1.8149211406707764, "duration_str": "1.81s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.2714, "relative_start": 1.8149490356445312, "end": **********.477571, "relative_end": 4.0531158447265625e-06, "duration": 0.20617103576660156, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44722520, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1450\" onclick=\"\">app/Http/Controllers/UserController.php:1450-1474</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0064, "accumulated_duration_str": "6.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.373067, "duration": 0.00513, "duration_str": "5.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 80.156}, {"sql": "select * from `users` where `users`.`id` = '79' limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\UserController.php", "line": 1452}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3912299, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "UserController.php:1452", "source": "app/Http/Controllers/UserController.php:1452", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1452", "ajax": false, "filename": "UserController.php", "line": "1452"}, "connection": "radhe_same", "start_percent": 80.156, "width_percent": 19.844}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/users/79/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/79/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2134909070 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2134909070\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im9xT1BBOElDL0pZcTJvZ2ZBOWhoaFE9PSIsInZhbHVlIjoiS0JtS25XNFF3UlQyVFNoVUVrd2xjbDd1cmU5ajVpaVY1THM4ekJ5UVU2eGlnQkp3OGdvSVNhaGhjbnFGZG9qVFlSVklGTTB1b1BnelVZYkRtV2ZsTm1sRDFGOU45QTh2OUtIcWRmeUNYb0Y5aSsxS3ROT3diMkpITHByT3N0UUFBbUtZZGlTTURKMTFaWktrRU9zN2pBUFM1UVJFeS9DRDYxUUpFaENZMGg3TFZFK3pQbFNwUk9MTnpZNlFjMm1Yc0ppZnIremZpR3Q1Vkk3VWJUMTgrQkltOVh1SGhUZGx3cVRlWHVQbkJoNGRlVmpKd1U4Q0N6SFNrR0NrRmtqOTdCaG9ybW9jQXBhN1lPSkg5VXJMY2ErcXE3S283RktZdFJPTUluZ3ZCTW85Y0hvQ3ZaZFVqZ0ducDJYQ1hDc2crVE9ZZzhvRjdpWXNuS2xmck45UUNDOFBaTzM1ZjlTTU92eHNmbzZjM2hLOU8zTTIvRURmVktpMkVjeVBKSnZEQ0VLdnZSRVNnRmJXQmRubzRJL3A3RTY0dDU4ZHRYajNwV0MrSW9sYWhUcHR1UnBrM1dXNTFnL3F5dVNCTThCNzBZeGhDeFRkV3R2Rm14WWxialNkMWU2YllSR1o3dWJDVi8wUHpHamdqL1ZoMm05TlNJV3RqMW5Tajc2R1RWSGgiLCJtYWMiOiIzMDRlNTVhMDZhMWEzZGQ2Y2M2MGJkYzRhMjQwMzFkNmM2ODY4ZDk1NjBiNGM3MjQ4YzEwMmM2ZDMyNWRmODNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImtXcTVtNndVME8zbkFhZFNSU3Rwbmc9PSIsInZhbHVlIjoieGVLejhvMnlIYmRkSE9QbjlWU21GZWEwZVBOcCtKMnVNbG9JTHRLL2RLV3VYcWJuTDNsa2ZZY3ZYM2hMckVkT3pKcWJDMFRPQzFyYnZiM0FxWWh1MjRXQ3d4WlZrLzB2eitwOTdTQ0VIV1h0S3h0U0drbHNvZERrYWowMUg4Qlo4OGFEV2N4Q0t2bzRPZ01FOE5CdHN0U0hXZGlld0NLek1PUjQvelM4dDJsaFRFTVpWOWUzWnVRZzBXSXc4RjdhR1VuNGJ6RUdaVWkydytLK3hZSzdMdlRZcmxST3Vnb0hBYkMwQjFnUXQ5a1d5MU94aG9FWmZiQzYwNFowWHpvRXd3Z3c0aTVBSkN3VXFiVkNHZVBrWXI1SkZuRHJINk5vK3BCTzFsdXJLcEtMSzJ5eFl5NFpZbThlYUtiem1EMFQrbjNiNzJ1Q3NBanZOZ29ERVlxNGU4anBScWNkQnRVb21QUmoxeEVrWXVQWUcyYUZtYmVmMW9TR2hRT0F5N0oyZWpDZUp1OGhpUUxOZEhoV1VDazBJUmVOdThQNlRKTzRWbUxBb05qclRyejU0aXlMTllrNGVQemJRam54RDVBakNoaS9RR3BObThzbHI0eDRrYU5EMXR1NHdXUlgvb0p4aTVmYmYrK3RXN1NNbUJXdWJUVGV3VHhQNy9GUkRVc1MiLCJtYWMiOiJkYzVjNzA3YzZlNjg2MzZiZTAyY2ZkMzA3Zjk1ZWJkY2M4MGViN2RlNWRkOGU0ZjY2MThhMGQwNDEzNDIwMTE0IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlByZWhsa3Y1U0FUZVdFc0FIKzRVYWc9PSIsInZhbHVlIjoidXJPM3BTcWpiNXZQT0M4TldUSUlSN2x6NCtuUjlTRnI2a2QvRmxzSjg1RG1xM1RnUC9IcUEyVXVOYndsV2Z2NnQwY1NrOGxHQjlYa203b0RJVTY5eTRTM2V2bFpiT1FlTm95OVlwdWlFTkFiM0l6eEVHMTdnenNoMWZYOEpwNjZBendPMGkzU2xDbGdSMEk3b25tVktKSDhVYk5VZFVSMzFhVGUvVnJvbC9xdXRUMWhWNW1mT3pzeDlRUS9wS1B3UEtVbWpmSTQzc1hDR2JGbmJGK1NPRCtRZVZYQUdzRnEvcWZyQldPQkhLSldsSXZoKy9WbTU5bDhzT215dGtSemVhZDV0QmZubkIxUW9BNXFoc1BlaEFWamhwOHUwU1hRTlEvM2dyMTlESnlKaGtrazRYUnNlME94cTdVUWVPd3BxRjFWU2NXdmxkZ3BUQkVDOU5Dby9RVGdNbk0wZUpaR2NqMGxGdXhjaTlpK3kvSkNDNUxDRVRuZ3VoRjQ5TWYxbmVLRENaNjA1dXVveEpaR1JKVzFvTUtFaUo0TU5YU2MxZ0VhWkovRjhaMUYwTE9SMHZ6Z1I3blFrczhKVGtHRnIxcXpKcEZPcGc1MXpaWjRnSUlwUTJXTk5mSjlBYWhkRDloOVAveEtiazFVY3hIRjRuN0FNNGhNam9DR3NaeVAiLCJtYWMiOiI3YmNjYjk3Nzg2ZDFlZjVjOWU4ZGM4YzYyMGJjMWM0YWIxYjExYmRlNmIxYTRkMzI0NzMyYWY4Nzc4YmI5OGUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1636004826 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5LJND5bUclZnLzzClCMEaxKzZ5Yt8AAmGjprz0I</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sUbnPperdA1I3PaRxBpMGV4YsR5AVGq0ERh2LBdJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636004826\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-802801214 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:17:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdYbkxHTXJIR21NWjdDUk05aHpYSnc9PSIsInZhbHVlIjoid2xWME5sSXdsemZaQnBlVmZ0cTJTWmhrWExFdVY1R3pmMGVEY1IyWDNwb2g1OHpPQ2taSCtPMTZ6S0lQcnNvRVQ5TlhGc3pjcDR1TEtpUFJyOHNPa1oxdjNUYXN4cENQbktuOTBIcnZ3dXdIT3IvQkt1VVBHM0orenNOVDNuSEdxWTZ6WUcrQnlCL1RQdGZtTldrWE1xd1ZMVm5HVFlyVHY5YlozMlE4dFZ2Y2JzNmRlQ1A3Q1VZWUNBNXZrUFJuVXFiNDZiWDVwalMyc3VsTXo5QkVsRlpnWFE1Yzd0U0pkTTJxU2V4N21tT0dzdisyN3NvOEp2dFB3d2lOYWw1d21LWHR4YW5iU3VOcE5lMEJyM1RUR0pIY1h3NTZ3bjNkQmNuRG94T0x5RElhSGpuRUJ3dEViNlllazc5UWxDQ3RaSG1GcDhpSmtHVjF2dEdjai9xMWRwTWJLQ0tQU25SNU9BVnFtMjAyTEkyVkdSRG5qc25qY2ZscjFmWTZvb1dXWGE4MFJaT0FmR2QyQ2dYSjlkaUN3WHNuMWZCcmxyQkVmWlpCaEhhb3NFTzUxcTlHblhVWjhQbTVtdm15dHpEMUR6RVE2RVNkdDBsQmxEcDhPQndUbmp5c0t0OXEyZWNMaTdpWTRxWG55RlJBTmNSZVBid0J3c0VoRUFiV2htSEkiLCJtYWMiOiJkMTlkOTNkYWZiMmFkZGQyMDRmNjMzNGEzMTk1M2U1Y2Y3ODdlNGEwM2NhZGQ2MDRjOTNiY2QxNjZkYTUzYzFhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:17:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IjZEanJGTWNwNEh2WW8xWjR2Nkc4Q2c9PSIsInZhbHVlIjoiUk5iYWYvbXcrL21za2p0Qk84bno2U3RpVzVUQWNXU1RKRkxONWtRRkZWclkweld3dnJNY2EyZ1R4SkNCWE40ZDZJQ0NTcUNycXlRYWQ0WUZXUFBkdHdLUFZtSS96MFpoS0ZPSEdhZ0FvdDRzYUQ2aENibWpPdFlEcmlWNCs2d0FJUkdhWDgxWjhIOTZsaThWazJ2NFh2d1ZvZFl2bGhJekxyZXJLcXptMXlxemh1ZXJSWTZSaVQ4NXhENWhRbWYrS0ZPNDc0Q3Q5WHNsTTdIQjJrRUhIWEhCV2NlN3JTaFUzOEczbUtpRGxyT3d1R01QSzFXVXM0cithOUhjUFBoMnQxRHZEMjYrd3F2UUtPMllBWG5ndVdrQjFNZTNucHJKVkpUWUk2dXB4aTdCUzVoN3JPQThxbDJteWFVREFmd3JGaFcxVGhDOXY2MktRM2haWXJ0UW1nYXB1RE43RWZMUW5hZU5pKzEySTR5K3dHaDdtUzAvL1BBaTJ3U2d0dnZVRnJvZlZRd1lwOWg0WlJBYUVSRk96dnUxMzJhb1hPcU02K1VKSUZmSDVjWEhPaXpLTmFhOS9kQVRrZ2dHc0xtc2h5U3FEUUphMFNHS1lrV3RMSmM5SDdEMFZkUklQS0V4TUl3enhvNlRCWnJvWnZMZzJrRllEREdEOEQ1S0pmVFYiLCJtYWMiOiJkOTY3MmRkNWNjZTZmZDU4MGQzYjgyZTc2MDQ2ZTg0NjU3YjI2MDc4NTJkNmJiZWVkNGU3YjlhMDkyNjJiMWE5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:17:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdYbkxHTXJIR21NWjdDUk05aHpYSnc9PSIsInZhbHVlIjoid2xWME5sSXdsemZaQnBlVmZ0cTJTWmhrWExFdVY1R3pmMGVEY1IyWDNwb2g1OHpPQ2taSCtPMTZ6S0lQcnNvRVQ5TlhGc3pjcDR1TEtpUFJyOHNPa1oxdjNUYXN4cENQbktuOTBIcnZ3dXdIT3IvQkt1VVBHM0orenNOVDNuSEdxWTZ6WUcrQnlCL1RQdGZtTldrWE1xd1ZMVm5HVFlyVHY5YlozMlE4dFZ2Y2JzNmRlQ1A3Q1VZWUNBNXZrUFJuVXFiNDZiWDVwalMyc3VsTXo5QkVsRlpnWFE1Yzd0U0pkTTJxU2V4N21tT0dzdisyN3NvOEp2dFB3d2lOYWw1d21LWHR4YW5iU3VOcE5lMEJyM1RUR0pIY1h3NTZ3bjNkQmNuRG94T0x5RElhSGpuRUJ3dEViNlllazc5UWxDQ3RaSG1GcDhpSmtHVjF2dEdjai9xMWRwTWJLQ0tQU25SNU9BVnFtMjAyTEkyVkdSRG5qc25qY2ZscjFmWTZvb1dXWGE4MFJaT0FmR2QyQ2dYSjlkaUN3WHNuMWZCcmxyQkVmWlpCaEhhb3NFTzUxcTlHblhVWjhQbTVtdm15dHpEMUR6RVE2RVNkdDBsQmxEcDhPQndUbmp5c0t0OXEyZWNMaTdpWTRxWG55RlJBTmNSZVBid0J3c0VoRUFiV2htSEkiLCJtYWMiOiJkMTlkOTNkYWZiMmFkZGQyMDRmNjMzNGEzMTk1M2U1Y2Y3ODdlNGEwM2NhZGQ2MDRjOTNiY2QxNjZkYTUzYzFhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:17:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IjZEanJGTWNwNEh2WW8xWjR2Nkc4Q2c9PSIsInZhbHVlIjoiUk5iYWYvbXcrL21za2p0Qk84bno2U3RpVzVUQWNXU1RKRkxONWtRRkZWclkweld3dnJNY2EyZ1R4SkNCWE40ZDZJQ0NTcUNycXlRYWQ0WUZXUFBkdHdLUFZtSS96MFpoS0ZPSEdhZ0FvdDRzYUQ2aENibWpPdFlEcmlWNCs2d0FJUkdhWDgxWjhIOTZsaThWazJ2NFh2d1ZvZFl2bGhJekxyZXJLcXptMXlxemh1ZXJSWTZSaVQ4NXhENWhRbWYrS0ZPNDc0Q3Q5WHNsTTdIQjJrRUhIWEhCV2NlN3JTaFUzOEczbUtpRGxyT3d1R01QSzFXVXM0cithOUhjUFBoMnQxRHZEMjYrd3F2UUtPMllBWG5ndVdrQjFNZTNucHJKVkpUWUk2dXB4aTdCUzVoN3JPQThxbDJteWFVREFmd3JGaFcxVGhDOXY2MktRM2haWXJ0UW1nYXB1RE43RWZMUW5hZU5pKzEySTR5K3dHaDdtUzAvL1BBaTJ3U2d0dnZVRnJvZlZRd1lwOWg0WlJBYUVSRk96dnUxMzJhb1hPcU02K1VKSUZmSDVjWEhPaXpLTmFhOS9kQVRrZ2dHc0xtc2h5U3FEUUphMFNHS1lrV3RMSmM5SDdEMFZkUklQS0V4TUl3enhvNlRCWnJvWnZMZzJrRllEREdEOEQ1S0pmVFYiLCJtYWMiOiJkOTY3MmRkNWNjZTZmZDU4MGQzYjgyZTc2MDQ2ZTg0NjU3YjI2MDc4NTJkNmJiZWVkNGU3YjlhMDkyNjJiMWE5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:17:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-802801214\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://127.0.0.1:8000/users/79/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}