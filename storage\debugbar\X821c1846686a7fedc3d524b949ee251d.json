{"__meta": {"id": "X821c1846686a7fedc3d524b949ee251d", "datetime": "2025-07-31 05:37:28", "utime": **********.145325, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:37:28] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.134475, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753940246.570536, "end": **********.145367, "duration": 1.5748310089111328, "duration_str": "1.57s", "measures": [{"label": "Booting", "start": 1753940246.570536, "relative_start": 0, "end": **********.822988, "relative_end": **********.822988, "duration": 1.2524521350860596, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.823021, "relative_start": 1.2524850368499756, "end": **********.145372, "relative_end": 5.0067901611328125e-06, "duration": 0.32235097885131836, "duration_str": "322ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50614672, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.095886, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.08313999999999999, "accumulated_duration_str": "83.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9041271, "duration": 0.03401, "duration_str": "34.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 40.907}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.977756, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 40.907, "width_percent": 2.285}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.995821, "duration": 0.044039999999999996, "duration_str": "44.04ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 43.192, "width_percent": 52.971}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.052193, "duration": 0.00319, "duration_str": "3.19ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 96.163, "width_percent": 3.837}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1024503118 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1024503118\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1279324018 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1279324018\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-366771183 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-366771183\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-819863655 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImQ4S3hQUkVEekpFa0pUWmxqRFdKWFE9PSIsInZhbHVlIjoiUzZSdXNqMlVuK3RnZCt5UUZEYmhLOEFDS1AyanVsMlVOdlMzQ2dNQWRmQjRFQURyeDc1bE10d3pZWjFnK1hYdmJaV0I5OFRES1VnYjBuVitRaUlyU0ZNMVhhYklMUHl1bU5SRm81d0VNY2o0UTd6SW5mOXc4eGVDZWhCZyttT3YrMDMrZXRLQmRvVlhCRjFXOXlRM2svcjBScXlrRXd1WUxNeVdkV1RScG1MZkMzWEsyQjZvc1NOeTFheXdkY0YvN29BZWtmTnRXWlBYaXJaaW5ydW9HcnQ2Nm44aWlKalo4M1ZzSjZYNnZFVTlrWitCTmdyaU15NlJzLy9vZEJWWDN6RUhsdlh3dC8vQjNSekQ3eG5JNGtUMzMvV1pEKzRiMldwb2l6UnliTDViZVRNL0krZ2h2a1hTMEV3c1llbHUzNmlvN2NZaDRLWFFhSlFGSFlFOE9ZU05hYjA0Z1NHYXYyVWVBaStYdmZLbGFocXo4U0NYaC9CMDZrZVA3UzVzRVR2ampOdXdMb2srejkzWm1TVDdrelV4d0MwRk1ibTBVN2l3MlhXRmxQWGlvZmtSVWhycW5wUnZGc3J0RlNBWTZDVDBoY3ZPVXk3UXJGdE4xVExxMXFmN1orWGJDNGtjcU5Kd2dtTE9sZVZoclNTQWh1czBBSEVIa3hMcXRBdUciLCJtYWMiOiI4MmI2MjQyOGMwMTMzZTJhNzVlZDY4YjI1NmVmOWM3MTgwZTE3ZmUzMWM0MzljZWYwMzc4NDkzNWZjMWUwODNjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImVVdGZJTGM5WGJBb0IydUZoTUM5UlE9PSIsInZhbHVlIjoiekJzSEZjQ3gyYnU1SlFLZVBLSFZvUXdsN0t1L1dUSjlLMTZFY3l4VVVJU05xb0p0dkcxUTJjL1ZyYUQrc0RleHhZK1BHQ1R1YVgrZmF3WGtoaVJpUDhCamtSUVR1WDlyMHZUOGhOS005dmJnTDNoTmdYbE5nSFI0RHpGYnlyMDMzUEJMa3hmQXBOSTZxbEdjTWZsOXFVN2xvR1BtdnpyZFp0M3dnbEgxd0tFelhUT0dIcjU1V2FLQ2U0V0k4RVZkazBaTmJPdTVlMWkxV0xMLzFFUDJadytVWVRCK1gycDFiU2kycGx5QStPbHBaRWdrVTNHTWIwcEdac1dYbnR4QWI4UXY5LzJya0dEdzZWMzB0cGZhQWQyYmwrUnVjb1BIMTVpekdjOUVjZlQ5TGpFTzh3akFHdnJYdkR2STlKNHJ1NG1tck40K1VDOFR2UFFDa2twV2xodTMwWlhZYkRsZlNtY1ZORnhyNzNhQm5LMGdNRFFPQkVEZlV1K21BUkdtRTMrMWFVTWIyTkRrT2paNTRUTkMyd0R6OWJhd2JFYW1jeURXWXdoUjlXR0lrd3dpd21XQjIxYnltMlpoaDk4b2k5bVlHeEZmWWpLSWdKSWRscXJaN0Fxd3lBbXJUZGZ2Ni9RaW1yeC9XWVRFMi9BY3NHUDZNQVVYcTdYWUp0TlEiLCJtYWMiOiJlYzU5NGQ1MjNkNjUyMzdkNzA1MjdmODZjNjQ3NmY5YmNmM2JmMjkzMzgyYmNhNjhlNjE1YjEyNjVkNDI0ZTMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-819863655\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-850656205 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lOWuOQi5K5zalKq6lxC9bmXS2G5Yh3URND5pGg9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-850656205\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-456368402 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:37:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldRMysySDZUaGpITUlwb295REtOSEE9PSIsInZhbHVlIjoiUVNPVyt6Mk9ldUp5bDNOb3NvQzBrcUZqalZlbjJrMkdaQ3JXSTY2V0wvK2ROY2h1TkQ4VUVJaHVRRElySXRzSkdNSy9CR0lPekxBM3E3engwTGtUZUF0dDgrWEN4d2Q3ZEtZR21tVllTTlVJbnM4aGFLQk1iWEgvZzJmZ3Z0NTZTTFh4ZkRlYkhXOE9udW1YZzdlcDYvcVU0cDI4ZlJycTR4MWQrczlkMXRqTlBoOEQyT3lxZXVHVldHelpobnh4OEgrK294cmZQMWlqN0R5bUo3QVlFUWt4ZU5nVXBkL0cvTTlVZlNpZE1iUU9UNDJIdVVYNksyeEt4MVlUeVBqVXBUMmZ3b1lkSHhQaC9jRCtyQ25kZS83ZHNCaldCbnY4elN1d2lNcWpvVHQ0OXJaRzVzbFYrZWxYcjhVUUNrYWN6SlR1eTN1L0Q1OHlZRWNUZFhpT2FwRzQwa1lydDVRcTJIdkpxZ1dKL2U4TERndFRYYnNBbVJNTWUwTkY2WjI0ZDg4Y3BiMFRHU1NJdGhzS3AxcmJnTCtsbWc3bk1wSk91cWpuOGFVRjZHOFVGdWN4VENCM3R5RTR2NndwcWJUejA5Sm96blhJUDRtV2twd0lVcC9JRUVGWFE3Nk0vWmhTR3lZY0pjZGcrV3dPVUF6aVFtYW41ZklsSTVFcU1GTVQiLCJtYWMiOiI1ZTMyNjY2Njc2ZWIzNTk5ZDVhOTA1MTcxNjBjNWFjZjRiOWFiMzU5YmRlMjkyYmU5NTYxOTIwNmIzNDFhNTRjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:37:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlQwSTgvU3puUWN6MVBHNGlpM1FWNFE9PSIsInZhbHVlIjoiUmMvblBQSnRvdWdLZnhJbCsrRXZmQThqaFJnb1kwZ0VZTDZMVnUvK2ZtUHJmeFhyUjBLOEh5VkJSYitPVHdKd2lZZDM2dU1iL2ZDcUhzZ2dUUUx4M2NyTFo4c3VlMzcyVXg4SUZMRE83QVA3VFZkL0plUngwMXF3UDNMaTc0V1QzOEJuaG9nYTF5eHoyN3F2YTB4SUphMVIxeDd2TXBsVU1VVGI2VHpMZmJDWmFPTXovNWVkN2wxTEhzcDZIM3NZSXo0Z01NU3ZiQWlCdTB3cWhtYUkzTDNLcXJqQmNDUVowMHcySG92QUwwQUJZbVM3ZlpWOVBZWkRrdDhySFpIaGhjVkZjSXVsK3dJdWN2d2ZuVTBFMEtzd0ZMSGpXVTNJQXVocnV2UE5lbTUxeWV4NWZTeGE1YmR2T2VidXZYeG1xaW9lSnBrZjAvcG9yZTBRK2YvcUh6L2dic1BncWYvaW5RTGJYblhCamFGeWRXbVNmZzNaOXFqaUVPK2FkaVFpVzArTU8wbGpvR25rR1crZy9FMVltVmEvbExtaWN2WStyVWxOSy8yYWZYeWFaOUhVZjhUN0pycnZwZGpibGliakhYaSsvWWJnd25xeU5TOHdSLytlT25TUHVNS2thc1lyNVRpUjRLOFhIWjd1YmNJeTBHM0pYNHY0d0NTazJqZU8iLCJtYWMiOiJiY2JiNWQyMTI2MDIzNWU2ODEzMjIwYjlkNzIxNmQ2MDJhNTZmMjEyY2ZkOGE1OGZhYTcwNjU3MGUwZDU0ZmZkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:37:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldRMysySDZUaGpITUlwb295REtOSEE9PSIsInZhbHVlIjoiUVNPVyt6Mk9ldUp5bDNOb3NvQzBrcUZqalZlbjJrMkdaQ3JXSTY2V0wvK2ROY2h1TkQ4VUVJaHVRRElySXRzSkdNSy9CR0lPekxBM3E3engwTGtUZUF0dDgrWEN4d2Q3ZEtZR21tVllTTlVJbnM4aGFLQk1iWEgvZzJmZ3Z0NTZTTFh4ZkRlYkhXOE9udW1YZzdlcDYvcVU0cDI4ZlJycTR4MWQrczlkMXRqTlBoOEQyT3lxZXVHVldHelpobnh4OEgrK294cmZQMWlqN0R5bUo3QVlFUWt4ZU5nVXBkL0cvTTlVZlNpZE1iUU9UNDJIdVVYNksyeEt4MVlUeVBqVXBUMmZ3b1lkSHhQaC9jRCtyQ25kZS83ZHNCaldCbnY4elN1d2lNcWpvVHQ0OXJaRzVzbFYrZWxYcjhVUUNrYWN6SlR1eTN1L0Q1OHlZRWNUZFhpT2FwRzQwa1lydDVRcTJIdkpxZ1dKL2U4TERndFRYYnNBbVJNTWUwTkY2WjI0ZDg4Y3BiMFRHU1NJdGhzS3AxcmJnTCtsbWc3bk1wSk91cWpuOGFVRjZHOFVGdWN4VENCM3R5RTR2NndwcWJUejA5Sm96blhJUDRtV2twd0lVcC9JRUVGWFE3Nk0vWmhTR3lZY0pjZGcrV3dPVUF6aVFtYW41ZklsSTVFcU1GTVQiLCJtYWMiOiI1ZTMyNjY2Njc2ZWIzNTk5ZDVhOTA1MTcxNjBjNWFjZjRiOWFiMzU5YmRlMjkyYmU5NTYxOTIwNmIzNDFhNTRjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:37:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlQwSTgvU3puUWN6MVBHNGlpM1FWNFE9PSIsInZhbHVlIjoiUmMvblBQSnRvdWdLZnhJbCsrRXZmQThqaFJnb1kwZ0VZTDZMVnUvK2ZtUHJmeFhyUjBLOEh5VkJSYitPVHdKd2lZZDM2dU1iL2ZDcUhzZ2dUUUx4M2NyTFo4c3VlMzcyVXg4SUZMRE83QVA3VFZkL0plUngwMXF3UDNMaTc0V1QzOEJuaG9nYTF5eHoyN3F2YTB4SUphMVIxeDd2TXBsVU1VVGI2VHpMZmJDWmFPTXovNWVkN2wxTEhzcDZIM3NZSXo0Z01NU3ZiQWlCdTB3cWhtYUkzTDNLcXJqQmNDUVowMHcySG92QUwwQUJZbVM3ZlpWOVBZWkRrdDhySFpIaGhjVkZjSXVsK3dJdWN2d2ZuVTBFMEtzd0ZMSGpXVTNJQXVocnV2UE5lbTUxeWV4NWZTeGE1YmR2T2VidXZYeG1xaW9lSnBrZjAvcG9yZTBRK2YvcUh6L2dic1BncWYvaW5RTGJYblhCamFGeWRXbVNmZzNaOXFqaUVPK2FkaVFpVzArTU8wbGpvR25rR1crZy9FMVltVmEvbExtaWN2WStyVWxOSy8yYWZYeWFaOUhVZjhUN0pycnZwZGpibGliakhYaSsvWWJnd25xeU5TOHdSLytlT25TUHVNS2thc1lyNVRpUjRLOFhIWjd1YmNJeTBHM0pYNHY0d0NTazJqZU8iLCJtYWMiOiJiY2JiNWQyMTI2MDIzNWU2ODEzMjIwYjlkNzIxNmQ2MDJhNTZmMjEyY2ZkOGE1OGZhYTcwNjU3MGUwZDU0ZmZkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:37:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-456368402\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-938988348 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-938988348\", {\"maxDepth\":0})</script>\n"}}