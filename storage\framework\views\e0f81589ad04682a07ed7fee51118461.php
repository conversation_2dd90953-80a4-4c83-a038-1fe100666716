<?php
    $businessData = $businessInfo ?? null;
    function getBusinessValue($businessData, $field, $default = '') {
        // Handle both object and array access for backward compatibility
        if ($businessData) {
            if (is_object($businessData)) {
                return old($field, $businessData->$field ?? $default);
            } elseif (is_array($businessData)) {
                return old($field, $businessData[$field] ?? $default);
            }
        }
        return old($field, $default);
    }
?>



<!-- Business Information Form -->
<div class="container-fluid">
    <form id="businessInfoForm" method="POST" action="<?php echo e(route('business.info.update')); ?>" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>

        <!-- General Section -->
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title mb-4"><?php echo e(__('General')); ?></h5>

                <!-- Company Name -->
                <div class="row mb-3">
                    <div class="col-12">
                        <label for="companyName" class="form-label"><?php echo e(__('Company Name')); ?></label>
                        <input type="text" class="form-control" id="companyName" name="company_name"
                               value="<?php echo e(getBusinessValue($businessData, 'company_name')); ?>"
                               placeholder="<?php echo e(__('Enter your company name')); ?>" required>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <!-- Company Email and Phone -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="companyEmail" class="form-label"><?php echo e(__('Company Email')); ?><span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="companyEmail" name="company_email"
                               value="<?php echo e(getBusinessValue($businessData, 'company_email')); ?>"
                               placeholder="<?php echo e(__('Enter company email')); ?>" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="col-md-6">
                        <label for="companyPhone" class="form-label"><?php echo e(__('Company Phone')); ?><span class="text-danger">*</span></label>
                        <input type="tel" class="form-control" id="companyPhone" name="company_phone"
                               value="<?php echo e(getBusinessValue($businessData, 'company_phone')); ?>"
                               placeholder="<?php echo e(__('Enter company phone number')); ?>" required>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <!-- Country and Currency -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="country" class="form-label"><?php echo e(__('Country')); ?></label>
                        <select class="form-select" id="country" name="country" required>
                            <option value=""><?php echo e(__('Select Country')); ?></option>
                            <option value="India" <?php echo e(getBusinessValue($businessData, 'country') == 'India' ? 'selected' : ''); ?>><?php echo e(__('India')); ?></option>
                            <option value="United States" <?php echo e(getBusinessValue($businessData, 'country') == 'United States' ? 'selected' : ''); ?>><?php echo e(__('United States')); ?></option>
                            <option value="United Kingdom" <?php echo e(getBusinessValue($businessData, 'country') == 'United Kingdom' ? 'selected' : ''); ?>><?php echo e(__('United Kingdom')); ?></option>
                            <option value="Canada" <?php echo e(getBusinessValue($businessData, 'country') == 'Canada' ? 'selected' : ''); ?>><?php echo e(__('Canada')); ?></option>
                            <option value="Australia" <?php echo e(getBusinessValue($businessData, 'country') == 'Australia' ? 'selected' : ''); ?>><?php echo e(__('Australia')); ?></option>
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="col-md-6">
                        <label for="currency" class="form-label"><?php echo e(__('Currency')); ?></label>
                        <select class="form-select" id="currency" name="currency" required>
                            <option value=""><?php echo e(__('Select Currency')); ?></option>
                            <option value="INR" <?php echo e(getBusinessValue($businessData, 'currency') == 'INR' ? 'selected' : ''); ?>><?php echo e(__('INR (₹)')); ?></option>
                            <option value="USD" <?php echo e(getBusinessValue($businessData, 'currency') == 'USD' ? 'selected' : ''); ?>><?php echo e(__('USD ($)')); ?></option>
                            <option value="EUR" <?php echo e(getBusinessValue($businessData, 'currency') == 'EUR' ? 'selected' : ''); ?>><?php echo e(__('EUR (€)')); ?></option>
                            <option value="GBP" <?php echo e(getBusinessValue($businessData, 'currency') == 'GBP' ? 'selected' : ''); ?>><?php echo e(__('GBP (£)')); ?></option>
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <!-- Business GST Number and State -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="businessGst" class="form-label"><?php echo e(__('Business GST Number')); ?></label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="businessGst" name="business_gst"
                                   value="<?php echo e(getBusinessValue($businessData, 'business_gst')); ?>"
                                   placeholder="<?php echo e(__('Enter GST number')); ?>">
                            <button class="btn btn-success" type="button" id="verifyGstBtn"><?php echo e(__('Verify')); ?></button>
                        </div>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="col-md-6">
                        <label for="businessState" class="form-label"><?php echo e(__('Business State')); ?></label>
                        <select class="form-select" id="businessState" name="business_state" required>
                            <option value=""><?php echo e(__('Select State')); ?></option>
                            <option value="West Bengal" <?php echo e(getBusinessValue($businessData, 'business_state') == 'West Bengal' ? 'selected' : ''); ?>><?php echo e(__('West Bengal')); ?></option>
                            <option value="Maharashtra" <?php echo e(getBusinessValue($businessData, 'business_state') == 'Maharashtra' ? 'selected' : ''); ?>><?php echo e(__('Maharashtra')); ?></option>
                            <option value="Karnataka" <?php echo e(getBusinessValue($businessData, 'business_state') == 'Karnataka' ? 'selected' : ''); ?>><?php echo e(__('Karnataka')); ?></option>
                            <option value="Tamil Nadu" <?php echo e(getBusinessValue($businessData, 'business_state') == 'Tamil Nadu' ? 'selected' : ''); ?>><?php echo e(__('Tamil Nadu')); ?></option>
                            <option value="Delhi" <?php echo e(getBusinessValue($businessData, 'business_state') == 'Delhi' ? 'selected' : ''); ?>><?php echo e(__('Delhi')); ?></option>
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <!-- Business Logo -->
                <div class="row mb-3">
                    <div class="col-12">
                        <label for="businessLogo" class="form-label"><?php echo e(__('Business Logo')); ?></label>
                        <div class="d-flex align-items-start">
                            <div class="me-3">
                                <div id="logoPreview" class="border rounded d-flex align-items-center justify-content-center"
                                     style="width: 120px; height: 120px; background-color: #f8f9fa;">
                                    <?php if($businessData && $businessData->business_logo): ?>
                                        <img src="<?php echo e(asset('storage/' . $businessData->business_logo)); ?>" alt="Business Logo"
                                             class="img-fluid rounded" style="max-width: 100%; max-height: 100%;">
                                    <?php else: ?>
                                        <span class="text-muted">200 x 200</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <input type="file" class="form-control" id="businessLogo" name="business_logo"
                                       accept="image/jpeg,image/png,image/jpg">
                                <small class="text-muted"><?php echo e(__('Support : JPEG, PNG, JPG')); ?></small>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Agreement Checkbox -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="form-check">
                            <!-- Hidden field to ensure a value is always sent -->
                            <input type="hidden" name="agree_gst_change" value="0">
                            <input class="form-check-input" type="checkbox" id="agreeGstChange" name="agree_gst_change" value="1"
                                   <?php echo e(getBusinessValue($businessData, 'agree_gst_change', false) == '1' ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="agreeGstChange">
                                <?php echo e(__('I agree to change company gst information.')); ?>

                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Physical Address Section -->
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title mb-4">
                    <i class="ti ti-map-pin text-primary me-2"></i><?php echo e(__('Business Physical Address')); ?>

                </h5>

                <!-- Street Address -->
                <div class="row mb-3">
                    <div class="col-12">
                        <label for="streetAddress" class="form-label"><?php echo e(__('Street Address')); ?></label>
                        <input type="text" class="form-control" id="streetAddress" name="street_address"
                               value="<?php echo e(getBusinessValue($businessData, 'street_address')); ?>"
                               placeholder="<?php echo e(__('Enter street address')); ?>" required>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <!-- City and Pincode -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="city" class="form-label"><?php echo e(__('City')); ?></label>
                        <input type="text" class="form-control" id="city" name="city"
                               value="<?php echo e(getBusinessValue($businessData, 'city')); ?>"
                               placeholder="<?php echo e(__('Enter city')); ?>" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="col-md-6">
                        <label for="pincode" class="form-label"><?php echo e(__('Pincode')); ?></label>
                        <input type="text" class="form-control" id="pincode" name="pincode"
                               value="<?php echo e(getBusinessValue($businessData, 'pincode')); ?>"
                               placeholder="<?php echo e(__('Enter pincode')); ?>" required>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <!-- State, Country, and Time Zone -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="stateProvRegion" class="form-label"><?php echo e(__('State/Prov/Region')); ?></label>
                        <input type="text" class="form-control" id="stateProvRegion" name="state_prov_region"
                               value="<?php echo e(getBusinessValue($businessData, 'state_prov_region')); ?>"
                               placeholder="<?php echo e(__('Enter state/province/region')); ?>" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="col-md-4">
                        <label for="addressCountry" class="form-label"><?php echo e(__('Country')); ?></label>
                        <input type="text" class="form-control" id="addressCountry" name="address_country"
                               value="<?php echo e(getBusinessValue($businessData, 'address_country')); ?>"
                               placeholder="<?php echo e(__('Enter country')); ?>" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="col-md-4">
                        <label for="timeZone" class="form-label"><?php echo e(__('Time Zone')); ?></label>
                        <input type="text" class="form-control" id="timeZone" name="time_zone"
                               value="<?php echo e(getBusinessValue($businessData, 'time_zone')); ?>"
                               placeholder="<?php echo e(__('Enter timezone (e.g., Asia/Kolkata)')); ?>" required>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Authorized Contact Person Section -->
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title mb-4">
                    <i class="ti ti-user-check text-primary me-2"></i><?php echo e(__('Authorized Contact Person')); ?>

                </h5>

                <!-- Contact Person Name and Job Position -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="contactPersonName" class="form-label"><?php echo e(__('Authorized Contact Person Name')); ?></label>
                        <input type="text" class="form-control" id="contactPersonName" name="contact_person_name"
                               placeholder="<?php echo e(__('Enter Authorized Contact Person Name')); ?>"
                               value="<?php echo e(getBusinessValue($businessData, 'contact_person_name')); ?>" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="col-md-6">
                        <label for="jobPosition" class="form-label"><?php echo e(__('Job Position')); ?></label>
                        <select class="form-select" id="jobPosition" name="job_position" required>
                            <option value=""><?php echo e(__('Select Position')); ?></option>
                            <option value="CEO" <?php echo e(getBusinessValue($businessData, 'job_position') == 'CEO' ? 'selected' : ''); ?>><?php echo e(__('CEO')); ?></option>
                            <option value="CTO" <?php echo e(getBusinessValue($businessData, 'job_position') == 'CTO' ? 'selected' : ''); ?>><?php echo e(__('CTO')); ?></option>
                            <option value="CFO" <?php echo e(getBusinessValue($businessData, 'job_position') == 'CFO' ? 'selected' : ''); ?>><?php echo e(__('CFO')); ?></option>
                            <option value="Manager" <?php echo e(getBusinessValue($businessData, 'job_position') == 'Manager' ? 'selected' : ''); ?>><?php echo e(__('Manager')); ?></option>
                            <option value="Director" <?php echo e(getBusinessValue($businessData, 'job_position') == 'Director' ? 'selected' : ''); ?>><?php echo e(__('Director')); ?></option>
                            <option value="Owner" <?php echo e(getBusinessValue($businessData, 'job_position') == 'Owner' ? 'selected' : ''); ?>><?php echo e(__('Owner')); ?></option>
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <!-- Email and Phone -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="contactEmail" class="form-label"><?php echo e(__('Email')); ?></label>
                        <input type="email" class="form-control" id="contactEmail" name="contact_email"
                               value="<?php echo e(getBusinessValue($businessData, 'contact_email')); ?>"
                               placeholder="<?php echo e(__('Enter contact email')); ?>" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="col-md-6">
                        <label for="contactPhone" class="form-label"><?php echo e(__('Phone No.')); ?></label>
                        <input type="tel" class="form-control" id="contactPhone" name="contact_phone"
                               value="<?php echo e(getBusinessValue($businessData, 'contact_phone')); ?>"
                               placeholder="<?php echo e(__('Enter contact phone number')); ?>" required>
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-between">
            <button type="button" class="btn btn-outline-secondary">
                <i class="ti ti-arrow-left me-1"></i><?php echo e(__('Prev')); ?>

            </button>
            <button type="submit" class="btn btn-success">
                <i class="ti ti-device-floppy me-1"></i><?php echo e(__('Save Business Information')); ?>

            </button>
        </div>
    </form>
</div>

<?php $__env->startPush('script-page'); ?>
<script>
$(document).ready(function() {
    console.log('Business Info Form Script Loaded');

    // Handle business logo preview
    $('#businessLogo').on('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#logoPreview').html(`<img src="${e.target.result}" alt="Business Logo" class="img-fluid rounded" style="max-width: 100%; max-height: 100%;">`);
            };
            reader.readAsDataURL(file);
        }
    });

    // Handle GST verification
    $('#verifyGstBtn').on('click', function() {
        const gstNumber = $('#businessGst').val();
        if (!gstNumber) {
            if (typeof show_toastr === 'function') {
                show_toastr('error', '<?php echo e(__("Please enter GST number")); ?>');
            } else {
                alert('<?php echo e(__("Please enter GST number")); ?>');
            }
            return;
        }

        const btn = $(this);
        const originalText = btn.text();
        btn.prop('disabled', true).text('<?php echo e(__("Verifying...")); ?>');

        // Simulate GST verification (replace with actual API call)
        setTimeout(function() {
            btn.prop('disabled', false).text(originalText);
            if (typeof show_toastr === 'function') {
                show_toastr('success', '<?php echo e(__("GST number verified successfully")); ?>');
            } else {
                alert('<?php echo e(__("GST number verified successfully")); ?>');
            }
        }, 2000);
    });

    // Handle form submission
    $('#businessInfoForm').on('submit', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('Submitting business information form...');

        const formData = new FormData(this);
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();

        submitBtn.prop('disabled', true).html('<i class="ti ti-loader"></i> <?php echo e(__("Saving...")); ?>');

        $.ajax({
            url: '<?php echo e(route("business.info.update")); ?>',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                console.log('AJAX Success response:', response);

                if (response.success) {
                    // Show success message
                    if (typeof show_toastr === 'function') {
                        show_toastr('success', '<?php echo e(__("Profile updated successfully")); ?>');
                    } else {
                        alert('<?php echo e(__("Profile updated successfully")); ?>');
                    }

                    // Reload the page after a short delay to show updated data
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    console.log('Response success is false:', response);
                    if (typeof show_toastr === 'function') {
                        show_toastr('error', response.message || '<?php echo e(__("Something went wrong")); ?>');
                    } else {
                        alert(response.message || '<?php echo e(__("Something went wrong")); ?>');
                    }
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX Error:', {xhr, status, error});
                console.log('Status:', xhr.status);
                console.log('Response text:', xhr.responseText);

                if (xhr.status === 422) {
                    const errors = xhr.responseJSON?.errors || {};
                    console.log('Validation errors:', errors);

                    // Clear previous errors
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').text('');

                    // Show validation errors
                    let errorMessages = [];
                    $.each(errors, function(field, messages) {
                        const input = $(`[name="${field}"]`);
                        input.addClass('is-invalid');

                        // Find or create invalid-feedback element
                        let feedbackElement = input.siblings('.invalid-feedback');
                        if (feedbackElement.length === 0) {
                            feedbackElement = $('<div class="invalid-feedback"></div>');
                            input.after(feedbackElement);
                        }
                        feedbackElement.text(messages[0]);

                        errorMessages.push(messages[0]);
                    });

                    // Show toast with first error message
                    const firstError = errorMessages.length > 0 ? errorMessages[0] : '<?php echo e(__("Please fix the validation errors")); ?>';
                    if (typeof show_toastr === 'function') {
                        show_toastr('error', firstError);
                    } else {
                        alert(firstError);
                    }
                } else {
                    const errorMessage = xhr.responseJSON?.error || '<?php echo e(__("Something went wrong. Please try again.")); ?>';
                    if (typeof show_toastr === 'function') {
                        show_toastr('error', errorMessage);
                    } else {
                        alert(errorMessage);
                    }
                }
            },
            complete: function() {
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Clear validation errors on input
    $('.form-control, .form-select').on('input change', function() {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').text('');
    });

    // Auto-populate state based on country selection
    $('#country').on('change', function() {
        const country = $(this).val();
        const stateSelect = $('#businessState');

        // Clear current options
        stateSelect.html('<option value=""><?php echo e(__("Select State")); ?></option>');

        if (country === 'India') {
            const indianStates = [
                'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh',
                'Goa', 'Gujarat', 'Haryana', 'Himachal Pradesh', 'Jharkhand',
                'Karnataka', 'Kerala', 'Madhya Pradesh', 'Maharashtra', 'Manipur',
                'Meghalaya', 'Mizoram', 'Nagaland', 'Odisha', 'Punjab',
                'Rajasthan', 'Sikkim', 'Tamil Nadu', 'Telangana', 'Tripura',
                'Uttar Pradesh', 'Uttarakhand', 'West Bengal', 'Delhi'
            ];

            indianStates.forEach(function(state) {
                stateSelect.append(`<option value="${state}">${state}</option>`);
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/finance/tabs/business-info.blade.php ENDPATH**/ ?>