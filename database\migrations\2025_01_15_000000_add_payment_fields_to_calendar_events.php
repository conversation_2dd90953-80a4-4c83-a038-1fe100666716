<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the calendar_events table exists before trying to modify it
        if (Schema::hasTable('calendar_events')) {
            Schema::table('calendar_events', function (Blueprint $table) {
                // Check if columns don't already exist before adding them
                if (!Schema::hasColumn('calendar_events', 'payment_amount')) {
                    $table->decimal('payment_amount', 10, 2)->nullable()->after('description');
                }
                if (!Schema::hasColumn('calendar_events', 'payment_required')) {
                    $table->boolean('payment_required')->default(false)->after('payment_amount');
                }
                if (!Schema::hasColumn('calendar_events', 'payment_currency')) {
                    $table->string('payment_currency', 3)->default('USD')->after('payment_required');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if the calendar_events table exists before trying to modify it
        if (Schema::hasTable('calendar_events')) {
            Schema::table('calendar_events', function (Blueprint $table) {
                // Only drop columns that exist
                $columnsToDrop = [];
                if (Schema::hasColumn('calendar_events', 'payment_amount')) {
                    $columnsToDrop[] = 'payment_amount';
                }
                if (Schema::hasColumn('calendar_events', 'payment_required')) {
                    $columnsToDrop[] = 'payment_required';
                }
                if (Schema::hasColumn('calendar_events', 'payment_currency')) {
                    $columnsToDrop[] = 'payment_currency';
                }

                if (!empty($columnsToDrop)) {
                    $table->dropColumn($columnsToDrop);
                }
            });
        }
    }
}; 