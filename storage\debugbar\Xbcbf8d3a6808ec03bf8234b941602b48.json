{"__meta": {"id": "Xbcbf8d3a6808ec03bf8234b941602b48", "datetime": "2025-07-31 06:29:11", "utime": **********.376761, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943349.066746, "end": **********.376814, "duration": 2.310067892074585, "duration_str": "2.31s", "measures": [{"label": "Booting", "start": 1753943349.066746, "relative_start": 0, "end": **********.206658, "relative_end": **********.206658, "duration": 2.1399118900299072, "duration_str": "2.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.206694, "relative_start": 2.****************, "end": **********.376819, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "170ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "shl44bKy0sNZiD0KVvh9VY2xhSEwDbjCeGqWcvJ8", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-271294976 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-271294976\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1937492598 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1937492598\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1532334455 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1532334455\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-719760763 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-719760763\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1125808743 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1125808743\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-545756826 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:29:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFoMnhmaWxnZ09oVVJyL2tNdTljV0E9PSIsInZhbHVlIjoiVDVrc3NDUUg5cTVYaGpxWmRuTm5PczRoMDNZQ3llamY3akE4V2V4WXVmRVhPRDRBUnJHeWc1NUtEbUtEYzlndzk0TStOaVZpQVliYkhZbUpQUDdEczlQMnpZeTJ4WHhpVk92UmVwS0ZjRlQwYkdTdG9NQzhmc295a1Y0cEVGUzJIbC8rczJHYW5NeU11Z2FTSDJsNzc3amJtMkFseFdlWVp4Y05lM1ZDMWpwcmtLNUsyTmFYMHFpaGxORWdzYXQ4M1Z2OVdTTEZ0cXd2V0V1cFlyTHJaYXZnOERRaFBjbzB5T0pBYlhWSHpIMUt4NmltbDJlSzRPNnlNSTRTblJ2N0hRQkVsSWUzck5JcjBjSisrNzVITTJMa0FyYjNQT2dRRWJhdXF3bytiTlJPQWJPMEt1dVBidWVlY2pkakhTejQrL2FRb0V1a2pKUlZ1dEpFSGZEdk93N1hkNzRqanBDWnkyUlNPQUI0a2Vta2JiTW9EVnRJSS9meTltSEE0WmlOMEpjZElHWlRWbVEzL2xZY0xlNHZJZXJPUktvRndLeXBWMDhYMWhxMlNzQ3hrUTVXS0Q4eXplMlN4V2htTUdxSEEzRHN2a25oemFGVTRFd0xNd1RYNVd1TkJQcTN6VjdMN0YrWk8zbWpqTGlNZHdLdjhzYkc5bXB4TnZpZGhKakwiLCJtYWMiOiJhNjVlZWNkNDcxZjU0Y2RhZGVjMWYzYWVlMDQ5MGU2ZGQxOTVmODkyYTI1MzNhOTk4MThkOWQwYzVhNzE2YWQwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik92bWdSUWRLcVltRVIxblBHSGkzclE9PSIsInZhbHVlIjoiSFlRYlE3L2hGYUtzaDlYYVpLcVR5V21PVTJZTEZJVlcrTjc2S3BHYk05L1pTNHNwRzVQbWRKRE1YbTZGaDJWTmZFdjBDL2tOZTRIVEtCRGI2SFFkQnNHMzZsd2RHbE1sT3JXN0p1dTVXOVRDbFp2enhQa3JIUmVlM1NOVWlPYkY0ZURwcTVOUzVaVlVJTHdNWjJJWnhrY29QMkFVTFJ2K08rMXFpT21HTk5yUVRlek1yK25RMXBIWDdOYjk3NGw2UWtPcXJ1d2E4Rk5menEwNXpnY0RQVlJFWGh4OUx4Wi9qTk1QV29obU1ncDNQR29SYVAzUXdzelQvWTdCajRJUm1PeFNhTzc0RUFYQXJBTm5pRzBjUGlpV0hMMC8wS09pdnpjT3Y4ZXhRRWZWZmFjUFErUm5RZy9aZ1JUVjhnT3JSdXJPTEQvdzlRdVhOT3NUM003SEliR0RkNm5ud3RDOFhtTEI4b204REFaY1BxcjA3aXJmV2txdjhSN0tHeTNWZGFSUm1kMk1sckM4S3g4R0FobUF3Q3BycVlZdXlrMytLNHBNTWk2ZllwZHlHT25vbmEvNWt6QkJzRmQzOGhmWTcydy95TWgwL0RrUC90cHJkcGp1KzdtSU5rbU1LY0xkbmF3Q1czTHRNdTdEL3ZPZHdTU0grZklUblRRckgvUW4iLCJtYWMiOiJjNWVlZTEyNTJkZDY0MmM5NmY4MzljYzk0ZDg0NGJmNjlhZjhlNTlmZTlmMjU3ZGQ4NGYzYjdkNTNmNDY5YTVjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFoMnhmaWxnZ09oVVJyL2tNdTljV0E9PSIsInZhbHVlIjoiVDVrc3NDUUg5cTVYaGpxWmRuTm5PczRoMDNZQ3llamY3akE4V2V4WXVmRVhPRDRBUnJHeWc1NUtEbUtEYzlndzk0TStOaVZpQVliYkhZbUpQUDdEczlQMnpZeTJ4WHhpVk92UmVwS0ZjRlQwYkdTdG9NQzhmc295a1Y0cEVGUzJIbC8rczJHYW5NeU11Z2FTSDJsNzc3amJtMkFseFdlWVp4Y05lM1ZDMWpwcmtLNUsyTmFYMHFpaGxORWdzYXQ4M1Z2OVdTTEZ0cXd2V0V1cFlyTHJaYXZnOERRaFBjbzB5T0pBYlhWSHpIMUt4NmltbDJlSzRPNnlNSTRTblJ2N0hRQkVsSWUzck5JcjBjSisrNzVITTJMa0FyYjNQT2dRRWJhdXF3bytiTlJPQWJPMEt1dVBidWVlY2pkakhTejQrL2FRb0V1a2pKUlZ1dEpFSGZEdk93N1hkNzRqanBDWnkyUlNPQUI0a2Vta2JiTW9EVnRJSS9meTltSEE0WmlOMEpjZElHWlRWbVEzL2xZY0xlNHZJZXJPUktvRndLeXBWMDhYMWhxMlNzQ3hrUTVXS0Q4eXplMlN4V2htTUdxSEEzRHN2a25oemFGVTRFd0xNd1RYNVd1TkJQcTN6VjdMN0YrWk8zbWpqTGlNZHdLdjhzYkc5bXB4TnZpZGhKakwiLCJtYWMiOiJhNjVlZWNkNDcxZjU0Y2RhZGVjMWYzYWVlMDQ5MGU2ZGQxOTVmODkyYTI1MzNhOTk4MThkOWQwYzVhNzE2YWQwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik92bWdSUWRLcVltRVIxblBHSGkzclE9PSIsInZhbHVlIjoiSFlRYlE3L2hGYUtzaDlYYVpLcVR5V21PVTJZTEZJVlcrTjc2S3BHYk05L1pTNHNwRzVQbWRKRE1YbTZGaDJWTmZFdjBDL2tOZTRIVEtCRGI2SFFkQnNHMzZsd2RHbE1sT3JXN0p1dTVXOVRDbFp2enhQa3JIUmVlM1NOVWlPYkY0ZURwcTVOUzVaVlVJTHdNWjJJWnhrY29QMkFVTFJ2K08rMXFpT21HTk5yUVRlek1yK25RMXBIWDdOYjk3NGw2UWtPcXJ1d2E4Rk5menEwNXpnY0RQVlJFWGh4OUx4Wi9qTk1QV29obU1ncDNQR29SYVAzUXdzelQvWTdCajRJUm1PeFNhTzc0RUFYQXJBTm5pRzBjUGlpV0hMMC8wS09pdnpjT3Y4ZXhRRWZWZmFjUFErUm5RZy9aZ1JUVjhnT3JSdXJPTEQvdzlRdVhOT3NUM003SEliR0RkNm5ud3RDOFhtTEI4b204REFaY1BxcjA3aXJmV2txdjhSN0tHeTNWZGFSUm1kMk1sckM4S3g4R0FobUF3Q3BycVlZdXlrMytLNHBNTWk2ZllwZHlHT25vbmEvNWt6QkJzRmQzOGhmWTcydy95TWgwL0RrUC90cHJkcGp1KzdtSU5rbU1LY0xkbmF3Q1czTHRNdTdEL3ZPZHdTU0grZklUblRRckgvUW4iLCJtYWMiOiJjNWVlZTEyNTJkZDY0MmM5NmY4MzljYzk0ZDg0NGJmNjlhZjhlNTlmZTlmMjU3ZGQ4NGYzYjdkNTNmNDY5YTVjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-545756826\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1068716771 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">shl44bKy0sNZiD0KVvh9VY2xhSEwDbjCeGqWcvJ8</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1068716771\", {\"maxDepth\":0})</script>\n"}}