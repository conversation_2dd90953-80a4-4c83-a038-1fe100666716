{"__meta": {"id": "X3f9458aa4630b11fc097a482983a34f8", "datetime": "2025-07-31 05:53:07", "utime": **********.280085, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753941184.865723, "end": **********.280138, "duration": 2.414415121078491, "duration_str": "2.41s", "measures": [{"label": "Booting", "start": 1753941184.865723, "relative_start": 0, "end": **********.115616, "relative_end": **********.115616, "duration": 2.2498931884765625, "duration_str": "2.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.11564, "relative_start": 2.****************, "end": **********.280144, "relative_end": 5.9604644775390625e-06, "duration": 0.****************, "duration_str": "165ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MUwm9Nlhz9IQIA2jv0XFvBxQCQJt0M4VOiv6IhZb", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1029048888 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1029048888\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1745628754 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1745628754\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-245540866 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-245540866\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1925598695 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925598695\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1741615248 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1741615248\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1216699147 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:53:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjYwZlpGUHZuOVF6c2RUeE9mL1hWQWc9PSIsInZhbHVlIjoiM0tZcjgxYzVybnZkOU9ySjNDMG5TT3lBQzNnWDNRaUdyODJ0ZWxhUXF1dms5a0lGYUNicjJibWhoL0hSUVYzRkFaVytRbmZsREZmUFJabFdQbU93MWw4YjRldUorZkZXTUJ0L0tCdVpNVlB4NXJoS0kvVFVMSTliVDVoaUhPLzk0dk53aUxZKzNaL0JxbmlLT2IvYVVldlNqNkcxMDVCTjJ5a1RLaHdJTExRdFFIZldtRDFqU2ZmLy9paGx3QmU4R3V2ZTRhWDJaQUh5MWxuQWU3RHVGVGNkam5URHdLMEt3eEJ5SVAwTXQ2cDVEbnA4a2NrRmdzUEJ4c2N0OTVIRnJGRDJ6OWZ6RVZvQ05DSEZnT1lhdlFtQ1Z5bE9UZUdiVVRGaGZnVEdNaUdiS2NRaU9TQ1FqQnNUVGlNM3I3YmpsZVNZcUJCQXBDL3NaTEEvdlZPTWFSSGw4Z2hMeGtiYVpSMUw2c3JkeEkwZWs3VmJLc3BnbjZIdWR6VFhiTWZ5LzNpY1FEWU1UTUlPcm5TOFI4cXZPbVlVbjlvbDJPUjk4dnFVR0hBdjBpemh1RkkzTWoxYkJ1TkdUdW1DbkFKTmd6dFM3ekxsSE5SaTFESDd3ak5HYlJIVDh6SGI4UEF5TUdWaXluMExuZWpHL0Mxbk1JUnFrVVFNazlFeE9VNWEiLCJtYWMiOiJmYzkzYjRmOTBmZGZjZDkyNWMzNWNkNTQ0YWFiODVjMDQxZDA1NDY4YTJkNWJiZGRlM2QwNWE2ZWQ4MTliMjVmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:53:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlhWUmZTM0paVTRlMUVIWDZSM05ENHc9PSIsInZhbHVlIjoia1FLYlA1UWZhWDVYTVd0blFCUlNOYzlYaDdCWmF3dDROK1ZlaTNTNnZQV2FMYXBLRDBZZUJqUUwyVjd4cEh3cmZ3em5WdjJwTXB0N0NleTl3Vlo0VC9OMXlZSWFzLzE4QXlLclpydEJ3NHdxR0xnYmlHQ0VWNDNnVUVQa1NvdCtQc0ptYm1yT1czSnZOV1BkQlRHQ0tJRTdxdS9iU1ZYVUZtUDNJUVlvVU9rcEg4SnJzOXRwWnM1RWNrM0tQZ1MwK1liVVZSYnNuSDVhZWdMUUlYVDJqZGJ1Z2l5TjIvZDRLeWV3cExuaVowK25xd3B1ZFFoYmRDLzBSclFFcXVDNEFmYzdUM2ZTYzhvYlh3YXB1Z2g3eXlTWGlWVlJha09EYVpSUVFyRW14dVRNb3M1bHVVK2FsNnRoU0RRZi9CbC9MWkptOU5TbjB0VElIeWs5V3hYeWNleS9LOVYybzVTcU5tWHZSeU9pa0UwNkpoSW9WcHc2RTdLa0FHS2JXV3dNRVoyY1o2NEh2Q2RxSlplTlQ5T2lmVW1qajlva2xuTVMzc0RET0IyUHNLRERML2xSUFFmMGthSzRtMHJVbGFvcVowNHBTVVVDQmVncG9vSkJnQ3FGNjhJZWM3dityVGNoMXhKb1M5THZUZkQxTzNJakIvQTY5ZDBoK0tVNTFQY3QiLCJtYWMiOiIwNThlMzUyMjhjZGVlMDZjYWFlZmY3ODk1OWY3NTFmYjU4YTc3OTEzNTdmNDA0ZGY2OGExOWM0YmU3NzFmOWM0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:53:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjYwZlpGUHZuOVF6c2RUeE9mL1hWQWc9PSIsInZhbHVlIjoiM0tZcjgxYzVybnZkOU9ySjNDMG5TT3lBQzNnWDNRaUdyODJ0ZWxhUXF1dms5a0lGYUNicjJibWhoL0hSUVYzRkFaVytRbmZsREZmUFJabFdQbU93MWw4YjRldUorZkZXTUJ0L0tCdVpNVlB4NXJoS0kvVFVMSTliVDVoaUhPLzk0dk53aUxZKzNaL0JxbmlLT2IvYVVldlNqNkcxMDVCTjJ5a1RLaHdJTExRdFFIZldtRDFqU2ZmLy9paGx3QmU4R3V2ZTRhWDJaQUh5MWxuQWU3RHVGVGNkam5URHdLMEt3eEJ5SVAwTXQ2cDVEbnA4a2NrRmdzUEJ4c2N0OTVIRnJGRDJ6OWZ6RVZvQ05DSEZnT1lhdlFtQ1Z5bE9UZUdiVVRGaGZnVEdNaUdiS2NRaU9TQ1FqQnNUVGlNM3I3YmpsZVNZcUJCQXBDL3NaTEEvdlZPTWFSSGw4Z2hMeGtiYVpSMUw2c3JkeEkwZWs3VmJLc3BnbjZIdWR6VFhiTWZ5LzNpY1FEWU1UTUlPcm5TOFI4cXZPbVlVbjlvbDJPUjk4dnFVR0hBdjBpemh1RkkzTWoxYkJ1TkdUdW1DbkFKTmd6dFM3ekxsSE5SaTFESDd3ak5HYlJIVDh6SGI4UEF5TUdWaXluMExuZWpHL0Mxbk1JUnFrVVFNazlFeE9VNWEiLCJtYWMiOiJmYzkzYjRmOTBmZGZjZDkyNWMzNWNkNTQ0YWFiODVjMDQxZDA1NDY4YTJkNWJiZGRlM2QwNWE2ZWQ4MTliMjVmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:53:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlhWUmZTM0paVTRlMUVIWDZSM05ENHc9PSIsInZhbHVlIjoia1FLYlA1UWZhWDVYTVd0blFCUlNOYzlYaDdCWmF3dDROK1ZlaTNTNnZQV2FMYXBLRDBZZUJqUUwyVjd4cEh3cmZ3em5WdjJwTXB0N0NleTl3Vlo0VC9OMXlZSWFzLzE4QXlLclpydEJ3NHdxR0xnYmlHQ0VWNDNnVUVQa1NvdCtQc0ptYm1yT1czSnZOV1BkQlRHQ0tJRTdxdS9iU1ZYVUZtUDNJUVlvVU9rcEg4SnJzOXRwWnM1RWNrM0tQZ1MwK1liVVZSYnNuSDVhZWdMUUlYVDJqZGJ1Z2l5TjIvZDRLeWV3cExuaVowK25xd3B1ZFFoYmRDLzBSclFFcXVDNEFmYzdUM2ZTYzhvYlh3YXB1Z2g3eXlTWGlWVlJha09EYVpSUVFyRW14dVRNb3M1bHVVK2FsNnRoU0RRZi9CbC9MWkptOU5TbjB0VElIeWs5V3hYeWNleS9LOVYybzVTcU5tWHZSeU9pa0UwNkpoSW9WcHc2RTdLa0FHS2JXV3dNRVoyY1o2NEh2Q2RxSlplTlQ5T2lmVW1qajlva2xuTVMzc0RET0IyUHNLRERML2xSUFFmMGthSzRtMHJVbGFvcVowNHBTVVVDQmVncG9vSkJnQ3FGNjhJZWM3dityVGNoMXhKb1M5THZUZkQxTzNJakIvQTY5ZDBoK0tVNTFQY3QiLCJtYWMiOiIwNThlMzUyMjhjZGVlMDZjYWFlZmY3ODk1OWY3NTFmYjU4YTc3OTEzNTdmNDA0ZGY2OGExOWM0YmU3NzFmOWM0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:53:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1216699147\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1038409751 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MUwm9Nlhz9IQIA2jv0XFvBxQCQJt0M4VOiv6IhZb</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1038409751\", {\"maxDepth\":0})</script>\n"}}