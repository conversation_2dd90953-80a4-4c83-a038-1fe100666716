<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the calendar_events table exists before trying to modify it
        if (Schema::hasTable('calendar_events')) {
            Schema::table('calendar_events', function (Blueprint $table) {
                // Check if columns don't already exist before adding them
                if (!Schema::hasColumn('calendar_events', 'minimum_notice_value')) {
                    $table->integer('minimum_notice_value')->nullable()->after('minimum_notice');
                }
                if (!Schema::hasColumn('calendar_events', 'minimum_notice_unit')) {
                    $table->enum('minimum_notice_unit', ['minutes', 'hours', 'days', 'months'])->default('minutes')->after('minimum_notice_value');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if the calendar_events table exists before trying to modify it
        if (Schema::hasTable('calendar_events')) {
            Schema::table('calendar_events', function (Blueprint $table) {
                // Only drop columns that exist
                $columnsToDrop = [];
                if (Schema::hasColumn('calendar_events', 'minimum_notice_value')) {
                    $columnsToDrop[] = 'minimum_notice_value';
                }
                if (Schema::hasColumn('calendar_events', 'minimum_notice_unit')) {
                    $columnsToDrop[] = 'minimum_notice_unit';
                }

                if (!empty($columnsToDrop)) {
                    $table->dropColumn($columnsToDrop);
                }
            });
        }
    }
};
