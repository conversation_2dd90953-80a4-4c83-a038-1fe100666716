{"__meta": {"id": "Xf159e8714b8186266c95ea9922275aac", "datetime": "2025-07-31 05:38:14", "utime": **********.996972, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940293.281392, "end": **********.997138, "duration": 1.7157459259033203, "duration_str": "1.72s", "measures": [{"label": "Booting", "start": 1753940293.281392, "relative_start": 0, "end": **********.805059, "relative_end": **********.805059, "duration": 1.5236668586730957, "duration_str": "1.52s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.80511, "relative_start": 1.****************, "end": **********.997147, "relative_end": 9.059906005859375e-06, "duration": 0.*****************, "duration_str": "192ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TKoZNLCOD2VHhv6jQCo3jSGHwmFQfT0I3a8tswI8", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-61372398 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-61372398\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-152264017 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-152264017\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-253583923 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-253583923\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-532317188 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-532317188\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1987659647 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1987659647\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1980156622 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:38:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitkUE1xOStOSDA3S1RhclZmRTl5dmc9PSIsInZhbHVlIjoidzIra281SmJEN09QOFVwU2tuUnY4cG1KUThSbXdobGxMUlh1aUNYdGhrZksrakcxS2FtU0NYK2ErNGtOcHJBQnZoeUFDUUw3blVFRTh5cElXaHgzcEJJNEMyU2tpcEtGMVFiblFoaDhHTEtsd2RNM2ZqZlg5cThYQnZlNXNWSjhZc1dSYlg0WmJEbmRpWUp5QjhrcVRISVR5dm90anlxREFlUXdFVmpqdzJCakpzWVh5Ulp0OS9LSnp0ZzR5TWdWTExpV1llNFZVNmM5dkJCTS8vcjBrRlBMWEd2TnE4Q2tBT0I5Z295M3ZNajVOUy9qd2RTaVlZVkZqUDlCSE9OQVduVkxYWDdSWkluMFRJOUVzdzhmN25NakxFTnVJMzVqV3Z5T0NCWFBySExBME1sdXhxU0VFTjNmZ1hzc0FweWFnb3dhZzBTWnp2MHR5cmpyUDMwNnYxdFphdjNtaUtPcDYvN0pRQTRrL0dMd01OVXY4TzdnM0tBYTVOaEtES1E1a21xM2xJeXc2d09BdFd5TVpCYlhXa2RBOWgwaVNIdlVuOVRvNFdzK2xNZ3NuQWF6d1o5WDlhR2hHUHUwSDUzRnNkUVpwTFpNM0dKV0JCMHBtdTg4bXI2M0NTaklINEZPdTB6SWF2Nk5uc29kc1F3VEZFS3daeHVRc1hYMTFFZ20iLCJtYWMiOiI1YzAxYzMxMzBlNDVhYTg0NTNiNDU2ZDdmMTg5ZWQ3NWQ1N2E1MjUwYTdkZmZmYmY3Nzk0ZjU0NGY0MThhODk5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:38:14 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkxENDRSY3hhRVl2VE9hQURyTjlWSWc9PSIsInZhbHVlIjoiY0VmUGR1VFkrSzBjUDI3dGxCNWNHVG5iUVBLdWpPaFJxb0w1YTlURHMrSW13MUhMRWFyZ2JQUEpsd3N4RGRhYWY4SS9BYkdCZ2kxdUZtSVEzeFhERFg5UW1YWDIvWXFHVGJpRkowZ0hDbFRZcTNwQlNudksrSFQwK29HTXdrUzJ6M1NOU3M2alIxNWppbS9JWDRzckdHU2JScTVSQUdoMDhzT3A2YUpkVS9IbENiSW1RMWt6NDIvaHlGS1k4Q25qL2V1NENNeis2T2o4NkcramFUZFMwNmdmeW5zL3Q1ZVNyYkVvSy96SUVCTjBVYUhjVlI2SVFZNlQyNlA0Z3lRTE5QK3h5TXFEOW82RTJxTWZaUnlDRG1kUU0ybGY0MkxLV1NmbGk3NUQ0cnpyTXJyTkYvVW94TEwyZ2VZdEdwUDNUK2JlenhqT1pQaXQ5THhKWllPWnhnU2w3NEJBV1pwdGltMkltUkVkY1RxdGd2NnM1TDhZZWt4T1owanEzRm93UmdiTTFnTGh6bTVKNHk5Q3o4SkJJS0tLM3VEemR4MU5Hai93Qy9YYzBDMi9qcHlvaXBRL1hNVzBTZ0YwKzcvU0NmWWR5OHRkaWwzbjZYa2pLSVgrOVFpL0RxL29KUEUxdUVxdEpnNldNcFBiWWFPYkE1Z3ZBc1BqQnFiL0VFQ3IiLCJtYWMiOiI1NTkzNDFkZWE3OWM2ZTkyMDQzYmYzMjU5ZmI4ZTdjNjIzMTA5NDFlMjQxYWM4ZGQ3ZmQzODVlY2Y1ODNkMmUyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:38:14 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitkUE1xOStOSDA3S1RhclZmRTl5dmc9PSIsInZhbHVlIjoidzIra281SmJEN09QOFVwU2tuUnY4cG1KUThSbXdobGxMUlh1aUNYdGhrZksrakcxS2FtU0NYK2ErNGtOcHJBQnZoeUFDUUw3blVFRTh5cElXaHgzcEJJNEMyU2tpcEtGMVFiblFoaDhHTEtsd2RNM2ZqZlg5cThYQnZlNXNWSjhZc1dSYlg0WmJEbmRpWUp5QjhrcVRISVR5dm90anlxREFlUXdFVmpqdzJCakpzWVh5Ulp0OS9LSnp0ZzR5TWdWTExpV1llNFZVNmM5dkJCTS8vcjBrRlBMWEd2TnE4Q2tBT0I5Z295M3ZNajVOUy9qd2RTaVlZVkZqUDlCSE9OQVduVkxYWDdSWkluMFRJOUVzdzhmN25NakxFTnVJMzVqV3Z5T0NCWFBySExBME1sdXhxU0VFTjNmZ1hzc0FweWFnb3dhZzBTWnp2MHR5cmpyUDMwNnYxdFphdjNtaUtPcDYvN0pRQTRrL0dMd01OVXY4TzdnM0tBYTVOaEtES1E1a21xM2xJeXc2d09BdFd5TVpCYlhXa2RBOWgwaVNIdlVuOVRvNFdzK2xNZ3NuQWF6d1o5WDlhR2hHUHUwSDUzRnNkUVpwTFpNM0dKV0JCMHBtdTg4bXI2M0NTaklINEZPdTB6SWF2Nk5uc29kc1F3VEZFS3daeHVRc1hYMTFFZ20iLCJtYWMiOiI1YzAxYzMxMzBlNDVhYTg0NTNiNDU2ZDdmMTg5ZWQ3NWQ1N2E1MjUwYTdkZmZmYmY3Nzk0ZjU0NGY0MThhODk5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:38:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkxENDRSY3hhRVl2VE9hQURyTjlWSWc9PSIsInZhbHVlIjoiY0VmUGR1VFkrSzBjUDI3dGxCNWNHVG5iUVBLdWpPaFJxb0w1YTlURHMrSW13MUhMRWFyZ2JQUEpsd3N4RGRhYWY4SS9BYkdCZ2kxdUZtSVEzeFhERFg5UW1YWDIvWXFHVGJpRkowZ0hDbFRZcTNwQlNudksrSFQwK29HTXdrUzJ6M1NOU3M2alIxNWppbS9JWDRzckdHU2JScTVSQUdoMDhzT3A2YUpkVS9IbENiSW1RMWt6NDIvaHlGS1k4Q25qL2V1NENNeis2T2o4NkcramFUZFMwNmdmeW5zL3Q1ZVNyYkVvSy96SUVCTjBVYUhjVlI2SVFZNlQyNlA0Z3lRTE5QK3h5TXFEOW82RTJxTWZaUnlDRG1kUU0ybGY0MkxLV1NmbGk3NUQ0cnpyTXJyTkYvVW94TEwyZ2VZdEdwUDNUK2JlenhqT1pQaXQ5THhKWllPWnhnU2w3NEJBV1pwdGltMkltUkVkY1RxdGd2NnM1TDhZZWt4T1owanEzRm93UmdiTTFnTGh6bTVKNHk5Q3o4SkJJS0tLM3VEemR4MU5Hai93Qy9YYzBDMi9qcHlvaXBRL1hNVzBTZ0YwKzcvU0NmWWR5OHRkaWwzbjZYa2pLSVgrOVFpL0RxL29KUEUxdUVxdEpnNldNcFBiWWFPYkE1Z3ZBc1BqQnFiL0VFQ3IiLCJtYWMiOiI1NTkzNDFkZWE3OWM2ZTkyMDQzYmYzMjU5ZmI4ZTdjNjIzMTA5NDFlMjQxYWM4ZGQ3ZmQzODVlY2Y1ODNkMmUyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:38:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980156622\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1793105072 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TKoZNLCOD2VHhv6jQCo3jSGHwmFQfT0I3a8tswI8</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793105072\", {\"maxDepth\":0})</script>\n"}}