{"__meta": {"id": "Xa8953a2dcff63c791b439d2a010c952f", "datetime": "2025-07-31 06:28:46", "utime": **********.353899, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:28:46] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.329758, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943323.752546, "end": **********.354004, "duration": 2.6014578342437744, "duration_str": "2.6s", "measures": [{"label": "Booting", "start": 1753943323.752546, "relative_start": 0, "end": 1753943325.803682, "relative_end": 1753943325.803682, "duration": 2.051136016845703, "duration_str": "2.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753943325.803731, "relative_start": 2.051184892654419, "end": **********.354014, "relative_end": 1.0013580322265625e-05, "duration": 0.5502829551696777, "duration_str": "550ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50618600, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.270705, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.06648, "accumulated_duration_str": "66.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.039563, "duration": 0.01145, "duration_str": "11.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 17.223}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.12117, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 17.223, "width_percent": 4.106}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.148747, "duration": 0.04828, "duration_str": "48.28ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 21.33, "width_percent": 72.623}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.21471, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 93.953, "width_percent": 6.047}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1939401802 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1939401802\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1653863340 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1653863340\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-702229471 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702229471\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1316660353 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Inc4VWxqbERFRzlmalV5RzFvNy9oN2c9PSIsInZhbHVlIjoiL3E1TThQMGY1RkZrMGNINnQ0cndZTWdOWTR3OTFQK0lkNTl4dmU4Sk5wR2Y1VnJiMTVYSHkrSE1HVXpqRzJ3L0ROdTk2R0NIakx0cXZMMEdMWURKaStubGVlQW9tZW42YVlrVGlRMUV4TjhaUHB3am8wVTBmQW5IS3pzU2orMFZITEQ2c1VLbUpXUGFsb1N6bkdEa2h4R1VkZXRaVllrRTQyamNvY3d1Nndid0s5cDRDY3NDUGt4cmdobWkyTGpFQ0Z1WEVMWThhOUtvT2dCby9QYlY4K0FUVDE5eFdScStoRGhXZW9rV1VTTEgvcjRGVW00V0NpVWJDenhibXkrT0xzRGdZckdVL3pxTjY3T2NjbFV1dWplQ0wzTnRPNTE4M2o1NzdmVXpoRCtyVnljZVpaVit2SStaTDAyWWluNFJRTjRwWHQrRnNPaGRTYWJGZTVsbXJtT052aHg2NFJWWlVKMWo2U3RvNjAycjB2ZFVlN2hpSk9HTnVTb3U3QURSL0VGUmNYbGwyRGJoSkpyM2hmSDJTODFqTHVIVVVuaC9ZVGlhaXpIZ1N5UDczNDV1aGVSNHRHdHVXb1JGOFVtajY3SWVxNGl5QXFhZmRPaGJKOWZKVUUvOXZTOXJHbmZzUlh3alZFOUdZekxNUnBlL0g5L3J0alByTnhhcVJmMjYiLCJtYWMiOiIzZWJhZGI2OTc4ZmY0OGEwYWEwODdjNjg0MDg1NDg3MmJmZjg3MzE1ZjRkMmNkNjAyNmMyY2YyZTc1MjA1MTE1IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImtMT1Y5UnV5N2t1WU1hS2lYS1NWcXc9PSIsInZhbHVlIjoiNk12MXk4Rkd1VkorVjJuQ1NYZWFhT0dWQ051Q0Q5QmJEbmc3T2liQ2Rvc05KKzNvbUNQNDFPR3FtNWlMQUJ6aWxJNHNpWnlxbHB6ejkydWFLRTRtOXJnNGUvaGtxbFFpMDZNTUdrSHUzajExV2lTUGttM0p5emhqVzQ5Rk5JaVNQR2dGa3o3MFpwOHA0d0ZITWx6Rm8vRjNub3phVWVIQ1pUa2RGZytuVFdFL0kycnNocWtudG91K3Z6MVBSNW5zampaOGJpa1NUdXlXV2hnWXYvZVlPLzNuYkErc3M3Vm0vVVFUVnhKODVNeFN4b0hBYmpBNU5aRExFdnJiOEVtM2tzcWo0ODRXU05NYm13MzJmb3VZSVd6c09mV00rTXFzQS9VeE5DemhBbVBNYlJ0b2VCTGFKc3JwNlREQTRVT3JsekFrV0xtTWkzSGd1QVpvUW1UOEhxVjR5Y001ekZPMTNqYVpQRVVYbitrUEVETnZXbWExdENrRUlKc2FKS0g0cXJGMFVkUVZOSGxPYkNaSzQ5M2hpSEMwT0FodEZEUFRHRFFxdzVkRUJFWWxjeUpvb3pKU3FseWZLN2lsdE9qRzhNTTNkNUdvZnZNTTIwQXlOT1o4RTc5SlNNYVh5bm44R3hLWDRvRitIdkU0Q0R1V3JXQmlCeUhMY2FGUE1JRWMiLCJtYWMiOiI3NGU1ZTgwZmNlM2U3NmNlZjdiOTI1OWJjYWM0NWI0NjQ3MGQ1NGZkMjcyZDliYWQ3OWNiYmE1MGVlZWZhNzkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1316660353\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1759969199 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1759969199\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-919947093 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:28:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVXZUdzWWl4Vy9kaWprZE0zbjdQL0E9PSIsInZhbHVlIjoiVEhjc2x5eWJFNnZrY3V4VDhwNzZPVTF3Q2szdUpsN29NWE5YQ2N2SGlRcjE3RjdxcVByRngvZFY2bDZMVlZDdUF6MWJoRlV6OXhERVFxb1ZiZTM3cUxhWllRRk5lSDhnQ3FSWDRaYnZwcjBIbWxLUGJ2T0hmV3RzcVBzZEUrL21zQjV3UjlWRlI4VVRrS2hOUHZvayszaXhZaU5kKzlKdkowMGtpcDhHR3crcGR1QlRyb3lNeEROTk5IL3N5NnpxekFlREJlNXo1NmtRTmtoSGFpMDdvTEo3c0QwWWcxUTQ2YWtsOGJ1NERSTDNtVlVsMCtGQ2IrQWphZzZhMkU2dTRCQ2x4Z24vWVpZZmk3L0xNVmpQMXhCZGwwU3dhZzFYWEV4ZDVJUHF4QjdnRlV6Y2JOMGVRVWtSZW1WU1dwZ3J4TzM1TVo2YjR3MjFBWVJYL1g3WWZiYjdYUk43RVZ6dGZscWNhWjRRdjdRY3VGOFQvcmwrSHNteXhtcWF1cHJaK0h2VGorVXAzWEJQZW1mT3pHRzZFdWFLS2V5elNrQ3plOVlNNldSV2g4OW0xS2VhdnBnT2gxekpzWHc4MmY5dDJKTkZKUG5LN3dvSzR1S2F2VTZVeUN3emU0UzdjQ1U5cVJUZldJM3Mvc2FwMTdzZy9XWEtEZzBRWGlFNzRWU20iLCJtYWMiOiJjMzQ2ODViYjQ4OTUwZWRhZjQ0OWQ2ZjI2OWEzMGY2MTQ3YTk5NjY5MDhmYTkxYTYyMjIwMThmNTRiNjdiNDVmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:28:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlJzQlY1enVvU1orNS9SYWJWb3M4cWc9PSIsInZhbHVlIjoiNkpxeldZSGJGT1YvZzRBR1FkVHFpcnJRWGZMNHZiQXEvUnFXNFRzMmJlSjJEazQrZGdhdHYxckhreTJ5MGsrK2tsbnRua3o1Znc2cTNEU0xDR3hab0I2VGNLYThJOG8xai9zdmlNUTdkMy9Mb0RITmNyYXZiTDBTK3FxZVZYTWgxcTd2YzN3VE9GSHpxbmN5SXRNKzBvcmswR2lEeGY0bXd1V2xxZ3JBWjBLRHVtTUlsZVhZaXZnNjNDbHNwaTBjSXlTdDRWWWMvK3pBSkl4UCs4RW16amJVRzZOK0UzcXVyNkZNdWdCK2NSMnI1MCtpZ0R6ckdIZWhQTGJRT1kxQ3VQdm51VXVVU0QwRVh1U1NvQ3NJb1lvVUY0SEdoZXVwVHVjekJRMlZkSHd4c3FaUWxGa28ra2o3emIwY0pMZElSaDZtRHZodFpYb2lDN1k5K3VYcUhCK0pxY1lNbHJTWGtsZ1hSTW9zeUFCdzVvOGw1ckNyYmVya2hmUUZUMDhrSUtWcHlmZHJybVdOOUtVRU5MM1dISXFvQ0hldWJoQ2VNZ1FncHdSOTFuT1Rham5WY0VuWk1vMlpHQ281N2x1Ui81NTV6aWZqT3ZRYmpRRzcxOERWaFdubGl0cUNwUVRvSmdPSDg0WjJYWnFsMmpsb0JZRWhtbEo5eHFkTGdIK0ciLCJtYWMiOiJkODIzY2JjY2E3ZGEwYTZjNmY3ODQ3ZjI4M2JhYWNhMGYwYzBjODEzYjdhN2UwNzhhMGM5NTUwMWU5MDU1ZGNlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:28:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVXZUdzWWl4Vy9kaWprZE0zbjdQL0E9PSIsInZhbHVlIjoiVEhjc2x5eWJFNnZrY3V4VDhwNzZPVTF3Q2szdUpsN29NWE5YQ2N2SGlRcjE3RjdxcVByRngvZFY2bDZMVlZDdUF6MWJoRlV6OXhERVFxb1ZiZTM3cUxhWllRRk5lSDhnQ3FSWDRaYnZwcjBIbWxLUGJ2T0hmV3RzcVBzZEUrL21zQjV3UjlWRlI4VVRrS2hOUHZvayszaXhZaU5kKzlKdkowMGtpcDhHR3crcGR1QlRyb3lNeEROTk5IL3N5NnpxekFlREJlNXo1NmtRTmtoSGFpMDdvTEo3c0QwWWcxUTQ2YWtsOGJ1NERSTDNtVlVsMCtGQ2IrQWphZzZhMkU2dTRCQ2x4Z24vWVpZZmk3L0xNVmpQMXhCZGwwU3dhZzFYWEV4ZDVJUHF4QjdnRlV6Y2JOMGVRVWtSZW1WU1dwZ3J4TzM1TVo2YjR3MjFBWVJYL1g3WWZiYjdYUk43RVZ6dGZscWNhWjRRdjdRY3VGOFQvcmwrSHNteXhtcWF1cHJaK0h2VGorVXAzWEJQZW1mT3pHRzZFdWFLS2V5elNrQ3plOVlNNldSV2g4OW0xS2VhdnBnT2gxekpzWHc4MmY5dDJKTkZKUG5LN3dvSzR1S2F2VTZVeUN3emU0UzdjQ1U5cVJUZldJM3Mvc2FwMTdzZy9XWEtEZzBRWGlFNzRWU20iLCJtYWMiOiJjMzQ2ODViYjQ4OTUwZWRhZjQ0OWQ2ZjI2OWEzMGY2MTQ3YTk5NjY5MDhmYTkxYTYyMjIwMThmNTRiNjdiNDVmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:28:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlJzQlY1enVvU1orNS9SYWJWb3M4cWc9PSIsInZhbHVlIjoiNkpxeldZSGJGT1YvZzRBR1FkVHFpcnJRWGZMNHZiQXEvUnFXNFRzMmJlSjJEazQrZGdhdHYxckhreTJ5MGsrK2tsbnRua3o1Znc2cTNEU0xDR3hab0I2VGNLYThJOG8xai9zdmlNUTdkMy9Mb0RITmNyYXZiTDBTK3FxZVZYTWgxcTd2YzN3VE9GSHpxbmN5SXRNKzBvcmswR2lEeGY0bXd1V2xxZ3JBWjBLRHVtTUlsZVhZaXZnNjNDbHNwaTBjSXlTdDRWWWMvK3pBSkl4UCs4RW16amJVRzZOK0UzcXVyNkZNdWdCK2NSMnI1MCtpZ0R6ckdIZWhQTGJRT1kxQ3VQdm51VXVVU0QwRVh1U1NvQ3NJb1lvVUY0SEdoZXVwVHVjekJRMlZkSHd4c3FaUWxGa28ra2o3emIwY0pMZElSaDZtRHZodFpYb2lDN1k5K3VYcUhCK0pxY1lNbHJTWGtsZ1hSTW9zeUFCdzVvOGw1ckNyYmVya2hmUUZUMDhrSUtWcHlmZHJybVdOOUtVRU5MM1dISXFvQ0hldWJoQ2VNZ1FncHdSOTFuT1Rham5WY0VuWk1vMlpHQ281N2x1Ui81NTV6aWZqT3ZRYmpRRzcxOERWaFdubGl0cUNwUVRvSmdPSDg0WjJYWnFsMmpsb0JZRWhtbEo5eHFkTGdIK0ciLCJtYWMiOiJkODIzY2JjY2E3ZGEwYTZjNmY3ODQ3ZjI4M2JhYWNhMGYwYzBjODEzYjdhN2UwNzhhMGM5NTUwMWU5MDU1ZGNlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:28:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-919947093\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1379083323 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1379083323\", {\"maxDepth\":0})</script>\n"}}