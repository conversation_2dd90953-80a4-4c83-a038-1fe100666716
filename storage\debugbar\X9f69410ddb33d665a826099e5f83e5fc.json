{"__meta": {"id": "X9f69410ddb33d665a826099e5f83e5fc", "datetime": "2025-07-31 06:32:00", "utime": **********.975383, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:32:00] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.964154, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943518.462097, "end": **********.975478, "duration": 2.513381004333496, "duration_str": "2.51s", "measures": [{"label": "Booting", "start": 1753943518.462097, "relative_start": 0, "end": **********.653337, "relative_end": **********.653337, "duration": 2.191240072250366, "duration_str": "2.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.653482, "relative_start": 2.19138503074646, "end": **********.975485, "relative_end": 7.152557373046875e-06, "duration": 0.3220031261444092, "duration_str": "322ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45918384, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01008, "accumulated_duration_str": "10.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.83708, "duration": 0.00692, "duration_str": "6.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 68.651}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.8894, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 68.651, "width_percent": 11.607}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.900574, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 80.258, "width_percent": 8.433}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9125798, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 88.69, "width_percent": 11.31}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies/79/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2038039972 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2038039972\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1987655023 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1987655023\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2027205983 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2027205983\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/system-admin/companies/79/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im02RW9yRThpWVp6VlgzbmpNaXpOb2c9PSIsInZhbHVlIjoieHV5WUk4V1NKZWNLTkhvS0ZqdkhXQlNYMlRidGk1dTJ1eFZTZnpLdWZNTWtBamRHallJcTlUM3AxekN4dXA1blFpQzJWMjE2bmMyZXBYYVZxMkVEZk8ycklodTdCMGFKL0xSRU9PRm5kQWRpa3pXTXFoQVhiVXVaNW9Ed2dqdERrdDBiUjNZMUVBcisxNzVDbG14ckFEZHRiOUpXaE11K3ZURGNLMW1VU0U1OWRjeTFlYS9IaTVQOUxCSmsva0hWbHc0c1BQNTNLcnRiWlpvNTRTZUtmM2x5b3ZxRDNEdUk2Mk0vVWJ0SHp3NzNGbnZ5RWRHU1BtQnlmWkJnVkVzeGhYNlJCL3VrVFFUc3RIVWM4ZzJ4NEpRckkyMW1mRWtRRmlrVGEyRFQ5d0dWRWxzbDZXL3FiOUtJdXlWVXVTMGxGZ2JYYWl4Ylk1ZEs0b0xuekl1Q2d0eWl0KzNqSEZwb2owQUk1U1FaSVRJeGxYTXlMRHN1UUt4K1RVOGNSRnZhWEl1TXRLZVpzbUZvcDJIK0h2NzNmS3EvWW1DNDhuV1hVVnRxb2dHSnd0TGhmZzg1SmNuUFRBTGNQcklEWWRSem1YdjYxM0JEMGhObU1UTXB0Y1NBSEJDOTlheTR0Y0NnRzJRVTM0WjVYWjhDdXJWSnQzZi9ldHM5cURMWDhnUUYiLCJtYWMiOiI3ZGUxZjRlMDM4N2YzZTA5NTIyMGYyZWMwZjMzY2Y0YzgwMzE0ZjljYTc0MjMzYjY3YzNhMDNhNDFhNDFmMzg0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik1OMkFkdnU3a1F6MHhhb083WVdaNEE9PSIsInZhbHVlIjoiYzVrbWRGSCtBZkFLcFRMc3BQZXkrbmo1b0lUZ2xUMS8vb3V3N1FtNklvQTcyYUVYQnZodDErM2FrT1pFVHhIeG8wWHBLV000OGt3ck8zVDdpRHY5UWNQaFB1Q01HMER0cEVua2MvcHhNMU1SNWtmS29Mb24yUWxVdzV3ZTVUbWtvMXRzRmk0a3FXYlE0dm9OWFpGMFpqeHMwaFBFMlIxN3NFcVZTQUxmQUhORHQ4bWhORnFjSGxSamRPRXZCdEFuV1RadmRJMkE4RWZKaC9CUTdsbG1HMnVMd3YrRzZGRHhxWlJqbk9hQysyTVpZYTIxb3NkRWQ4T2lYYXdzajNyQ0x1RUVBU3NaWldOM2FVQVNhSTFmRklKRXh6eFhaSWpNZ1o3Tnk3dEZRVUlmYlh5T01nQ0pYaThBbzR2dE5jY0p1dGVsSVFGdmxRYXp4T1U2bFh1VW4xOWE3RkxzZmVORkV6RnpEakUyUXVKWkg4QjdBa3lHUW5lOExzZEs3Z1lYcUVFM3A5OEdBNjl2SkNYZ0IxemJwaFYxdWFBeXh5aHA1WUdsOUVlMXRqY2hKY3BWTEsraWg5Ym1LU0NVWHpheE96dC9HMGVIQVdTWWNGTUJGcDlrU2huS3NzbmZ5RkF4WW9IMlpENzN3aXVkcldpZllIZW9mTnBPdFE5UEFqMEQiLCJtYWMiOiJlYzllMTJiZjAzYjE1M2YwZDc2ZTA5OTZhYTliZWU1NDNhMzc2MmU1YzI2OWY0ZGVhY2FmNDJhYTY4NjIxMTI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-807227224 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-807227224\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1108370940 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:32:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imhsc0w2VGFaMEE1MzdlUENPdC9qMmc9PSIsInZhbHVlIjoiUVlxVkM1aHNJUzEyeDZhS05BRUpTUVlaMk1DMkFlaXlZOVBLMU0yKzgxQm80TytFak5OUzBadHVUc1BXclBlSlZSRENxN0JxUGYvRTZjZnlicG5ZNU5pcUIxaVhCbWNtZ2orSloyM3BsK2piVEpFYnpnV2RvUkRWK2JQODNuL3NTZFNnZWhRdW9hTU85emhTWjQ1bEJmbDRtQjBic05ZOVYzd3hJbUVBSUd0citDSFphV1lRZDQ1VmM5dVdFNVl1S01UYXk2dXpKdXN5dlNPclhXcnlwVGVXN3UzeDJhTXBob25ZTCtoSERMbDVPd29zMVVFeDNHOStSdFRqbzFnZ21paU8rU2VmSXVWenpTUlBsYkp0dG8yOWlmaVZKbXhHUFRocEZES0ltTDBoY1dwYVJycGh4M24wa3JTOWI5T0hkZ00wMXdSUnhQK1hGeTlwL0N2MHhvSmxuTVJzNElUak0ydHowdzZCT25vRWE0UVhBSk9XRFlLYnNENHY4TnJBSjdJSy93c3pIZW9jMmlDRjkyL0w2UUtTUEQyQWN4WEdzeEFiYkxvTmY5Q1VVZmJNUzVkalpXZUwrQjBwTnRzbzY1Qll2MTlJeGgwVXpSWDVROVdvVkJwNS9DQk9DL2JqYXNsYjl6QnVGT0p3UGdncFdFT3BzWTRQckJZdXZuemMiLCJtYWMiOiI5MDZiNmY4MDE2NTI5MmY2ZTIyMWQ4ZTMxOTgzZjhkN2ZhZmY0Y2FhOWRhMTg4ZDAyNDI0MzE4ZDU5MzUxYzg0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkpsYThMcWN3eERwTiswSlNKenpvN0E9PSIsInZhbHVlIjoiSjFnbC9BelZYZlIvSHhXV2JvNXNaMXRjZ1gyaWhWWCs4VjlZM0t4NkREQWRUa0taRGc5WncybzNuUS9XRUpaMWxrV2NKa29wQld1cHl5Qjd6TS9PeXhYUFBHV3NvZ0U0U0ZyMWxzMEhXb1V5WGMrMFJOcCtDdmtHeENzTGcxSjNVT2UzMTBxdHgzSlBwSmRHK1pKTFBuUWYvZG9GT04yWlRaamZkcGllRW84QUFwcHZXUC8zRVd0YXJkMWZVN2FyZ3ZkM3VvczcxSWVoV1pvK2x6aXhScml4a0UvaFNlcWFYRE1iOFhMRC9kem90N0s2RndKcU82R1NFNWcvWk8zMHJsdFhrakJmQzhqSUpRMS9KQXpEYTBJM0doUnA3YVM3M1dhdWJ5OVVTRWptWVFTWkhQY2RoUXFmamUxT1Zhck9pV0ZkcTNQL1h0WXNDekROeTZHZjdTRWRaRVFjdGVIZ0p2bjREVjNUd1RCWTJNMS9CMFZZcUNhT0ZXTklKbERuanB4U0VLMHJKRmcyblRxLzF2ODNPSkhYREJQV1JNWTdmcm1BM1JYSDAwVWI5aldtMFV5WEFxYnNmdjdvZGhXOGRvcUlVdVhSdUppM1NNbDhTdGdFbzlkWjZKRzU2OHZwOERCSDNqRVJiOS90NnZWV21rZ0tsaXR5Q2xKMEVhN1YiLCJtYWMiOiJmMWFiZjY2ZmM3ZjhkNTgwN2ZkN2FjZTNlM2QyNWVlZTNhNThlNjI3MDJkYThhZDZiZTZiMjRlOGNmYjYwYWU1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imhsc0w2VGFaMEE1MzdlUENPdC9qMmc9PSIsInZhbHVlIjoiUVlxVkM1aHNJUzEyeDZhS05BRUpTUVlaMk1DMkFlaXlZOVBLMU0yKzgxQm80TytFak5OUzBadHVUc1BXclBlSlZSRENxN0JxUGYvRTZjZnlicG5ZNU5pcUIxaVhCbWNtZ2orSloyM3BsK2piVEpFYnpnV2RvUkRWK2JQODNuL3NTZFNnZWhRdW9hTU85emhTWjQ1bEJmbDRtQjBic05ZOVYzd3hJbUVBSUd0citDSFphV1lRZDQ1VmM5dVdFNVl1S01UYXk2dXpKdXN5dlNPclhXcnlwVGVXN3UzeDJhTXBob25ZTCtoSERMbDVPd29zMVVFeDNHOStSdFRqbzFnZ21paU8rU2VmSXVWenpTUlBsYkp0dG8yOWlmaVZKbXhHUFRocEZES0ltTDBoY1dwYVJycGh4M24wa3JTOWI5T0hkZ00wMXdSUnhQK1hGeTlwL0N2MHhvSmxuTVJzNElUak0ydHowdzZCT25vRWE0UVhBSk9XRFlLYnNENHY4TnJBSjdJSy93c3pIZW9jMmlDRjkyL0w2UUtTUEQyQWN4WEdzeEFiYkxvTmY5Q1VVZmJNUzVkalpXZUwrQjBwTnRzbzY1Qll2MTlJeGgwVXpSWDVROVdvVkJwNS9DQk9DL2JqYXNsYjl6QnVGT0p3UGdncFdFT3BzWTRQckJZdXZuemMiLCJtYWMiOiI5MDZiNmY4MDE2NTI5MmY2ZTIyMWQ4ZTMxOTgzZjhkN2ZhZmY0Y2FhOWRhMTg4ZDAyNDI0MzE4ZDU5MzUxYzg0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkpsYThMcWN3eERwTiswSlNKenpvN0E9PSIsInZhbHVlIjoiSjFnbC9BelZYZlIvSHhXV2JvNXNaMXRjZ1gyaWhWWCs4VjlZM0t4NkREQWRUa0taRGc5WncybzNuUS9XRUpaMWxrV2NKa29wQld1cHl5Qjd6TS9PeXhYUFBHV3NvZ0U0U0ZyMWxzMEhXb1V5WGMrMFJOcCtDdmtHeENzTGcxSjNVT2UzMTBxdHgzSlBwSmRHK1pKTFBuUWYvZG9GT04yWlRaamZkcGllRW84QUFwcHZXUC8zRVd0YXJkMWZVN2FyZ3ZkM3VvczcxSWVoV1pvK2x6aXhScml4a0UvaFNlcWFYRE1iOFhMRC9kem90N0s2RndKcU82R1NFNWcvWk8zMHJsdFhrakJmQzhqSUpRMS9KQXpEYTBJM0doUnA3YVM3M1dhdWJ5OVVTRWptWVFTWkhQY2RoUXFmamUxT1Zhck9pV0ZkcTNQL1h0WXNDekROeTZHZjdTRWRaRVFjdGVIZ0p2bjREVjNUd1RCWTJNMS9CMFZZcUNhT0ZXTklKbERuanB4U0VLMHJKRmcyblRxLzF2ODNPSkhYREJQV1JNWTdmcm1BM1JYSDAwVWI5aldtMFV5WEFxYnNmdjdvZGhXOGRvcUlVdVhSdUppM1NNbDhTdGdFbzlkWjZKRzU2OHZwOERCSDNqRVJiOS90NnZWV21rZ0tsaXR5Q2xKMEVhN1YiLCJtYWMiOiJmMWFiZjY2ZmM3ZjhkNTgwN2ZkN2FjZTNlM2QyNWVlZTNhNThlNjI3MDJkYThhZDZiZTZiMjRlOGNmYjYwYWU1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1108370940\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2011735047 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/system-admin/companies/79/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2011735047\", {\"maxDepth\":0})</script>\n"}}