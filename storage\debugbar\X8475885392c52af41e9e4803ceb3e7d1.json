{"__meta": {"id": "X8475885392c52af41e9e4803ceb3e7d1", "datetime": "2025-07-31 06:31:05", "utime": **********.704987, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943463.859464, "end": **********.705045, "duration": 1.8455810546875, "duration_str": "1.85s", "measures": [{"label": "Booting", "start": 1753943463.859464, "relative_start": 0, "end": **********.546084, "relative_end": **********.546084, "duration": 1.6866199970245361, "duration_str": "1.69s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.546109, "relative_start": 1.****************, "end": **********.705051, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "159ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zEEDcHRW1y0BLjEnUePPoAQN9kHeaFED1VQ6Fn4z", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-791524160 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-791524160\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1531730082 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1531730082\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-328344285 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-328344285\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-271089924 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-271089924\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-408139618 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-408139618\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:31:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1pUWkwNytTb0FxVDBVcXhFZElSVkE9PSIsInZhbHVlIjoiK0VDOWlHYlpMVmlGZkZrb1VMSXgxWWtFTkZPdm5UNk5SamltRW0zczl1MFB5SjFPSnVBY0t1bGJVeFVxbktqK2JxZ1MwSVhack83OVpIWTM5K3o0aFNyQ1JPSUlEVkZTRzhIT0c3YWh3ODM4dUdQRnJiVFhwVFl2QUJOK29CcjFWRXlLR2NQM3FnZXFKSW1CcENkTlViMXFNcHA1SGNFVkI3c0hiTmFvVUtTaDVnMElpd0lOQUpwRER4cVVtY2psM3RyM2tlNklPaFhDTW5tWDZwL1Z5RnAwckRjR1RKc1lEcmV0UEZMTFlMdEFKb3hqbmlTNGdqRUNaUlJlM0Zia2Q0YUZKYjlyV3docldVY2l4VFVMOTQ4SVQzL05CS3RZS2tNZXM2R0dTVlhlRUVLUUh5WkRlOWI0RHRlUW93TTNTUDRSeTVPR0o5K013b3VLODBURnJaR2Z1WVdsaFRPZ1Nua09ZMmJzZnBZb1M0SW9vR2hVSjJPdXZ2amc2bmlZeURVaGMrU3lwenFlNXNEaXEvT2V5QmE3Yyt0eURjVWlaeHZUV01Ma09aZHlLSGRwVWEwOWxKYXVOaGdRZk9aRFVYdVVPM3JzUk94Q2pUWElxVFpRMXNSTW5OUTlYT2JscnE3TU1uVXpRVmNjcmNZRkt0SDhzSVRaczRXSlUyTUMiLCJtYWMiOiI5ZWFkOGI4YjBjNDg2ZWFhMzRmN2FkNjRlOGE3M2JkNmEzMzY4Mjc5OGFkNjU3NGRmNGI1MzlkMjA0NDk4NTQ3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InEyRnlyeGhuOHpPL0I4M1BEZ0VQMlE9PSIsInZhbHVlIjoiOW95YnNJZnB6UzRrWCtqKzFUWnZxbFVhZTF4Z1lIbS82cmFpdDdmQ1lFNUlyRXFMTUtqSE9MT2FZYTZWRTBIbWRORFJmZFNMRHRJejNHYXBqZ2Y2d1pHTDYvYTRrUHh5R1BMV2s1ditBWlJXVFhUUDI1VWxjcDBqemhmYjU3NW1KNGdWM1UzdnBKcmtDMjVjc0VqWE9oOTBHc3NHUFc0SE5UOU15b3FmMEpLK3BuUTVUUldHUmlkUHpza0luWjQxa0hRYzJnUlRkTWdMMU9yalZuZDg2ejBQTFpRWDJNMlBhU3ZLckdodXNTRWRiV3REWTd3N3B0L1l0WGtMVnVsUHhTVUowMHpOV2dYMHF4NjV1cXlrcmhtMEIyS1h4bm5OSkloVmVCVEhMWVF1amQ1V01JYzd3UGRLRVg2ditYVXNnVHVzMDZSY3ltKzdjM293dEF1ejdHQjhpT0c5cWlPTmxDMGIwVWQ5MEtkUE01bWR4N2oyRm1kSWY0NWJUTVN0V1NHOEhBOGQ5TEJxaFRUSGk1TW1LZUc2MU0vSzZYR1V4cWN0d2wzLzRZUE45b2kxTXBVZmlmWmJWUDNpcFN4MDRKcUdsNm12cStjdTY2M1JQSlhKR3ErZlhiWkNVaTZJOUFhTDFPUDVjVWVMd3VVSGlNaEQxYzVYOUp4dkpHbDEiLCJtYWMiOiJlNTJmOTRjOTYxNzg5NDliZjgxN2FlMWRiNTcyOTgyYTAxYzQ1NGI1ZTQ2Mzk4MDY3ZTkyZGY0YjIyMmUxMWViIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1pUWkwNytTb0FxVDBVcXhFZElSVkE9PSIsInZhbHVlIjoiK0VDOWlHYlpMVmlGZkZrb1VMSXgxWWtFTkZPdm5UNk5SamltRW0zczl1MFB5SjFPSnVBY0t1bGJVeFVxbktqK2JxZ1MwSVhack83OVpIWTM5K3o0aFNyQ1JPSUlEVkZTRzhIT0c3YWh3ODM4dUdQRnJiVFhwVFl2QUJOK29CcjFWRXlLR2NQM3FnZXFKSW1CcENkTlViMXFNcHA1SGNFVkI3c0hiTmFvVUtTaDVnMElpd0lOQUpwRER4cVVtY2psM3RyM2tlNklPaFhDTW5tWDZwL1Z5RnAwckRjR1RKc1lEcmV0UEZMTFlMdEFKb3hqbmlTNGdqRUNaUlJlM0Zia2Q0YUZKYjlyV3docldVY2l4VFVMOTQ4SVQzL05CS3RZS2tNZXM2R0dTVlhlRUVLUUh5WkRlOWI0RHRlUW93TTNTUDRSeTVPR0o5K013b3VLODBURnJaR2Z1WVdsaFRPZ1Nua09ZMmJzZnBZb1M0SW9vR2hVSjJPdXZ2amc2bmlZeURVaGMrU3lwenFlNXNEaXEvT2V5QmE3Yyt0eURjVWlaeHZUV01Ma09aZHlLSGRwVWEwOWxKYXVOaGdRZk9aRFVYdVVPM3JzUk94Q2pUWElxVFpRMXNSTW5OUTlYT2JscnE3TU1uVXpRVmNjcmNZRkt0SDhzSVRaczRXSlUyTUMiLCJtYWMiOiI5ZWFkOGI4YjBjNDg2ZWFhMzRmN2FkNjRlOGE3M2JkNmEzMzY4Mjc5OGFkNjU3NGRmNGI1MzlkMjA0NDk4NTQ3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InEyRnlyeGhuOHpPL0I4M1BEZ0VQMlE9PSIsInZhbHVlIjoiOW95YnNJZnB6UzRrWCtqKzFUWnZxbFVhZTF4Z1lIbS82cmFpdDdmQ1lFNUlyRXFMTUtqSE9MT2FZYTZWRTBIbWRORFJmZFNMRHRJejNHYXBqZ2Y2d1pHTDYvYTRrUHh5R1BMV2s1ditBWlJXVFhUUDI1VWxjcDBqemhmYjU3NW1KNGdWM1UzdnBKcmtDMjVjc0VqWE9oOTBHc3NHUFc0SE5UOU15b3FmMEpLK3BuUTVUUldHUmlkUHpza0luWjQxa0hRYzJnUlRkTWdMMU9yalZuZDg2ejBQTFpRWDJNMlBhU3ZLckdodXNTRWRiV3REWTd3N3B0L1l0WGtMVnVsUHhTVUowMHpOV2dYMHF4NjV1cXlrcmhtMEIyS1h4bm5OSkloVmVCVEhMWVF1amQ1V01JYzd3UGRLRVg2ditYVXNnVHVzMDZSY3ltKzdjM293dEF1ejdHQjhpT0c5cWlPTmxDMGIwVWQ5MEtkUE01bWR4N2oyRm1kSWY0NWJUTVN0V1NHOEhBOGQ5TEJxaFRUSGk1TW1LZUc2MU0vSzZYR1V4cWN0d2wzLzRZUE45b2kxTXBVZmlmWmJWUDNpcFN4MDRKcUdsNm12cStjdTY2M1JQSlhKR3ErZlhiWkNVaTZJOUFhTDFPUDVjVWVMd3VVSGlNaEQxYzVYOUp4dkpHbDEiLCJtYWMiOiJlNTJmOTRjOTYxNzg5NDliZjgxN2FlMWRiNTcyOTgyYTAxYzQ1NGI1ZTQ2Mzk4MDY3ZTkyZGY0YjIyMmUxMWViIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zEEDcHRW1y0BLjEnUePPoAQN9kHeaFED1VQ6Fn4z</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}