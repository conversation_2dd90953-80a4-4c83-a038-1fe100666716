{"__meta": {"id": "X22a36f49973cc286e7a4e09bae5c330d", "datetime": "2025-07-31 06:29:15", "utime": **********.928117, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:29:15] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.915038, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943353.61385, "end": **********.928167, "duration": 2.314316987991333, "duration_str": "2.31s", "measures": [{"label": "Booting", "start": 1753943353.61385, "relative_start": 0, "end": **********.700365, "relative_end": **********.700365, "duration": 2.086514949798584, "duration_str": "2.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.700404, "relative_start": 2.0865538120269775, "end": **********.928172, "relative_end": 5.0067901611328125e-06, "duration": 0.2277681827545166, "duration_str": "228ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50618648, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.882406, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03154, "accumulated_duration_str": "31.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.794383, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 10.875}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.815008, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 10.875, "width_percent": 2.41}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.825302, "duration": 0.02529, "duration_str": "25.29ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 13.285, "width_percent": 80.184}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.856872, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 93.469, "width_percent": 6.531}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-406135627 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-406135627\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1354658213 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1354658213\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1871469488 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1871469488\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-708689449 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Img1amVmdEVpMEo1YTNNUy8xRUxEdmc9PSIsInZhbHVlIjoiUGI4ckVHQzZnOHdKNkVDVnFMSExZNnp5eDl0d0l2M2VsN28zUGNBNVRYUktZUkIwcXZRMmNEWTVqTlE2MkhZLzFWb1A3cFowS2JuRDlTTk14dFQyYktjRzlnYk13Q25wek43T205RGdSRE54T1JjU2huZ09DSmV1eG9yLzlENVJoSWc1Q0Z2K3FtRWl4RDRaT0pZR1gyVDdxcmZIbXNRRjBvMFFuZDkvaHlJLytZY3MvTDNvYWh3a2ZGKzYwTkZ6d1o3cnNDSS8vRUhsT3BKUWJkUXZQa1Bzc3VGRERZbkR3S2YwZHRkRWhENkJ3M0Ruem5qZ1lKQURrRG5FRlZlaWp3VkdOWEV1SjVIenR5YjRISFFOVjhkVUV2MHduaEhrUGswRjgwYUNKalVVcyszRDZBQ290Y2F1R01JUnVZQkE3UURKbFlzeVhrbUd5UDdVdlNUZ3NPNUVYazZPMmhhS3hTV2JmTmFBOUhBaFhldEkrc2tZWGdHd2tVMm9RMEgwcUlJUnBRTHNtWEI4bDBnYUdIVzFMNmk0bnpHL255Yzg0V2hDZ29mdHFpOXZmMkJITFRnWlJ6VmpMVkpPWkhVQzlScXpoelpEeFR4SEZhanhOZkljTy8yYlE3NGp6UWw4UVBSUSsxRndpUUtYK2p3U0xXWkpkd1cvTENPdmYyNSsiLCJtYWMiOiIzYjllMDhjOTM2ZGRhMjBmNWFmYWU1NjdlNzM2NDJlNzNlYTQ0OGZmZDkxZmUwZDk3YTdiMTZkNjFmODA2N2Y2IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Imtpbnl3eTdDZ0xTd2JXUmVFbjU2MGc9PSIsInZhbHVlIjoiM2VEV2s1TGZrVXVvYTFYZkdKU3BqVFAyR29tMmUrWVZ1bDVMeFlVSFJMcEFKRFZpVWRqYTIrWFlYTTI2bnNSZTJkanVUY29NTzI4eW5pTzN0OHZ2SlVrYVl6WTgrZlBkWFBmOElvUUhLTWpXbkloS0w5ZFFHdW83UHJ1MitBbGJocDdnZnpUZ3JEZXBoQ2VnTUVkcStZWnltd2R5UHZORkN1SFR0eFhPSFZmdk02Y0tJdzRCdHhXM2MxZUpOanZpUjNzOWl0SWdvbGpIUENCc1hGSWZuWHBDVUJ3eTRjMllScnlPRXBLc2o1a25BZklRWUZhWFB2RldlWWdvK3IzSVliRHp5bWZqZFk3Y1owN0dlZGZTU0hsc0l6TmpsL3Y1MjgvQWJCdVk1dlE3Y2ttVzhVNjBESFN5Ym9FeXV1bXNaOVppWDdIeTF1Z0RCcEFWNGhTZ2thdUFOcmZiSko2ZjByVmx5Z2NkdkJ2cTUrZFVlczFPcUo2Z1lFSFhpTVQ5OVd5MDI1QkM4dnFDRnhZYitscm1hSTFBcm9OcUNXZFZsM205NE45Vk1xM2Y2NmhQTk5UUStmMk5WK3RTdkJDYkhvMkJvblRDTStBYUNYYnpqZDNuQWN1dVdiaFgwMTEwenpIY2dOd2RtS3BESGhRdDVDMVV4M3Y0dkNadFFoQXYiLCJtYWMiOiJkODEyNWE4YWMxNTVhNGNhNWU2OTYwZTM4MTVhNjUxY2Q0M2UyMzM0Njk1YWViZmQ2MWVjNTQyNTM3MGQwZWM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708689449\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2143970815 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:29:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFMVzVwVkVSNUcwYmk4K1hORUk4ZHc9PSIsInZhbHVlIjoiemJUbnVId2tWZWk5SUpQQVRkWU9JTmVvbXVXa2xPRGRpOXpOYk4wc0NYSzM0OXFtYlJYZjl6VWMzRmJ2eGUxNjdXSU9MMk9nWWE1bDZMQXF5ZGFSSmkvekpaUmRUNXRlSk1rREpDdi9ZOEtGYm5pbTdqVnhiZXZwRUFYYnhOZUdnbjM1VzdBR1dkNnZTeFlrTzFIeVhRbG91b3BCN0NXdHQ1QUNrbVJURHRuajMzbktlYyt5aWVxMitvbnlyejRjQjNMdEdrSjRraGR1OGF6WGxqbVFtK0YzM3VOSGFkTmQrbkZQVzZtUks3cTFYcmlzSGJNL1RHc1JIdEF1WHJ6bWxHZTV2eDEwbzBzV2wxLzVPY01KMVJJK0lsNUJpd3AzaEczZ1ZyMDl5SGk2ZGQzVzRpeEs2K2I1cTNKdlM3bXBhcnEwdWxZb09leVlVY1IzODM0cGk4SEpuS3dlay9ZNFA4VitGaGEzcm9xa2w3d3lWb2NKK0VSWHJPamZmOUdINGgrSzg2ODl3T1UwSUtCSHRRQ01qRmhtbm05Zm9naU1mRUd6YXpaQnJhWjJMQ1hRQk9EZ1VOS3JYb1Rzdk94cTZxZit3d2tJN250d0JSdEhIZW9uOXJHQUtxS3E4NlQ4NmpmVnpKOWtaWkF3VVlPcEo3Um9pQVh3Z29RekdqdkYiLCJtYWMiOiI3ZGRkZjNlMGEzN2E0ZDYxMjgxMTQ5MzkxNzkyYTk0OWJhYzFjM2M2YzIwMmQ1OGZjZWEyZmE5YzJkMWI2NWEyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkFpaG5tbVFDYXcvOGZoaDJZYkxxRFE9PSIsInZhbHVlIjoiZzVpeEVnU3NSaElCS0p0RzVqMTBmQmNOY0RUOW1DaXdjUHR5cFRuN3pMMFNzNlpMajFBbk45bThzeVBVNFd2MStDN3NudFk2RnI5NDFJVFhVTG9WUUJlMTQwMWRrbEc3Q2ZscFVQUnNsMklNUnorcWRoOTk0b3dNdG14UVRkMytTcjM4a0dqQTFsSUFSTU0raE9oek1kbjR3enY4b1p1TitYRG55aEFnVFRSWnlPQ0ZaNUgwRjVmdHZJYy94dWtDN1FqY0pheFk3elhCSGk5UHJNb3RTaDVwclN4OTlxSG0xRG43YVFWdmlZU0hlenIwRUk0dzFlb0R5dWt2UVdleko1NFhSVGhrenR5QjhmMzVkL3NHLzNmQVQ4R2YweitEYTZ5N3ZYbzRBY0lVMUtpRnF5OW9YMXVGcUFoVXUxdHdPdmU2bDVKdXNScHpjMlVHV0ZCWmhXQVlEQVlFL25GY1JEbFdVR3ZuUEpFN09zcThKMUx3ZXVaOGJub0xBTktTUjJpUE9CdTV1WCtQYWJjOUtzWVk4MktvWmJlTTk2d2Q1YWVBYlVONHRVazA5eVVRa2dNMGZHQUVlK2JOTmNXdEhQYlE1eUFvL1JMaCt6R2k2dU1YRkZMejJsTEtVRlhvSzJ1eGJEMXduaHlxR0hYeklqQzlZUnJBbGRpVEdnNGwiLCJtYWMiOiI2ZDU3OTc3MzM1OGU5ZmVjYjY1MmEwMjg5MmUyMDdmYTdjNDMxZmUxMGQyNTM3MjUyZmNkZDc5ZTdlYTE4Y2FhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFMVzVwVkVSNUcwYmk4K1hORUk4ZHc9PSIsInZhbHVlIjoiemJUbnVId2tWZWk5SUpQQVRkWU9JTmVvbXVXa2xPRGRpOXpOYk4wc0NYSzM0OXFtYlJYZjl6VWMzRmJ2eGUxNjdXSU9MMk9nWWE1bDZMQXF5ZGFSSmkvekpaUmRUNXRlSk1rREpDdi9ZOEtGYm5pbTdqVnhiZXZwRUFYYnhOZUdnbjM1VzdBR1dkNnZTeFlrTzFIeVhRbG91b3BCN0NXdHQ1QUNrbVJURHRuajMzbktlYyt5aWVxMitvbnlyejRjQjNMdEdrSjRraGR1OGF6WGxqbVFtK0YzM3VOSGFkTmQrbkZQVzZtUks3cTFYcmlzSGJNL1RHc1JIdEF1WHJ6bWxHZTV2eDEwbzBzV2wxLzVPY01KMVJJK0lsNUJpd3AzaEczZ1ZyMDl5SGk2ZGQzVzRpeEs2K2I1cTNKdlM3bXBhcnEwdWxZb09leVlVY1IzODM0cGk4SEpuS3dlay9ZNFA4VitGaGEzcm9xa2w3d3lWb2NKK0VSWHJPamZmOUdINGgrSzg2ODl3T1UwSUtCSHRRQ01qRmhtbm05Zm9naU1mRUd6YXpaQnJhWjJMQ1hRQk9EZ1VOS3JYb1Rzdk94cTZxZit3d2tJN250d0JSdEhIZW9uOXJHQUtxS3E4NlQ4NmpmVnpKOWtaWkF3VVlPcEo3Um9pQVh3Z29RekdqdkYiLCJtYWMiOiI3ZGRkZjNlMGEzN2E0ZDYxMjgxMTQ5MzkxNzkyYTk0OWJhYzFjM2M2YzIwMmQ1OGZjZWEyZmE5YzJkMWI2NWEyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkFpaG5tbVFDYXcvOGZoaDJZYkxxRFE9PSIsInZhbHVlIjoiZzVpeEVnU3NSaElCS0p0RzVqMTBmQmNOY0RUOW1DaXdjUHR5cFRuN3pMMFNzNlpMajFBbk45bThzeVBVNFd2MStDN3NudFk2RnI5NDFJVFhVTG9WUUJlMTQwMWRrbEc3Q2ZscFVQUnNsMklNUnorcWRoOTk0b3dNdG14UVRkMytTcjM4a0dqQTFsSUFSTU0raE9oek1kbjR3enY4b1p1TitYRG55aEFnVFRSWnlPQ0ZaNUgwRjVmdHZJYy94dWtDN1FqY0pheFk3elhCSGk5UHJNb3RTaDVwclN4OTlxSG0xRG43YVFWdmlZU0hlenIwRUk0dzFlb0R5dWt2UVdleko1NFhSVGhrenR5QjhmMzVkL3NHLzNmQVQ4R2YweitEYTZ5N3ZYbzRBY0lVMUtpRnF5OW9YMXVGcUFoVXUxdHdPdmU2bDVKdXNScHpjMlVHV0ZCWmhXQVlEQVlFL25GY1JEbFdVR3ZuUEpFN09zcThKMUx3ZXVaOGJub0xBTktTUjJpUE9CdTV1WCtQYWJjOUtzWVk4MktvWmJlTTk2d2Q1YWVBYlVONHRVazA5eVVRa2dNMGZHQUVlK2JOTmNXdEhQYlE1eUFvL1JMaCt6R2k2dU1YRkZMejJsTEtVRlhvSzJ1eGJEMXduaHlxR0hYeklqQzlZUnJBbGRpVEdnNGwiLCJtYWMiOiI2ZDU3OTc3MzM1OGU5ZmVjYjY1MmEwMjg5MmUyMDdmYTdjNDMxZmUxMGQyNTM3MjUyZmNkZDc5ZTdlYTE4Y2FhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2143970815\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-493324018 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-493324018\", {\"maxDepth\":0})</script>\n"}}