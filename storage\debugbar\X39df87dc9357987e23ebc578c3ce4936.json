{"__meta": {"id": "X39df87dc9357987e23ebc578c3ce4936", "datetime": "2025-07-31 06:16:26", "utime": **********.297606, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:16:26] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.288375, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753942584.194376, "end": **********.297663, "duration": 2.1032869815826416, "duration_str": "2.1s", "measures": [{"label": "Booting", "start": 1753942584.194376, "relative_start": 0, "end": 1753942585.912555, "relative_end": 1753942585.912555, "duration": 1.7181789875030518, "duration_str": "1.72s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753942585.912595, "relative_start": 1.7182190418243408, "end": **********.297669, "relative_end": 5.9604644775390625e-06, "duration": 0.3850739002227783, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45942864, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03099, "accumulated_duration_str": "30.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.109793, "duration": 0.02571, "duration_str": "25.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 82.962}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.189636, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 82.962, "width_percent": 6.292}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.210642, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 89.255, "width_percent": 5.55}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.229387, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 94.805, "width_percent": 5.195}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-690873786 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-690873786\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-305510396 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-305510396\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1602366510 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602366510\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1466543009 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im9xT1BBOElDL0pZcTJvZ2ZBOWhoaFE9PSIsInZhbHVlIjoiS0JtS25XNFF3UlQyVFNoVUVrd2xjbDd1cmU5ajVpaVY1THM4ekJ5UVU2eGlnQkp3OGdvSVNhaGhjbnFGZG9qVFlSVklGTTB1b1BnelVZYkRtV2ZsTm1sRDFGOU45QTh2OUtIcWRmeUNYb0Y5aSsxS3ROT3diMkpITHByT3N0UUFBbUtZZGlTTURKMTFaWktrRU9zN2pBUFM1UVJFeS9DRDYxUUpFaENZMGg3TFZFK3pQbFNwUk9MTnpZNlFjMm1Yc0ppZnIremZpR3Q1Vkk3VWJUMTgrQkltOVh1SGhUZGx3cVRlWHVQbkJoNGRlVmpKd1U4Q0N6SFNrR0NrRmtqOTdCaG9ybW9jQXBhN1lPSkg5VXJMY2ErcXE3S283RktZdFJPTUluZ3ZCTW85Y0hvQ3ZaZFVqZ0ducDJYQ1hDc2crVE9ZZzhvRjdpWXNuS2xmck45UUNDOFBaTzM1ZjlTTU92eHNmbzZjM2hLOU8zTTIvRURmVktpMkVjeVBKSnZEQ0VLdnZSRVNnRmJXQmRubzRJL3A3RTY0dDU4ZHRYajNwV0MrSW9sYWhUcHR1UnBrM1dXNTFnL3F5dVNCTThCNzBZeGhDeFRkV3R2Rm14WWxialNkMWU2YllSR1o3dWJDVi8wUHpHamdqL1ZoMm05TlNJV3RqMW5Tajc2R1RWSGgiLCJtYWMiOiIzMDRlNTVhMDZhMWEzZGQ2Y2M2MGJkYzRhMjQwMzFkNmM2ODY4ZDk1NjBiNGM3MjQ4YzEwMmM2ZDMyNWRmODNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InBDMGpIUVJaRWdmNENCTlliMk40anc9PSIsInZhbHVlIjoiS05OelVDaDV2ZU96c3FwdVFpalFmSUwxeXhwbng2OU9zNjQ3b0JIbEVkQXJ4ZUZLM3phRVNrdkhwNE1NNFlSUVlFdXpDL2hHaHVSVzNFWENmMG9iMnF3djZrNlE2NVpGM1JEUWNUZmdFMDk0ZmpDV3p0NkdEdG9FRm43MzVJZHA3YVhocjhLY1drNG84ZFhic1BTdEN2aGdhYjY1YVc2OGdUT3ZkWjVyRE5ZM3dKNk9uZkhJNWRubXAzYlgwd1dXZW9tcFVIQjR5eENrbGhXd0JOSERHc04raXVJR09LbnRucUN2elNIVUI1VzJRcGw0SzZjSEFsUUxtcXlUT0YzQjJSRnhFQTM2aVQ0N01ubTBiMTlrdlplOFhSSDczSCs2TDYwTjVkMFJ0NTFKT1V6MnlMZWtZMVRSNUlNQno2RDZPeEhFS2VqMFNmSUdWRExzeDlFOTM1cDRxSmc5WHdkTnkrbmF5UTc2OCtIT2tHYStTc1RQbmR5a1grdXVLY25zd3k1YUFwQU1ua2J5eDNIN3hFczJjR1VPVTlvN2t3clZDcWxyRnM0SXcrcjBqRCsyRytNVlk0QzFSWUloUEZoUTJkT1k1bGJZN1l5dHJnMkNyTWkwWUlmeGZsMVlIa21ZYVpOeVNpdnMwbXRGeFZQbUs1L3UxZUp5dnVnVlBEU3AiLCJtYWMiOiIwMjljMTM0ZDFkNmJhNWRlZjEyYzI5OTUxZDU1NDAwNWZjMzc5NDkyNTFlYmQ5M2Y5ZWZiNmY2N2VmZTlhMzI0IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkVKVVlQMWRZL25GclJVeEs4NUVISXc9PSIsInZhbHVlIjoibUlMNkV2T1RIaHNUa1h6MTlua3FkS3FkWm5sRFIzZ3JwOWY5dDIvVDh0c3JJNHk5dHJlSzNWUGVQZjVBSk5OdWhSMGdXMU03c29IczFacTVncjRaZWFvTmdkdXBDeEh6aGVveFRQSkNya0NVZndQTldkNkttSFI3V1pRSDkwSEc0ZGRVRUtoaWtDODgzM2xrc0ptQnZValcvZEdzUTNHRDlGZFEzempJcDBQTVN5MVVXY3RXOTlSK0FGMjVYNmlhdWhVZDE0Ly9qNTFUbHRsclFEekRqbkNMTWJrb0tvL0VsUjBUNnM1Z29DWTZnSXArTnNNK0YyZExBaURDZzdRZUVyUjg2K1JGVGo0SkczaVVsSUJTUlB4NzFybWRTS25aeFZWRVdaRkovNld1QUk0MCs3bHk4WFVudVlGZ0hxc3hmMWZoVFFQQjdQSGl2YXZEbzc2MUtHcEdXT29STnpWckVUVlR5RkdNT2NMNXlYakhDNytsRnRtY0FlNTJwVUc5RWpXWEhSVGRPaTFKcHhZYklUdHQyOXlWcGN6SnQ4TGtpMVM3dnBqdFZQc1lRNDJ2K3FndFNiMTNheld0L0szdFFhdHJGSkRlSjNVdTdlK3dWN1p6Y2l1NFFrajIwQ1lObTdBMG04RDRlSjZ4ZTE4Wk44WVBKMTF5Q3FNSXVBdysiLCJtYWMiOiIxMTc4NzkyZDg1ZGNhOGU3Nzg4YzNiOTUxOTJhNmUyYTkwMzA4MmJjNDNiZjU0NzU3ZGE2YTVhMDdmY2VkYTk1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1466543009\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1402421336 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5LJND5bUclZnLzzClCMEaxKzZ5Yt8AAmGjprz0I</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sUbnPperdA1I3PaRxBpMGV4YsR5AVGq0ERh2LBdJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1402421336\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-186612178 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:16:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtXcTVtNndVME8zbkFhZFNSU3Rwbmc9PSIsInZhbHVlIjoieGVLejhvMnlIYmRkSE9QbjlWU21GZWEwZVBOcCtKMnVNbG9JTHRLL2RLV3VYcWJuTDNsa2ZZY3ZYM2hMckVkT3pKcWJDMFRPQzFyYnZiM0FxWWh1MjRXQ3d4WlZrLzB2eitwOTdTQ0VIV1h0S3h0U0drbHNvZERrYWowMUg4Qlo4OGFEV2N4Q0t2bzRPZ01FOE5CdHN0U0hXZGlld0NLek1PUjQvelM4dDJsaFRFTVpWOWUzWnVRZzBXSXc4RjdhR1VuNGJ6RUdaVWkydytLK3hZSzdMdlRZcmxST3Vnb0hBYkMwQjFnUXQ5a1d5MU94aG9FWmZiQzYwNFowWHpvRXd3Z3c0aTVBSkN3VXFiVkNHZVBrWXI1SkZuRHJINk5vK3BCTzFsdXJLcEtMSzJ5eFl5NFpZbThlYUtiem1EMFQrbjNiNzJ1Q3NBanZOZ29ERVlxNGU4anBScWNkQnRVb21QUmoxeEVrWXVQWUcyYUZtYmVmMW9TR2hRT0F5N0oyZWpDZUp1OGhpUUxOZEhoV1VDazBJUmVOdThQNlRKTzRWbUxBb05qclRyejU0aXlMTllrNGVQemJRam54RDVBakNoaS9RR3BObThzbHI0eDRrYU5EMXR1NHdXUlgvb0p4aTVmYmYrK3RXN1NNbUJXdWJUVGV3VHhQNy9GUkRVc1MiLCJtYWMiOiJkYzVjNzA3YzZlNjg2MzZiZTAyY2ZkMzA3Zjk1ZWJkY2M4MGViN2RlNWRkOGU0ZjY2MThhMGQwNDEzNDIwMTE0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:16:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IlByZWhsa3Y1U0FUZVdFc0FIKzRVYWc9PSIsInZhbHVlIjoidXJPM3BTcWpiNXZQT0M4TldUSUlSN2x6NCtuUjlTRnI2a2QvRmxzSjg1RG1xM1RnUC9IcUEyVXVOYndsV2Z2NnQwY1NrOGxHQjlYa203b0RJVTY5eTRTM2V2bFpiT1FlTm95OVlwdWlFTkFiM0l6eEVHMTdnenNoMWZYOEpwNjZBendPMGkzU2xDbGdSMEk3b25tVktKSDhVYk5VZFVSMzFhVGUvVnJvbC9xdXRUMWhWNW1mT3pzeDlRUS9wS1B3UEtVbWpmSTQzc1hDR2JGbmJGK1NPRCtRZVZYQUdzRnEvcWZyQldPQkhLSldsSXZoKy9WbTU5bDhzT215dGtSemVhZDV0QmZubkIxUW9BNXFoc1BlaEFWamhwOHUwU1hRTlEvM2dyMTlESnlKaGtrazRYUnNlME94cTdVUWVPd3BxRjFWU2NXdmxkZ3BUQkVDOU5Dby9RVGdNbk0wZUpaR2NqMGxGdXhjaTlpK3kvSkNDNUxDRVRuZ3VoRjQ5TWYxbmVLRENaNjA1dXVveEpaR1JKVzFvTUtFaUo0TU5YU2MxZ0VhWkovRjhaMUYwTE9SMHZ6Z1I3blFrczhKVGtHRnIxcXpKcEZPcGc1MXpaWjRnSUlwUTJXTk5mSjlBYWhkRDloOVAveEtiazFVY3hIRjRuN0FNNGhNam9DR3NaeVAiLCJtYWMiOiI3YmNjYjk3Nzg2ZDFlZjVjOWU4ZGM4YzYyMGJjMWM0YWIxYjExYmRlNmIxYTRkMzI0NzMyYWY4Nzc4YmI5OGUwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:16:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtXcTVtNndVME8zbkFhZFNSU3Rwbmc9PSIsInZhbHVlIjoieGVLejhvMnlIYmRkSE9QbjlWU21GZWEwZVBOcCtKMnVNbG9JTHRLL2RLV3VYcWJuTDNsa2ZZY3ZYM2hMckVkT3pKcWJDMFRPQzFyYnZiM0FxWWh1MjRXQ3d4WlZrLzB2eitwOTdTQ0VIV1h0S3h0U0drbHNvZERrYWowMUg4Qlo4OGFEV2N4Q0t2bzRPZ01FOE5CdHN0U0hXZGlld0NLek1PUjQvelM4dDJsaFRFTVpWOWUzWnVRZzBXSXc4RjdhR1VuNGJ6RUdaVWkydytLK3hZSzdMdlRZcmxST3Vnb0hBYkMwQjFnUXQ5a1d5MU94aG9FWmZiQzYwNFowWHpvRXd3Z3c0aTVBSkN3VXFiVkNHZVBrWXI1SkZuRHJINk5vK3BCTzFsdXJLcEtMSzJ5eFl5NFpZbThlYUtiem1EMFQrbjNiNzJ1Q3NBanZOZ29ERVlxNGU4anBScWNkQnRVb21QUmoxeEVrWXVQWUcyYUZtYmVmMW9TR2hRT0F5N0oyZWpDZUp1OGhpUUxOZEhoV1VDazBJUmVOdThQNlRKTzRWbUxBb05qclRyejU0aXlMTllrNGVQemJRam54RDVBakNoaS9RR3BObThzbHI0eDRrYU5EMXR1NHdXUlgvb0p4aTVmYmYrK3RXN1NNbUJXdWJUVGV3VHhQNy9GUkRVc1MiLCJtYWMiOiJkYzVjNzA3YzZlNjg2MzZiZTAyY2ZkMzA3Zjk1ZWJkY2M4MGViN2RlNWRkOGU0ZjY2MThhMGQwNDEzNDIwMTE0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:16:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IlByZWhsa3Y1U0FUZVdFc0FIKzRVYWc9PSIsInZhbHVlIjoidXJPM3BTcWpiNXZQT0M4TldUSUlSN2x6NCtuUjlTRnI2a2QvRmxzSjg1RG1xM1RnUC9IcUEyVXVOYndsV2Z2NnQwY1NrOGxHQjlYa203b0RJVTY5eTRTM2V2bFpiT1FlTm95OVlwdWlFTkFiM0l6eEVHMTdnenNoMWZYOEpwNjZBendPMGkzU2xDbGdSMEk3b25tVktKSDhVYk5VZFVSMzFhVGUvVnJvbC9xdXRUMWhWNW1mT3pzeDlRUS9wS1B3UEtVbWpmSTQzc1hDR2JGbmJGK1NPRCtRZVZYQUdzRnEvcWZyQldPQkhLSldsSXZoKy9WbTU5bDhzT215dGtSemVhZDV0QmZubkIxUW9BNXFoc1BlaEFWamhwOHUwU1hRTlEvM2dyMTlESnlKaGtrazRYUnNlME94cTdVUWVPd3BxRjFWU2NXdmxkZ3BUQkVDOU5Dby9RVGdNbk0wZUpaR2NqMGxGdXhjaTlpK3kvSkNDNUxDRVRuZ3VoRjQ5TWYxbmVLRENaNjA1dXVveEpaR1JKVzFvTUtFaUo0TU5YU2MxZ0VhWkovRjhaMUYwTE9SMHZ6Z1I3blFrczhKVGtHRnIxcXpKcEZPcGc1MXpaWjRnSUlwUTJXTk5mSjlBYWhkRDloOVAveEtiazFVY3hIRjRuN0FNNGhNam9DR3NaeVAiLCJtYWMiOiI3YmNjYjk3Nzg2ZDFlZjVjOWU4ZGM4YzYyMGJjMWM0YWIxYjExYmRlNmIxYTRkMzI0NzMyYWY4Nzc4YmI5OGUwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:16:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-186612178\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1807194519 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1807194519\", {\"maxDepth\":0})</script>\n"}}