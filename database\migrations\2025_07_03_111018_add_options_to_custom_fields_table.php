<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('custom_fields', function (Blueprint $table) {
            // Check if the column exists before trying to modify it
            if (!Schema::hasColumn('custom_fields', 'options')) {
                // Add the column if it doesn't exist
                $table->json('options')->nullable();
            } else {
                // Modify the existing column
                $table->json('options')->nullable()->change();
            }
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('custom_fields', function (Blueprint $table) {
            $table->dropColumn('options');
        });
    }
};
