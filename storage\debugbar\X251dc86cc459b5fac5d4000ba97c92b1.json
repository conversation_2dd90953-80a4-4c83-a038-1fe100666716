{"__meta": {"id": "X251dc86cc459b5fac5d4000ba97c92b1", "datetime": "2025-07-31 05:56:18", "utime": **********.392899, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753941377.504475, "end": **********.392927, "duration": 0.8884518146514893, "duration_str": "888ms", "measures": [{"label": "Booting", "start": 1753941377.504475, "relative_start": 0, "end": **********.312391, "relative_end": **********.312391, "duration": 0.8079159259796143, "duration_str": "808ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.312414, "relative_start": 0.***************, "end": **********.39293, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "80.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YbkmGqCM863gz4gYqGjMu1MQf4Qt5pg5Suawyntd", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1572048670 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1572048670\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-644014562 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-644014562\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1964815536 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1964815536\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-574659947 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574659947\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-974019212 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-974019212\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1318291963 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:56:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVUODB5SVpVbUVlTjJ0WGxtOE15Wmc9PSIsInZhbHVlIjoiRXZjNWUydk4wNUw1ZTMxNkt5d1RmWHdPWE0wNDB5UlBEMkl1MXgrQ3FST2UraEtKU0dOTk5nSTJtRWxRdmlvYzJucGhIWmpWV1BpRTNUbDFVMmd0bDBBZHlwUDhZOHRCQlpSV0dMS3N4d3RFYzVRaWlYOFpQcjYzanJwQ25GMGUwNTFpS1VhbFMxZWRtQ2ZmNkxOaStONytJcG11SDkvUGk1aGtvQmFyUGZyc3psbGdIaWp1MEh1UG52MDRwTndiUUIzVWZoSlVOS2drejhsR1g1ZkhxL1BxNzZUM3JocUVsc08rekxXcHpZYW00aWlHRi9xOHNYVlVuQXBkQVh2UEkxWVlDZ0lRMG94ckRjZkZLeEo2d3BKcllsQXVvbjJHS2pSVGlCa3NHQzdsdTMrbTdiWGpEdllqKzcyMG9VNitZcS9NVThFNWVrckZPaVkrYXpNck5LS0tGZG03WHBpTFdTNlcvN1F4T1JKU29OMHVSSkd5OEpicitFOVBlemFsTDR5RUprcEZ0S2o1NTdMZXdLTDZJTlZHSTRjSzBXdmpTR3ZjQVEwa0pUU01wcG1UekJPVTdVS1hjKzJHVjBZOE9DUWEycnpTTGNldTVGZVRQemg1bXhLb1JnN2VOTzBjV05wTFFzZmdqM1BWSlpiSnJJWUdHNDB5WmZCWkNsNEwiLCJtYWMiOiI2MzcyZGEzNTlmYzg3NGYwM2MyNTdlMjNiMWI4M2M2YzNlNWRhZDY2OGE4YmJhYjU3ZWM4MTY1OTU5N2UxN2I3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:56:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkVuSEpBVFFoeGNLaW9QMzR2SHNEQlE9PSIsInZhbHVlIjoiR1dsVnFYN3lPSmlCQ1pzOWgzMGZTQ3plL2xrL1RCZEZCK21XSmFWM2VZZkQzL2NxN3VZK0FFSnVsZUxZMTFqdlFYNUJCWisrME5USVVkNlJ3OGpMNEUxa0cvRzZrOEpLYlJaeE9PTFRwTVRkZk5XOEJ5OWd3RTg3YS90TDNMRmZNRUdkVTdoUEJkZTJ4cHdvZXEwQVpkZXdjTXQxUzVsSXZKb0grQUJwdDJ5RWVRcHdSYXJUUHpaVEtYdGs4MnFsZVhxcXZydkJ3K1dqSWdMbkZJQmlsUEJWcnRZRzE2RnpMWXppYzlOS3F3bW5sS3dYQ0RlSklnYVBXdFY5Q0VhbW8xMXFZZWI3bWNhZk5QZjdsaVVNK1NWMVJ0NWhQemNqQk1VRStDYlVJOTlLNWFpU09idzZGS2NSUVJYN09vQVdNWUUwTkNRRmd6cFVobjNQTDFWci80QzdKUVV4VHVlM1UwMWRrWmMvNXczZHdkaG9QTU1MR3kvYVUrUW5EMkMxNVhRQ3hxajRJenVYZkpETmo3amd3bFpnQnZLdXJld1V2eGYrWVVHVC9oLy83UG5lL3FSZ2xHMkgyZGxTVi82OW0wS1VuZC9nSjRjTEJDWEVIYWw2aVRCZGMzdzBpRXNzelVUYzZNT0dLYlhLV2RtQW10NWdTa3RPbEF5QllVS1UiLCJtYWMiOiJiMmEzZTFhN2NiMmM5YTAwZjdkODE0NWY0NmJlYzBlODY5NzFhOWMyOTdmZDNhODQxZjllNTdkMTRiNTk5Y2ZkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:56:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVUODB5SVpVbUVlTjJ0WGxtOE15Wmc9PSIsInZhbHVlIjoiRXZjNWUydk4wNUw1ZTMxNkt5d1RmWHdPWE0wNDB5UlBEMkl1MXgrQ3FST2UraEtKU0dOTk5nSTJtRWxRdmlvYzJucGhIWmpWV1BpRTNUbDFVMmd0bDBBZHlwUDhZOHRCQlpSV0dMS3N4d3RFYzVRaWlYOFpQcjYzanJwQ25GMGUwNTFpS1VhbFMxZWRtQ2ZmNkxOaStONytJcG11SDkvUGk1aGtvQmFyUGZyc3psbGdIaWp1MEh1UG52MDRwTndiUUIzVWZoSlVOS2drejhsR1g1ZkhxL1BxNzZUM3JocUVsc08rekxXcHpZYW00aWlHRi9xOHNYVlVuQXBkQVh2UEkxWVlDZ0lRMG94ckRjZkZLeEo2d3BKcllsQXVvbjJHS2pSVGlCa3NHQzdsdTMrbTdiWGpEdllqKzcyMG9VNitZcS9NVThFNWVrckZPaVkrYXpNck5LS0tGZG03WHBpTFdTNlcvN1F4T1JKU29OMHVSSkd5OEpicitFOVBlemFsTDR5RUprcEZ0S2o1NTdMZXdLTDZJTlZHSTRjSzBXdmpTR3ZjQVEwa0pUU01wcG1UekJPVTdVS1hjKzJHVjBZOE9DUWEycnpTTGNldTVGZVRQemg1bXhLb1JnN2VOTzBjV05wTFFzZmdqM1BWSlpiSnJJWUdHNDB5WmZCWkNsNEwiLCJtYWMiOiI2MzcyZGEzNTlmYzg3NGYwM2MyNTdlMjNiMWI4M2M2YzNlNWRhZDY2OGE4YmJhYjU3ZWM4MTY1OTU5N2UxN2I3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:56:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkVuSEpBVFFoeGNLaW9QMzR2SHNEQlE9PSIsInZhbHVlIjoiR1dsVnFYN3lPSmlCQ1pzOWgzMGZTQ3plL2xrL1RCZEZCK21XSmFWM2VZZkQzL2NxN3VZK0FFSnVsZUxZMTFqdlFYNUJCWisrME5USVVkNlJ3OGpMNEUxa0cvRzZrOEpLYlJaeE9PTFRwTVRkZk5XOEJ5OWd3RTg3YS90TDNMRmZNRUdkVTdoUEJkZTJ4cHdvZXEwQVpkZXdjTXQxUzVsSXZKb0grQUJwdDJ5RWVRcHdSYXJUUHpaVEtYdGs4MnFsZVhxcXZydkJ3K1dqSWdMbkZJQmlsUEJWcnRZRzE2RnpMWXppYzlOS3F3bW5sS3dYQ0RlSklnYVBXdFY5Q0VhbW8xMXFZZWI3bWNhZk5QZjdsaVVNK1NWMVJ0NWhQemNqQk1VRStDYlVJOTlLNWFpU09idzZGS2NSUVJYN09vQVdNWUUwTkNRRmd6cFVobjNQTDFWci80QzdKUVV4VHVlM1UwMWRrWmMvNXczZHdkaG9QTU1MR3kvYVUrUW5EMkMxNVhRQ3hxajRJenVYZkpETmo3amd3bFpnQnZLdXJld1V2eGYrWVVHVC9oLy83UG5lL3FSZ2xHMkgyZGxTVi82OW0wS1VuZC9nSjRjTEJDWEVIYWw2aVRCZGMzdzBpRXNzelVUYzZNT0dLYlhLV2RtQW10NWdTa3RPbEF5QllVS1UiLCJtYWMiOiJiMmEzZTFhN2NiMmM5YTAwZjdkODE0NWY0NmJlYzBlODY5NzFhOWMyOTdmZDNhODQxZjllNTdkMTRiNTk5Y2ZkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:56:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1318291963\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1864311919 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YbkmGqCM863gz4gYqGjMu1MQf4Qt5pg5Suawyntd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864311919\", {\"maxDepth\":0})</script>\n"}}