{"__meta": {"id": "Xb954415ee4ef11a2260462fe2aa599a1", "datetime": "2025-07-31 06:29:55", "utime": **********.386623, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:29:55] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.366998, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943393.149502, "end": **********.386713, "duration": 2.237210988998413, "duration_str": "2.24s", "measures": [{"label": "Booting", "start": 1753943393.149502, "relative_start": 0, "end": **********.026475, "relative_end": **********.026475, "duration": 1.8769729137420654, "duration_str": "1.88s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.026517, "relative_start": 1.8770148754119873, "end": **********.386722, "relative_end": 9.059906005859375e-06, "duration": 0.36020517349243164, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50618648, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.319714, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.05213000000000001, "accumulated_duration_str": "52.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1451058, "duration": 0.005860000000000001, "duration_str": "5.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 11.241}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.196438, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 11.241, "width_percent": 3.261}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.218248, "duration": 0.04218, "duration_str": "42.18ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 14.502, "width_percent": 80.913}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2762432, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 95.415, "width_percent": 4.585}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies/79/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-463585260 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-463585260\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2044609225 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2044609225\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1949953426 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1949953426\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-207454246 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/system-admin/companies/79/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlFGbHJOMUxLendlWHJFRVJ2b3R2dkE9PSIsInZhbHVlIjoiV2E0VG1WMG8zVDhoNkhmeE9VM1MyWXk0bmdaWTRIMldyM1V5ZHJucjNZbVVHbjJJbkk1d3BnSS9HK2hoc3pXQWErcU04OEw2N3N4bmh5Z2syTnFuNUs5SXJ6dEVVenZkOWhnR0xSak1kdmE0YzR4em1HOVpROERGNUdNdlRldkkrYVVmdThKeUVEL1F3VC9BdGsvK01TZnFCR2J1c200UDBpVWQ4TVR1WGIzaTVNSG8xRGV3ZThjM0pSVjBpTkF2am1MOXNadG16emlLVExvZlowNktEQ1g1Y1l5cVY4UDZNYkcxUlRiU1JVeWRQUklaV0VmWURWQlUrT1FSR3k4VG5MVzVaNUtyRG96cVRyZGJpWUYxWHRXclRPU0xmTmhjK2IwN3NXU1VaekdnR0ZVU281S1lTWTQrajN4L1l5TlUvZHJJbCt6WmZKazBNbTBrNUZnL1NLT0xDWWgwekdpUE5HSDBMa3IxWjV6anlGZTNYWnRWSDJlWkE5VVA3Y3kyR3FDUitsN0hxczRTOEFEQXVwZnpDMUo0MHlzVktNQ1hXZkpWRDBxS1lKM3BodVBiWmhqakZLTnZPMVVTY21GZzhkT3pldGhwM0pZelF4OTBqTUJ3UzNMMVVtVU1HMnFoQTFZR3RmSlBkcjlMWVR4cXlZWkgrbHdhMndteG5lTHgiLCJtYWMiOiI3YmI2NmQyMjEzZTliM2UwODNhMGZiZmMyMDIxOGQ0OWYzOTI2YTI0ZTg3NGJkOTZkZDc2NmU5YWRlZjY2ODEyIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkR5R0ZyVzE3RHJ4UzFOblBud0xNVVE9PSIsInZhbHVlIjoiTG1KR3FTcXRJdWpzTmp5L0VHV0dDN1JBM0g0V1ltNHNyM29SeGFIb1R0ekl1OXBuRU9kRG51a3BjVWtFaytLZ0dOeElYQjNTMW5FL2VoQkQxVjFhOUVjK0JVUnhPNFJkSGhZQWNTcGg2TzU1cGRzeXVPeUJyQmp5bGhWSFhKSzhFajd6SjdyZy9NYXMxV1RVRlc0UXl2Z2hqZTlMU2VKakhVdFg5QUV3a2toMlh5VnNGdThiQmtINm40bC9iR2Qydm8vOW44Wmc3R3B6a2xRMXdkVUM4Yk1WSGRQMlZLZjY1MDFVYTZPaThFTHlaTURPOUl3b3FzN3R2bjhJWVo2UUFrMHY0bXZpNWNCVlc5NDBtbTltd1FWcVRqbUhuMkV0Ri9oWXhaa2g0M3l6NWVuRWNySC9JUjVEdENDK3ZGbVFoeTF2d21YZ20yU3o0amUyUTlJeUxSTnJsYTRGYU1UZDNmTG1tMG44MjI2Q2daTVJiMUdFTmwwWUVTakZJeG0yZFJSbVBubkpHNm5zM3hYeFFnMkgrdjhnNWRlUTJLNThOVkNQeWFpeVFTRTE2WlZxT1Y5UDFyU2VzbHl3bWZsdTZueWpaZjJWTVlzWVJGR3RZNk5UaU1mb3ppZkR6ZW1NNURVTkZsWTJaVkw3TGxIRXM5ZDJjdXFYQklQWm1wSG4iLCJtYWMiOiI1NmRmZmEwZjJhMWIwZGY1NDY3OTM4OGFhMmUzNzVmYjYyN2U3ZTU2MmJjYjBlNTc1NzY2ZGY0OGZkNmZjM2IxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-207454246\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-244910420 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-244910420\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1673905365 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:29:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndFQjZraEN5V2hTNnBIcmpybGhqQUE9PSIsInZhbHVlIjoieGxJM2lLcFUwWHNKUjdrb1dEaHZOemRNMkJrU2R4UFBBU2UzbnBqVWNZUkJJVkhFUU10a215cmo4ZFhnMTg5NGlsK05ydWU2M3RsZGhuWFM3bmpQNVV0UVErRW1ydUtTZXFTaGQwOUpqSGx5eGpiWkF2V0kyaXlzYTRPYmo1SmhrUllMcVgvbVd3UVpMWm01T0RkWld3blFDRlJsbGJIMC8zRFk1QlJzYlczU2pSMHcwVEVyZFhQUUFwSFZ6NDFzd3RyQUdsdFQrOEtiRC93Tm05ZCtKUzlYZnoyTjV5QnNmVVJDTm1kbmZhdE0zWTczSFlrak1ZQUxNWjFkdk15dHVOVEgxbEJTWDJJOHVZeEZYOUsyNDV4OW5hbklOcVV5MlU1YUxlblcvcWZ6VFpiZndrYzRHMlJIRDhDK1UxcDRubjJVZGJBOGM0NUI1ejJ3UzVzaVp3SkYxS1d1NXFucXF0SEVqSXZ0VVp4OW8vUGV4S0x0eE93V0dKUmNvRjAySTlWMHFFQTlvUVNqZlZJc1h2MmkzUlNQU2VLTWxtb3U1ejl4MzBUNG9vNXFSbVFSRGhFcmVzdzVDaTdvTms3R0NTRUd5YXhRUXU3VzhieGlISWRrOGtQeUgydG40S0ZDSUEzZEZaQ1ZXZFVzamR1Z3FQaUpKL2ZHQ2hTWjd5RysiLCJtYWMiOiI3YzNhZDViZjE2YThkNDlmNGRhMWJlZWJkOTAzZmIyMjYyYzhkNDQ0Zjg3N2UxZDAxMjIyZjQ2NTM4ZDA5Mzg4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjA3WTZ0c3RCdkI4b2wwMWpySzRuOGc9PSIsInZhbHVlIjoiL3dhaEhFQzQ4QVRZNWNVMzQxNlBXV0VrSDF2eVpwbmZITjBMb2tLZUJDR3NXamV1WWszRncxaEg3ZHhENE03dVZEUUVGWDlEdmRUMmRaU3BNb3NzTlR2VTFieHRMeGRaUEp3U2JoZGRkOUU0aW0xWXAyYTl5d0Q0MlVzcjZkM1FBclBBTG40RVlBaTNJRHR0eWsxV2xrbzdQdHpEWGtSb1c0em1ZeUoybWV5c2RRV3hnSXdTcXhoYjR2T0xDSUtHS2hLK2k3M0c3aEd0ZFBaQkpHUXMxWW9mMVkrTno4RnpEQnhwSnhBVFo1a3hBV0JHaUEwenFGK1MvYktKanBiNHFxZ3oxNjdxeE9kZnBGdWFmaWJ4Z3BlL0Nzb3BqN0Q0SklTNnZGZS9tNklWTkNNbmtFUjZBRjJUc3F4QWlvVllaQ2ljSVJJSmJ3Y09adDVCOGZPRk42M0pWNlN6NngyUDRkRkdVN2YyOGZRTDNmSkl3UlpISGJlVnNzVzJ6YmxvQVc2eUJXWUMxS2lBSU5Jc1piTVQwT0ptZ3orYzhMa0pNQmMwR0Y0S1kwTkZnODNqNHFkM2VxYzFpRURDVEVlMlFIUXQvL3FVenRuWTdyMm15Tm1qb1Vqbm1Uc2R4ZG8weG5lYk4xSnVRemtqY3UzOTRlYkJ3TmE2bE1NNU1vc3IiLCJtYWMiOiJlNmI3MWZmNjA3MWQzMTZhOTkwNjkzN2UxNTZlMTNmOGJiMGUxMDQ5M2VjNWZhOTE4ZDRhNzcyYzExMmViZjk5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndFQjZraEN5V2hTNnBIcmpybGhqQUE9PSIsInZhbHVlIjoieGxJM2lLcFUwWHNKUjdrb1dEaHZOemRNMkJrU2R4UFBBU2UzbnBqVWNZUkJJVkhFUU10a215cmo4ZFhnMTg5NGlsK05ydWU2M3RsZGhuWFM3bmpQNVV0UVErRW1ydUtTZXFTaGQwOUpqSGx5eGpiWkF2V0kyaXlzYTRPYmo1SmhrUllMcVgvbVd3UVpMWm01T0RkWld3blFDRlJsbGJIMC8zRFk1QlJzYlczU2pSMHcwVEVyZFhQUUFwSFZ6NDFzd3RyQUdsdFQrOEtiRC93Tm05ZCtKUzlYZnoyTjV5QnNmVVJDTm1kbmZhdE0zWTczSFlrak1ZQUxNWjFkdk15dHVOVEgxbEJTWDJJOHVZeEZYOUsyNDV4OW5hbklOcVV5MlU1YUxlblcvcWZ6VFpiZndrYzRHMlJIRDhDK1UxcDRubjJVZGJBOGM0NUI1ejJ3UzVzaVp3SkYxS1d1NXFucXF0SEVqSXZ0VVp4OW8vUGV4S0x0eE93V0dKUmNvRjAySTlWMHFFQTlvUVNqZlZJc1h2MmkzUlNQU2VLTWxtb3U1ejl4MzBUNG9vNXFSbVFSRGhFcmVzdzVDaTdvTms3R0NTRUd5YXhRUXU3VzhieGlISWRrOGtQeUgydG40S0ZDSUEzZEZaQ1ZXZFVzamR1Z3FQaUpKL2ZHQ2hTWjd5RysiLCJtYWMiOiI3YzNhZDViZjE2YThkNDlmNGRhMWJlZWJkOTAzZmIyMjYyYzhkNDQ0Zjg3N2UxZDAxMjIyZjQ2NTM4ZDA5Mzg4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjA3WTZ0c3RCdkI4b2wwMWpySzRuOGc9PSIsInZhbHVlIjoiL3dhaEhFQzQ4QVRZNWNVMzQxNlBXV0VrSDF2eVpwbmZITjBMb2tLZUJDR3NXamV1WWszRncxaEg3ZHhENE03dVZEUUVGWDlEdmRUMmRaU3BNb3NzTlR2VTFieHRMeGRaUEp3U2JoZGRkOUU0aW0xWXAyYTl5d0Q0MlVzcjZkM1FBclBBTG40RVlBaTNJRHR0eWsxV2xrbzdQdHpEWGtSb1c0em1ZeUoybWV5c2RRV3hnSXdTcXhoYjR2T0xDSUtHS2hLK2k3M0c3aEd0ZFBaQkpHUXMxWW9mMVkrTno4RnpEQnhwSnhBVFo1a3hBV0JHaUEwenFGK1MvYktKanBiNHFxZ3oxNjdxeE9kZnBGdWFmaWJ4Z3BlL0Nzb3BqN0Q0SklTNnZGZS9tNklWTkNNbmtFUjZBRjJUc3F4QWlvVllaQ2ljSVJJSmJ3Y09adDVCOGZPRk42M0pWNlN6NngyUDRkRkdVN2YyOGZRTDNmSkl3UlpISGJlVnNzVzJ6YmxvQVc2eUJXWUMxS2lBSU5Jc1piTVQwT0ptZ3orYzhMa0pNQmMwR0Y0S1kwTkZnODNqNHFkM2VxYzFpRURDVEVlMlFIUXQvL3FVenRuWTdyMm15Tm1qb1Vqbm1Uc2R4ZG8weG5lYk4xSnVRemtqY3UzOTRlYkJ3TmE2bE1NNU1vc3IiLCJtYWMiOiJlNmI3MWZmNjA3MWQzMTZhOTkwNjkzN2UxNTZlMTNmOGJiMGUxMDQ5M2VjNWZhOTE4ZDRhNzcyYzExMmViZjk5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1673905365\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1342749822 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/system-admin/companies/79/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342749822\", {\"maxDepth\":0})</script>\n"}}