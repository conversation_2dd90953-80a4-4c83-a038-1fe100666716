{"__meta": {"id": "X699fa7c215d6c8f731817204f2657723", "datetime": "2025-07-31 05:13:20", "utime": **********.536497, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753938799.395261, "end": **********.536525, "duration": 1.1412639617919922, "duration_str": "1.14s", "measures": [{"label": "Booting", "start": 1753938799.395261, "relative_start": 0, "end": **********.443414, "relative_end": **********.443414, "duration": 1.0481529235839844, "duration_str": "1.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.443456, "relative_start": 1.****************, "end": **********.536528, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "93.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xn3zjigFIgj3eEPFY4OTyzuHLBnhz03bLgzR4ZUQ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-928763826 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-928763826\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1520587220 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1520587220\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1181583310 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1181583310\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1200171549 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1200171549\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-908133054 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-908133054\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:13:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9WNHBVWXhDaTMzY0htZFVPaUh5QWc9PSIsInZhbHVlIjoiMkVUYm1XNUFuNVRscFNMTnN5YVphb1htcFVhbU9PTkxFSEhYT1dyUzN2eVQvVk1pczRoWGlDK2dJemRZa2gzenEzVjgydHVJeExRVHhta3NFWmlYZkVwUGpyYTRGd3VIbzIvWnNlZlhONklxMUlxaUF0emJwRzVZNTNyYTdlNmxhTjFlVjlDa2tpS1o1RWYwMVI5blJrNDdtZzRQY3pRZjVUcnFKR1VWZ081dnNYZEhFUGxGL202SUFjeGl5anY0Tmw0Q2haN1FIYnBTVmVIV1NJKzlrSkhxTDdZY3BSbGlOSHV4SUNIT0twcmtudVpjRW1xUkdxT1FBTTNzcWdwbXd3dXZiRnZKNS9zeTFJNHhIL1VRYy9YcVFWay82ZktKUVFSb1R3eXZBNDdyR1Z3SVdudmNCcHA5N2tmN201NTloRWxSU2dXcWxiZHdhSkF4ektmMStzZlNnUldYYTBhMDZNNnV6MU1NYjVDbWFoRmhiKy9zaU96aitGa20yYVc5T2FLekk4ZkdXQXNpUTYyNFlsRURPdjQvRnNRdktySVRxQnRaTk1aOThPTWRJZmpUd1pja2dWZEg0OWlmVjZ6akhtMjgrU1lrWWE3cVM0R3NIWWZBd29iYjRXTTJhdzM4a0U0QWk2aVE4Rjdoc1huM3c2UzFmSVdGaWxzYy9lVk8iLCJtYWMiOiI4OWQ2MDM5NjZlY2FiOTVhYmNhNmE0YjVjNzU5ZmIzMzdiYWFkZDAzN2NlOWJkOWNlY2MxNDJmYWRmYmU2ZThlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:13:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlUzS3k1MmNKUkx2SEJydzYwTGFXREE9PSIsInZhbHVlIjoiZU1nNXlTdWRHRmU1ZjhBUkl5RFVFZFN4TkZPUVJIU1V0SGN5WURtd3hPaWx5bkczYzhMaVBTbEhVZFFQZm9GempYc3dHY0wvMngwcjRhZXVXZS93YVIxNUZYVGVaRWtJTEtlQ2p5S0lteWJjc3htRFBsbXJIc29DeXpjcWh1MUFzUFBtT1l1aHl3N0gxamRhOFJWZFhPTTZaRXh4d1VxV0tuS3pCdGtCL2lKWlB2YVpaMWdYWllzU2tiL2lRNG9oblRMVEFEdHh5Q0NLNWNzTmtOR2hHekZybFdCQTlaa0VwWkRWdWFQUnNOTWd1Y2NMeFlwTlFVZFlHTFJpaFUyUjBzN1l2U0RCdkRqQmVrLzVrc2RpdjVURVBpejdBTGhkTFNma3NYRUJiUzZaMHlaWTdueS9EWDEyZ1g0NHUxYkhaNzFYN1NOd0tvaDRlWFBQMTR0MXl1WFpDTFZ0dW8ybGFVNDVpUmI2NzB1T1JhNGg2Tzd3SG9HWXJpUU1iaXluL3ZJQThCYmFqTU0xOENRZGl5UnlPR0ljSElNU3NONUhOaUh2N1ZhaXkwZTRVL010YjN0STY1bnU5VnlwcWVteVlRMFRpM3hkRkJxZzVHY0RVUmF6cWtCbjFyVUxjZTdsNy9ueDJES1c3M2lkamxkeU1xbGg3ZzkxRlRTeldBVk8iLCJtYWMiOiI5MzQ5MTYxNWZiNTgwMjVkMzNmODhhZTE3MTAwNmQ3NTlmNjgzYjFjNmNhY2JjMWViMmZjYzMwZDZhZTcyOGYyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:13:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9WNHBVWXhDaTMzY0htZFVPaUh5QWc9PSIsInZhbHVlIjoiMkVUYm1XNUFuNVRscFNMTnN5YVphb1htcFVhbU9PTkxFSEhYT1dyUzN2eVQvVk1pczRoWGlDK2dJemRZa2gzenEzVjgydHVJeExRVHhta3NFWmlYZkVwUGpyYTRGd3VIbzIvWnNlZlhONklxMUlxaUF0emJwRzVZNTNyYTdlNmxhTjFlVjlDa2tpS1o1RWYwMVI5blJrNDdtZzRQY3pRZjVUcnFKR1VWZ081dnNYZEhFUGxGL202SUFjeGl5anY0Tmw0Q2haN1FIYnBTVmVIV1NJKzlrSkhxTDdZY3BSbGlOSHV4SUNIT0twcmtudVpjRW1xUkdxT1FBTTNzcWdwbXd3dXZiRnZKNS9zeTFJNHhIL1VRYy9YcVFWay82ZktKUVFSb1R3eXZBNDdyR1Z3SVdudmNCcHA5N2tmN201NTloRWxSU2dXcWxiZHdhSkF4ektmMStzZlNnUldYYTBhMDZNNnV6MU1NYjVDbWFoRmhiKy9zaU96aitGa20yYVc5T2FLekk4ZkdXQXNpUTYyNFlsRURPdjQvRnNRdktySVRxQnRaTk1aOThPTWRJZmpUd1pja2dWZEg0OWlmVjZ6akhtMjgrU1lrWWE3cVM0R3NIWWZBd29iYjRXTTJhdzM4a0U0QWk2aVE4Rjdoc1huM3c2UzFmSVdGaWxzYy9lVk8iLCJtYWMiOiI4OWQ2MDM5NjZlY2FiOTVhYmNhNmE0YjVjNzU5ZmIzMzdiYWFkZDAzN2NlOWJkOWNlY2MxNDJmYWRmYmU2ZThlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:13:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlUzS3k1MmNKUkx2SEJydzYwTGFXREE9PSIsInZhbHVlIjoiZU1nNXlTdWRHRmU1ZjhBUkl5RFVFZFN4TkZPUVJIU1V0SGN5WURtd3hPaWx5bkczYzhMaVBTbEhVZFFQZm9GempYc3dHY0wvMngwcjRhZXVXZS93YVIxNUZYVGVaRWtJTEtlQ2p5S0lteWJjc3htRFBsbXJIc29DeXpjcWh1MUFzUFBtT1l1aHl3N0gxamRhOFJWZFhPTTZaRXh4d1VxV0tuS3pCdGtCL2lKWlB2YVpaMWdYWllzU2tiL2lRNG9oblRMVEFEdHh5Q0NLNWNzTmtOR2hHekZybFdCQTlaa0VwWkRWdWFQUnNOTWd1Y2NMeFlwTlFVZFlHTFJpaFUyUjBzN1l2U0RCdkRqQmVrLzVrc2RpdjVURVBpejdBTGhkTFNma3NYRUJiUzZaMHlaWTdueS9EWDEyZ1g0NHUxYkhaNzFYN1NOd0tvaDRlWFBQMTR0MXl1WFpDTFZ0dW8ybGFVNDVpUmI2NzB1T1JhNGg2Tzd3SG9HWXJpUU1iaXluL3ZJQThCYmFqTU0xOENRZGl5UnlPR0ljSElNU3NONUhOaUh2N1ZhaXkwZTRVL010YjN0STY1bnU5VnlwcWVteVlRMFRpM3hkRkJxZzVHY0RVUmF6cWtCbjFyVUxjZTdsNy9ueDJES1c3M2lkamxkeU1xbGg3ZzkxRlRTeldBVk8iLCJtYWMiOiI5MzQ5MTYxNWZiNTgwMjVkMzNmODhhZTE3MTAwNmQ3NTlmNjgzYjFjNmNhY2JjMWViMmZjYzMwZDZhZTcyOGYyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:13:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1365584071 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xn3zjigFIgj3eEPFY4OTyzuHLBnhz03bLgzR4ZUQ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365584071\", {\"maxDepth\":0})</script>\n"}}