{"__meta": {"id": "X7f839bc4f17343bfe0fc0bbd30df84c7", "datetime": "2025-07-31 05:35:35", "utime": **********.819757, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940134.295059, "end": **********.819788, "duration": 1.5247290134429932, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": 1753940134.295059, "relative_start": 0, "end": **********.731045, "relative_end": **********.731045, "duration": 1.435986042022705, "duration_str": "1.44s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.731061, "relative_start": 1.****************, "end": **********.819791, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "88.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "roD7XOscS6sS2XYe1j7V7g0uTb0LBRPW3URRvDWz", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-1884072649 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1884072649\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1918994183 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1918994183\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-756953048 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-756953048\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1960273758 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960273758\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2003279023 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2003279023\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1779307040 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:35:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZtSkZXS2tEdWtrOGhHQVE0SHhIRFE9PSIsInZhbHVlIjoibW9iU1QwUlZzckVBcE1LVlc2YU5FY29hU3JVQ0xDTENyaVVib3RuVjlTaE5UNEJBeEJRRkhKWnJaVVk3V2RXSzhpcUtaaXlzWk1RL3Y2VnZyM0VLbmtXY1NtdC83YlVsMG5oQ0l3UGw1ZEJLWDZNcVVER3lQdFAzUTNrZFZTR2JpeWZwZmtSM1N5aWo0RUVRWG9qVi9ubFJzZWJVN0J2RGNjRTZ3OW9uaUlGZG5OZDY3TXpOQWVmS3I4b1pXR2hnc2pEc1M3b2pWc1I5R3hQWlhheCtPbEtRcE8xeHRXY2I2T2xaWmdsSkNKM0lvNkF6Sy82WnN4RDBQUGNJdVhMREZZZjRpQjNpVkdvTWNWS2x0c3hLOWNOMmpRcUtKSi9Zb0NmbGRiQ0hHSjh1OEM1ZzVoVnkxS3NNdXVINzFvbEFDbmtDNC94NFdnakM0R0VYaUhDMHJ5a2JsSzkzL2o2MFA1VGNiNmhWR3Y0a283WlZiWDRkVW1FMGtCbEJURGNJcDZ1Qmk2NkNyV3hKN2pYakJYL1VwV1NEbzJSNjVQd2R6QjNkNmNKcVBRdVYvUHV1bUtOQ0RNYUM4Mmg5RXdNdjVFK0hMcVJuRDBsTTQ2Z0U3anZncURmbFp3cFU1VWRsTDZTNGEwZEhOQzlQUDc5MS9lVERxRFpQVndtMlhvdjMiLCJtYWMiOiI5ZWI5MGJkY2U1NjY3MzM2MWUwNmE2NWYxNmYzMjQ2OTVjNGVjZTM4ZjMyOTNlYmM2MTgxNmNhZWQ3ZTE2ODAyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:35:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InlmMFVGT3FQREpDdzlDVHJ6VTJQTmc9PSIsInZhbHVlIjoiejA4S1pWci83Z2Q3QlNyR2hLNTdmUzlZeGJIazkxVkEyWjJuR0dZN005NHJYQzRYZDc0SVZaMERuWHNJVGdoM3l0aFJWVGFKY3IxS25QRWcxR2tLUmtyREVLazk5d29zMUM5SnFuWkRVWmJSaEdveE5GMVRqZDQ0WlRyVE1ZelV1N1gvT2YveXdUV01RaTFhTlJ5eS9MSGtZUTdGZmlWZHp5aFUrMlRGUGM4M3R3Y3RFY2huV3YxWmNacEVvelBtcDNNUnpUQy9HMWJ5V21vcnpxM0tzZmF6UzBvVjlqNUdHTzBWcUVLa0pNQjhtSElhd2lvV0FXM2htelM2YlJMUDhPRk0rbnIxYjVkVFlRSU9iZGYyQWNBbXdEcm45ZUtWZUxRL1pzNG1wTGhvQ3R0bTZLVzZ3aFNWcFZ6RWU0Q3JCNk5tZy9GdzhqclZtVHNYYnE3bVN5QmJsV3duWDhuSVVpRnNMdXRha3FmVlE3aDloUDZTRzJUYTU3aUhnZnd5U1hjSkZ2VDdTSzA0dnI0YlgrNUNaNlB1OExjclB4SmYycHg1TUtER3V5NzJML1dxMGF2c294TmZvUmtuNUVPVEJuRmthZys5T3dtR2pkaktWaGd5MWY0L0JYZkJBNnU3dEhhNGhzdHJFODVFbHhpcVJJRVFIVVpCZ3dhUXpGTUEiLCJtYWMiOiJhNzYzYzc0MmJiY2ZlNjMxNjVjYjNkYmQ1ZWViNmM3NGIyOWI1NDViMTdlZDQ4MzU0YWRjMGU3NDFkYjhlZWI0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:35:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZtSkZXS2tEdWtrOGhHQVE0SHhIRFE9PSIsInZhbHVlIjoibW9iU1QwUlZzckVBcE1LVlc2YU5FY29hU3JVQ0xDTENyaVVib3RuVjlTaE5UNEJBeEJRRkhKWnJaVVk3V2RXSzhpcUtaaXlzWk1RL3Y2VnZyM0VLbmtXY1NtdC83YlVsMG5oQ0l3UGw1ZEJLWDZNcVVER3lQdFAzUTNrZFZTR2JpeWZwZmtSM1N5aWo0RUVRWG9qVi9ubFJzZWJVN0J2RGNjRTZ3OW9uaUlGZG5OZDY3TXpOQWVmS3I4b1pXR2hnc2pEc1M3b2pWc1I5R3hQWlhheCtPbEtRcE8xeHRXY2I2T2xaWmdsSkNKM0lvNkF6Sy82WnN4RDBQUGNJdVhMREZZZjRpQjNpVkdvTWNWS2x0c3hLOWNOMmpRcUtKSi9Zb0NmbGRiQ0hHSjh1OEM1ZzVoVnkxS3NNdXVINzFvbEFDbmtDNC94NFdnakM0R0VYaUhDMHJ5a2JsSzkzL2o2MFA1VGNiNmhWR3Y0a283WlZiWDRkVW1FMGtCbEJURGNJcDZ1Qmk2NkNyV3hKN2pYakJYL1VwV1NEbzJSNjVQd2R6QjNkNmNKcVBRdVYvUHV1bUtOQ0RNYUM4Mmg5RXdNdjVFK0hMcVJuRDBsTTQ2Z0U3anZncURmbFp3cFU1VWRsTDZTNGEwZEhOQzlQUDc5MS9lVERxRFpQVndtMlhvdjMiLCJtYWMiOiI5ZWI5MGJkY2U1NjY3MzM2MWUwNmE2NWYxNmYzMjQ2OTVjNGVjZTM4ZjMyOTNlYmM2MTgxNmNhZWQ3ZTE2ODAyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:35:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InlmMFVGT3FQREpDdzlDVHJ6VTJQTmc9PSIsInZhbHVlIjoiejA4S1pWci83Z2Q3QlNyR2hLNTdmUzlZeGJIazkxVkEyWjJuR0dZN005NHJYQzRYZDc0SVZaMERuWHNJVGdoM3l0aFJWVGFKY3IxS25QRWcxR2tLUmtyREVLazk5d29zMUM5SnFuWkRVWmJSaEdveE5GMVRqZDQ0WlRyVE1ZelV1N1gvT2YveXdUV01RaTFhTlJ5eS9MSGtZUTdGZmlWZHp5aFUrMlRGUGM4M3R3Y3RFY2huV3YxWmNacEVvelBtcDNNUnpUQy9HMWJ5V21vcnpxM0tzZmF6UzBvVjlqNUdHTzBWcUVLa0pNQjhtSElhd2lvV0FXM2htelM2YlJMUDhPRk0rbnIxYjVkVFlRSU9iZGYyQWNBbXdEcm45ZUtWZUxRL1pzNG1wTGhvQ3R0bTZLVzZ3aFNWcFZ6RWU0Q3JCNk5tZy9GdzhqclZtVHNYYnE3bVN5QmJsV3duWDhuSVVpRnNMdXRha3FmVlE3aDloUDZTRzJUYTU3aUhnZnd5U1hjSkZ2VDdTSzA0dnI0YlgrNUNaNlB1OExjclB4SmYycHg1TUtER3V5NzJML1dxMGF2c294TmZvUmtuNUVPVEJuRmthZys5T3dtR2pkaktWaGd5MWY0L0JYZkJBNnU3dEhhNGhzdHJFODVFbHhpcVJJRVFIVVpCZ3dhUXpGTUEiLCJtYWMiOiJhNzYzYzc0MmJiY2ZlNjMxNjVjYjNkYmQ1ZWViNmM3NGIyOWI1NDViMTdlZDQ4MzU0YWRjMGU3NDFkYjhlZWI0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:35:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779307040\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-16541095 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">roD7XOscS6sS2XYe1j7V7g0uTb0LBRPW3URRvDWz</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16541095\", {\"maxDepth\":0})</script>\n"}}