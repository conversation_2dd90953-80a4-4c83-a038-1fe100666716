{"__meta": {"id": "X144b1a2f8c3eeb88d2ba71d208f95f6a", "datetime": "2025-07-31 05:37:18", "utime": **********.473155, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940236.97985, "end": **********.473205, "duration": 1.4933550357818604, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": 1753940236.97985, "relative_start": 0, "end": **********.351879, "relative_end": **********.351879, "duration": 1.3720288276672363, "duration_str": "1.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.351907, "relative_start": 1.****************, "end": **********.473211, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "121ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qYgv2UIavh6Yp1Qu5jwyOJxdwFIYVheDg4VRGcIv", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-979525417 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-979525417\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1475930820 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1475930820\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1820725844 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1820725844\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-583167447 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-583167447\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1103911106 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1103911106\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1192415673 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:37:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVwa0d3eUIvWXd6Q1JMRG9rbGdXZ0E9PSIsInZhbHVlIjoiL2tvTE1OUFVNQ2lsUTZ4Qm1zOVplWmNtOGJxaGh4RWZwWkUzek1sZit5UXFCTDZpaUpqR2t3N0YxVVFjV2xyV01mRU1lTGdnMnZOZ3F5WWZYTFNPMjdENzVXam1RSzRJK29ZOFdwSnRFY0hQaVNORlBZZjRkbUF0TGxWWWU2NmJaS3hzZ1NNVXd0MkJrMWRxOHRzc0pwN1h1eFkyWWs0aUIzQm5sS1MremtBQzdUelc2Wll5ZTMydmxkMGpNQktUeEVkZ0tybmpiSlRCdDJCRXNZdjVyU3lyak9peHhlT1NETjJzdE52VkpjR2ZuM2c5SjVSNStmTllPY00vRWxMVGQzUjVrTFdJbTRsbDBZbGtRd0g0VkNqdkxHL1R1VHRUQzZEeG1TdnNEbnNsdnZUR0g5aGNzVndXbHBGYmJSZExzTWxLT0VnUUZlUHMrOCt5UWZvY1JCTTdlaURrS2FaS0ZkVzBTZFdjL244YnZOdTZrYWoyd0EwMVR3cG5QS3dFSDh6QlhuUTFESHlZS2p3K1grOEpRSjFYV1BKSWlQcGVWOFhaNnhvOGt2L0V0L0JOWTFYc1pZSXJZdkZGeTNYZ1NjOXVscTFUUmJuQ3RtOHQwNmp6Vzh5VFlwYktCQTJ2SnpWMFNoRXlWdnllcEpMSGhYbGFWekpEcnVzNlFZcnkiLCJtYWMiOiIzYWQyM2NmYWFjY2E4NzNhZGRlYWMwYWRkNzJkMmY5NTMyOTE5ZDgwZTk2MDE5YjA2YTRlYTIxOWI4MTUxMzIwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:37:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkpFN1R2SkIzU3JKMEQvaWpobnErdFE9PSIsInZhbHVlIjoiamh4RHBLaThpbVB3Q1I5ZHVrNXNiNEd2bjVVNkFTRmZ3NUEwWFlKUW5wcTUxVndBT1U5SFdFMVVWa0dka1dVL3ppM0hkRE1QOHNmRURwK1pZWkJPRkxsT0hDTWxKMWs3bUErZ21uQW1qdXN3RmM5UFFkZEFSd0E1cnNQeER0NkF2TC90VEdWSHpGNVhDUTRrZnZUYjRQN0I4NVozYVdSZFZ0dTdocGVrWEpkQzAyTlkwL09OeTVPVGNMbUxoRS80M0wvaXZ1cS9WcnVCUmpOUTI4MjVxV0tOVURqOFNGRURhWGQwZDcxZURVbjBUOTBYNlVzczB0QytwME9FM1RkNFphOVVsYUhuNVQwVUthckVsQ2RjTEJpRnN6U2dOd2pVWkJqa1NXTEtuYjMzSTA5Ly9ZN0NBbUlyMHVYSXdyNDJPelo4OHRMalZ0VUpWdmVIL0pXT1BQbTdtejFDUTRMbDJHaHhpUnJGb2pRaDZRVXNvN3hjRXg5eUZ6UGFPR1NsWXRnc2VSREgrMmhCcWsyYkY1end0MlBFY3dvUHBXWTN6Y3BwNWY1VmpPbmNNME5rd3FkN2tpc1Z1b1RTWCtRZzQ1ZCtSSFVJWTN4T2pFbVYzV1B5Nlpia05taU9pT0NPOXBFeW9vYlE4Q001aCtLekxrM3lPaTMvY0ltUHJPWFkiLCJtYWMiOiI3MDcxMmQwYmZiYzI2ZDZkNGE4YzNmNWM0NzU0OGVlMzNmNzgzNjIxNGE2ZGU1MjU0NjhlZjM3NWM0ODlhNTE0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:37:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVwa0d3eUIvWXd6Q1JMRG9rbGdXZ0E9PSIsInZhbHVlIjoiL2tvTE1OUFVNQ2lsUTZ4Qm1zOVplWmNtOGJxaGh4RWZwWkUzek1sZit5UXFCTDZpaUpqR2t3N0YxVVFjV2xyV01mRU1lTGdnMnZOZ3F5WWZYTFNPMjdENzVXam1RSzRJK29ZOFdwSnRFY0hQaVNORlBZZjRkbUF0TGxWWWU2NmJaS3hzZ1NNVXd0MkJrMWRxOHRzc0pwN1h1eFkyWWs0aUIzQm5sS1MremtBQzdUelc2Wll5ZTMydmxkMGpNQktUeEVkZ0tybmpiSlRCdDJCRXNZdjVyU3lyak9peHhlT1NETjJzdE52VkpjR2ZuM2c5SjVSNStmTllPY00vRWxMVGQzUjVrTFdJbTRsbDBZbGtRd0g0VkNqdkxHL1R1VHRUQzZEeG1TdnNEbnNsdnZUR0g5aGNzVndXbHBGYmJSZExzTWxLT0VnUUZlUHMrOCt5UWZvY1JCTTdlaURrS2FaS0ZkVzBTZFdjL244YnZOdTZrYWoyd0EwMVR3cG5QS3dFSDh6QlhuUTFESHlZS2p3K1grOEpRSjFYV1BKSWlQcGVWOFhaNnhvOGt2L0V0L0JOWTFYc1pZSXJZdkZGeTNYZ1NjOXVscTFUUmJuQ3RtOHQwNmp6Vzh5VFlwYktCQTJ2SnpWMFNoRXlWdnllcEpMSGhYbGFWekpEcnVzNlFZcnkiLCJtYWMiOiIzYWQyM2NmYWFjY2E4NzNhZGRlYWMwYWRkNzJkMmY5NTMyOTE5ZDgwZTk2MDE5YjA2YTRlYTIxOWI4MTUxMzIwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:37:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkpFN1R2SkIzU3JKMEQvaWpobnErdFE9PSIsInZhbHVlIjoiamh4RHBLaThpbVB3Q1I5ZHVrNXNiNEd2bjVVNkFTRmZ3NUEwWFlKUW5wcTUxVndBT1U5SFdFMVVWa0dka1dVL3ppM0hkRE1QOHNmRURwK1pZWkJPRkxsT0hDTWxKMWs3bUErZ21uQW1qdXN3RmM5UFFkZEFSd0E1cnNQeER0NkF2TC90VEdWSHpGNVhDUTRrZnZUYjRQN0I4NVozYVdSZFZ0dTdocGVrWEpkQzAyTlkwL09OeTVPVGNMbUxoRS80M0wvaXZ1cS9WcnVCUmpOUTI4MjVxV0tOVURqOFNGRURhWGQwZDcxZURVbjBUOTBYNlVzczB0QytwME9FM1RkNFphOVVsYUhuNVQwVUthckVsQ2RjTEJpRnN6U2dOd2pVWkJqa1NXTEtuYjMzSTA5Ly9ZN0NBbUlyMHVYSXdyNDJPelo4OHRMalZ0VUpWdmVIL0pXT1BQbTdtejFDUTRMbDJHaHhpUnJGb2pRaDZRVXNvN3hjRXg5eUZ6UGFPR1NsWXRnc2VSREgrMmhCcWsyYkY1end0MlBFY3dvUHBXWTN6Y3BwNWY1VmpPbmNNME5rd3FkN2tpc1Z1b1RTWCtRZzQ1ZCtSSFVJWTN4T2pFbVYzV1B5Nlpia05taU9pT0NPOXBFeW9vYlE4Q001aCtLekxrM3lPaTMvY0ltUHJPWFkiLCJtYWMiOiI3MDcxMmQwYmZiYzI2ZDZkNGE4YzNmNWM0NzU0OGVlMzNmNzgzNjIxNGE2ZGU1MjU0NjhlZjM3NWM0ODlhNTE0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:37:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1192415673\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1313456449 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qYgv2UIavh6Yp1Qu5jwyOJxdwFIYVheDg4VRGcIv</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313456449\", {\"maxDepth\":0})</script>\n"}}