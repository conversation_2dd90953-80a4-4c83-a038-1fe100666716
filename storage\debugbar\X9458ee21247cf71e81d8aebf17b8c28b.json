{"__meta": {"id": "X9458ee21247cf71e81d8aebf17b8c28b", "datetime": "2025-07-31 05:37:25", "utime": **********.669179, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940243.798811, "end": **********.669228, "duration": 1.8704171180725098, "duration_str": "1.87s", "measures": [{"label": "Booting", "start": 1753940243.798811, "relative_start": 0, "end": **********.558169, "relative_end": **********.558169, "duration": 1.7593579292297363, "duration_str": "1.76s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.558204, "relative_start": 1.****************, "end": **********.669233, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ENeX7zmnFTemiKDDTc9DT3hQS04jAHeHWQvGwNGG", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-255747708 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-255747708\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1782637851 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1782637851\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1968807459 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1968807459\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2069172782 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069172782\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1430838340 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1430838340\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-329719183 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:37:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5jVkUxS1YzVlk4MFV1Z3prb2VsVUE9PSIsInZhbHVlIjoiWm9KckcrUWZ4MUk1T1JKVDZHM2NMMmRRS2hBV2g2aXUvY0hZUmFuT2Z6d0ZzekdWeEk4Sk9tOU8vRVpWRnJIL3pibElNRFpsenFoRmxKMStwQW9UUDJnbFk4SUpEZTBJdlFQZm4wVTllUVRPdGpheGRwMEhBL3VQRXdZaUJHRE1wU2RuWEhkNFVTMTN2bmNaSkpXdFI4V1MwT1dHUzV2N0w2bUVxQWFuVTRJWWpIczFUTjBGN3FJcStyREhhSmlFblhHZm1VTjFOWThMWHZEVThnZng5SWRqNDlqRzloY2pJQXFESWZxVHJxdWg0Nkx3LzNqSm00SVhmb0oyTDZsWXB2UkZSUkZqZVhaZE5FNVBDNWtNVzZ1bWxpdzdPSEVxZjFJd3JtZkp2VGtQOHg4Ry9MNnBXTHRqS285UThrdWxFSlo4R0M5a21hclhJZmpkV1ZWZkRzWjVkNWJINUJGS3g4WlhNY1JjeTM5V0NzOFA5cEIzalJzL1kvVE56bWlVY3JFdW5PQXM2RHJjTURXWVpIMWZpSUNLRUtJNGc5ckpkRTFxMjlkUDVpbC9oUnRDVm5Ca1RZZVBpcklMNUhBdFdITXpVVm10aFRTQW1nTHIyWWppZDZ0eUtjY1lBRDNZT05iUHlROXhHKy92TWU0RUtKSkt4Z3NMRVF3WkVXU08iLCJtYWMiOiIyNDhmNzk3OWM5M2M5Mjg4YzBlNmJlNzY4NzYwNmY5ZjJmYTQ5NDFjZDBlYmNmOTUwOTM2NzBkOTM2NmFlZTRlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:37:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImRQNFU5Wm9ZMHdmSXBYOHN3eVZEa0E9PSIsInZhbHVlIjoiSXkzQmN5ZG1YbEExeE8wWHJLMjlmQmptUkx2by9nVUtlZU9ST3RxZDlwc0FYdUFNMS9KcFJTSUhGbnFOUEQzMk94Z1I5NlRRZVl0NDRQU1pIWlh6QzRUQnFvQS9KcC9uenJHWVIxakVOSlM0YStiaituMklBblhDUlkrUUFPMzJ3SitndGxGZ0o3WG04Z0kzNUFYK1c0anQwYU16SldBMWNEdndBdG1xUEZ5aHdIUVo5UjE1WXVXanBGWmNtOFVQWXdVbEFTWUhIRm9uakNCQnVtTjdDb3k2am9wWjkzVnE2anNZZGdSb3pScVJyZGM4ZFdONUNhQlBxS1EzclZ4UG11MVpBaGxvcXMwMXd5VDJUS1VpVmNMN2lHQ2Zia2xPQk14b2dCYzR6UVZwVldEdjllRTJVWGs5YlNselA3U05mVnNHVDFjSEFYdGo5NWtBTVpGS3FZbU1KK1ppSCtyTUJoeUIxYTFOcC80ZHJUekdkek5kS0JKY0RnVC83UEk2RHhMdHRwTjB0ZkNQa1k2S3ZPSjYvdVNiYWlONG5SVStVbFhHK3k4YUUzWkNwNXg3dEtlbmJRbmkrVWtuSzduRDJ6bnR5UTNRT1I2bEVSN1RodytvL1h1bW05TThkNHVmNldmaWVJNGYzdGpBOE9meXlZMHY1K0hEQXVpTWRwR2oiLCJtYWMiOiI3ZmM0ZTAwNmQ3YzU4NWFiMDU3YTg2MzlmZWI2OThhYTI2ZGI3NzkxODI2YWQ1MmY1OTE5ZGQ3ZTA0NzA2MTZhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:37:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5jVkUxS1YzVlk4MFV1Z3prb2VsVUE9PSIsInZhbHVlIjoiWm9KckcrUWZ4MUk1T1JKVDZHM2NMMmRRS2hBV2g2aXUvY0hZUmFuT2Z6d0ZzekdWeEk4Sk9tOU8vRVpWRnJIL3pibElNRFpsenFoRmxKMStwQW9UUDJnbFk4SUpEZTBJdlFQZm4wVTllUVRPdGpheGRwMEhBL3VQRXdZaUJHRE1wU2RuWEhkNFVTMTN2bmNaSkpXdFI4V1MwT1dHUzV2N0w2bUVxQWFuVTRJWWpIczFUTjBGN3FJcStyREhhSmlFblhHZm1VTjFOWThMWHZEVThnZng5SWRqNDlqRzloY2pJQXFESWZxVHJxdWg0Nkx3LzNqSm00SVhmb0oyTDZsWXB2UkZSUkZqZVhaZE5FNVBDNWtNVzZ1bWxpdzdPSEVxZjFJd3JtZkp2VGtQOHg4Ry9MNnBXTHRqS285UThrdWxFSlo4R0M5a21hclhJZmpkV1ZWZkRzWjVkNWJINUJGS3g4WlhNY1JjeTM5V0NzOFA5cEIzalJzL1kvVE56bWlVY3JFdW5PQXM2RHJjTURXWVpIMWZpSUNLRUtJNGc5ckpkRTFxMjlkUDVpbC9oUnRDVm5Ca1RZZVBpcklMNUhBdFdITXpVVm10aFRTQW1nTHIyWWppZDZ0eUtjY1lBRDNZT05iUHlROXhHKy92TWU0RUtKSkt4Z3NMRVF3WkVXU08iLCJtYWMiOiIyNDhmNzk3OWM5M2M5Mjg4YzBlNmJlNzY4NzYwNmY5ZjJmYTQ5NDFjZDBlYmNmOTUwOTM2NzBkOTM2NmFlZTRlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:37:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImRQNFU5Wm9ZMHdmSXBYOHN3eVZEa0E9PSIsInZhbHVlIjoiSXkzQmN5ZG1YbEExeE8wWHJLMjlmQmptUkx2by9nVUtlZU9ST3RxZDlwc0FYdUFNMS9KcFJTSUhGbnFOUEQzMk94Z1I5NlRRZVl0NDRQU1pIWlh6QzRUQnFvQS9KcC9uenJHWVIxakVOSlM0YStiaituMklBblhDUlkrUUFPMzJ3SitndGxGZ0o3WG04Z0kzNUFYK1c0anQwYU16SldBMWNEdndBdG1xUEZ5aHdIUVo5UjE1WXVXanBGWmNtOFVQWXdVbEFTWUhIRm9uakNCQnVtTjdDb3k2am9wWjkzVnE2anNZZGdSb3pScVJyZGM4ZFdONUNhQlBxS1EzclZ4UG11MVpBaGxvcXMwMXd5VDJUS1VpVmNMN2lHQ2Zia2xPQk14b2dCYzR6UVZwVldEdjllRTJVWGs5YlNselA3U05mVnNHVDFjSEFYdGo5NWtBTVpGS3FZbU1KK1ppSCtyTUJoeUIxYTFOcC80ZHJUekdkek5kS0JKY0RnVC83UEk2RHhMdHRwTjB0ZkNQa1k2S3ZPSjYvdVNiYWlONG5SVStVbFhHK3k4YUUzWkNwNXg3dEtlbmJRbmkrVWtuSzduRDJ6bnR5UTNRT1I2bEVSN1RodytvL1h1bW05TThkNHVmNldmaWVJNGYzdGpBOE9meXlZMHY1K0hEQXVpTWRwR2oiLCJtYWMiOiI3ZmM0ZTAwNmQ3YzU4NWFiMDU3YTg2MzlmZWI2OThhYTI2ZGI3NzkxODI2YWQ1MmY1OTE5ZGQ3ZTA0NzA2MTZhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:37:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-329719183\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1669812651 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ENeX7zmnFTemiKDDTc9DT3hQS04jAHeHWQvGwNGG</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1669812651\", {\"maxDepth\":0})</script>\n"}}