{"__meta": {"id": "X8e14969382881f0527a2eebbeb62da83", "datetime": "2025-07-31 06:33:07", "utime": **********.130009, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943584.740062, "end": **********.130062, "duration": 2.390000104904175, "duration_str": "2.39s", "measures": [{"label": "Booting", "start": 1753943584.740062, "relative_start": 0, "end": 1753943586.935959, "relative_end": 1753943586.935959, "duration": 2.195897102355957, "duration_str": "2.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753943586.935997, "relative_start": 2.***************, "end": **********.130068, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "194ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "z6y9DwgF9wOIoucrbq0vbdmkKLYXxh8aJ9ID71VS", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1305136407 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1305136407\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1862328042 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1862328042\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-576835056 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-576835056\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1047073132 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1047073132\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1080625662 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1080625662\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:33:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhTQy9mczQweDZNYjFEUE5aSitDd3c9PSIsInZhbHVlIjoia3diNTg5ZHdBc2wyTXBXYlo2bXY2a3B5cnljWVVZZUVwRlgvRExsMTU1dFBUbGJCeE9HaVFnZldSbkVrSGRybDFvL3ZVNm9kbHRaTS9XMnlLOGpUTTJoUHRSbVkzdU5JdWlBZjZUWlFTcUwyRG1NRUg0R1Vzak9NeXArVDNpeHBVdXZLRUpJaURYL2lqVkc1cThQRWN1dXF1L0dweVpJNUs2M0ZUTlErcm13dDdOQ053MG1SYVpMZ0NaekhBWEU1SG9KNGtPNmFHa1EzOHd3UFN4WC9oSGRPYUpZWHY1Qlh6TTZ0Zmp6VldXRWpZNlN3VTVGWmVCOVZ3N0pvWVdKQXFPbnZJVDRqQlFVUnYvRlZMY0RiOXE5NU9KVkd1SXVNZ2lCMkZldnpybjNiNEZjNDFJVUIxK3gvSUh4SDk1WkRVSmM0dGNwbXNnQ0taa1ptZ0IvZlIxL3FMMEVxMXFLYWVKc045bk1YTEZtMDFseHIrWGg4NlVQdllmalFRTDVmZmVDMWg2bUZvckZRVWIrd1p1eU1kbk52Q2dCVWFHV1IzVktaYWR5VHZGd3ZYR1Bpd2MwZ0lJV2tTYW1wY1cxcEc4Y0U0NjFyNk9Qa1NRMW81UTgyMkRIK0l4bmlIZUZ6TFI5cWVHbkxrYVcvbHBUajdXeC9ZME1TbWRkZG5KZlEiLCJtYWMiOiJlMmJhN2M0OTY2N2Q5NzhjYTA5MzVhYWI4YTcyMThkMzdmNWJmNTVhNjU3NzRlN2EwMGViMmNlYjNhZDU4NjA3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:33:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlIwd3B2c0lJS1dyN05qM3hnTmQwZ2c9PSIsInZhbHVlIjoiWmlKM05TbkpNdGxsUHZEV05JbDMyQm04RTYyYnJUV3Z4MFdXNEozeUFZbUJLWVQ3NXdDam93UzNIMndHMklBblRIdnFCOEMwN0dqUXowWm1HdHNPTERZaFdnUHlneithMjkrcHhMMWtDeTJ5bmozUTk5TFpDVjZKcXdYa2Q2YnRiL2JSNDdyUncyeFBlMGdRdE9hSUhMeXhMTkwzeWo4di9oQXZvWmxoTXBDZmpnOS9IUVVkanBxOEEwTHNvU2krem9lcllxSUdVeWJQT0VMcDZPN1FIVEtWWFV0bjVJdDJaQzhPeUJKWW5KMFpqM2h1SStFdk9vTmFRTmlCTXl3NFpUZFpUNkt2NGpOU3ZyNFB0VU1SUUk5dVJTaDB3dzBFamdGY1Y3MVNoRnlZbDdSWS8yK1MwYVYxVHJBdlVTRXoxMm8vZ2xNY3dUQXh4blYxUHF4eGtlQ2NMellwVEFvNW0zeFYxaFFqR1ZubzVUellaRzlrU21Qa3BvVUFtOWY0TFhMdzZEQU9oYmdISDR4QituVFRHSTFsaDhGZlVUWi9SU2prcHcwOC9pNW1VcVJqY0ErR2toamkzb0pmMEk4WjUvYXVtQ2JVdmhtZWcvQW9HQzl3SWtLbThXaXllcEY5ZWM1QnhKUmk5bE1qRklYUDdDUlpIY0padHRUWXUvQTYiLCJtYWMiOiJjZWM0ODYzMWU4MjZhMTVkMjllMWMyOWEzMWQxNWExZDQyZjI5NWYyYTQzYjVlOGE0NzhmMjVlYTU5MzFmY2QyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:33:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhTQy9mczQweDZNYjFEUE5aSitDd3c9PSIsInZhbHVlIjoia3diNTg5ZHdBc2wyTXBXYlo2bXY2a3B5cnljWVVZZUVwRlgvRExsMTU1dFBUbGJCeE9HaVFnZldSbkVrSGRybDFvL3ZVNm9kbHRaTS9XMnlLOGpUTTJoUHRSbVkzdU5JdWlBZjZUWlFTcUwyRG1NRUg0R1Vzak9NeXArVDNpeHBVdXZLRUpJaURYL2lqVkc1cThQRWN1dXF1L0dweVpJNUs2M0ZUTlErcm13dDdOQ053MG1SYVpMZ0NaekhBWEU1SG9KNGtPNmFHa1EzOHd3UFN4WC9oSGRPYUpZWHY1Qlh6TTZ0Zmp6VldXRWpZNlN3VTVGWmVCOVZ3N0pvWVdKQXFPbnZJVDRqQlFVUnYvRlZMY0RiOXE5NU9KVkd1SXVNZ2lCMkZldnpybjNiNEZjNDFJVUIxK3gvSUh4SDk1WkRVSmM0dGNwbXNnQ0taa1ptZ0IvZlIxL3FMMEVxMXFLYWVKc045bk1YTEZtMDFseHIrWGg4NlVQdllmalFRTDVmZmVDMWg2bUZvckZRVWIrd1p1eU1kbk52Q2dCVWFHV1IzVktaYWR5VHZGd3ZYR1Bpd2MwZ0lJV2tTYW1wY1cxcEc4Y0U0NjFyNk9Qa1NRMW81UTgyMkRIK0l4bmlIZUZ6TFI5cWVHbkxrYVcvbHBUajdXeC9ZME1TbWRkZG5KZlEiLCJtYWMiOiJlMmJhN2M0OTY2N2Q5NzhjYTA5MzVhYWI4YTcyMThkMzdmNWJmNTVhNjU3NzRlN2EwMGViMmNlYjNhZDU4NjA3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:33:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlIwd3B2c0lJS1dyN05qM3hnTmQwZ2c9PSIsInZhbHVlIjoiWmlKM05TbkpNdGxsUHZEV05JbDMyQm04RTYyYnJUV3Z4MFdXNEozeUFZbUJLWVQ3NXdDam93UzNIMndHMklBblRIdnFCOEMwN0dqUXowWm1HdHNPTERZaFdnUHlneithMjkrcHhMMWtDeTJ5bmozUTk5TFpDVjZKcXdYa2Q2YnRiL2JSNDdyUncyeFBlMGdRdE9hSUhMeXhMTkwzeWo4di9oQXZvWmxoTXBDZmpnOS9IUVVkanBxOEEwTHNvU2krem9lcllxSUdVeWJQT0VMcDZPN1FIVEtWWFV0bjVJdDJaQzhPeUJKWW5KMFpqM2h1SStFdk9vTmFRTmlCTXl3NFpUZFpUNkt2NGpOU3ZyNFB0VU1SUUk5dVJTaDB3dzBFamdGY1Y3MVNoRnlZbDdSWS8yK1MwYVYxVHJBdlVTRXoxMm8vZ2xNY3dUQXh4blYxUHF4eGtlQ2NMellwVEFvNW0zeFYxaFFqR1ZubzVUellaRzlrU21Qa3BvVUFtOWY0TFhMdzZEQU9oYmdISDR4QituVFRHSTFsaDhGZlVUWi9SU2prcHcwOC9pNW1VcVJqY0ErR2toamkzb0pmMEk4WjUvYXVtQ2JVdmhtZWcvQW9HQzl3SWtLbThXaXllcEY5ZWM1QnhKUmk5bE1qRklYUDdDUlpIY0padHRUWXUvQTYiLCJtYWMiOiJjZWM0ODYzMWU4MjZhMTVkMjllMWMyOWEzMWQxNWExZDQyZjI5NWYyYTQzYjVlOGE0NzhmMjVlYTU5MzFmY2QyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:33:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z6y9DwgF9wOIoucrbq0vbdmkKLYXxh8aJ9ID71VS</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}