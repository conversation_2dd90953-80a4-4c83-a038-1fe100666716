{"__meta": {"id": "X92940d4bc31bae9f163a7cebe19050a3", "datetime": "2025-07-31 05:24:18", "utime": **********.152912, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939455.813581, "end": **********.153, "duration": 2.33941912651062, "duration_str": "2.34s", "measures": [{"label": "Booting", "start": 1753939455.813581, "relative_start": 0, "end": 1753939457.826281, "relative_end": 1753939457.826281, "duration": 2.012700080871582, "duration_str": "2.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753939457.826382, "relative_start": 2.***************, "end": **********.15301, "relative_end": 9.775161743164062e-06, "duration": 0.****************, "duration_str": "327ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NoHRteget6rgwKQYKjrjGvpvTVsmkv0ah4W5QdDq", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2119734691 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2119734691\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1175577766 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1175577766\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1781275363 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1781275363\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1830207198 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1830207198\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1168063737 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1168063737\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1614685471 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:24:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik44TGhkdk5rU1c0OXAyd25pcVo2dkE9PSIsInZhbHVlIjoiNkwrdXZucm9kUHVkYWlDZVZEMGlFNmU3bzRrVTIzdTZybnlZQlN0d3JvSVhyZzBRZzVWeWZYV3kyZCtna1p4dEs3bHphRUkrNldTSGNFcFoxeHlLUFMramRoV0VjcEgvMFZHeTZrR1prZTBVbEhjb3U4NXdRaVpzT3FodE1OR2ViMlBlZEpGc05tWldSK3RDbGlXaG1MT3dQYTNNZFIvdTFOSnk0MlozTnpkUUg1bHhTbHpPUFhqZUJLUWZtUGNqN0pnSnpJKzZyV2lPajZ3SzNBU1Fnc3FnbmRiMkFRbTdBYjlJWjdtMDdEdFhlMUFiUnhjaFZkTlIwM3hkSW1yMVVlVzJKWVpGRWtRWERxWWg2UTUwK0FUS1l5WU5xNTM4c3A1VDhhQk0vTjdKVDhOdTlDeVZRa1N5eFY0VTV5YW5DVW1SM3ZxK1ZNMVpzRGVEZDZDZzhOZ2JtZWhXTG8rV05nZm5lY3ZFVDM5cUYrbllFaDQvd3NjUmsvQnZRR3hjdDJwT1VxR2p0WDRvYlZhK2hlNzE2ZldSUkdWNVR4TGw1YndkTisyS2pIc0wrdks2WVZ1VnVKRTJoMXNUa2hZek55Ui91bStQUVRCcnpkeGJrRjVtdGNtbklaTUNJeEpRZGtmYWNBYzRXUjVZdmRKdzZBRmdKU01PQWZKbnJUb1giLCJtYWMiOiI2MWQxYjJkZDJhNDEyMmU3NzE3ZTRjODI3MGI1NzEzMWFiNmJjMzBmNGI4NDBhMDk5OGRmZjEyZTU0ZjY3ZGUyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:24:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik44dUEvaUlxZWR2Y3d6VUFzMkxpeWc9PSIsInZhbHVlIjoic1F0TmVscWZVdW5wdUdha1R2YjNpc040bjZkRnJucjUxTGI5R0pNTmRCQUdrNEY2MkFUT0NnN3B1aU9UUkoranlqM2hSUmtMNG96b0ZuajVQYlllYjA3ekNRWHI0TmtOV3Qxb0NiNXRzdmt6TTBMRTVUMlFsb1I1dEJGV3NiTE10RzZQT21KZEdleXo0bmg2bkNXejlvTnhJZi9OUlRTbWVRMkNkWFA2VzhOeHh2YmorZTZvQlAxV3dDTkN4OUkwMWU0VTJXa0FqWGtPK3BiTm9UK3BjTlk3cWVYdFdhWWhCeWxsOTVaejRxVGErK2QwbHpaVytEVkJEZE5mbHZ1My9OeStmOEZsOWk4ZDVhWHFEUHo1QmczaGJwL2NiMlYwci9CREtRTU01aEZvNTYzOHl1TGxTYUVTVjJUWFR1Y1J0WVI3Q3JvbXBmc25vQ3FRbjYrbFdha2xNN3A5RUNrMUhUS2IxaFVtbnNrQVZMYWsyRkxJUU92dEM2Ym5aekVIZy8xS2xvK0trV29iTUpkU2lIbktxa0ZrREJLSVlIWURKSmR4Z21TUGVHTnBiam1tZnpGcHZsdzBpcmZacjRrRExXOU1nU3ZoYVVoaGp0V2JCS1FESWR3cnpqQzU5SVhvWFJtVkJtZmt1UWZmSm01WU5wTkZwV2dBTlY5aXo3c1UiLCJtYWMiOiI5NjcyOWM1MTRmNjJiYWVjMTcyMWU2NjYxZDAzNmRmNWZiM2RjOWMwMmYxZDJlZjUwYzhmNWNmNDk5MWI3NmE3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:24:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik44TGhkdk5rU1c0OXAyd25pcVo2dkE9PSIsInZhbHVlIjoiNkwrdXZucm9kUHVkYWlDZVZEMGlFNmU3bzRrVTIzdTZybnlZQlN0d3JvSVhyZzBRZzVWeWZYV3kyZCtna1p4dEs3bHphRUkrNldTSGNFcFoxeHlLUFMramRoV0VjcEgvMFZHeTZrR1prZTBVbEhjb3U4NXdRaVpzT3FodE1OR2ViMlBlZEpGc05tWldSK3RDbGlXaG1MT3dQYTNNZFIvdTFOSnk0MlozTnpkUUg1bHhTbHpPUFhqZUJLUWZtUGNqN0pnSnpJKzZyV2lPajZ3SzNBU1Fnc3FnbmRiMkFRbTdBYjlJWjdtMDdEdFhlMUFiUnhjaFZkTlIwM3hkSW1yMVVlVzJKWVpGRWtRWERxWWg2UTUwK0FUS1l5WU5xNTM4c3A1VDhhQk0vTjdKVDhOdTlDeVZRa1N5eFY0VTV5YW5DVW1SM3ZxK1ZNMVpzRGVEZDZDZzhOZ2JtZWhXTG8rV05nZm5lY3ZFVDM5cUYrbllFaDQvd3NjUmsvQnZRR3hjdDJwT1VxR2p0WDRvYlZhK2hlNzE2ZldSUkdWNVR4TGw1YndkTisyS2pIc0wrdks2WVZ1VnVKRTJoMXNUa2hZek55Ui91bStQUVRCcnpkeGJrRjVtdGNtbklaTUNJeEpRZGtmYWNBYzRXUjVZdmRKdzZBRmdKU01PQWZKbnJUb1giLCJtYWMiOiI2MWQxYjJkZDJhNDEyMmU3NzE3ZTRjODI3MGI1NzEzMWFiNmJjMzBmNGI4NDBhMDk5OGRmZjEyZTU0ZjY3ZGUyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:24:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik44dUEvaUlxZWR2Y3d6VUFzMkxpeWc9PSIsInZhbHVlIjoic1F0TmVscWZVdW5wdUdha1R2YjNpc040bjZkRnJucjUxTGI5R0pNTmRCQUdrNEY2MkFUT0NnN3B1aU9UUkoranlqM2hSUmtMNG96b0ZuajVQYlllYjA3ekNRWHI0TmtOV3Qxb0NiNXRzdmt6TTBMRTVUMlFsb1I1dEJGV3NiTE10RzZQT21KZEdleXo0bmg2bkNXejlvTnhJZi9OUlRTbWVRMkNkWFA2VzhOeHh2YmorZTZvQlAxV3dDTkN4OUkwMWU0VTJXa0FqWGtPK3BiTm9UK3BjTlk3cWVYdFdhWWhCeWxsOTVaejRxVGErK2QwbHpaVytEVkJEZE5mbHZ1My9OeStmOEZsOWk4ZDVhWHFEUHo1QmczaGJwL2NiMlYwci9CREtRTU01aEZvNTYzOHl1TGxTYUVTVjJUWFR1Y1J0WVI3Q3JvbXBmc25vQ3FRbjYrbFdha2xNN3A5RUNrMUhUS2IxaFVtbnNrQVZMYWsyRkxJUU92dEM2Ym5aekVIZy8xS2xvK0trV29iTUpkU2lIbktxa0ZrREJLSVlIWURKSmR4Z21TUGVHTnBiam1tZnpGcHZsdzBpcmZacjRrRExXOU1nU3ZoYVVoaGp0V2JCS1FESWR3cnpqQzU5SVhvWFJtVkJtZmt1UWZmSm01WU5wTkZwV2dBTlY5aXo3c1UiLCJtYWMiOiI5NjcyOWM1MTRmNjJiYWVjMTcyMWU2NjYxZDAzNmRmNWZiM2RjOWMwMmYxZDJlZjUwYzhmNWNmNDk5MWI3NmE3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:24:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1614685471\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1991847142 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NoHRteget6rgwKQYKjrjGvpvTVsmkv0ah4W5QdDq</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1991847142\", {\"maxDepth\":0})</script>\n"}}