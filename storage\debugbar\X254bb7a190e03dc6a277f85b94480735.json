{"__meta": {"id": "X254bb7a190e03dc6a277f85b94480735", "datetime": "2025-07-31 05:11:17", "utime": **********.689783, "method": "GET", "uri": "/users/79/login-with-company", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753938676.857912, "end": **********.689848, "duration": 0.8319358825683594, "duration_str": "832ms", "measures": [{"label": "Booting", "start": 1753938676.857912, "relative_start": 0, "end": **********.581488, "relative_end": **********.581488, "duration": 0.7235758304595947, "duration_str": "724ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.581513, "relative_start": 0.7236008644104004, "end": **********.689857, "relative_end": 9.059906005859375e-06, "duration": 0.10834407806396484, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44693048, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1450\" onclick=\"\">app/Http/Controllers/UserController.php:1450-1474</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02521, "accumulated_duration_str": "25.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.637451, "duration": 0.02441, "duration_str": "24.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 96.827}, {"sql": "select * from `users` where `users`.`id` = '79' limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\UserController.php", "line": 1452}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6687632, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "UserController.php:1452", "source": "app/Http/Controllers/UserController.php:1452", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1452", "ajax": false, "filename": "UserController.php", "line": "1452"}, "connection": "radhe_same", "start_percent": 96.827, "width_percent": 3.173}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/users/79/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/79/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-1612654128 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1612654128\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1271534427 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IndZN284RlYxNXU2YTVWY3d6Z1pnaXc9PSIsInZhbHVlIjoicG9wNXd3M2d2MWlaZ1B0bExGV00wT0RvdDZvd2NCWEREMytHaTFqZVRjWnB0U2NqYUJnN3Vpd3pWdmw4eW83V1NhMTVObk9uT0FaeG9RZVdkOFdQby8rOGhxT2NUTnVHOG4wYzVhSmczbzdaWXgxQm16UEE2eTZ5ckZNTnFwR0FRZkMvamo3M3hndnc0RVR0aktXRmloSytKcW1MM2tSbzdMOHd6aFVvRWpkT2VmamF2SUlXSkhtRlphVWk1d2sxSWFBc0dzV2ovSGtIbWh0Rk9wZlVqaGJvUEVCTDFsd1BaQU5GaUdFcHpTaGs2YWVOSTN6cXNyQWRHRkVZQlR3cHBLcE9sek1RZlNZUSsveWFmeFJMMzExS2lJeUNrSnZtd2VmRVhOaFN5TzA3UkRFdGZ5Q1Izdno5MnZ6VWczVkZtVU85WTkrMjBWcUhicVB1ckd0L1ZnaitsZUd2RUpJWWlYTCtWSzZOSzRZdmJxREhBbXQzQ1cwVDZBOW1hWm1qOEtrWlk2bFFzZGVqS1pzZEw0OCtzTGlDRGN2RytwSUhxOHpoU3J5dUl1c2hWYS9mSk83cVJqV25WZ3lSMEpYTExNb0Jzamlja040d1Q5bzFZWUxoZ2ZyV2E4ODBReXdQbDFvUkZZN1pOZlNDcTdGb2hISHN4VGZVRm9iMFNoWlYiLCJtYWMiOiJmNjk2NGJlMmFjOGYyNTJhNjM3MjkwMmVjNGI2NzQ3NWZmZWRiOWZkNjVmZjNmZmZhNTEzMDZkMDcxODhiMWUxIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkVBdjZrcE44WC92MUtaTmVPSU1EdWc9PSIsInZhbHVlIjoiUTVsa0JFL3lreGsvUnBpN3dYSHJiQ2E2WUdJSGI4bEZmUit5SkFhY04yVVU2cFV2K292bC9YbjlXazdyTXkxK3MxMThvd2o1MnRLRWVxTjZtMDgvZDZ0S2doTzZ2RzVyeEI1bzVWSWY0bnE4cHhWMTlPMi9UZEJ4b1ZYSnBwU29vbllhTG9pbjBxRDVMT3ZEMDFhYzlGZnFuc0EvRjVNV002UE1kTTEvczV6V2NOQTlEV3dJTHdzRTdLWkpjY3RyN0lKZzA3OG9FZUtzKzdDS3FOa3Y0d3JHSTQ1aWRvN05CZkVHRXpzRzNkRlU1L0VYcW5QM0JoSnc5U1Z0SVYwSzdJVUNFWGxIWnF2RmNadU9CT0ZvSFhsYXM0UUMvaVNrUTMzRUNoMVJyRkE3R1V0UWlJRWRLUFhQS2UyMUNldHU2WnVDK1p5R0t1eEx4eHlLd2VyWFRVV1lSVjBtMDF1U0VaSDd6aTI4cDBocUViekl2d2JLSVRzZ3cwejZFa0pKUURiRGpuVTRHekZiYlg1bGRrV043RUFSTFR3YXk2TXlmeW1NdjZKOUpKR3N1WXd1MGpKMG1rNFkyYWJVbVR1RXk0ZGhyckdCa3RVa29wNkZsRUZVOUEwUmpsNHBEK2VrNHBKUEYxQ00rWEtRbkNIeHFSMFRSYzc0d0R3dzJPUHciLCJtYWMiOiJhODU4MzYzM2Y0MWU3ZDY1NDgxMGFmOTAxMzhiNDNiODI3NWQ0NDg5YTRhYzRkZDAyNDAwNDdkZWE1MjI1NDI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1271534427\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXcIUJMcydAyqtnEKWEUzIzoSEq1x7CzphaduhZT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:11:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlYyMm1hV3pWUjJMaVlJdklrQ0hUYlE9PSIsInZhbHVlIjoiSDEyQUg0V0F5SVkrZ0VSNG51MEFsTWhnZ2ZhWWVORGsyNER6V05QSUl1Q1ZCdE5YOXI4RzlRb2RvWlFjZGg3KzY0MHZ6WThxUnY0akNEUTBwZ0lLNllxeXBKMDNRMG56Z3FXNzdmVzVxR3J3RndLeHdGSk1QcHVVZzdid1pPVmo5YUJxazVIRXpqdWpGeFV1LzZmMXpDS2F3b2Vla2ZjY25sbFNQVDQybitaSlJIMDBaNm1Pell5SWJiQnFtVUlib1djYWF1elF1Q2ROUzdBZk0ySzhOQUNaUW0vV3dvZ1JabXFjOUNrTmxzbnloOStmQ0xseDBDcnRkL1BudWN0OHBBdU9YUVR4M1ozY1cwblBMd3E0ZXdPdHo2bjRwWHVlRko3VHM5cmlvckp5aTc5TGJMd1ZBYmVLK2tQOVQ1VldNejJGZHErKzRrUnc2K012ZTE1ZnZYdVBBZHFEVVdHTHJHYThSOXVRZ0JrRmhXSDVCbm1FQWszaWpwRWtOb2ZIOVpWdDMxcS9ZeG1idW9pTENaMUdCWGhIVExDdjJOM2kzY09rMGEvRnBxMVdWb1lkbUtBNU5Ed053L2hXYm1MdGpNelkzaGM1RDVCUlpreUpUQzduTEhaOVVZZ0dublNYS3MvNTlzNG94THVFdGF5SXpvZW9GRkREekpxZjlIcUsiLCJtYWMiOiI0M2YzMjc5Y2VhNWRlNmEyOWU1YzAzODJhMmU4ZDY4YzE4ZGRmZjM1YWY4MjVmZjdhNzJiYjk3MjViZTM4NTZjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:11:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im8xYXlxL05iTVNjTkk0N0x3SGd2RXc9PSIsInZhbHVlIjoiVzB0ME03NVY3T2h2dGhJRlBLamJmZW43NDlXVXR5NWJZandWQ2NYUWpvcXgvVVZ5ZFJFdC9JRVhULzh0NU1icHpMajdlemhsR0ptMElkZ3VNa09oT0kzR3dRTU5EQWwrSy9ES0FkSS9kYVJHajRIOVFnVnlPS3IxbVc3NEtqVkdOWG1jajdIb3RXZDZIdTBBVnp1Qkp5aXZablIwTE5jeVVzVUR2SHhUTGJjeEQrTkdpKzhSQVNTNGJ5eXJrRXNRc0MyOTFGSUo2akNsWHE4VVNsQ3JMWjVoU3FxZWVGZWtJcldhamdJN1F2OTZzMmFMWWxlbHE0ai9ZV00rL2RuaVpoQ2ZyaUFTekNqelBYUms3Zk5JQ1FUTm9maEloOVRjVHk3bExkSVdlamd6bGxOMkg5ci96UnBKTWlIdzI2RFI5TXEwS1hJWVFPVy90VmVBNUZCRFFPYVNEOWQvQVlGNUZjNUNvM0VZTStCK1JsbGtXWnFITEI4TUhOTUU5bDl4bnQyQkxveG5uejRJR0xMZjR3MkpNYWNUTHpORnhtRVZ1RGlNdms2dW9CWlRTaFBLRGJsOVFtNzdSR1NBU2J6VU9iTUVCY3NzTXc1SHpFc2dMMkwxNlhCcnBlYjd0ZnYzZ0hzVURmdzhDZmw4VjhCYWQrK3pBcWMwSkxuZ1c2YjkiLCJtYWMiOiJlMmU0MTlmZTAxY2QyZmJhYzVmYzY3OGViNTQwOGI3Yjg3MGVhYzM3YTM0YThjZmU0NmZjODgwMDAzMzM3YjdjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:11:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlYyMm1hV3pWUjJMaVlJdklrQ0hUYlE9PSIsInZhbHVlIjoiSDEyQUg0V0F5SVkrZ0VSNG51MEFsTWhnZ2ZhWWVORGsyNER6V05QSUl1Q1ZCdE5YOXI4RzlRb2RvWlFjZGg3KzY0MHZ6WThxUnY0akNEUTBwZ0lLNllxeXBKMDNRMG56Z3FXNzdmVzVxR3J3RndLeHdGSk1QcHVVZzdid1pPVmo5YUJxazVIRXpqdWpGeFV1LzZmMXpDS2F3b2Vla2ZjY25sbFNQVDQybitaSlJIMDBaNm1Pell5SWJiQnFtVUlib1djYWF1elF1Q2ROUzdBZk0ySzhOQUNaUW0vV3dvZ1JabXFjOUNrTmxzbnloOStmQ0xseDBDcnRkL1BudWN0OHBBdU9YUVR4M1ozY1cwblBMd3E0ZXdPdHo2bjRwWHVlRko3VHM5cmlvckp5aTc5TGJMd1ZBYmVLK2tQOVQ1VldNejJGZHErKzRrUnc2K012ZTE1ZnZYdVBBZHFEVVdHTHJHYThSOXVRZ0JrRmhXSDVCbm1FQWszaWpwRWtOb2ZIOVpWdDMxcS9ZeG1idW9pTENaMUdCWGhIVExDdjJOM2kzY09rMGEvRnBxMVdWb1lkbUtBNU5Ed053L2hXYm1MdGpNelkzaGM1RDVCUlpreUpUQzduTEhaOVVZZ0dublNYS3MvNTlzNG94THVFdGF5SXpvZW9GRkREekpxZjlIcUsiLCJtYWMiOiI0M2YzMjc5Y2VhNWRlNmEyOWU1YzAzODJhMmU4ZDY4YzE4ZGRmZjM1YWY4MjVmZjdhNzJiYjk3MjViZTM4NTZjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:11:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im8xYXlxL05iTVNjTkk0N0x3SGd2RXc9PSIsInZhbHVlIjoiVzB0ME03NVY3T2h2dGhJRlBLamJmZW43NDlXVXR5NWJZandWQ2NYUWpvcXgvVVZ5ZFJFdC9JRVhULzh0NU1icHpMajdlemhsR0ptMElkZ3VNa09oT0kzR3dRTU5EQWwrSy9ES0FkSS9kYVJHajRIOVFnVnlPS3IxbVc3NEtqVkdOWG1jajdIb3RXZDZIdTBBVnp1Qkp5aXZablIwTE5jeVVzVUR2SHhUTGJjeEQrTkdpKzhSQVNTNGJ5eXJrRXNRc0MyOTFGSUo2akNsWHE4VVNsQ3JMWjVoU3FxZWVGZWtJcldhamdJN1F2OTZzMmFMWWxlbHE0ai9ZV00rL2RuaVpoQ2ZyaUFTekNqelBYUms3Zk5JQ1FUTm9maEloOVRjVHk3bExkSVdlamd6bGxOMkg5ci96UnBKTWlIdzI2RFI5TXEwS1hJWVFPVy90VmVBNUZCRFFPYVNEOWQvQVlGNUZjNUNvM0VZTStCK1JsbGtXWnFITEI4TUhOTUU5bDl4bnQyQkxveG5uejRJR0xMZjR3MkpNYWNUTHpORnhtRVZ1RGlNdms2dW9CWlRTaFBLRGJsOVFtNzdSR1NBU2J6VU9iTUVCY3NzTXc1SHpFc2dMMkwxNlhCcnBlYjd0ZnYzZ0hzVURmdzhDZmw4VjhCYWQrK3pBcWMwSkxuZ1c2YjkiLCJtYWMiOiJlMmU0MTlmZTAxY2QyZmJhYzVmYzY3OGViNTQwOGI3Yjg3MGVhYzM3YTM0YThjZmU0NmZjODgwMDAzMzM3YjdjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:11:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1964397393 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://127.0.0.1:8000/users/79/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1964397393\", {\"maxDepth\":0})</script>\n"}}