{"__meta": {"id": "X0ebc2e628c2195cf34393d06c995aefa", "datetime": "2025-07-31 06:00:40", "utime": **********.730578, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753941639.90087, "end": **********.730614, "duration": 0.8297438621520996, "duration_str": "830ms", "measures": [{"label": "Booting", "start": 1753941639.90087, "relative_start": 0, "end": **********.641998, "relative_end": **********.641998, "duration": 0.7411279678344727, "duration_str": "741ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.642012, "relative_start": 0.****************, "end": **********.730618, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "88.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rrACNpNzGL3ycNyPkqW3rR3b0DPMwuTJVX1UKEOl", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1406013127 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1406013127\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1708517159 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1708517159\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1749915416 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1749915416\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1840402319 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1840402319\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1514944356 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1514944356\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:00:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNqR3hHLzhDejFyaE5qWExZNFg3Qnc9PSIsInZhbHVlIjoiWmFJMW91RlE3RVB6ZGp0VTdMd3FxZlVCYXVEcTZTbGxlK1A0QndqaENmd1VteWhVemJNdlBBSFpwcXErZjB3b25kWEVzODhBQzRKenhlR3liUk1SZnQraWNnbWwrL3BBYVpRUWhoVEg4K21yMjJoeDI1YU04Vml5dkFJbFJ5azROdHllRmp1MmNJNGtSOEJISmNFQ0lvaFNQUmIrcncxOExXcW1BRXUrL2dodE10a2YvYzJhUFJ1SGF5L3lFYUlINURhWDNnck1BcDkwOEplTU9VVmpMTnJZd2Q1aXlVQkx1WVVtY0pSZnhTZkZIQ2Exci8xbTd3aHB3MXVMUHVva3VzYkE4TE9JVzVIejVvLzdqanJ0MERiQTE5MzEzd2V2UDhwQ0dDRVo2U1NKY3NITWY2V2pYazNlMEQvclQ2VXRKTm4vUGU5UmlvL3BSQ2ltKzN3UnE2Y3BZTGNISGVzMUNXYU90WkVDZzg5ODF6elE1VHJlMEpzM1BuTjNlL29jRXdIVFhCL0FjOE9GNEF2TXNxbS9uZkRNVXA0QVFNZmt6cFF2UUJMa2p2MWV1TkVwMmgvMkhyaThaT0NYN3A2eXhXSk9ZL2VkaTFDQVh0UFpRWWFEQ0k5OEYzNy8wa1JKMkgwZVJSd2s5SHRXTWwvZytZbU1qOVV1UEpMRVU3dTEiLCJtYWMiOiI0MjQxNTY1N2JhNGJlOWI4MzlhYWRhZmQ5ZDBjYWVmNzM4YzI5MGIyZGRlYzc4MGE5NjM4MjVkODRjNmI5NTg0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:00:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjFXOHNPc1JkOGtrOElSL3o5Q0JRU3c9PSIsInZhbHVlIjoiOHliVU9EVndjRFAzVUZmNGZVdVNqT0FmUVNBc3BhdWZYcVJHQngwYmZZMFVSK0JBdjg2Vys3a2xrcC9keXUxMURzTjBDRG91d2QxaWkwYmk3SWxVbG9RUnB0M2xDa0VmM00zUVlLVlUvenQ5b2d3Rit6QlNndDJrRE1sQlplbzdOQWNJdnBoSDZKdG5JR1pXM3BDUUVrNmg4VjR3cUVNL3FwMkV5MEprdzVaNFBUeWF4RzF0RG9JSWtFY2tyWGRTTDk5Y0pQWmtwd2RVZFNwMnVJSFBrMnRWYmJSdXNYYVdmRTV0T09PQUdvS3BmV0daYXZoNjYyNDVQQ0RXUFV3L3JlenY4dXVuMmhDWTNrbkJvVmg5NDdETTgyK3hIU013L3FEYzVpR2dVbDlmQmw2TmZ2eFh6Umk2SHdjQXZSK3IrdWhhWGlEb1YxN3FlbmpUWDhDLzhaSkFGRllsUTJta1NoY1hJYlJpOVd1VFVmdGhPWUZFVXNQck9DbDAyNFV1NFJxRDB5bEdDR1ZESWlhNHgxY2pwa0l3bVlGcTFJWTZDMGxQTGx2Vm03bC90WmI5a1VNU2s3ZmFZVnJWT2NxM1FvMDdQc3oxVGxsNTI3aWYwVzBXY2p4WmFHSkRkU3pSZGJwcWJIN2wxOXpEZ3lTdTZON3RuNlpYdXhKV282Y1ciLCJtYWMiOiJhMDk4N2RhNzkyOGFkOTU1MWYwNmEyMzYxYjAyZmUwMTVhNGNmZDI5ZDVmZjEyNGQ2MWJiMmIwN2E3NTgxMWFlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:00:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNqR3hHLzhDejFyaE5qWExZNFg3Qnc9PSIsInZhbHVlIjoiWmFJMW91RlE3RVB6ZGp0VTdMd3FxZlVCYXVEcTZTbGxlK1A0QndqaENmd1VteWhVemJNdlBBSFpwcXErZjB3b25kWEVzODhBQzRKenhlR3liUk1SZnQraWNnbWwrL3BBYVpRUWhoVEg4K21yMjJoeDI1YU04Vml5dkFJbFJ5azROdHllRmp1MmNJNGtSOEJISmNFQ0lvaFNQUmIrcncxOExXcW1BRXUrL2dodE10a2YvYzJhUFJ1SGF5L3lFYUlINURhWDNnck1BcDkwOEplTU9VVmpMTnJZd2Q1aXlVQkx1WVVtY0pSZnhTZkZIQ2Exci8xbTd3aHB3MXVMUHVva3VzYkE4TE9JVzVIejVvLzdqanJ0MERiQTE5MzEzd2V2UDhwQ0dDRVo2U1NKY3NITWY2V2pYazNlMEQvclQ2VXRKTm4vUGU5UmlvL3BSQ2ltKzN3UnE2Y3BZTGNISGVzMUNXYU90WkVDZzg5ODF6elE1VHJlMEpzM1BuTjNlL29jRXdIVFhCL0FjOE9GNEF2TXNxbS9uZkRNVXA0QVFNZmt6cFF2UUJMa2p2MWV1TkVwMmgvMkhyaThaT0NYN3A2eXhXSk9ZL2VkaTFDQVh0UFpRWWFEQ0k5OEYzNy8wa1JKMkgwZVJSd2s5SHRXTWwvZytZbU1qOVV1UEpMRVU3dTEiLCJtYWMiOiI0MjQxNTY1N2JhNGJlOWI4MzlhYWRhZmQ5ZDBjYWVmNzM4YzI5MGIyZGRlYzc4MGE5NjM4MjVkODRjNmI5NTg0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:00:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjFXOHNPc1JkOGtrOElSL3o5Q0JRU3c9PSIsInZhbHVlIjoiOHliVU9EVndjRFAzVUZmNGZVdVNqT0FmUVNBc3BhdWZYcVJHQngwYmZZMFVSK0JBdjg2Vys3a2xrcC9keXUxMURzTjBDRG91d2QxaWkwYmk3SWxVbG9RUnB0M2xDa0VmM00zUVlLVlUvenQ5b2d3Rit6QlNndDJrRE1sQlplbzdOQWNJdnBoSDZKdG5JR1pXM3BDUUVrNmg4VjR3cUVNL3FwMkV5MEprdzVaNFBUeWF4RzF0RG9JSWtFY2tyWGRTTDk5Y0pQWmtwd2RVZFNwMnVJSFBrMnRWYmJSdXNYYVdmRTV0T09PQUdvS3BmV0daYXZoNjYyNDVQQ0RXUFV3L3JlenY4dXVuMmhDWTNrbkJvVmg5NDdETTgyK3hIU013L3FEYzVpR2dVbDlmQmw2TmZ2eFh6Umk2SHdjQXZSK3IrdWhhWGlEb1YxN3FlbmpUWDhDLzhaSkFGRllsUTJta1NoY1hJYlJpOVd1VFVmdGhPWUZFVXNQck9DbDAyNFV1NFJxRDB5bEdDR1ZESWlhNHgxY2pwa0l3bVlGcTFJWTZDMGxQTGx2Vm03bC90WmI5a1VNU2s3ZmFZVnJWT2NxM1FvMDdQc3oxVGxsNTI3aWYwVzBXY2p4WmFHSkRkU3pSZGJwcWJIN2wxOXpEZ3lTdTZON3RuNlpYdXhKV282Y1ciLCJtYWMiOiJhMDk4N2RhNzkyOGFkOTU1MWYwNmEyMzYxYjAyZmUwMTVhNGNmZDI5ZDVmZjEyNGQ2MWJiMmIwN2E3NTgxMWFlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:00:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1071904683 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rrACNpNzGL3ycNyPkqW3rR3b0DPMwuTJVX1UKEOl</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1071904683\", {\"maxDepth\":0})</script>\n"}}