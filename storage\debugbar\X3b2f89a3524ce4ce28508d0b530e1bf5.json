{"__meta": {"id": "X3b2f89a3524ce4ce28508d0b530e1bf5", "datetime": "2025-07-31 06:25:29", "utime": **********.776734, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943127.621817, "end": **********.776792, "duration": 2.154974937438965, "duration_str": "2.15s", "measures": [{"label": "Booting", "start": 1753943127.621817, "relative_start": 0, "end": **********.592733, "relative_end": **********.592733, "duration": 1.9709157943725586, "duration_str": "1.97s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.592762, "relative_start": 1.***************, "end": **********.776798, "relative_end": 5.9604644775390625e-06, "duration": 0.****************, "duration_str": "184ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "IgbEestpZxHdlCVD9UGfhJImkkjatPTtzkY6eDAY", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-611080953 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-611080953\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1458697188 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1458697188\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1996524532 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1996524532\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-157162410 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-157162410\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2124248498 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2124248498\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-223778430 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:25:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkowY0djNGJEemVobDNPNXRlc2Frd0E9PSIsInZhbHVlIjoidWtqV1BUUndlMFhZaHN5MHdHT2RKK1kwQWMvOEg4eHJnQlg2b0tDdFR5KzVSS0IwWW51c1VadFpoNi9RU2YxT0hOUjQvWVpieDE4ODVISTBZL0pHcDdsL2ZsdWxqdHRFYmd4NWUzbUZ0QkdIenhscmE2eDNOSytFYTBGNlRBMklrN3J3S2ZVY2d4YTcyWTh5dk54cnlKUThUNUhYRnZVSkNER0NENXc1bENvSUpSTGRMYm83aWQvMWJGQythWGFzVEh2bk5QUFoza2F0TGdGZDMzaytsTjEvNCtieUxUQXBPTWx4QmZwL0h4SEgvY0MwdTY3OVVhWHFWS012UktZRTRmTUdmbUlzZloyZ2k0WGFUYXZKMTMydTZ0Vjlpc1dySDVnWEZZM1Q1anp5b1VuRzEra1A4WGdkTVFsbDJJU2pndzMwTGR2UEZ6Mjh5VTNXMjRiVm1raWdiQW9ZRDJXUUp2U1cwOEE4eG00R1BPbUtkTlFFazh2djFraFc3NVFEaXIyWE1Wdml2SjZaMmFSU1NWOG1nYlo2elNzMWwvM2crSWV4VjZLZUVnMzRxb1Y1MTI5aU9tcUFFZDV1VjZWQUxPT0JLcnVlaGdVRkYyRy9XcERJQXFvRnVyUkMxZWpxZ2RyT2dVR042bDlsYjYyMFl3aXY3eGNPTlNscFIxMmwiLCJtYWMiOiJkOTg4MjUyNjNiYWE3MGZlZGUxYTgwYWZkYzAxNGNiNWZkZGI2MGJjYTg4NmRlMGQwOTgyNjc2YzdhMjQ4ODkyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:25:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImtYeGFNV0hVcHk4b3o1aFdyb1dBc1E9PSIsInZhbHVlIjoic3Zvd25TTTdoZWdxSTVTLzVBQlhlaWp1Wk9EcVVaSEJ5MkdvMXFIV0dzRGxkQi9BZ0xOQnNJdysyRFVKYTMyVEtsYmQ5emN2SUJBTS9BbmhZTGF1VTR6aEY1eXJCOEx1NUZxNVVXSmw1Uy9jaytKRzV3cjJoYmNmWUUyT0V0akRmSjVnbllaNHUzdzZEQlltV3dPc2grZXVGRzM5SnY1TEM3emhxOHh1VGROd3RJbDNtM0t3QjkzYkZmZlJVUHQzVnZIU1lFbUlibk4xMGFDb3ptbFZTUE4vNVBybnU4aWZ3UTZGL0EwUEZzYTZkS05pT1l2b1lWcmNKbWR0TDZ5cFl5WGlzTWNzcnF3eGdJcUdSNHBBSG80aWR2MXAxQURVT1k0NTBTRDJCNTJjclEzUHNKbnYraGczb1N4RkFXWDBtVzdlcmgvcmxsWUxqcHJhSkI3Snh3cDlrSTVWKzEvL0xLZi9TRVRzTHVNek9zYlcvVVVmdTV2RkQ3dW1JcUUwdENudU8yRGVhZXlPZXd5NCtEZkxxSGRaMlNPZGNUK0pteHI4enFlaHFqK1dFbm5EWWNteDBaVTZTQ0dsQnczem42NDNyVzd6RDY3VjZPenlxVFNMai8wVU5vanJ1SWJZV29wejYyZ3J2eW00dER6ZElmYU8zWHpRdEkvUjIzS2YiLCJtYWMiOiI1ZjlhNjQ4N2JhNzMzYzk5OTEzY2JjNzYwMDVmNGFjNjQ0ZGZkNjg2MGZiYzIxYWRmZmE1Y2UwNGQ0ZDNlMDZiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:25:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkowY0djNGJEemVobDNPNXRlc2Frd0E9PSIsInZhbHVlIjoidWtqV1BUUndlMFhZaHN5MHdHT2RKK1kwQWMvOEg4eHJnQlg2b0tDdFR5KzVSS0IwWW51c1VadFpoNi9RU2YxT0hOUjQvWVpieDE4ODVISTBZL0pHcDdsL2ZsdWxqdHRFYmd4NWUzbUZ0QkdIenhscmE2eDNOSytFYTBGNlRBMklrN3J3S2ZVY2d4YTcyWTh5dk54cnlKUThUNUhYRnZVSkNER0NENXc1bENvSUpSTGRMYm83aWQvMWJGQythWGFzVEh2bk5QUFoza2F0TGdGZDMzaytsTjEvNCtieUxUQXBPTWx4QmZwL0h4SEgvY0MwdTY3OVVhWHFWS012UktZRTRmTUdmbUlzZloyZ2k0WGFUYXZKMTMydTZ0Vjlpc1dySDVnWEZZM1Q1anp5b1VuRzEra1A4WGdkTVFsbDJJU2pndzMwTGR2UEZ6Mjh5VTNXMjRiVm1raWdiQW9ZRDJXUUp2U1cwOEE4eG00R1BPbUtkTlFFazh2djFraFc3NVFEaXIyWE1Wdml2SjZaMmFSU1NWOG1nYlo2elNzMWwvM2crSWV4VjZLZUVnMzRxb1Y1MTI5aU9tcUFFZDV1VjZWQUxPT0JLcnVlaGdVRkYyRy9XcERJQXFvRnVyUkMxZWpxZ2RyT2dVR042bDlsYjYyMFl3aXY3eGNPTlNscFIxMmwiLCJtYWMiOiJkOTg4MjUyNjNiYWE3MGZlZGUxYTgwYWZkYzAxNGNiNWZkZGI2MGJjYTg4NmRlMGQwOTgyNjc2YzdhMjQ4ODkyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:25:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImtYeGFNV0hVcHk4b3o1aFdyb1dBc1E9PSIsInZhbHVlIjoic3Zvd25TTTdoZWdxSTVTLzVBQlhlaWp1Wk9EcVVaSEJ5MkdvMXFIV0dzRGxkQi9BZ0xOQnNJdysyRFVKYTMyVEtsYmQ5emN2SUJBTS9BbmhZTGF1VTR6aEY1eXJCOEx1NUZxNVVXSmw1Uy9jaytKRzV3cjJoYmNmWUUyT0V0akRmSjVnbllaNHUzdzZEQlltV3dPc2grZXVGRzM5SnY1TEM3emhxOHh1VGROd3RJbDNtM0t3QjkzYkZmZlJVUHQzVnZIU1lFbUlibk4xMGFDb3ptbFZTUE4vNVBybnU4aWZ3UTZGL0EwUEZzYTZkS05pT1l2b1lWcmNKbWR0TDZ5cFl5WGlzTWNzcnF3eGdJcUdSNHBBSG80aWR2MXAxQURVT1k0NTBTRDJCNTJjclEzUHNKbnYraGczb1N4RkFXWDBtVzdlcmgvcmxsWUxqcHJhSkI3Snh3cDlrSTVWKzEvL0xLZi9TRVRzTHVNek9zYlcvVVVmdTV2RkQ3dW1JcUUwdENudU8yRGVhZXlPZXd5NCtEZkxxSGRaMlNPZGNUK0pteHI4enFlaHFqK1dFbm5EWWNteDBaVTZTQ0dsQnczem42NDNyVzd6RDY3VjZPenlxVFNMai8wVU5vanJ1SWJZV29wejYyZ3J2eW00dER6ZElmYU8zWHpRdEkvUjIzS2YiLCJtYWMiOiI1ZjlhNjQ4N2JhNzMzYzk5OTEzY2JjNzYwMDVmNGFjNjQ0ZGZkNjg2MGZiYzIxYWRmZmE1Y2UwNGQ0ZDNlMDZiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:25:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-223778430\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1370937635 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IgbEestpZxHdlCVD9UGfhJImkkjatPTtzkY6eDAY</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1370937635\", {\"maxDepth\":0})</script>\n"}}