{"__meta": {"id": "X92c5cb8b73a09c691c59ec2677433021", "datetime": "2025-07-31 05:24:48", "utime": **********.233565, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939486.457674, "end": **********.233603, "duration": 1.7759289741516113, "duration_str": "1.78s", "measures": [{"label": "Booting", "start": 1753939486.457674, "relative_start": 0, "end": **********.122176, "relative_end": **********.122176, "duration": 1.6645019054412842, "duration_str": "1.66s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.122202, "relative_start": 1.****************, "end": **********.233606, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "5UhMqM0FcGgHHvKVv6ggDWR0HttmRLcrBuT48DiQ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-956059995 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-956059995\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1290985437 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1290985437\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-403800308 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-403800308\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1680967246 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1680967246\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1478610476 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1478610476\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1118504584 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:24:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InIwRnpMMFdkMEFONjV3bmphdDF0elE9PSIsInZhbHVlIjoidmszYmozSldveE5oU01CeE14QVpzVzRWUVA0WTFMbmxiQ0JYakNhL3llZitmR042cVVRamNoRjEySGQyRC8wdlhpWFBiZSs0dGtBM2tsRXNPL0sxUlVtRnhVNWM2Ukp0TW5rSzJ5SUtCM0JyM1oxOTQwYVRCOGt3aEJPbjcrWjJnNTl0cDQxaklQRDFQeUp3QUVkZTlMOE1Dbnh4WlFqdFgwb2YrUVJmWW5XdkQxcEV5OHpHd2lJUTB4OWcwRDJVWjBFSGgxNEVmWFdWRkU5aDdEQVVhd2JDdW13NkRJZmZkZlc1NTdhR0lja29obk1acmVDK2lJZHBKTXdYSEkxTnIrZWxYRWZ2cXpidUNUVDRrLzVyWUMxN0FTZ0dncjBwcWNuUCt3MEp0eUgvWm1WQTVhM2ZmREZQYjdBY0dCMG8xVjhFRHZ6MUowemMycS9TeUMySmM5cDVQU1JQS3gyOEd0RktPYVNHd0hDTlF3LzQzcnJySnJ3NDYyM0dDY2VsdTRjWU1ZYWxCaXV2dW91dHVJSlJQVVdOcHlqYUQ0R2RETU1UMVlmbnFuTGUyN2NjaWdtc2kxbnpxQjNUdTQxcTZ4VWVIdEUrK3NsbUkxdGd6K0l5MEx4UUFGU2g1dEJaWTNVWVRURGxyWldadzV1cE9BWXI1MG1OM0szUmtnVnciLCJtYWMiOiJkZDdiOWNjMDM0ZWRhZThmNjA5NWVlZGZmNzMzNmU3ZDZkYjliMjg2YjcyODkzNzI2ZTA5ZjhlZDViNDhiNGE5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:24:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InV3UCtMMW5vTW5MZWlSUzkxTjFnZFE9PSIsInZhbHVlIjoiWWdLT1NDYVFTUE90Y1hBMHdkc1JLbW5SamZmVktwRDlORHhQWnBySFhWdUN5RmplVTh2QVkrYUdkdkk0aW83VWRMMmxRMWRXN1U3SEc4aGpTYkpkMkJzOWk3YkJobmVIWXR2SWdOTVkxRG1FZHVUOVNxYWFJcHVUVmNtNXpiTUxIVi9BbnZpNkpKOTJrM0twS1dOTFZYTVM5a2RaenRkc0JZdCswMjYrYW9NNnN1VzRxcWg2SzNvYkZKWnRHZnNUM0Z4UHE3UlFwUjFuNko5Skp1NEw2SUxHVmk0VE1RTElCVm44NERxMFc0QzViK0NHdXVDckVTSVZvcTB6eFBIeFcxcXhqazNKQ3ZCbEtaMURkQWtmZzg5d1hRd2oyaVpINWg2RWpweDFVSTJsdDFHSytCNS9zeTZvalcrdFkvczY5Y2NHNE1BRGFRdFhqakpkaWlHZTJqQjEwTWpXeWt2MVJRVkg0dWl4dTFXL2daazBtMUN6RElIL05LM1dBQ1RIelk3Zm1zR3E3aGN3dnJmSEpQdGFUaEg0ZldVV0JrUStJNXFaRXVTU1Y3ci8zUjM2U3RoVGcxNEwyNGM4QjRuQUhJT09RcThWZStJLzlKRThUaVllWU9ydmM0ZHlVWHZKaG16azdNdkV2Y29Ba3NnNGQzQnVTRERQOHBOODJGK0giLCJtYWMiOiJlZWY3YzI5ZTEyNjg1MjlkYmE4OTc3YmQzZGUyOTQ4MGU0ODBlZjhkMGZkYmViNjNiYzNjMDAzYzE0NmQ0YzA3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:24:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InIwRnpMMFdkMEFONjV3bmphdDF0elE9PSIsInZhbHVlIjoidmszYmozSldveE5oU01CeE14QVpzVzRWUVA0WTFMbmxiQ0JYakNhL3llZitmR042cVVRamNoRjEySGQyRC8wdlhpWFBiZSs0dGtBM2tsRXNPL0sxUlVtRnhVNWM2Ukp0TW5rSzJ5SUtCM0JyM1oxOTQwYVRCOGt3aEJPbjcrWjJnNTl0cDQxaklQRDFQeUp3QUVkZTlMOE1Dbnh4WlFqdFgwb2YrUVJmWW5XdkQxcEV5OHpHd2lJUTB4OWcwRDJVWjBFSGgxNEVmWFdWRkU5aDdEQVVhd2JDdW13NkRJZmZkZlc1NTdhR0lja29obk1acmVDK2lJZHBKTXdYSEkxTnIrZWxYRWZ2cXpidUNUVDRrLzVyWUMxN0FTZ0dncjBwcWNuUCt3MEp0eUgvWm1WQTVhM2ZmREZQYjdBY0dCMG8xVjhFRHZ6MUowemMycS9TeUMySmM5cDVQU1JQS3gyOEd0RktPYVNHd0hDTlF3LzQzcnJySnJ3NDYyM0dDY2VsdTRjWU1ZYWxCaXV2dW91dHVJSlJQVVdOcHlqYUQ0R2RETU1UMVlmbnFuTGUyN2NjaWdtc2kxbnpxQjNUdTQxcTZ4VWVIdEUrK3NsbUkxdGd6K0l5MEx4UUFGU2g1dEJaWTNVWVRURGxyWldadzV1cE9BWXI1MG1OM0szUmtnVnciLCJtYWMiOiJkZDdiOWNjMDM0ZWRhZThmNjA5NWVlZGZmNzMzNmU3ZDZkYjliMjg2YjcyODkzNzI2ZTA5ZjhlZDViNDhiNGE5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:24:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InV3UCtMMW5vTW5MZWlSUzkxTjFnZFE9PSIsInZhbHVlIjoiWWdLT1NDYVFTUE90Y1hBMHdkc1JLbW5SamZmVktwRDlORHhQWnBySFhWdUN5RmplVTh2QVkrYUdkdkk0aW83VWRMMmxRMWRXN1U3SEc4aGpTYkpkMkJzOWk3YkJobmVIWXR2SWdOTVkxRG1FZHVUOVNxYWFJcHVUVmNtNXpiTUxIVi9BbnZpNkpKOTJrM0twS1dOTFZYTVM5a2RaenRkc0JZdCswMjYrYW9NNnN1VzRxcWg2SzNvYkZKWnRHZnNUM0Z4UHE3UlFwUjFuNko5Skp1NEw2SUxHVmk0VE1RTElCVm44NERxMFc0QzViK0NHdXVDckVTSVZvcTB6eFBIeFcxcXhqazNKQ3ZCbEtaMURkQWtmZzg5d1hRd2oyaVpINWg2RWpweDFVSTJsdDFHSytCNS9zeTZvalcrdFkvczY5Y2NHNE1BRGFRdFhqakpkaWlHZTJqQjEwTWpXeWt2MVJRVkg0dWl4dTFXL2daazBtMUN6RElIL05LM1dBQ1RIelk3Zm1zR3E3aGN3dnJmSEpQdGFUaEg0ZldVV0JrUStJNXFaRXVTU1Y3ci8zUjM2U3RoVGcxNEwyNGM4QjRuQUhJT09RcThWZStJLzlKRThUaVllWU9ydmM0ZHlVWHZKaG16azdNdkV2Y29Ba3NnNGQzQnVTRERQOHBOODJGK0giLCJtYWMiOiJlZWY3YzI5ZTEyNjg1MjlkYmE4OTc3YmQzZGUyOTQ4MGU0ODBlZjhkMGZkYmViNjNiYzNjMDAzYzE0NmQ0YzA3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:24:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1118504584\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1960639504 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5UhMqM0FcGgHHvKVv6ggDWR0HttmRLcrBuT48DiQ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960639504\", {\"maxDepth\":0})</script>\n"}}