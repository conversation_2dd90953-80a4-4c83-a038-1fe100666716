<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class InstallmentPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'plan_id',
        'customer_id',
        'customer_name',
        'customer_email',
        'customer_phone',
        'product_id',
        'product_name',
        'product_price',
        'quantity',
        'down_payment',
        'paid_amount',
        'pending_amount',
        'discount_amount',
        'total_amount',
        'status',
        'start_date',
        'end_date',
        'next_installment_date',
        'total_installments',
        'paid_installments',
        'installment_amount',
        'payment_frequency',
        'description',
        'notes',
        'receipt_url',
        'payment_method',
        'created_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'next_installment_date' => 'date',
        'product_price' => 'decimal:2',
        'down_payment' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'pending_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'installment_amount' => 'decimal:2',
    ];

    /**
     * Generate unique plan ID
     */
    public static function generatePlanId()
    {
        do {
            $id = 'INS-' . date('Y') . '-' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (self::where('plan_id', $id)->exists());

        return $id;
    }

    /**
     * Get the customer that owns the installment plan
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the product associated with the installment plan
     */
    public function product()
    {
        return $this->belongsTo(ProductService::class, 'product_id');
    }

    /**
     * Get the user who created the installment plan
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get installment payments
     */
    public function payments()
    {
        return $this->hasMany(InstallmentPayment::class);
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClass()
    {
        return match($this->status) {
            'active' => 'bg-success',
            'completed' => 'bg-primary',
            'cancelled' => 'bg-danger',
            'paused' => 'bg-warning',
            'overdue' => 'bg-danger',
            default => 'bg-secondary'
        };
    }

    /**
     * Get formatted status
     */
    public function getFormattedStatus()
    {
        return ucfirst($this->status);
    }

    /**
     * Calculate next installment date
     */
    public function calculateNextInstallmentDate()
    {
        if (!$this->next_installment_date) {
            return null;
        }

        $nextDate = $this->next_installment_date;
        
        switch ($this->payment_frequency) {
            case 'weekly':
                return $nextDate->addWeek();
            case 'monthly':
                return $nextDate->addMonth();
            case 'quarterly':
                return $nextDate->addMonths(3);
            case 'yearly':
                return $nextDate->addYear();
            default:
                return $nextDate->addMonth();
        }
    }

    /**
     * Check if installment plan is overdue
     */
    public function isOverdue()
    {
        return $this->next_installment_date && 
               $this->next_installment_date->isPast() && 
               in_array($this->status, ['active', 'paused']);
    }

    /**
     * Get remaining installments
     */
    public function getRemainingInstallments()
    {
        return max(0, $this->total_installments - $this->paid_installments);
    }

    /**
     * Get progress percentage
     */
    public function getProgressPercentage()
    {
        if ($this->total_installments == 0) {
            return 0;
        }
        
        return round(($this->paid_installments / $this->total_installments) * 100, 2);
    }

    /**
     * Get remaining amount
     */
    public function getRemainingAmount()
    {
        return $this->total_amount - $this->paid_amount;
    }

    /**
     * Check if plan is completed
     */
    public function isCompleted()
    {
        return $this->paid_installments >= $this->total_installments || 
               $this->paid_amount >= $this->total_amount;
    }

    /**
     * Update status based on payments
     */
    public function updateStatus()
    {
        if ($this->isCompleted()) {
            $this->status = 'completed';
        } elseif ($this->isOverdue()) {
            $this->status = 'overdue';
        } elseif ($this->status === 'overdue' && !$this->isOverdue()) {
            $this->status = 'active';
        }
        
        $this->save();
    }

    /**
     * Scope for active plans
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for overdue plans
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue')
                    ->orWhere(function($q) {
                        $q->where('status', 'active')
                          ->where('next_installment_date', '<', now());
                    });
    }

    /**
     * Scope for completed plans
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }
}
