{"__meta": {"id": "X9c7cf6603775e6a359f021778a39bdac", "datetime": "2025-07-31 06:32:35", "utime": **********.646883, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:32:35] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.63867, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943553.189238, "end": **********.646945, "duration": 2.457706928253174, "duration_str": "2.46s", "measures": [{"label": "Booting", "start": 1753943553.189238, "relative_start": 0, "end": **********.382322, "relative_end": **********.382322, "duration": 2.1930840015411377, "duration_str": "2.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.382372, "relative_start": 2.19313383102417, "end": **********.646951, "relative_end": 5.9604644775390625e-06, "duration": 0.26457905769348145, "duration_str": "265ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45918384, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02022, "accumulated_duration_str": "20.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.53287, "duration": 0.01686, "duration_str": "16.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 83.383}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.581075, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 83.383, "width_percent": 6.33}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5914161, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 89.713, "width_percent": 4.847}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6021209, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 94.56, "width_percent": 5.44}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1564791485 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1564791485\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-871466043 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-871466043\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-497107953 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-497107953\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjhwUTQ2Z3BndW9xZnJhVnU2R2NhNWc9PSIsInZhbHVlIjoiR3kwamhNTFZMMGpsMlBUY0ZlcjNVekRHQU9nZEFIVTB4TTVXWTFkclRmb2lNYWI1Ni9HUktUWUZNUlh5ZFVCc29BU0dBem1ON0xjaEpudmx5ejFMYUxIdjhYMWFZUjVLMHVoSmRBcGphTnBYbjJEaXdwallaZldla1pSTXNDZWdnN2E2dFp3ajEzZk52cnVTVTNHS3lyVDYwYi9ydE5JZTdScFkrdVlMMVJrelIrekorajZmSW1OZzhFbUZveUdNOFVpa2FjTjFKMUlheFhwUjNvZ3BDVjNDK1pxcjd5WUdRMUNjcTZDZzA1b1RNd0Qzd0xVaWEzcmdwcVdtblk5OThWZWJDMUxDdzNhZ1ZjN0V3VjZOYTZRSC9HcGlTTzFrUnprWndCRldkNFgxVDVHaXRBUnkrUUtmZFFudDRCMWtCUFhNUC8wa2JGaHdhdFduOGNQdVhFc09CY2FPV3B1ZXVsQ3JmaVJqWjFiTVVPZWl4cXU3amgxZ1luNHh4clo3RnpSVlN1NGxDZlJhVGlCOXJ6cTQ3eTJjaHFwcXRvaGIwdTM3ckF5NWNsS3dFZkpMQVBCcENJcDNjWVIrOVVha0c0MDJGN2s1eGlzS0ZzdnJmbDFKOUVNdm9xQzVTL3Rrd3hpY2RrYTFUNFVFdlB2TnlMRlk3NjJndFhOUlpZelMiLCJtYWMiOiIzM2MyYWI3NTJiNTdjYWY1ZGExMDAwODk4NzIxMjIzZDk4MjlmYWQ3N2Q1MmY3ZWFkYjZkYjA2YTE5YjI0NmQ0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlNXaTJ6bGVCYkxSLzNHZHE5N0hTYXc9PSIsInZhbHVlIjoiTS9qTUQ5MFFKWFNvRnZ5dzhKa1h4THo3QTlHQzZvY3RIMEJ6S2N3bitqQjI3Y3EyMHg3a3R4czdpNkdzdWl2MlpDM1RWbEdrSEsxcXFscXY1ZDdoanI0TU5NZTVkdnhGSWErTXN4QmZpQ1BEZHJ5U1M4b1lXdVhMZHhhRWw4cFhhRHlXbDRzemtKdGo2U1EzYWp2dlh5RmI2ZnRoc0ZKdnFvcjBsVzBDNDJFZElwNjczU3pHcDIzSEdHVnAvQjg3MW0yRytkb0doZmk0RFVocEVrN3lhcGtGZkp0WE95bDRTWE05cjJuSHpjSmlGZHczWHlVclBzWVhVNVZaUkNVWjhOdXd6S2xXZG4zTUFnOHFUd3RBWGpuU0tZWlFLNmNIV2paSjZPU01SWWg0YmNQeGMvd0t0dVQ4ck5qNmhGd3Foc2R3NnFuZ3AyaGU0YjJhY0JnY0Ixd3BKYUlrcE15d2hPWWZHK2Q4U0NEYzEzbzlmVFh6bGJaUmk3ZXVrSnR4OVpwY29jNUg2OTNiUk5sRzJTWGNia2tGTDhuZkwralJsaGNNb2xnK1hKNytlUTRLNjdNbHNyZXZoWjFRejRlajN6ODNiU00reHlqSGkwTFNmY1lYZCtWb25tUWZhZXZiaTgxcGdFUzViRExsbGRURTFzenJkNjBoemNJaVkzdFgiLCJtYWMiOiIyOTM1NmQ2Y2NmNjMxNGU2MzFlOTUwMTE4ZTU0NGU1ZGFkNjk2M2I2YjA5ZDE5ODhjN2M2MDE1Y2QzYWEyY2Q2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2021737492 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021737492\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-822873836 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:32:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJKd2hBOHMvKzZvWE5lb2d2QktTQVE9PSIsInZhbHVlIjoienZmdUdDWDRGdnNhRlIxUVdTSTBuSWNVOTdxdE5Sbm8vdHJsZTdTVThtMzFBbGVTYUVCV2NUQjUwSGNCY1NNZFdaRVpaV1Q3aUtEcVlxdnQ5eVQyb3VhRFNiREllNTQrMHAwY2VRdTcvWmwwQzI2NXFoRWg2dUtOdGYrVm5UcWdYNXprS3JaTDR4YUkwTHZvU1VabG1hU1QxSmVQRDJ6MGR2WksrNVpOb0ZGKzJCeEhMUmNCV3hhMWFGaTF5TVBqK1ZSSHAzamE0WWtKNnRJT2Nic2dVcHc1RG4vQTljVkJtdzlRanJGajQ2YUYrQlptRXI2RE1vb1Z5NXk0SWE2YWtuSnpQRmwvWGZOQ0RCZTFmSTZRMGxNbUJZOTJBdERFWERpaVplaHFlTENjWXllOGRmMjNLSkRLQ3krKzhVSDJuU0hvSjVUQ0FVMzRrT2I5M1VqTXpJU09OTksrL0RtbmxvYkk5MDRrRk1OOUdybENzNGZCNjVPQnpiRnhEd0RQaXNGMjVGUDhvSGViWnFUOXJpVEtyb2pFRDBZaVJMUHV3aXpIR1hQaDFiZUJyMUx3M08zMmJMRXRhUmxVcGtwNjI3aFFCZnN2TG1sVW9YZkl2dUtZNVE2REM0bWJaZ05RUTVBb2QrUXlrNVlxVTNjS1FZN0FDazFCa1k3QmVObHMiLCJtYWMiOiJkMDgxZWE3YjRkMWRiNGY2MzQ0MGU0MmMyYTFlYmI0ZDg4ZTA0MThiYzZlZTU0N2NiNDg4ZDgyOTFhMjJjNmE3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkdlN3pwYmJlV2tHNmplaUVtMEFoY0E9PSIsInZhbHVlIjoiNnE0Zzd0dkJYTnQrclVKbVFySXBRcSthV2FZS0t3dGgraUhna1BSTGRZMGV4Y005R3BhS0todTNmV2lTMlIveWVaRFY0VmNWWkpaTXZEUTJjZ3dKN2FxNWpjbWhRQ3F4L25HTjZNTWxMaXBQeDlTNlI1YkZ5WFVnTmo0RTJYR3dWeXlTNzZTbjI2UjNtQzRrZ3R2eHE2TEJGb2ZLY3lqZDg2Qm5RU1dVSTNhcWhYV3BhRmVyUkN3M2JwbDVkWmRLZnNYbGdvcjZzMGlFb09pVXJNakEzL3UvTDVST1I2d3VKTHcrSTArNHdDemxVTUQxaXN3ZS83Z1p6VTZwYUlLdUo4Q3Z2ODdPa04ySzJZcW1mNWdlNFFTSDJBVEdGaDRzd0xXekF2UnpBYXg4b0lSTlRVRUF1eUVYaWU4VHJEMmg5RHlHU091YzNkQm9ibitvMHpLUjN5c0x2SkNZbDV4SkRVdEJjaC9aRCtCME02WVplaHpwU1RHeExuQmE2dnB0SFRiaUcwNGdrYllpV2xadFVJL2NUb290aDZkdW1JQ1ZkVTlsUEgraE9NeWhheW1JajVQRmdOU28zdVNqa1E4WTFOc0IxU29Xc3hEQW11bGdhdW9yTG0wM0FWbVl2UzA0eTlUMlQ2QnRIOCtFaUdmbTdFeTA5eGhwODVpTXpCNmwiLCJtYWMiOiI3NDBhZTExZmMyZDA0MTExMzE1MTUyYTg0MDExY2Q0YjJkOTQzMzE2ODA4NzM4NWFlMGFhZGM3MWYzNTdjODc5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJKd2hBOHMvKzZvWE5lb2d2QktTQVE9PSIsInZhbHVlIjoienZmdUdDWDRGdnNhRlIxUVdTSTBuSWNVOTdxdE5Sbm8vdHJsZTdTVThtMzFBbGVTYUVCV2NUQjUwSGNCY1NNZFdaRVpaV1Q3aUtEcVlxdnQ5eVQyb3VhRFNiREllNTQrMHAwY2VRdTcvWmwwQzI2NXFoRWg2dUtOdGYrVm5UcWdYNXprS3JaTDR4YUkwTHZvU1VabG1hU1QxSmVQRDJ6MGR2WksrNVpOb0ZGKzJCeEhMUmNCV3hhMWFGaTF5TVBqK1ZSSHAzamE0WWtKNnRJT2Nic2dVcHc1RG4vQTljVkJtdzlRanJGajQ2YUYrQlptRXI2RE1vb1Z5NXk0SWE2YWtuSnpQRmwvWGZOQ0RCZTFmSTZRMGxNbUJZOTJBdERFWERpaVplaHFlTENjWXllOGRmMjNLSkRLQ3krKzhVSDJuU0hvSjVUQ0FVMzRrT2I5M1VqTXpJU09OTksrL0RtbmxvYkk5MDRrRk1OOUdybENzNGZCNjVPQnpiRnhEd0RQaXNGMjVGUDhvSGViWnFUOXJpVEtyb2pFRDBZaVJMUHV3aXpIR1hQaDFiZUJyMUx3M08zMmJMRXRhUmxVcGtwNjI3aFFCZnN2TG1sVW9YZkl2dUtZNVE2REM0bWJaZ05RUTVBb2QrUXlrNVlxVTNjS1FZN0FDazFCa1k3QmVObHMiLCJtYWMiOiJkMDgxZWE3YjRkMWRiNGY2MzQ0MGU0MmMyYTFlYmI0ZDg4ZTA0MThiYzZlZTU0N2NiNDg4ZDgyOTFhMjJjNmE3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkdlN3pwYmJlV2tHNmplaUVtMEFoY0E9PSIsInZhbHVlIjoiNnE0Zzd0dkJYTnQrclVKbVFySXBRcSthV2FZS0t3dGgraUhna1BSTGRZMGV4Y005R3BhS0todTNmV2lTMlIveWVaRFY0VmNWWkpaTXZEUTJjZ3dKN2FxNWpjbWhRQ3F4L25HTjZNTWxMaXBQeDlTNlI1YkZ5WFVnTmo0RTJYR3dWeXlTNzZTbjI2UjNtQzRrZ3R2eHE2TEJGb2ZLY3lqZDg2Qm5RU1dVSTNhcWhYV3BhRmVyUkN3M2JwbDVkWmRLZnNYbGdvcjZzMGlFb09pVXJNakEzL3UvTDVST1I2d3VKTHcrSTArNHdDemxVTUQxaXN3ZS83Z1p6VTZwYUlLdUo4Q3Z2ODdPa04ySzJZcW1mNWdlNFFTSDJBVEdGaDRzd0xXekF2UnpBYXg4b0lSTlRVRUF1eUVYaWU4VHJEMmg5RHlHU091YzNkQm9ibitvMHpLUjN5c0x2SkNZbDV4SkRVdEJjaC9aRCtCME02WVplaHpwU1RHeExuQmE2dnB0SFRiaUcwNGdrYllpV2xadFVJL2NUb290aDZkdW1JQ1ZkVTlsUEgraE9NeWhheW1JajVQRmdOU28zdVNqa1E4WTFOc0IxU29Xc3hEQW11bGdhdW9yTG0wM0FWbVl2UzA0eTlUMlQ2QnRIOCtFaUdmbTdFeTA5eGhwODVpTXpCNmwiLCJtYWMiOiI3NDBhZTExZmMyZDA0MTExMzE1MTUyYTg0MDExY2Q0YjJkOTQzMzE2ODA4NzM4NWFlMGFhZGM3MWYzNTdjODc5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-822873836\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2130812848 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2130812848\", {\"maxDepth\":0})</script>\n"}}