{"__meta": {"id": "X6ac13c72db7754efbbf408710c2d7d1b", "datetime": "2025-07-31 06:16:24", "utime": **********.153406, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:16:24] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.140499, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753942582.415146, "end": **********.153472, "duration": 1.738325834274292, "duration_str": "1.74s", "measures": [{"label": "Booting", "start": 1753942582.415146, "relative_start": 0, "end": 1753942583.934844, "relative_end": 1753942583.934844, "duration": 1.519697904586792, "duration_str": "1.52s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753942583.934868, "relative_start": 1.5197219848632812, "end": **********.153477, "relative_end": 5.0067901611328125e-06, "duration": 0.21860885620117188, "duration_str": "219ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50612736, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.112722, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.027989999999999998, "accumulated_duration_str": "27.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0220559, "duration": 0.0047599999999999995, "duration_str": "4.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 17.006}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.052962, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 17.006, "width_percent": 3.894}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.063325, "duration": 0.02061, "duration_str": "20.61ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 20.9, "width_percent": 73.633}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.091063, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 94.534, "width_percent": 5.466}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1630669025 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1630669025\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-654974288 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-654974288\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1758045974 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1758045974\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-534889964 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im9xT1BBOElDL0pZcTJvZ2ZBOWhoaFE9PSIsInZhbHVlIjoiS0JtS25XNFF3UlQyVFNoVUVrd2xjbDd1cmU5ajVpaVY1THM4ekJ5UVU2eGlnQkp3OGdvSVNhaGhjbnFGZG9qVFlSVklGTTB1b1BnelVZYkRtV2ZsTm1sRDFGOU45QTh2OUtIcWRmeUNYb0Y5aSsxS3ROT3diMkpITHByT3N0UUFBbUtZZGlTTURKMTFaWktrRU9zN2pBUFM1UVJFeS9DRDYxUUpFaENZMGg3TFZFK3pQbFNwUk9MTnpZNlFjMm1Yc0ppZnIremZpR3Q1Vkk3VWJUMTgrQkltOVh1SGhUZGx3cVRlWHVQbkJoNGRlVmpKd1U4Q0N6SFNrR0NrRmtqOTdCaG9ybW9jQXBhN1lPSkg5VXJMY2ErcXE3S283RktZdFJPTUluZ3ZCTW85Y0hvQ3ZaZFVqZ0ducDJYQ1hDc2crVE9ZZzhvRjdpWXNuS2xmck45UUNDOFBaTzM1ZjlTTU92eHNmbzZjM2hLOU8zTTIvRURmVktpMkVjeVBKSnZEQ0VLdnZSRVNnRmJXQmRubzRJL3A3RTY0dDU4ZHRYajNwV0MrSW9sYWhUcHR1UnBrM1dXNTFnL3F5dVNCTThCNzBZeGhDeFRkV3R2Rm14WWxialNkMWU2YllSR1o3dWJDVi8wUHpHamdqL1ZoMm05TlNJV3RqMW5Tajc2R1RWSGgiLCJtYWMiOiIzMDRlNTVhMDZhMWEzZGQ2Y2M2MGJkYzRhMjQwMzFkNmM2ODY4ZDk1NjBiNGM3MjQ4YzEwMmM2ZDMyNWRmODNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InBDMGpIUVJaRWdmNENCTlliMk40anc9PSIsInZhbHVlIjoiS05OelVDaDV2ZU96c3FwdVFpalFmSUwxeXhwbng2OU9zNjQ3b0JIbEVkQXJ4ZUZLM3phRVNrdkhwNE1NNFlSUVlFdXpDL2hHaHVSVzNFWENmMG9iMnF3djZrNlE2NVpGM1JEUWNUZmdFMDk0ZmpDV3p0NkdEdG9FRm43MzVJZHA3YVhocjhLY1drNG84ZFhic1BTdEN2aGdhYjY1YVc2OGdUT3ZkWjVyRE5ZM3dKNk9uZkhJNWRubXAzYlgwd1dXZW9tcFVIQjR5eENrbGhXd0JOSERHc04raXVJR09LbnRucUN2elNIVUI1VzJRcGw0SzZjSEFsUUxtcXlUT0YzQjJSRnhFQTM2aVQ0N01ubTBiMTlrdlplOFhSSDczSCs2TDYwTjVkMFJ0NTFKT1V6MnlMZWtZMVRSNUlNQno2RDZPeEhFS2VqMFNmSUdWRExzeDlFOTM1cDRxSmc5WHdkTnkrbmF5UTc2OCtIT2tHYStTc1RQbmR5a1grdXVLY25zd3k1YUFwQU1ua2J5eDNIN3hFczJjR1VPVTlvN2t3clZDcWxyRnM0SXcrcjBqRCsyRytNVlk0QzFSWUloUEZoUTJkT1k1bGJZN1l5dHJnMkNyTWkwWUlmeGZsMVlIa21ZYVpOeVNpdnMwbXRGeFZQbUs1L3UxZUp5dnVnVlBEU3AiLCJtYWMiOiIwMjljMTM0ZDFkNmJhNWRlZjEyYzI5OTUxZDU1NDAwNWZjMzc5NDkyNTFlYmQ5M2Y5ZWZiNmY2N2VmZTlhMzI0IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkVKVVlQMWRZL25GclJVeEs4NUVISXc9PSIsInZhbHVlIjoibUlMNkV2T1RIaHNUa1h6MTlua3FkS3FkWm5sRFIzZ3JwOWY5dDIvVDh0c3JJNHk5dHJlSzNWUGVQZjVBSk5OdWhSMGdXMU03c29IczFacTVncjRaZWFvTmdkdXBDeEh6aGVveFRQSkNya0NVZndQTldkNkttSFI3V1pRSDkwSEc0ZGRVRUtoaWtDODgzM2xrc0ptQnZValcvZEdzUTNHRDlGZFEzempJcDBQTVN5MVVXY3RXOTlSK0FGMjVYNmlhdWhVZDE0Ly9qNTFUbHRsclFEekRqbkNMTWJrb0tvL0VsUjBUNnM1Z29DWTZnSXArTnNNK0YyZExBaURDZzdRZUVyUjg2K1JGVGo0SkczaVVsSUJTUlB4NzFybWRTS25aeFZWRVdaRkovNld1QUk0MCs3bHk4WFVudVlGZ0hxc3hmMWZoVFFQQjdQSGl2YXZEbzc2MUtHcEdXT29STnpWckVUVlR5RkdNT2NMNXlYakhDNytsRnRtY0FlNTJwVUc5RWpXWEhSVGRPaTFKcHhZYklUdHQyOXlWcGN6SnQ4TGtpMVM3dnBqdFZQc1lRNDJ2K3FndFNiMTNheld0L0szdFFhdHJGSkRlSjNVdTdlK3dWN1p6Y2l1NFFrajIwQ1lObTdBMG04RDRlSjZ4ZTE4Wk44WVBKMTF5Q3FNSXVBdysiLCJtYWMiOiIxMTc4NzkyZDg1ZGNhOGU3Nzg4YzNiOTUxOTJhNmUyYTkwMzA4MmJjNDNiZjU0NzU3ZGE2YTVhMDdmY2VkYTk1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-534889964\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1508721148 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5LJND5bUclZnLzzClCMEaxKzZ5Yt8AAmGjprz0I</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sUbnPperdA1I3PaRxBpMGV4YsR5AVGq0ERh2LBdJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508721148\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-387874581 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:16:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhmMlk5WGd3b3ZxNWpyaTg3ZHpoMmc9PSIsInZhbHVlIjoiZDM4ZUg0WUpFUVJhSkhCRktmL2J2WFpGMDBvMUluYnlpeUZOSFRybWNyMytrdzB4c0R2ODlZd1h6TTd2aVhTZ2I5WUVCNTFDYk1aRmVvY0hVUWQvSTVjaWZaV3pUSlJGZzFVeVZ4VlBaNHRZTy8yck1iK2hlMERGV0taZTU3dkNkemI0SThsb3l1b1dEVXE3dlRzZGFqN1JqMXp4YUFNZkZ6cjZXUFpaTk5FRUoySEhGaWhWcWVMOXdTRWZnbFBUOU93bHFTbm94NzZQanE2WGdpKzZqQTNaZEJPV21wNytPV2w3M251dk4zTXgxdUJXOVZZZWVUdzkxQW9heEFPbDBibDJPQ21kTTVPTUdWZUdILzBHWXFIT0hjNU5Xa3pXNGhvcGtKRndrTUY5NU9hcVRFT0orM08wUWg5amRjRS9UVVRtL2VXaUZmTEhkZytnMVFRblNvSjJpTVFtYVAyeG9GbTVFeHhHNjAzK3pKU0pjZEs4b0oySHpLdzVzR0E5NlRLY0QraXZzYW1iWXZYQ2RacTVIc3BWNEtHbk1xTzFZckJnZ1hEU0hZbWhCcUNxbGI5L2hmVEtVVkNsMVp4QmtJRjRLei8yK2lRcUxNcS9ubTJoazkxb2o0WGhhNFRUcFNsdnpBQXUrVENKRk9NM00xbG1yUUUzUzZ2ZlAySjkiLCJtYWMiOiIzMDIyN2U4ZDlhNTNiZWYyODI3NGIwYTAwZTQzMzYzNDIwNDBlMjhhN2MyYjcxMzVkMjVmMTBlYTJmMjdkYjc1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:16:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IlNSK0JLTkk3WjIzZ0l0S3E4SjV1dWc9PSIsInZhbHVlIjoiYVZlYTFlQ21hdm53Z1EveDNEVlRrUHF6cHA1RTVKblRPSFVYOXkzbUZwUEtCaTc2MUM3cEdFamdPN3pzaGFkTUlGN0JFRGowTFhNS014S00rUDJwejBteHRGTHBHNElvR1dBb1lPajdpVU9meXdPSmZRYzBQRitVTFR6MWppYVlJRUpJd09zZHBWTVVaRmxSVDgrY3V6Zk9lTitZMXdhQ3VNQ1dxQWp3Q0c2Vk1CTWREZkx1T2l1d1hIcVNsMlByd3BIOXoyZHlDbEw1eURLMGtEV2NZV1l6RjI1RjlSVzZReHowU1ZzclJzVldwMWJ0eE51emYvUHNGRys0OG1BWHBXUEgzdkJaWldHZUZkN29PNEptaE9GQnI0V1VraS8rbFJkelhicXZreTNFTUNVZlV1TW9uN01LMGtNMGlYd0hZc2xYSUxPZVo5VkprSjdCRmlvVktMOHhvUnN3aXB2NDFJRlhrYWFkL29Hdis1QUt1ZFVEWnBvSW1IcjYrTU10ckNSVStBZmRDa3VwR3RFTXRjdnd1Qll1dlFTdUhBWkRpaTlvR0U1UVU2ckJNVGd5clJOTUN3dW9PM2tEd1RzV3pFdURudkNaVUVsdHV6VkpnQzFkM0ZBemw3WWozNjZyQ1IwWU9tRUcrZlZzM1hpU3RPbko2MW90KzA4QVI4ZzgiLCJtYWMiOiIxMjBiMTM1MzU3MWFjMTY1NThjYjI2N2U0ZDJiNGUzMmI0NzAzNGU5Y2Y2NDgwZTBiNTZlNDM1ZGIzM2Q4MDhjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:16:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhmMlk5WGd3b3ZxNWpyaTg3ZHpoMmc9PSIsInZhbHVlIjoiZDM4ZUg0WUpFUVJhSkhCRktmL2J2WFpGMDBvMUluYnlpeUZOSFRybWNyMytrdzB4c0R2ODlZd1h6TTd2aVhTZ2I5WUVCNTFDYk1aRmVvY0hVUWQvSTVjaWZaV3pUSlJGZzFVeVZ4VlBaNHRZTy8yck1iK2hlMERGV0taZTU3dkNkemI0SThsb3l1b1dEVXE3dlRzZGFqN1JqMXp4YUFNZkZ6cjZXUFpaTk5FRUoySEhGaWhWcWVMOXdTRWZnbFBUOU93bHFTbm94NzZQanE2WGdpKzZqQTNaZEJPV21wNytPV2w3M251dk4zTXgxdUJXOVZZZWVUdzkxQW9heEFPbDBibDJPQ21kTTVPTUdWZUdILzBHWXFIT0hjNU5Xa3pXNGhvcGtKRndrTUY5NU9hcVRFT0orM08wUWg5amRjRS9UVVRtL2VXaUZmTEhkZytnMVFRblNvSjJpTVFtYVAyeG9GbTVFeHhHNjAzK3pKU0pjZEs4b0oySHpLdzVzR0E5NlRLY0QraXZzYW1iWXZYQ2RacTVIc3BWNEtHbk1xTzFZckJnZ1hEU0hZbWhCcUNxbGI5L2hmVEtVVkNsMVp4QmtJRjRLei8yK2lRcUxNcS9ubTJoazkxb2o0WGhhNFRUcFNsdnpBQXUrVENKRk9NM00xbG1yUUUzUzZ2ZlAySjkiLCJtYWMiOiIzMDIyN2U4ZDlhNTNiZWYyODI3NGIwYTAwZTQzMzYzNDIwNDBlMjhhN2MyYjcxMzVkMjVmMTBlYTJmMjdkYjc1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:16:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IlNSK0JLTkk3WjIzZ0l0S3E4SjV1dWc9PSIsInZhbHVlIjoiYVZlYTFlQ21hdm53Z1EveDNEVlRrUHF6cHA1RTVKblRPSFVYOXkzbUZwUEtCaTc2MUM3cEdFamdPN3pzaGFkTUlGN0JFRGowTFhNS014S00rUDJwejBteHRGTHBHNElvR1dBb1lPajdpVU9meXdPSmZRYzBQRitVTFR6MWppYVlJRUpJd09zZHBWTVVaRmxSVDgrY3V6Zk9lTitZMXdhQ3VNQ1dxQWp3Q0c2Vk1CTWREZkx1T2l1d1hIcVNsMlByd3BIOXoyZHlDbEw1eURLMGtEV2NZV1l6RjI1RjlSVzZReHowU1ZzclJzVldwMWJ0eE51emYvUHNGRys0OG1BWHBXUEgzdkJaWldHZUZkN29PNEptaE9GQnI0V1VraS8rbFJkelhicXZreTNFTUNVZlV1TW9uN01LMGtNMGlYd0hZc2xYSUxPZVo5VkprSjdCRmlvVktMOHhvUnN3aXB2NDFJRlhrYWFkL29Hdis1QUt1ZFVEWnBvSW1IcjYrTU10ckNSVStBZmRDa3VwR3RFTXRjdnd1Qll1dlFTdUhBWkRpaTlvR0U1UVU2ckJNVGd5clJOTUN3dW9PM2tEd1RzV3pFdURudkNaVUVsdHV6VkpnQzFkM0ZBemw3WWozNjZyQ1IwWU9tRUcrZlZzM1hpU3RPbko2MW90KzA4QVI4ZzgiLCJtYWMiOiIxMjBiMTM1MzU3MWFjMTY1NThjYjI2N2U0ZDJiNGUzMmI0NzAzNGU5Y2Y2NDgwZTBiNTZlNDM1ZGIzM2Q4MDhjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:16:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387874581\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1037316972 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1037316972\", {\"maxDepth\":0})</script>\n"}}