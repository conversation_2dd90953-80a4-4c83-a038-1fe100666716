<!-- Expenses Tab Content -->
<style>
.upload-area {
    cursor: pointer;
    transition: all 0.3s ease;
}
.upload-area:hover {
    border-color: #007bff !important;
    background-color: #f8f9fa;
}
.upload-area.dragover {
    border-color: #28a745 !important;
    background-color: #d4edda;
}
</style>
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0"><?php echo e(__('Expense Management')); ?></h4>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-warning active" data-expense-view="manage">
                    <i class="ti ti-list me-1"></i><?php echo e(__('Manage')); ?>

                </button>
                <button type="button" class="btn btn-outline-warning" data-expense-view="categories">
                    <i class="ti ti-category me-1"></i><?php echo e(__('Categories')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<!-- Manage View -->
<div id="manage-view" class="expense-view active">
    <div class="row mb-4">
        <!-- Expense Stats -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-danger text-white">
                <div class="card-body text-center">
                    <i class="ti ti-receipt" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1"><?php echo e(\Auth::user()->priceFormat(0)); ?></h4>
                    <small><?php echo e(__('Total Expenses')); ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-warning text-white">
                <div class="card-body text-center">
                    <i class="ti ti-clock" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1"><?php echo e(\Auth::user()->priceFormat(0)); ?></h4>
                    <small><?php echo e(__('This Month')); ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-info text-white">
                <div class="card-body text-center">
                    <i class="ti ti-clock" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0</h4>
                    <small><?php echo e(__('Pending Bills')); ?></small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-success text-white">
                <div class="card-body text-center">
                    <i class="ti ti-trending-down" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0%</h4>
                    <small><?php echo e(__('vs Last Month')); ?></small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Recent Expenses')); ?></h5>
                    <div class="d-flex gap-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create expense')): ?>
                            <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#expenseModal">
                                <i class="ti ti-plus me-1"></i><?php echo e(__('Create')); ?>

                            </button>
                        <?php endif; ?>
                        <a href="<?php echo e(route('expense.index')); ?>" class="btn btn-primary btn-sm">
                            <i class="ti ti-eye me-1"></i><?php echo e(__('View All')); ?>

                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="expensesTable">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('ID')); ?></th>
                                    <th><?php echo e(__('Expense Category')); ?></th>
                                    <th><?php echo e(__('Expense')); ?></th>
                                    <th><?php echo e(__('Vendor')); ?></th>
                                    <th><?php echo e(__('Date')); ?></th>
                                    <th><?php echo e(__('Expense Receipt')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                    $expenses = \App\Models\Bill::where('created_by', \Auth::user()->creatorId())
                                        ->where('type', 'Expense')
                                        ->with(['category', 'vender'])
                                        ->latest()
                                        ->limit(5)
                                        ->get();
                                ?>
                                <?php $__empty_1 = true; $__currentLoopData = $expenses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $expense): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr data-expense-id="<?php echo e($expense->id); ?>">
                                    <td><?php echo e($expense->bill_id); ?></td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            <?php echo e(!empty($expense->category) ? $expense->category->name : '-'); ?>

                                        </span>
                                    </td>
                                    <td><?php echo e(\Auth::user()->priceFormat($expense->getTotal())); ?></td>
                                    <td><?php echo e(!empty($expense->vender) ? $expense->vender->name : '-'); ?></td>
                                    <td><?php echo e(\Auth::user()->dateFormat($expense->bill_date)); ?></td>
                                    <td>
                                        <?php if($expense->attachment): ?>
                                            <a href="<?php echo e(asset('storage/' . $expense->attachment)); ?>" target="_blank" class="btn btn-sm btn-outline-info">
                                                <i class="ti ti-file"></i>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo e(__('No Receipt')); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit expense')): ?>
                                                <button class="btn btn-sm btn-outline-secondary edit-expense"
                                                        data-expense-id="<?php echo e($expense->id); ?>"
                                                        title="<?php echo e(__('Edit')); ?>">
                                                    <i class="ti ti-edit"></i>
                                                </button>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete expense')): ?>
                                                <button class="btn btn-sm btn-outline-danger delete-expense"
                                                        data-expense-id="<?php echo e($expense->id); ?>"
                                                        title="<?php echo e(__('Delete')); ?>">
                                                    <i class="ti ti-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                            <button class="btn btn-sm btn-outline-info view-description"
                                                    data-expense-id="<?php echo e($expense->id); ?>"
                                                    title="<?php echo e(__('Description')); ?>">
                                                <i class="ti ti-file-text"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="ti ti-receipt" style="font-size: 3rem;"></i>
                                            <h5 class="mt-3"><?php echo e(__('No Expenses Found')); ?></h5>
                                            <p><?php echo e(__('Start tracking your business expenses')); ?></p>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create expense')): ?>
                                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#expenseModal">
                                                    <i class="ti ti-plus me-1"></i><?php echo e(__('Add Expense')); ?>

                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Categories View -->
<div id="categories-view" class="expense-view" style="display: none;">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Expense Categories')); ?></h5>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create constant category')): ?>
                        <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                            <i class="ti ti-plus me-1"></i><?php echo e(__('Add Category')); ?>

                        </button>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="categoriesTable">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('ID')); ?></th>
                                    <th><?php echo e(__('Created At')); ?></th>
                                    <th><?php echo e(__('Name')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody id="categoriesTableBody">
                                <!-- Categories will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                    <div id="no-categories" class="text-center py-5" style="display: none;">
                        <i class="ti ti-category text-muted" style="font-size: 4rem;"></i>
                        <h5 class="mt-3"><?php echo e(__('No Categories Found')); ?></h5>
                        <p class="text-muted"><?php echo e(__('Create categories to organize your expenses')); ?></p>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create constant category')): ?>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                <i class="ti ti-plus me-1"></i><?php echo e(__('Add Category')); ?>

                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><?php echo e(__('Quick Actions')); ?></h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="<?php echo e(route('expense.create')); ?>" class="btn btn-outline-danger w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-plus mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('Add Expense')); ?></span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="<?php echo e(route('bill.create')); ?>" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-file-invoice mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('Create Bill')); ?></span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="<?php echo e(route('product-category.index')); ?>" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-category mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('Categories')); ?></span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="<?php echo e(route('vender.index')); ?>" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-users mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('Vendors')); ?></span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="<?php echo e(route('report.expense.summary')); ?>" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-chart-line mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('Reports')); ?></span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-download mb-2" style="font-size: 1.5rem;"></i>
                            <span><?php echo e(__('Export')); ?></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Expense view switching
    const expenseViewButtons = document.querySelectorAll('[data-expense-view]');
    const expenseViews = document.querySelectorAll('.expense-view');

    expenseViewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetView = this.getAttribute('data-expense-view');

            // Remove active class from all buttons
            expenseViewButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            // Hide all views
            expenseViews.forEach(view => view.style.display = 'none');
            // Show target view
            const targetElement = document.getElementById(targetView + '-view');
            if (targetElement) {
                targetElement.style.display = 'block';
            }
        });
    });

    // File upload handling
    function setupFileUpload(uploadArea, fileInput) {
        uploadArea.addEventListener('click', function() {
            fileInput.click();
        });

        fileInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const uploadContent = uploadArea.querySelector('.upload-content');
                uploadContent.innerHTML = `
                    <i class="ti ti-file" style="font-size: 2rem; color: #28a745;"></i>
                    <p class="mb-0 text-success">${file.name}</p>
                    <small class="text-muted">File selected</small>
                `;
            }
        });
    }

    // Setup file uploads
    const uploadArea = document.querySelector('#expenseModal .upload-area');
    const fileInput = document.querySelector('#expense_receipt');
    if (uploadArea && fileInput) {
        setupFileUpload(uploadArea, fileInput);
    }

    const editUploadArea = document.querySelector('#editExpenseModal .upload-area');
    const editFileInput = document.querySelector('#edit_expense_receipt');
    if (editUploadArea && editFileInput) {
        setupFileUpload(editUploadArea, editFileInput);
    }

    // Set default date to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('expense_date').value = today;

    // Expense form submission
    document.getElementById('expenseForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="ti ti-loader"></i> <?php echo e(__("Saving...")); ?>';

        fetch('<?php echo e(route("expense.store.ajax")); ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('expenseModal'));
                modal.hide();

                // Reset form
                this.reset();
                document.getElementById('expense_date').value = today;

                // Reset file upload display
                const uploadContent = document.querySelector('#expenseModal .upload-content');
                uploadContent.innerHTML = `
                    <i class="ti ti-upload" style="font-size: 2rem; color: #6c757d;"></i>
                    <p class="mb-0 text-muted"><?php echo e(__('Click to upload receipt')); ?></p>
                    <small class="text-muted"><?php echo e(__('Supported: JPG, PNG, PDF, DOC')); ?></small>
                `;

                // Refresh table
                location.reload();

                // Show success message
                showAlert('success', data.message || '<?php echo e(__("Expense created successfully")); ?>');
            } else {
                showAlert('error', data.message || '<?php echo e(__("Error creating expense")); ?>');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', '<?php echo e(__("An error occurred while saving the expense")); ?>');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    });

    // Edit expense functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.edit-expense')) {
            const expenseId = e.target.closest('.edit-expense').getAttribute('data-expense-id');

            fetch(`<?php echo e(url('expense')); ?>/${expenseId}/edit-data`, {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const expense = data.expense;

                    // Populate edit form
                    document.getElementById('edit_expense_id').value = expense.id;
                    document.getElementById('edit_expense_category').value = expense.category_id || '';
                    document.getElementById('edit_expense_amount').value = expense.total || '';
                    document.getElementById('edit_vendor_name').value = expense.vendor_name || '';
                    document.getElementById('edit_expense_date').value = expense.bill_date || '';
                    document.getElementById('edit_expense_description').value = expense.description || '';

                    // Show current receipt if exists
                    const currentReceiptDiv = document.getElementById('current-receipt');
                    if (expense.attachment) {
                        currentReceiptDiv.innerHTML = `
                            <small class="text-muted">Current receipt:</small>
                            <a href="<?php echo e(asset('storage/')); ?>/${expense.attachment}" target="_blank" class="btn btn-sm btn-outline-info">
                                <i class="ti ti-file"></i> View Current
                            </a>
                        `;
                    } else {
                        currentReceiptDiv.innerHTML = '<small class="text-muted">No current receipt</small>';
                    }

                    // Show modal
                    const modal = new bootstrap.Modal(document.getElementById('editExpenseModal'));
                    modal.show();
                } else {
                    showAlert('error', data.message || '<?php echo e(__("Error loading expense data")); ?>');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', '<?php echo e(__("An error occurred while loading expense data")); ?>');
            });
        }

        // Delete expense functionality
        if (e.target.closest('.delete-expense')) {
            const expenseId = e.target.closest('.delete-expense').getAttribute('data-expense-id');

            if (confirm('<?php echo e(__("Are you sure you want to delete this expense?")); ?>')) {
                fetch(`<?php echo e(url('expense')); ?>/${expenseId}/delete-ajax`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove row from table
                        const row = document.querySelector(`tr[data-expense-id="${expenseId}"]`);
                        if (row) {
                            row.remove();
                        }
                        showAlert('success', data.message || '<?php echo e(__("Expense deleted successfully")); ?>');
                    } else {
                        showAlert('error', data.message || '<?php echo e(__("Error deleting expense")); ?>');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('error', '<?php echo e(__("An error occurred while deleting the expense")); ?>');
                });
            }
        }

        // View description functionality
        if (e.target.closest('.view-description')) {
            const expenseId = e.target.closest('.view-description').getAttribute('data-expense-id');

            fetch(`<?php echo e(url('expense')); ?>/${expenseId}/description`, {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const descriptionContent = document.getElementById('expense-description-content');
                    descriptionContent.innerHTML = data.description || '<em class="text-muted">No description available</em>';

                    const modal = new bootstrap.Modal(document.getElementById('descriptionModal'));
                    modal.show();
                } else {
                    showAlert('error', data.message || '<?php echo e(__("Error loading description")); ?>');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', '<?php echo e(__("An error occurred while loading description")); ?>');
            });
        }
    });

    // Edit expense form submission
    document.getElementById('editExpenseForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const expenseId = document.getElementById('edit_expense_id').value;
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="ti ti-loader"></i> <?php echo e(__("Updating...")); ?>';

        fetch(`<?php echo e(url('expense')); ?>/${expenseId}/update-ajax`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('editExpenseModal'));
                modal.hide();

                // Refresh table
                location.reload();

                // Show success message
                showAlert('success', data.message || '<?php echo e(__("Expense updated successfully")); ?>');
            } else {
                showAlert('error', data.message || '<?php echo e(__("Error updating expense")); ?>');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', '<?php echo e(__("An error occurred while updating the expense")); ?>');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    });

    // Alert function
    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // Insert alert at the top of the page
        const container = document.querySelector('.container-fluid') || document.body;
        container.insertAdjacentHTML('afterbegin', alertHtml);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    // Categories management functions
    function loadCategories() {
        fetch('<?php echo e(route("expense-categories.list.ajax")); ?>', {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const tbody = document.getElementById('categoriesTableBody');
                const noCategories = document.getElementById('no-categories');
                const categoriesTable = document.getElementById('categoriesTable');

                if (data.categories.length === 0) {
                    categoriesTable.style.display = 'none';
                    noCategories.style.display = 'block';
                } else {
                    categoriesTable.style.display = 'table';
                    noCategories.style.display = 'none';

                    tbody.innerHTML = '';
                    data.categories.forEach(category => {
                        const row = `
                            <tr data-category-id="${category.id}">
                                <td>${category.id}</td>
                                <td>${category.created_at}</td>
                                <td>${category.name}</td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit constant category')): ?>
                                            <button class="btn btn-sm btn-outline-primary edit-category"
                                                    data-category-id="${category.id}"
                                                    title="<?php echo e(__('Edit')); ?>">
                                                <i class="ti ti-edit"></i>
                                            </button>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete constant category')): ?>
                                            <button class="btn btn-sm btn-outline-danger delete-category"
                                                    data-category-id="${category.id}"
                                                    title="<?php echo e(__('Delete')); ?>">
                                                <i class="ti ti-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        `;
                        tbody.insertAdjacentHTML('beforeend', row);
                    });
                }
            } else {
                showAlert('error', data.message || '<?php echo e(__("Error loading categories")); ?>');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', '<?php echo e(__("An error occurred while loading categories")); ?>');
        });
    }

    // Load categories when categories view is shown
    const categoriesViewButton = document.querySelector('[data-expense-view="categories"]');
    if (categoriesViewButton) {
        categoriesViewButton.addEventListener('click', function() {
            setTimeout(() => {
                loadCategories();
            }, 100);
        });
    }

    // Add category form submission
    document.getElementById('addCategoryForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="ti ti-loader"></i> <?php echo e(__("Saving...")); ?>';

        fetch('<?php echo e(route("expense-categories.store.ajax")); ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('addCategoryModal'));
                modal.hide();

                // Reset form
                this.reset();

                // Reload categories
                loadCategories();

                // Show success message
                showAlert('success', data.message || '<?php echo e(__("Category created successfully")); ?>');
            } else {
                showAlert('error', data.message || '<?php echo e(__("Error creating category")); ?>');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', '<?php echo e(__("An error occurred while saving the category")); ?>');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    });

    // Edit and delete category functionality
    document.addEventListener('click', function(e) {
        // Edit category functionality
        if (e.target.closest('.edit-category')) {
            const categoryId = e.target.closest('.edit-category').getAttribute('data-category-id');

            fetch(`<?php echo e(url('expense-categories')); ?>/${categoryId}/edit-ajax`, {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const category = data.category;

                    // Populate edit form
                    document.getElementById('edit_category_id').value = category.id;
                    document.getElementById('edit_category_name').value = category.name;

                    // Show modal
                    const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
                    modal.show();
                } else {
                    showAlert('error', data.message || '<?php echo e(__("Error loading category data")); ?>');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', '<?php echo e(__("An error occurred while loading category data")); ?>');
            });
        }

        // Delete category functionality
        if (e.target.closest('.delete-category')) {
            const categoryId = e.target.closest('.delete-category').getAttribute('data-category-id');

            if (confirm('<?php echo e(__("Are you sure you want to delete this category?")); ?>')) {
                fetch(`<?php echo e(url('expense-categories')); ?>/${categoryId}/delete-ajax`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove row from table
                        const row = document.querySelector(`tr[data-category-id="${categoryId}"]`);
                        if (row) {
                            row.remove();
                        }

                        // Check if table is empty and show no categories message
                        const tbody = document.getElementById('categoriesTableBody');
                        if (tbody.children.length === 0) {
                            document.getElementById('categoriesTable').style.display = 'none';
                            document.getElementById('no-categories').style.display = 'block';
                        }

                        showAlert('success', data.message || '<?php echo e(__("Category deleted successfully")); ?>');
                    } else {
                        showAlert('error', data.message || '<?php echo e(__("Error deleting category")); ?>');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('error', '<?php echo e(__("An error occurred while deleting the category")); ?>');
                });
            }
        }
    });

    // Edit category form submission
    document.getElementById('editCategoryForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const categoryId = document.getElementById('edit_category_id').value;
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="ti ti-loader"></i> <?php echo e(__("Updating...")); ?>';

        fetch(`<?php echo e(url('expense-categories')); ?>/${categoryId}/update-ajax`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('editCategoryModal'));
                modal.hide();

                // Reload categories
                loadCategories();

                // Show success message
                showAlert('success', data.message || '<?php echo e(__("Category updated successfully")); ?>');
            } else {
                showAlert('error', data.message || '<?php echo e(__("Error updating category")); ?>');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', '<?php echo e(__("An error occurred while updating the category")); ?>');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    });
});
</script>

<!-- Expense Modal -->
<div class="modal fade" id="expenseModal" tabindex="-1" aria-labelledby="expenseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="expenseModalLabel"><?php echo e(__('Add Expense')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="expenseForm" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="expense_category" class="form-label"><?php echo e(__('Expense Category')); ?></label>
                            <select class="form-select" id="expense_category" name="category_id" required>
                                <option value=""><?php echo e(__('Select Category')); ?></option>
                                <?php
                                    $categories = \App\Models\ProductServiceCategory::where('created_by', \Auth::user()->creatorId())
                                        ->where('type', 'expense')
                                        ->get();
                                ?>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($category->id); ?>"><?php echo e($category->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="expense_amount" class="form-label"><?php echo e(__('Expense')); ?></label>
                            <input type="number" class="form-control" id="expense_amount" name="amount" step="0.01" min="0" required placeholder="<?php echo e(__('Enter amount')); ?>">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="vendor_name" class="form-label"><?php echo e(__('Vendor')); ?></label>
                            <input type="text" class="form-control" id="vendor_name" name="vendor_name" required placeholder="<?php echo e(__('Vendor Name')); ?>">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="expense_date" class="form-label"><?php echo e(__('Date')); ?> <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="expense_date" name="bill_date" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="expense_receipt" class="form-label"><?php echo e(__('Receipt')); ?></label>
                            <div class="upload-area border-2 border-dashed rounded p-3 text-center">
                                <input type="file" class="form-control d-none" id="expense_receipt" name="attachment" accept="image/*,.pdf,.doc,.docx">
                                <div class="upload-content">
                                    <i class="ti ti-upload" style="font-size: 2rem; color: #6c757d;"></i>
                                    <p class="mb-0 text-muted"><?php echo e(__('Click to upload receipt')); ?></p>
                                    <small class="text-muted"><?php echo e(__('Supported: JPG, PNG, PDF, DOC')); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="expense_description" class="form-label"><?php echo e(__('Description')); ?></label>
                            <textarea class="form-control" id="expense_description" name="description" rows="4" placeholder="<?php echo e(__('Enter description')); ?>"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-success"><?php echo e(__('Submit')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Expense Modal -->
<div class="modal fade" id="editExpenseModal" tabindex="-1" aria-labelledby="editExpenseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editExpenseModalLabel"><?php echo e(__('Edit Expense')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editExpenseForm" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <input type="hidden" id="edit_expense_id" name="expense_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_expense_category" class="form-label"><?php echo e(__('Expense Category')); ?></label>
                            <select class="form-select" id="edit_expense_category" name="category_id" required>
                                <option value=""><?php echo e(__('Select Category')); ?></option>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($category->id); ?>"><?php echo e($category->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_expense_amount" class="form-label"><?php echo e(__('Expense')); ?></label>
                            <input type="number" class="form-control" id="edit_expense_amount" name="amount" step="0.01" min="0" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_vendor_name" class="form-label"><?php echo e(__('Vendor')); ?></label>
                            <input type="text" class="form-control" id="edit_vendor_name" name="vendor_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_expense_date" class="form-label"><?php echo e(__('Date')); ?> <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="edit_expense_date" name="bill_date" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_expense_receipt" class="form-label"><?php echo e(__('Receipt')); ?></label>
                            <div class="upload-area border-2 border-dashed rounded p-3 text-center">
                                <input type="file" class="form-control d-none" id="edit_expense_receipt" name="attachment" accept="image/*,.pdf,.doc,.docx">
                                <div class="upload-content">
                                    <i class="ti ti-upload" style="font-size: 2rem; color: #6c757d;"></i>
                                    <p class="mb-0 text-muted"><?php echo e(__('Click to upload new receipt')); ?></p>
                                    <small class="text-muted"><?php echo e(__('Leave empty to keep current receipt')); ?></small>
                                </div>
                            </div>
                            <div id="current-receipt" class="mt-2"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_expense_description" class="form-label"><?php echo e(__('Description')); ?></label>
                            <textarea class="form-control" id="edit_expense_description" name="description" rows="4"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-success"><?php echo e(__('Update')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Description Modal -->
<div class="modal fade" id="descriptionModal" tabindex="-1" aria-labelledby="descriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="descriptionModalLabel"><?php echo e(__('Expense Description')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="expense-description-content"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Close')); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCategoryModalLabel"><?php echo e(__('Add Category')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addCategoryForm">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="category_name" class="form-label"><?php echo e(__('Category Name')); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="category_name" name="name" required placeholder="<?php echo e(__('Expense Category name')); ?>">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-success"><?php echo e(__('Submit')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCategoryModalLabel"><?php echo e(__('Edit Category')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editCategoryForm">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="edit_category_id" name="category_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_category_name" class="form-label"><?php echo e(__('Category Name')); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_category_name" name="name" required placeholder="<?php echo e(__('Expense Category name')); ?>">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-success"><?php echo e(__('Update')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/finance/tabs/expenses.blade.php ENDPATH**/ ?>