{"__meta": {"id": "X2cee4296d61e74d5752d4ddd9151db04", "datetime": "2025-07-31 05:42:24", "utime": **********.248518, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940542.42274, "end": **********.248603, "duration": 1.8258631229400635, "duration_str": "1.83s", "measures": [{"label": "Booting", "start": 1753940542.42274, "relative_start": 0, "end": **********.086389, "relative_end": **********.086389, "duration": 1.663649082183838, "duration_str": "1.66s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.086425, "relative_start": 1.****************, "end": **********.248609, "relative_end": 5.9604644775390625e-06, "duration": 0.****************, "duration_str": "162ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "umEV21tvx3gM8Lkytg5VjVkXAXc07Bp1kfEfYpcp", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-5285360 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-5285360\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-352474294 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-352474294\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-814292225 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-814292225\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1567561916 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1567561916\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-997090653 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-997090653\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1434933800 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:42:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNkOUs2TXlhUnJ2YnZyOWx5NzVXd2c9PSIsInZhbHVlIjoibEljeU5abk5ZaWZqa2d1a1FUOTV4MFc3V254WDdTNDgzYXpmaXhUWjIrV2RidmtYOHB3T2d1NFNvTThlTTZ2cTdCazIrL2lwZVdscWMwbUJMYkJXTHpJeHpqejlndXoxNFNNeGw2UUZGcjRCZnEyTU5BZjVhYkxBNVQxMzBiNncyR1MvNXdZMjQxejJpNWJXSE9QOEc4RDlFYzJzOTY5T2RZcEE3ZW4wL2hvdXA2a09TTjZ1Q0RnVnpwcWtWMnBiZHEzaWFKeTBuMU5vVUJMSlJKN0NrWXdtZ2Z1TkQrMHptOG5qRE1Gak10TG0zOXdDSkM1emwwbXVKeG05NGRNdXpvNm5oUjA1SW1sOXNSbElKVmpxT3NITTVGYWJnaTlWbG5DZHdYL2NYdGxwb1gxcVVLeVNaQlJPcGE1OEw0c0pXMURtWG4zSEVVck4yVjB3R1VqcjAyNysrdHFEYWc5Y3Y5UEtXb1hGMUpWdGJVWnBNN1M0YVpMcVNxbDI5RXhVb1FJSlBLbm5GWVRzSEhjMW0xeUdkRDErRDgzbERrSWVFU3g3eGxCYUNQRndza0xMUXg3K2hldzR3MHJWL3c5RjRUWDZwNy9XZmZNcFhFTlhJSzBoelJwSTZiTHFyRS9Jbzd4WWFaVjE2Z0piWGZxbXNUaERidFozSnl0RkE5c28iLCJtYWMiOiJlOGJlODZmNWNjZjcwNGJiMmVjYjg5MTc0NjhhMzdmNGVmMWNhNDA3YTY4YjA0YTE5MThhNDk0M2FhNmMyYmE1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:42:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Imw1RDZ3YXBQdzBDLzF2TWFUOG96aVE9PSIsInZhbHVlIjoiYWlHd3RjNDQ0VVduLzFCTEd1OXBFSy9vclZEQ0RheGRtOXlVMEJ5cVkvT2V3ZlF2M01hZ3RaTldrMDNZWGRFemlYUXZFaWNOQUZUU0FDN0tkaWFzVXA4MTcwaHBZa1lOdkFaVHJ5QUJHQVREMVFkYUdURzNuUnJHUFdPZ2FSVDFuemk5QVptVDlGZHZoUUdyK20zOVh1bHFTVlVTaEREekgvWTJVR2MzVk1CMzN1QUR4Mkc1cWpjQnRDZks0c01aTVp2QW92SktnQVJLRnBBQnRWRzFHWGZuVFVNS0pTZ0hISGhnaVNFWUw3ekRFOUpoaUdxbkJMZkJ1OE9EZEQyMDVkcEhqcVVZeTBkZEJLa1VIdmw4NEkzRU1BM2dqdjI0eUM4RjY4U1VxZTN4dk54WGRScHJwM2V2a0ZzajJWRFdBNkt1T2RaTkViUnpPY3JKMXI2d3FmN213ZW0rQm5aYXZZSXhLZUgrWmFIdm0rMmRqMnRtUHhFOFRKOXZ4ZGVOZC85LzR2QVExcGYwOUl3aXF0eDBvMG5XcVllM0szbDl2cWwxeXhSL0ZOTXVnVHlsSXc5dm93UDBGdjJxRU5DNVh6MVRQb21kM09lUHZJNzJZOG5JM3FLKzR4UlE2RXdLT2tEU0owQVNBdTNiYnduN0VNazhvWlVxQzIreExVS1MiLCJtYWMiOiI1MWNkMGU2MTkzOWI4ZTQwZmIwNzA2MzE2OTE5MjA4NzhmNzVkMDU2ZGRjZGRmNzI2ZTQxZTJiMGY5NWYzMDk4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:42:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNkOUs2TXlhUnJ2YnZyOWx5NzVXd2c9PSIsInZhbHVlIjoibEljeU5abk5ZaWZqa2d1a1FUOTV4MFc3V254WDdTNDgzYXpmaXhUWjIrV2RidmtYOHB3T2d1NFNvTThlTTZ2cTdCazIrL2lwZVdscWMwbUJMYkJXTHpJeHpqejlndXoxNFNNeGw2UUZGcjRCZnEyTU5BZjVhYkxBNVQxMzBiNncyR1MvNXdZMjQxejJpNWJXSE9QOEc4RDlFYzJzOTY5T2RZcEE3ZW4wL2hvdXA2a09TTjZ1Q0RnVnpwcWtWMnBiZHEzaWFKeTBuMU5vVUJMSlJKN0NrWXdtZ2Z1TkQrMHptOG5qRE1Gak10TG0zOXdDSkM1emwwbXVKeG05NGRNdXpvNm5oUjA1SW1sOXNSbElKVmpxT3NITTVGYWJnaTlWbG5DZHdYL2NYdGxwb1gxcVVLeVNaQlJPcGE1OEw0c0pXMURtWG4zSEVVck4yVjB3R1VqcjAyNysrdHFEYWc5Y3Y5UEtXb1hGMUpWdGJVWnBNN1M0YVpMcVNxbDI5RXhVb1FJSlBLbm5GWVRzSEhjMW0xeUdkRDErRDgzbERrSWVFU3g3eGxCYUNQRndza0xMUXg3K2hldzR3MHJWL3c5RjRUWDZwNy9XZmZNcFhFTlhJSzBoelJwSTZiTHFyRS9Jbzd4WWFaVjE2Z0piWGZxbXNUaERidFozSnl0RkE5c28iLCJtYWMiOiJlOGJlODZmNWNjZjcwNGJiMmVjYjg5MTc0NjhhMzdmNGVmMWNhNDA3YTY4YjA0YTE5MThhNDk0M2FhNmMyYmE1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:42:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Imw1RDZ3YXBQdzBDLzF2TWFUOG96aVE9PSIsInZhbHVlIjoiYWlHd3RjNDQ0VVduLzFCTEd1OXBFSy9vclZEQ0RheGRtOXlVMEJ5cVkvT2V3ZlF2M01hZ3RaTldrMDNZWGRFemlYUXZFaWNOQUZUU0FDN0tkaWFzVXA4MTcwaHBZa1lOdkFaVHJ5QUJHQVREMVFkYUdURzNuUnJHUFdPZ2FSVDFuemk5QVptVDlGZHZoUUdyK20zOVh1bHFTVlVTaEREekgvWTJVR2MzVk1CMzN1QUR4Mkc1cWpjQnRDZks0c01aTVp2QW92SktnQVJLRnBBQnRWRzFHWGZuVFVNS0pTZ0hISGhnaVNFWUw3ekRFOUpoaUdxbkJMZkJ1OE9EZEQyMDVkcEhqcVVZeTBkZEJLa1VIdmw4NEkzRU1BM2dqdjI0eUM4RjY4U1VxZTN4dk54WGRScHJwM2V2a0ZzajJWRFdBNkt1T2RaTkViUnpPY3JKMXI2d3FmN213ZW0rQm5aYXZZSXhLZUgrWmFIdm0rMmRqMnRtUHhFOFRKOXZ4ZGVOZC85LzR2QVExcGYwOUl3aXF0eDBvMG5XcVllM0szbDl2cWwxeXhSL0ZOTXVnVHlsSXc5dm93UDBGdjJxRU5DNVh6MVRQb21kM09lUHZJNzJZOG5JM3FLKzR4UlE2RXdLT2tEU0owQVNBdTNiYnduN0VNazhvWlVxQzIreExVS1MiLCJtYWMiOiI1MWNkMGU2MTkzOWI4ZTQwZmIwNzA2MzE2OTE5MjA4NzhmNzVkMDU2ZGRjZGRmNzI2ZTQxZTJiMGY5NWYzMDk4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:42:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1434933800\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-363829870 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">umEV21tvx3gM8Lkytg5VjVkXAXc07Bp1kfEfYpcp</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-363829870\", {\"maxDepth\":0})</script>\n"}}