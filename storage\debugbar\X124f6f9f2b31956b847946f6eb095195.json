{"__meta": {"id": "X124f6f9f2b31956b847946f6eb095195", "datetime": "2025-07-31 05:13:09", "utime": **********.901715, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753938788.888117, "end": **********.901777, "duration": 1.013659954071045, "duration_str": "1.01s", "measures": [{"label": "Booting", "start": 1753938788.888117, "relative_start": 0, "end": **********.826827, "relative_end": **********.826827, "duration": 0.9387099742889404, "duration_str": "939ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.82684, "relative_start": 0.****************, "end": **********.901782, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "74.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gwXTj0Kspdah5v9ZxSbm0CLLc13ADKdQ59Oeleik", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-668475497 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-668475497\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1383257977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1383257977\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-434177442 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-434177442\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-491855228 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-491855228\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-263009681 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-263009681\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-940322262 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:13:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNqamNPVk13TWZEWU9FWFBaRWdBT3c9PSIsInZhbHVlIjoiTDM5UnZ1MFBkeU52OW54RFgvZ05NQnBPVmwwYVh4WU0ycWlXSWhSR28vZ2sxaFlXdm92YkJrTVpjdHVDQmlpUHNSL0dPVU1qd2JQRVZ3d0drQlgrdzZIMlFYMFdJNTVoTXBLUHJvb1lEeGxHcmhVWGM5d1lsZmJlZndvZ2xjUVA0VVJ5NUU3K3NFNExnY3VNejgwY0xDdW5OVHFpTGNzZE0wVENFSTFmL0FiQlZjTktmMElPZ2doWTBhV3ZvVVM5dFZnUHFJSnlTWnh6QTcrbVJRTC81TWM3b3RYdmNaU3JEMHBUM2pBemx3MUVBV0c1cHBWZ3oxUVVmRWU3dlhNcTRuY0VuZDQ0eW8yTGdGN3FFRlhrZndHWXJaMU5ZZEVhRFpZWllGLzZQbkt5QzFRRXlhQzFYSWMvRCtJbTJINCtsUjdyeThkTGMzQXphT0N0MTg1cjEwRjd5SHgvKzkrYjlIWXpmVXZyNTVXMUlpL1BGejlnK1dZalhBY2wxUTRya1VRSTJDTXl1dTdWM0E5TkNMTXZOM2hOYUtBamY3RmRRL0IrQTJmdGxhTUVML2hPV3JJQW5zeWxpYWcxa1o0MFFSYkRldVNUZm83ZGpKekJxSUxHRHJHMjRVRFhwcm04R2JYeHM0ZGZSb0NvQ3h1MHZaeDRvYlJCS29sVWRqdkEiLCJtYWMiOiIyOTQ4ZDM2NjVlOTk5OTdjYjNiNmIwZjhkYjM5ODU5NGFjODM1Y2VkNTI0NWFjZWVhMjRlMDdjZTVkYjRjZjcwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:13:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkFqWGJuOGJJMk1SaE5jZlB2eFI2aHc9PSIsInZhbHVlIjoiZU5jZkV4RlljU3k2cUk1VmpPZnhlM2Z1cFV6QlQxWk8yVlZEMHVyNUZFL0RtYTZQQ2pNek82dFlEMjQxUllLMndyRjM5YUkwb084VEYyR0tKUXd1QW1KUWh2cHd0bHJjSkdvOSsrUWQrQzBWN2YyWGRVeGFLeHIvcGxxWExnS3RyWFBDL1ZTbmlaOWhrYW5BQXlIbmFsSjF0WWFYRVlEaFdMa0lvSzJlWFhUS3hnZzZDakJ1MEs0a1l3TzlzWkpaaDF6Szh2NzdlaVg0TUNKZ2UwVEYwWmh0Y1dwSTZUU0dyVWtWckszS0RIZkhZSDZVZ2l5alZCM21KNllxNVN6MGtSTkhzRnVtZXcvYnpUYmtLblA2ZUE1aUVOb21RN0R5TUlxL3J4bGduWVNyTS9RSndIWThTdDY5dUlBMlVKMnBCOXBFdloxL0VVNEd6RGMrN0tNVVpCMTNGUCtLNTR5UC9vTTJkN09QRzhjS2hkbWlmc1ZQRnF1SXR1M3dmN21mak4vbW1BRmcvVlVrRHhYdWJqbkpxMDJCOFJTL3p3MERndit2Zm5jQit6RkVGcDlaQnlsZDNYcHJRd3ltVG9QaUlya0FUcjJKVHpDRmlkRCswTDBkVTQ0d3Z4TU5jM1hxcDBiT0hMT0d2Ri9sM09kWjNjbGVlQjYvYW9VYWZkbDIiLCJtYWMiOiI0NDM4YTdiNzg5MTcwOWQ3NDlkOTNiMmE0ZWFhNTVhODEzOTM0Y2EyNmFjMTExNDllZmI3OGRiNDAyMDEyZGY2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:13:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNqamNPVk13TWZEWU9FWFBaRWdBT3c9PSIsInZhbHVlIjoiTDM5UnZ1MFBkeU52OW54RFgvZ05NQnBPVmwwYVh4WU0ycWlXSWhSR28vZ2sxaFlXdm92YkJrTVpjdHVDQmlpUHNSL0dPVU1qd2JQRVZ3d0drQlgrdzZIMlFYMFdJNTVoTXBLUHJvb1lEeGxHcmhVWGM5d1lsZmJlZndvZ2xjUVA0VVJ5NUU3K3NFNExnY3VNejgwY0xDdW5OVHFpTGNzZE0wVENFSTFmL0FiQlZjTktmMElPZ2doWTBhV3ZvVVM5dFZnUHFJSnlTWnh6QTcrbVJRTC81TWM3b3RYdmNaU3JEMHBUM2pBemx3MUVBV0c1cHBWZ3oxUVVmRWU3dlhNcTRuY0VuZDQ0eW8yTGdGN3FFRlhrZndHWXJaMU5ZZEVhRFpZWllGLzZQbkt5QzFRRXlhQzFYSWMvRCtJbTJINCtsUjdyeThkTGMzQXphT0N0MTg1cjEwRjd5SHgvKzkrYjlIWXpmVXZyNTVXMUlpL1BGejlnK1dZalhBY2wxUTRya1VRSTJDTXl1dTdWM0E5TkNMTXZOM2hOYUtBamY3RmRRL0IrQTJmdGxhTUVML2hPV3JJQW5zeWxpYWcxa1o0MFFSYkRldVNUZm83ZGpKekJxSUxHRHJHMjRVRFhwcm04R2JYeHM0ZGZSb0NvQ3h1MHZaeDRvYlJCS29sVWRqdkEiLCJtYWMiOiIyOTQ4ZDM2NjVlOTk5OTdjYjNiNmIwZjhkYjM5ODU5NGFjODM1Y2VkNTI0NWFjZWVhMjRlMDdjZTVkYjRjZjcwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:13:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkFqWGJuOGJJMk1SaE5jZlB2eFI2aHc9PSIsInZhbHVlIjoiZU5jZkV4RlljU3k2cUk1VmpPZnhlM2Z1cFV6QlQxWk8yVlZEMHVyNUZFL0RtYTZQQ2pNek82dFlEMjQxUllLMndyRjM5YUkwb084VEYyR0tKUXd1QW1KUWh2cHd0bHJjSkdvOSsrUWQrQzBWN2YyWGRVeGFLeHIvcGxxWExnS3RyWFBDL1ZTbmlaOWhrYW5BQXlIbmFsSjF0WWFYRVlEaFdMa0lvSzJlWFhUS3hnZzZDakJ1MEs0a1l3TzlzWkpaaDF6Szh2NzdlaVg0TUNKZ2UwVEYwWmh0Y1dwSTZUU0dyVWtWckszS0RIZkhZSDZVZ2l5alZCM21KNllxNVN6MGtSTkhzRnVtZXcvYnpUYmtLblA2ZUE1aUVOb21RN0R5TUlxL3J4bGduWVNyTS9RSndIWThTdDY5dUlBMlVKMnBCOXBFdloxL0VVNEd6RGMrN0tNVVpCMTNGUCtLNTR5UC9vTTJkN09QRzhjS2hkbWlmc1ZQRnF1SXR1M3dmN21mak4vbW1BRmcvVlVrRHhYdWJqbkpxMDJCOFJTL3p3MERndit2Zm5jQit6RkVGcDlaQnlsZDNYcHJRd3ltVG9QaUlya0FUcjJKVHpDRmlkRCswTDBkVTQ0d3Z4TU5jM1hxcDBiT0hMT0d2Ri9sM09kWjNjbGVlQjYvYW9VYWZkbDIiLCJtYWMiOiI0NDM4YTdiNzg5MTcwOWQ3NDlkOTNiMmE0ZWFhNTVhODEzOTM0Y2EyNmFjMTExNDllZmI3OGRiNDAyMDEyZGY2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:13:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940322262\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-400936792 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gwXTj0Kspdah5v9ZxSbm0CLLc13ADKdQ59Oeleik</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-400936792\", {\"maxDepth\":0})</script>\n"}}