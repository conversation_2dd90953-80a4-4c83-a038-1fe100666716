{"__meta": {"id": "X6465728dabcbef2f407d3585bd33c52e", "datetime": "2025-07-31 06:14:20", "utime": **********.872046, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:14:20] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.861468, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753942459.279619, "end": **********.872086, "duration": 1.5924670696258545, "duration_str": "1.59s", "measures": [{"label": "Booting", "start": 1753942459.279619, "relative_start": 0, "end": **********.642016, "relative_end": **********.642016, "duration": 1.3623969554901123, "duration_str": "1.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.642048, "relative_start": 1.362428903579712, "end": **********.87209, "relative_end": 4.0531158447265625e-06, "duration": 0.2300422191619873, "duration_str": "230ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50612736, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.836202, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03454, "accumulated_duration_str": "34.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.735805, "duration": 0.00528, "duration_str": "5.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 15.287}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.770634, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 15.287, "width_percent": 5.269}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.781183, "duration": 0.02577, "duration_str": "25.77ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 20.556, "width_percent": 74.609}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8138752, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 95.165, "width_percent": 4.835}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans/10/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-730497617 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-730497617\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-240092095 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-240092095\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-424683431 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-424683431\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pricing-plans/10/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im9xT1BBOElDL0pZcTJvZ2ZBOWhoaFE9PSIsInZhbHVlIjoiS0JtS25XNFF3UlQyVFNoVUVrd2xjbDd1cmU5ajVpaVY1THM4ekJ5UVU2eGlnQkp3OGdvSVNhaGhjbnFGZG9qVFlSVklGTTB1b1BnelVZYkRtV2ZsTm1sRDFGOU45QTh2OUtIcWRmeUNYb0Y5aSsxS3ROT3diMkpITHByT3N0UUFBbUtZZGlTTURKMTFaWktrRU9zN2pBUFM1UVJFeS9DRDYxUUpFaENZMGg3TFZFK3pQbFNwUk9MTnpZNlFjMm1Yc0ppZnIremZpR3Q1Vkk3VWJUMTgrQkltOVh1SGhUZGx3cVRlWHVQbkJoNGRlVmpKd1U4Q0N6SFNrR0NrRmtqOTdCaG9ybW9jQXBhN1lPSkg5VXJMY2ErcXE3S283RktZdFJPTUluZ3ZCTW85Y0hvQ3ZaZFVqZ0ducDJYQ1hDc2crVE9ZZzhvRjdpWXNuS2xmck45UUNDOFBaTzM1ZjlTTU92eHNmbzZjM2hLOU8zTTIvRURmVktpMkVjeVBKSnZEQ0VLdnZSRVNnRmJXQmRubzRJL3A3RTY0dDU4ZHRYajNwV0MrSW9sYWhUcHR1UnBrM1dXNTFnL3F5dVNCTThCNzBZeGhDeFRkV3R2Rm14WWxialNkMWU2YllSR1o3dWJDVi8wUHpHamdqL1ZoMm05TlNJV3RqMW5Tajc2R1RWSGgiLCJtYWMiOiIzMDRlNTVhMDZhMWEzZGQ2Y2M2MGJkYzRhMjQwMzFkNmM2ODY4ZDk1NjBiNGM3MjQ4YzEwMmM2ZDMyNWRmODNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ikt6ak9PdmdQOW9xRXVpVFVhQTZVSlE9PSIsInZhbHVlIjoiVjRWL1FKcWdZMFl2VnlvZXd2SDZmU1N4T3hBelBiUThvV2h0R0RCS25oYjAydGFoUTNxclp4SWhtSmNFT3h6cFQ5UXQ2anQ4c0R5M2R3dDJyRWlqVUN6Q1d3N2o2WFZnN0FlSU5ZK3J2RTNLUEgvTTJGQStOa0EvSjhtbitzNDRnUjdpcW1DQTk1clB6V3lyQ0p1bGZmTFY5b2FERi9YVVZZYmhYckJZZUtidUd6TVRXeExuQ0hKaVZ6NlBPelhGRUt6Qm9vc24yRGZSSFRLclhJWHFIM2h3Ui9tWjZPdWdSQ1JWam5weE5jaUlIdG1sV0wrNkNTMXlMQ0xwRWdxQjdGRVc0SnF2eFZNVGhlWTdvWmMxcEg0NUhtS2syaWhZVUJ0OFMrdXFXZDhZYzJmWHk5N0Vha3pTb1V5cEVDaUxhYmpMQjVuVkdkS3h5MnlPdHV3UDB3eTN3N2RrR2hWVEY1cXI1UGdkeklhMUxxeGZZaUVSMGJkUGxnbElzTjVaZG9ld2dkcjYvOUpzS0w4VUZjSXZSKytsb0NweFJWeFhmcFZtYXMwODJ5UnlST0dQbHhIZ1JqQmk2dkxYU3FMRGJiak1DMS9laEtDQlBPMUcvdWVNVW1DYW14OUk2RVZaZWp3ZUl5STNiK0RMdVNzc29McnZzT3lCUERKaHhwN04iLCJtYWMiOiJlODE2ZGM3N2NiYTZjOGRmMDYxODZlNWFiZjY1NmIyMmRhYWQ1MjBkYmUyYTBmNGU4NjE0NzA5NzVhMTZkMmY2IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Imk3OWhXVFc1MDM2MDI0RFlDcnJnWXc9PSIsInZhbHVlIjoiOUphN3NLdlNBZmlCTW9KMGFSTHU1a3R2RU5vWGNNMFVySEtjQ1JJanNUNEl5aGh3dEJqL0lYajBrcklDb2JiVTUwV3BQbzZtQ3NKeHNhVUNMUE5oS2V1NG9MWGh1Yk4xL0JjNVdxdG5lTGRFS0kzQWwrbHhVQkYyVGNaS2RUeTEyWEhjUmh3OUdUQ1dlN0pkZjkzSGNWZWVSNDVuUFNhd2xpNk9UY1F5djZPdmNiYVdWQm5QY2NzU2YrUkNiZzRoelJwZ0dvc2hKRGRRTngraGtMMHVlZ08xR3ZIZ0FSejNsb085OTI0c08xRTZJRkcxWktodVVvMEgrTWd5WXFHZHhSNWVVbTVZQUx0bFlWN2RTVXpvb0ZCbFovWGkxTTd4c0tJazArMXUvQTBMRisveUJsMGhUN3QvV1hadGxoWFBDUTFaWXVkVFQvUm9YWG9DYTE5SVowY2U3MVhZYjh3YjFFUnVRdVlvVVAzTTV3ak8xdVNtazlqakhNaDBvRDY4UUVHN2w4dGxZS2k4KzdqSy9ZRzA5WXF6ZmxsMVhaVUZ4c1pEWElFQkF5aHkxT2JqOHpPUHQwL0kzcWtmZVlJZ3B5RUpYcVVDT3grZnpIcEtLRFdZZldST1ZVN2FHdzlzalVYc0NpTTZNTy9UQ2l0RlIzWTJ2blBGQkRMZ3R0VngiLCJtYWMiOiIzNzY4NzUwZDg5YWQ2ODJlNWVjODQ3NWJlNzJiOWI3Nzk5NzZhMWMxZDJhMDhhY2NhMDViNTU4MmRhMzFjZjQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5LJND5bUclZnLzzClCMEaxKzZ5Yt8AAmGjprz0I</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sUbnPperdA1I3PaRxBpMGV4YsR5AVGq0ERh2LBdJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1939335227 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:14:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRRcVRjM3ppWk05TFp0eEZKQ0tuZ1E9PSIsInZhbHVlIjoiOEFPWEd5OW15Wko1MU1YWWNVZHJqc2hvalJZOG5QTTBjY3BFZUI3QVBXeDEvdEovcGJZRWVxOVg3aGpDU2hlaXVmUDU4aVRram1JaGhsblFjTlB5OFpCZjVXLzgyVUZVSWRmelp2R2RQTTIzczMwbEt2cTZMTEF1NUFqckZZbXVNTjVxS2RwbWpUMW0yclJpSzhaOStDTW1IaUNSa255eEFkNDduUlZuNUZhL3RZVTk1d3dGV1czemFZZDhvVFh4OWF3SFg0UTBwNXBQSmJoOGc0YlVwcHNKSHBCeFBCaGJBNk1FYVR1UGo5VldMV2dkQjhUTVh5VDNJb2tyR000OFR5NXV6US9DQW5yRzNNZEE3bFlIc2YxK0J6UXdvZGUwUmxMcTRrcjlMMVZGbkk2TmpqQU1ybExnY214aHlSMEFLRTA4WjhBTXJoanBSTWF1YS9aRlo1UU12QlFnOTJGb3M5L0RmWk8vK0MvS3Rod2FtYTkxLzdCQ0trN0xiRFplN0Z6UUE0OEhoUnZpYmEwK0ZuelJxMlozWXN1K3ZTVS96YmJxZzdoOGVJUDlsQ0dza2tCWk01Uk1ZVFhDQnN5LzFaYVFyTHMxVzBxaStrNndQVnpKTXlDSzY0YkhPVFk4SUNRak9hR3hKZzJya1pqZDdLajRlQW5CT3VmWWdtK1oiLCJtYWMiOiJjN2MxMDEzZDA5NmU0NzczZmFiOWIwNTUyZjVmYzliM2MzNzA1NzVlMmM3Y2QxNzg4MDI4ZWI5Y2NlNGNkNmZkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:14:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6Im5xUkVrQWVPRFZrVTNqSUFRMGxBVFE9PSIsInZhbHVlIjoiU29tWEZ5Z2djcnBZQjhSeStXUGlTT0l6eFAxNmJBbVZXRmdKeTYxRGZjR2c1aUFHUkliTEZrS00zVEtSSkFwdUVjV2JXV1NHVGQ5SW5GWksvWXRUS2s1VzQwTzU0MEh0a0IwQTFDeENVbXFBZGNNQVlVYjF1akVvVEpRanMyUEJWa1BIM2VqbE96NHBFRVk3M2JMQlpIMnhBdVdyK0VwUDBjd3UzcFYwK3FmcW1JWThleHlqUkJTcFVGc25wd3BZRWx2WmdHT3VkMHNLa3pKTE1VeWhBYmJid3NWdE92QjdyYUlUdXBwVzY4U29hUmZiNnFiOW40YVd2Q2dpMkNRYTliOHZINGRoU0hOa3daQ2piQTVCUDUwV2tYTkNlb0tVcnMyYllrczltVEc1KzJmOU5pV0VyNndjaUMzdmNGY3dPRjllTkVBbFArVkFQby9rTGI3UC9xelUwc0ZGQTU1UTRicU1scXR3Uy8wbndQbCtuMzRYeTVFejI3dzFPUVFpU1ZJa1BxaEk1Vk1ic3V0RWZ6bmhqN1ZYVjAxWU9aT2xZNitod094blBuMkRDcXZ0NTFYWHNKMVExd3NCempUNy8rakYvV1U1RmxFbEJGRDl1bkNJbzFPcUlvSzlqVkJ5b2VDS21zZENqb2xIcmxSRUlPQUhsUzc1eDZFc0prK0IiLCJtYWMiOiI1Y2FkZGVlYjY4NDlkZTE5YzcwZDYzMzAyNmVlNWZiN2EzMzhmMDJiYjJmOTRmMjljNjY2OWM1ODIwOGI5Y2QwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:14:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRRcVRjM3ppWk05TFp0eEZKQ0tuZ1E9PSIsInZhbHVlIjoiOEFPWEd5OW15Wko1MU1YWWNVZHJqc2hvalJZOG5QTTBjY3BFZUI3QVBXeDEvdEovcGJZRWVxOVg3aGpDU2hlaXVmUDU4aVRram1JaGhsblFjTlB5OFpCZjVXLzgyVUZVSWRmelp2R2RQTTIzczMwbEt2cTZMTEF1NUFqckZZbXVNTjVxS2RwbWpUMW0yclJpSzhaOStDTW1IaUNSa255eEFkNDduUlZuNUZhL3RZVTk1d3dGV1czemFZZDhvVFh4OWF3SFg0UTBwNXBQSmJoOGc0YlVwcHNKSHBCeFBCaGJBNk1FYVR1UGo5VldMV2dkQjhUTVh5VDNJb2tyR000OFR5NXV6US9DQW5yRzNNZEE3bFlIc2YxK0J6UXdvZGUwUmxMcTRrcjlMMVZGbkk2TmpqQU1ybExnY214aHlSMEFLRTA4WjhBTXJoanBSTWF1YS9aRlo1UU12QlFnOTJGb3M5L0RmWk8vK0MvS3Rod2FtYTkxLzdCQ0trN0xiRFplN0Z6UUE0OEhoUnZpYmEwK0ZuelJxMlozWXN1K3ZTVS96YmJxZzdoOGVJUDlsQ0dza2tCWk01Uk1ZVFhDQnN5LzFaYVFyTHMxVzBxaStrNndQVnpKTXlDSzY0YkhPVFk4SUNRak9hR3hKZzJya1pqZDdLajRlQW5CT3VmWWdtK1oiLCJtYWMiOiJjN2MxMDEzZDA5NmU0NzczZmFiOWIwNTUyZjVmYzliM2MzNzA1NzVlMmM3Y2QxNzg4MDI4ZWI5Y2NlNGNkNmZkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:14:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6Im5xUkVrQWVPRFZrVTNqSUFRMGxBVFE9PSIsInZhbHVlIjoiU29tWEZ5Z2djcnBZQjhSeStXUGlTT0l6eFAxNmJBbVZXRmdKeTYxRGZjR2c1aUFHUkliTEZrS00zVEtSSkFwdUVjV2JXV1NHVGQ5SW5GWksvWXRUS2s1VzQwTzU0MEh0a0IwQTFDeENVbXFBZGNNQVlVYjF1akVvVEpRanMyUEJWa1BIM2VqbE96NHBFRVk3M2JMQlpIMnhBdVdyK0VwUDBjd3UzcFYwK3FmcW1JWThleHlqUkJTcFVGc25wd3BZRWx2WmdHT3VkMHNLa3pKTE1VeWhBYmJid3NWdE92QjdyYUlUdXBwVzY4U29hUmZiNnFiOW40YVd2Q2dpMkNRYTliOHZINGRoU0hOa3daQ2piQTVCUDUwV2tYTkNlb0tVcnMyYllrczltVEc1KzJmOU5pV0VyNndjaUMzdmNGY3dPRjllTkVBbFArVkFQby9rTGI3UC9xelUwc0ZGQTU1UTRicU1scXR3Uy8wbndQbCtuMzRYeTVFejI3dzFPUVFpU1ZJa1BxaEk1Vk1ic3V0RWZ6bmhqN1ZYVjAxWU9aT2xZNitod094blBuMkRDcXZ0NTFYWHNKMVExd3NCempUNy8rakYvV1U1RmxFbEJGRDl1bkNJbzFPcUlvSzlqVkJ5b2VDS21zZENqb2xIcmxSRUlPQUhsUzc1eDZFc0prK0IiLCJtYWMiOiI1Y2FkZGVlYjY4NDlkZTE5YzcwZDYzMzAyNmVlNWZiN2EzMzhmMDJiYjJmOTRmMjljNjY2OWM1ODIwOGI5Y2QwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:14:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1939335227\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1954173426 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pricing-plans/10/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1954173426\", {\"maxDepth\":0})</script>\n"}}