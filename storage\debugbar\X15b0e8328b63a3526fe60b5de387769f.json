{"__meta": {"id": "X15b0e8328b63a3526fe60b5de387769f", "datetime": "2025-07-31 05:36:43", "utime": 1753940203.785019, "method": "PUT", "uri": "/pricing-plans/10", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 32, "messages": [{"message": "[05:36:24] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 74,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.987516, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:24] LOG.info: Observer: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 74,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"deep sha\",\n        \"company_description\": \"Company updated via pricing plan integration (Observer)\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.989223, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:24] LOG.info: Observer: Updated user module permissions after pricing plan change {\n    \"user_id\": 74,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.991255, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:27] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 79,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2042 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.093843, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:27] LOG.info: Observer: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 79,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"Parichay Singha AI\",\n        \"company_description\": \"Company updated via pricing plan integration (Observer)\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.095539, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:27] LOG.info: Observer: Updated user module permissions after pricing plan change {\n    \"user_id\": 79,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.099209, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:29] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 82,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2046 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.198402, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:29] LOG.info: Observer: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 82,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"Raja JI\",\n        \"company_description\": \"Company updated via pricing plan integration (Observer)\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.199697, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:29] LOG.info: Observer: Updated user module permissions after pricing plan change {\n    \"user_id\": 82,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.201351, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:31] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 83,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.291865, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:31] LOG.info: Observer: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 83,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"Mr. X\",\n        \"company_description\": \"Company updated via pricing plan integration (Observer)\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.293041, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:31] LOG.info: Observer: Updated user module permissions after pricing plan change {\n    \"user_id\": 83,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.295889, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:33] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 84,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.381669, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:33] LOG.info: Observer: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 84,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"parichay  Bot flow\",\n        \"company_description\": \"Company updated via pricing plan integration (Observer)\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.382879, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:33] LOG.info: Observer: Updated user module permissions after pricing plan change {\n    \"user_id\": 84,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.384538, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:33] LOG.info: Observer: Successfully updated module permissions for all users in pricing plan {\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"users_updated\": 5\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.385388, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:35] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 74,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.466742, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:35] LOG.info: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 74,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"deep sha\",\n        \"company_description\": \"Company updated via pricing plan integration\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.468007, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:35] LOG.info: Updated user module permissions after pricing plan change {\n    \"user_id\": 74,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.469507, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:37] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 79,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.530679, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:37] LOG.info: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 79,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"Parichay Singha AI\",\n        \"company_description\": \"Company updated via pricing plan integration\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.531776, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:37] LOG.info: Updated user module permissions after pricing plan change {\n    \"user_id\": 79,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.53327, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:39] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 82,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.580301, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:39] LOG.info: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 82,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"Raja JI\",\n        \"company_description\": \"Company updated via pricing plan integration\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.58158, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:39] LOG.info: Updated user module permissions after pricing plan change {\n    \"user_id\": 82,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.584145, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:41] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 83,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.645126, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:41] LOG.info: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 83,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"Mr. X\",\n        \"company_description\": \"Company updated via pricing plan integration\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.646896, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:41] LOG.info: Updated user module permissions after pricing plan change {\n    \"user_id\": 83,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.649385, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:43] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 84,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": 1753940203.711458, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:43] LOG.info: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 84,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"parichay  Bot flow\",\n        \"company_description\": \"Company updated via pricing plan integration\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753940203.712816, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:43] LOG.info: Updated user module permissions after pricing plan change {\n    \"user_id\": 84,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753940203.714332, "xdebug_link": null, "collector": "log"}, {"message": "[05:36:43] LOG.info: Successfully updated module permissions for all users in pricing plan {\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"users_updated\": 5\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753940203.71516, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753940176.160746, "end": 1753940203.785263, "duration": 27.62451696395874, "duration_str": "27.62s", "measures": [{"label": "Booting", "start": 1753940176.160746, "relative_start": 0, "end": **********.354504, "relative_end": **********.354504, "duration": 1.1937580108642578, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.354539, "relative_start": 1.1937928199768066, "end": 1753940203.78527, "relative_end": 6.9141387939453125e-06, "duration": 26.430731058120728, "duration_str": "26.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52482376, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT pricing-plans/{pricing_plan}", "middleware": "web, auth, XSS", "as": "pricing-plans.update", "controller": "App\\Http\\Controllers\\PricingPlanController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=164\" onclick=\"\">app/Http/Controllers/PricingPlanController.php:164-231</a>"}, "queries": {"nb_statements": 32, "nb_failed_statements": 0, "accumulated_duration": 0.12252000000000003, "accumulated_duration_str": "123ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.506631, "duration": 0.00547, "duration_str": "5.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 4.465}, {"sql": "select * from `pricing_plans` where `id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.528998, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "radhe_same", "start_percent": 4.465, "width_percent": 1.641}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.5691922, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 6.105, "width_percent": 1.526}, {"sql": "select count(*) as aggregate from `pricing_plans` where `name` = 'My Business Flow' and `id` <> '10'", "type": "query", "params": [], "bindings": ["My Business Flow", "10"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 983}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.455851, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "radhe_same", "start_percent": 7.631, "width_percent": 3.134}, {"sql": "update `pricing_plans` set `module_permissions` = '{\\\"crm\\\":[\\\"create lead\\\",\\\"create deal\\\",\\\"create form builder\\\",\\\"create contract\\\",\\\"create pipeline\\\",\\\"create stage\\\",\\\"create source\\\",\\\"create label\\\",\\\"view crm dashboard\\\",\\\"view lead\\\",\\\"view deal\\\",\\\"view form builder\\\",\\\"view contract\\\",\\\"view pipeline\\\",\\\"view stage\\\",\\\"view source\\\",\\\"view label\\\",\\\"delete lead\\\",\\\"delete deal\\\",\\\"delete form builder\\\",\\\"delete contract\\\",\\\"delete pipeline\\\",\\\"delete stage\\\",\\\"delete source\\\",\\\"delete label\\\",\\\"manage lead\\\",\\\"edit lead\\\",\\\"manage deal\\\",\\\"edit deal\\\",\\\"manage form builder\\\",\\\"edit form builder\\\",\\\"manage contract\\\",\\\"edit contract\\\",\\\"manage pipeline\\\",\\\"edit pipeline\\\",\\\"manage stage\\\",\\\"edit stage\\\",\\\"manage source\\\",\\\"edit source\\\",\\\"manage label\\\",\\\"edit label\\\"],\\\"hrm\\\":[\\\"create employee\\\",\\\"create set salary\\\",\\\"create pay slip\\\",\\\"create leave\\\",\\\"create attendance\\\",\\\"create training\\\",\\\"create award\\\",\\\"create branch\\\",\\\"create department\\\",\\\"create designation\\\",\\\"create document type\\\",\\\"view hrm dashboard\\\",\\\"view employee\\\",\\\"view set salary\\\",\\\"view pay slip\\\",\\\"view leave\\\",\\\"view attendance\\\",\\\"view training\\\",\\\"view award\\\",\\\"view branch\\\",\\\"view department\\\",\\\"view designation\\\",\\\"view document type\\\",\\\"delete employee\\\",\\\"delete set salary\\\",\\\"delete pay slip\\\",\\\"delete leave\\\",\\\"delete attendance\\\",\\\"delete training\\\",\\\"delete award\\\",\\\"delete branch\\\",\\\"delete department\\\",\\\"delete designation\\\",\\\"delete document type\\\",\\\"manage employee\\\",\\\"edit employee\\\",\\\"manage set salary\\\",\\\"edit set salary\\\",\\\"manage pay slip\\\",\\\"edit pay slip\\\",\\\"manage leave\\\",\\\"edit leave\\\",\\\"manage attendance\\\",\\\"edit attendance\\\",\\\"manage training\\\",\\\"edit training\\\",\\\"manage award\\\",\\\"edit award\\\",\\\"manage branch\\\",\\\"edit branch\\\",\\\"manage department\\\",\\\"edit department\\\",\\\"manage designation\\\",\\\"edit designation\\\",\\\"manage document type\\\",\\\"edit document type\\\"],\\\"project\\\":[\\\"create project\\\",\\\"create project task\\\",\\\"create timesheet\\\",\\\"create bug report\\\",\\\"create milestone\\\",\\\"create project stage\\\",\\\"create project task stage\\\",\\\"create project expense\\\",\\\"create activity\\\",\\\"create bug status\\\",\\\"view project dashboard\\\",\\\"view project\\\",\\\"view project task\\\",\\\"view timesheet\\\",\\\"view bug report\\\",\\\"view milestone\\\",\\\"view project stage\\\",\\\"view project task stage\\\",\\\"view project expense\\\",\\\"view activity\\\",\\\"view bug status\\\",\\\"delete project\\\",\\\"delete project task\\\",\\\"delete timesheet\\\",\\\"delete bug report\\\",\\\"delete milestone\\\",\\\"delete project stage\\\",\\\"delete project task stage\\\",\\\"delete project expense\\\",\\\"delete activity\\\",\\\"delete bug status\\\",\\\"manage project\\\",\\\"edit project\\\",\\\"manage project task\\\",\\\"edit project task\\\",\\\"manage timesheet\\\",\\\"edit timesheet\\\",\\\"manage bug report\\\",\\\"edit bug report\\\",\\\"manage milestone\\\",\\\"edit milestone\\\",\\\"manage project stage\\\",\\\"edit project stage\\\",\\\"manage project task stage\\\",\\\"edit project task stage\\\",\\\"manage project expense\\\",\\\"edit project expense\\\",\\\"manage activity\\\",\\\"edit activity\\\",\\\"manage bug status\\\",\\\"edit bug status\\\"],\\\"pos\\\":[\\\"create warehouse\\\",\\\"create purchase\\\",\\\"create quotation\\\",\\\"create pos\\\",\\\"create barcode\\\",\\\"create product\\\",\\\"create product category\\\",\\\"create product unit\\\",\\\"view pos dashboard\\\",\\\"view warehouse\\\",\\\"view purchase\\\",\\\"view quotation\\\",\\\"view pos\\\",\\\"view product\\\",\\\"view product category\\\",\\\"view product unit\\\",\\\"delete warehouse\\\",\\\"delete purchase\\\",\\\"delete quotation\\\",\\\"delete pos\\\",\\\"delete product\\\",\\\"delete product category\\\",\\\"delete product unit\\\",\\\"manage warehouse\\\",\\\"edit warehouse\\\",\\\"manage purchase\\\",\\\"edit purchase\\\",\\\"manage quotation\\\",\\\"edit quotation\\\",\\\"manage pos\\\",\\\"edit pos\\\",\\\"manage product\\\",\\\"edit product\\\",\\\"manage product category\\\",\\\"edit product category\\\",\\\"manage product unit\\\",\\\"edit product unit\\\"],\\\"support\\\":[\\\"create support\\\",\\\"view support dashboard\\\",\\\"view support\\\",\\\"delete support\\\",\\\"manage support\\\",\\\"edit support\\\",\\\"reply support\\\"],\\\"user_management\\\":[\\\"create user\\\",\\\"create client\\\",\\\"view user\\\",\\\"view client\\\",\\\"delete user\\\",\\\"delete client\\\",\\\"manage user\\\",\\\"edit user\\\",\\\"manage client\\\",\\\"edit client\\\"],\\\"booking\\\":[\\\"create booking\\\",\\\"create appointment\\\",\\\"create appointment booking\\\",\\\"create calendar event\\\",\\\"view booking dashboard\\\",\\\"view booking\\\",\\\"show booking\\\",\\\"view appointment\\\",\\\"show appointment\\\",\\\"view appointment booking\\\",\\\"show appointment booking\\\",\\\"view calendar event\\\",\\\"show calendar event\\\",\\\"delete booking\\\",\\\"delete appointment\\\",\\\"delete appointment booking\\\",\\\"delete calendar event\\\",\\\"manage booking\\\",\\\"edit booking\\\",\\\"manage appointment\\\",\\\"edit appointment\\\",\\\"manage appointment booking\\\",\\\"edit appointment booking\\\",\\\"manage calendar event\\\",\\\"edit calendar event\\\"],\\\"omx_flow\\\":[\\\"access omx flow\\\",\\\"whatsapp_flows\\\",\\\"whatsapp_orders\\\",\\\"campaigns\\\",\\\"templates\\\",\\\"chatbot\\\"],\\\"personal_tasks\\\":[\\\"create personal task\\\",\\\"create personal task comment\\\",\\\"create personal task file\\\",\\\"create personal task checklist\\\",\\\"view personal task\\\",\\\"delete personal task\\\",\\\"delete personal task comment\\\",\\\"delete personal task file\\\",\\\"delete personal task checklist\\\",\\\"manage personal task\\\",\\\"edit personal task\\\",\\\"edit personal task comment\\\",\\\"edit personal task checklist\\\",\\\"manage personal task time tracking\\\"],\\\"automatish\\\":[\\\"access automatish\\\"]}', `pricing_plans`.`updated_at` = '2025-07-31 05:36:18' where `id` = 10", "type": "query", "params": [], "bindings": ["{&quot;crm&quot;:[&quot;create lead&quot;,&quot;create deal&quot;,&quot;create form builder&quot;,&quot;create contract&quot;,&quot;create pipeline&quot;,&quot;create stage&quot;,&quot;create source&quot;,&quot;create label&quot;,&quot;view crm dashboard&quot;,&quot;view lead&quot;,&quot;view deal&quot;,&quot;view form builder&quot;,&quot;view contract&quot;,&quot;view pipeline&quot;,&quot;view stage&quot;,&quot;view source&quot;,&quot;view label&quot;,&quot;delete lead&quot;,&quot;delete deal&quot;,&quot;delete form builder&quot;,&quot;delete contract&quot;,&quot;delete pipeline&quot;,&quot;delete stage&quot;,&quot;delete source&quot;,&quot;delete label&quot;,&quot;manage lead&quot;,&quot;edit lead&quot;,&quot;manage deal&quot;,&quot;edit deal&quot;,&quot;manage form builder&quot;,&quot;edit form builder&quot;,&quot;manage contract&quot;,&quot;edit contract&quot;,&quot;manage pipeline&quot;,&quot;edit pipeline&quot;,&quot;manage stage&quot;,&quot;edit stage&quot;,&quot;manage source&quot;,&quot;edit source&quot;,&quot;manage label&quot;,&quot;edit label&quot;],&quot;hrm&quot;:[&quot;create employee&quot;,&quot;create set salary&quot;,&quot;create pay slip&quot;,&quot;create leave&quot;,&quot;create attendance&quot;,&quot;create training&quot;,&quot;create award&quot;,&quot;create branch&quot;,&quot;create department&quot;,&quot;create designation&quot;,&quot;create document type&quot;,&quot;view hrm dashboard&quot;,&quot;view employee&quot;,&quot;view set salary&quot;,&quot;view pay slip&quot;,&quot;view leave&quot;,&quot;view attendance&quot;,&quot;view training&quot;,&quot;view award&quot;,&quot;view branch&quot;,&quot;view department&quot;,&quot;view designation&quot;,&quot;view document type&quot;,&quot;delete employee&quot;,&quot;delete set salary&quot;,&quot;delete pay slip&quot;,&quot;delete leave&quot;,&quot;delete attendance&quot;,&quot;delete training&quot;,&quot;delete award&quot;,&quot;delete branch&quot;,&quot;delete department&quot;,&quot;delete designation&quot;,&quot;delete document type&quot;,&quot;manage employee&quot;,&quot;edit employee&quot;,&quot;manage set salary&quot;,&quot;edit set salary&quot;,&quot;manage pay slip&quot;,&quot;edit pay slip&quot;,&quot;manage leave&quot;,&quot;edit leave&quot;,&quot;manage attendance&quot;,&quot;edit attendance&quot;,&quot;manage training&quot;,&quot;edit training&quot;,&quot;manage award&quot;,&quot;edit award&quot;,&quot;manage branch&quot;,&quot;edit branch&quot;,&quot;manage department&quot;,&quot;edit department&quot;,&quot;manage designation&quot;,&quot;edit designation&quot;,&quot;manage document type&quot;,&quot;edit document type&quot;],&quot;project&quot;:[&quot;create project&quot;,&quot;create project task&quot;,&quot;create timesheet&quot;,&quot;create bug report&quot;,&quot;create milestone&quot;,&quot;create project stage&quot;,&quot;create project task stage&quot;,&quot;create project expense&quot;,&quot;create activity&quot;,&quot;create bug status&quot;,&quot;view project dashboard&quot;,&quot;view project&quot;,&quot;view project task&quot;,&quot;view timesheet&quot;,&quot;view bug report&quot;,&quot;view milestone&quot;,&quot;view project stage&quot;,&quot;view project task stage&quot;,&quot;view project expense&quot;,&quot;view activity&quot;,&quot;view bug status&quot;,&quot;delete project&quot;,&quot;delete project task&quot;,&quot;delete timesheet&quot;,&quot;delete bug report&quot;,&quot;delete milestone&quot;,&quot;delete project stage&quot;,&quot;delete project task stage&quot;,&quot;delete project expense&quot;,&quot;delete activity&quot;,&quot;delete bug status&quot;,&quot;manage project&quot;,&quot;edit project&quot;,&quot;manage project task&quot;,&quot;edit project task&quot;,&quot;manage timesheet&quot;,&quot;edit timesheet&quot;,&quot;manage bug report&quot;,&quot;edit bug report&quot;,&quot;manage milestone&quot;,&quot;edit milestone&quot;,&quot;manage project stage&quot;,&quot;edit project stage&quot;,&quot;manage project task stage&quot;,&quot;edit project task stage&quot;,&quot;manage project expense&quot;,&quot;edit project expense&quot;,&quot;manage activity&quot;,&quot;edit activity&quot;,&quot;manage bug status&quot;,&quot;edit bug status&quot;],&quot;pos&quot;:[&quot;create warehouse&quot;,&quot;create purchase&quot;,&quot;create quotation&quot;,&quot;create pos&quot;,&quot;create barcode&quot;,&quot;create product&quot;,&quot;create product category&quot;,&quot;create product unit&quot;,&quot;view pos dashboard&quot;,&quot;view warehouse&quot;,&quot;view purchase&quot;,&quot;view quotation&quot;,&quot;view pos&quot;,&quot;view product&quot;,&quot;view product category&quot;,&quot;view product unit&quot;,&quot;delete warehouse&quot;,&quot;delete purchase&quot;,&quot;delete quotation&quot;,&quot;delete pos&quot;,&quot;delete product&quot;,&quot;delete product category&quot;,&quot;delete product unit&quot;,&quot;manage warehouse&quot;,&quot;edit warehouse&quot;,&quot;manage purchase&quot;,&quot;edit purchase&quot;,&quot;manage quotation&quot;,&quot;edit quotation&quot;,&quot;manage pos&quot;,&quot;edit pos&quot;,&quot;manage product&quot;,&quot;edit product&quot;,&quot;manage product category&quot;,&quot;edit product category&quot;,&quot;manage product unit&quot;,&quot;edit product unit&quot;],&quot;support&quot;:[&quot;create support&quot;,&quot;view support dashboard&quot;,&quot;view support&quot;,&quot;delete support&quot;,&quot;manage support&quot;,&quot;edit support&quot;,&quot;reply support&quot;],&quot;user_management&quot;:[&quot;create user&quot;,&quot;create client&quot;,&quot;view user&quot;,&quot;view client&quot;,&quot;delete user&quot;,&quot;delete client&quot;,&quot;manage user&quot;,&quot;edit user&quot;,&quot;manage client&quot;,&quot;edit client&quot;],&quot;booking&quot;:[&quot;create booking&quot;,&quot;create appointment&quot;,&quot;create appointment booking&quot;,&quot;create calendar event&quot;,&quot;view booking dashboard&quot;,&quot;view booking&quot;,&quot;show booking&quot;,&quot;view appointment&quot;,&quot;show appointment&quot;,&quot;view appointment booking&quot;,&quot;show appointment booking&quot;,&quot;view calendar event&quot;,&quot;show calendar event&quot;,&quot;delete booking&quot;,&quot;delete appointment&quot;,&quot;delete appointment booking&quot;,&quot;delete calendar event&quot;,&quot;manage booking&quot;,&quot;edit booking&quot;,&quot;manage appointment&quot;,&quot;edit appointment&quot;,&quot;manage appointment booking&quot;,&quot;edit appointment booking&quot;,&quot;manage calendar event&quot;,&quot;edit calendar event&quot;],&quot;omx_flow&quot;:[&quot;access omx flow&quot;,&quot;whatsapp_flows&quot;,&quot;whatsapp_orders&quot;,&quot;campaigns&quot;,&quot;templates&quot;,&quot;chatbot&quot;],&quot;personal_tasks&quot;:[&quot;create personal task&quot;,&quot;create personal task comment&quot;,&quot;create personal task file&quot;,&quot;create personal task checklist&quot;,&quot;view personal task&quot;,&quot;delete personal task&quot;,&quot;delete personal task comment&quot;,&quot;delete personal task file&quot;,&quot;delete personal task checklist&quot;,&quot;manage personal task&quot;,&quot;edit personal task&quot;,&quot;edit personal task comment&quot;,&quot;edit personal task checklist&quot;,&quot;manage personal task time tracking&quot;],&quot;automatish&quot;:[&quot;access automatish&quot;]}", "2025-07-31 05:36:18", "10"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.479976, "duration": 0.03056, "duration_str": "30.56ms", "memory": 0, "memory_str": null, "filename": "PricingPlanController.php:217", "source": "app/Http/Controllers/PricingPlanController.php:217", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=217", "ajax": false, "filename": "PricingPlanController.php", "line": "217"}, "connection": "radhe_same", "start_percent": 10.766, "width_percent": 24.943}, {"sql": "select * from `users` where `plan` = 10 and `type` = 'company'", "type": "query", "params": [], "bindings": ["10", "company"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 30}, {"index": 16, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.568699, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:30", "source": "app/Observers/PricingPlanObserver.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=30", "ajax": false, "filename": "PricingPlanObserver.php", "line": "30"}, "connection": "radhe_same", "start_percent": 35.708, "width_percent": 1.494}, {"sql": "update `users` set `module_permissions` = '{\\\"crm\\\":[\\\"create lead\\\",\\\"create deal\\\",\\\"create form builder\\\",\\\"create contract\\\",\\\"create pipeline\\\",\\\"create stage\\\",\\\"create source\\\",\\\"create label\\\",\\\"view crm dashboard\\\",\\\"view lead\\\",\\\"view deal\\\",\\\"view form builder\\\",\\\"view contract\\\",\\\"view pipeline\\\",\\\"view stage\\\",\\\"view source\\\",\\\"view label\\\",\\\"delete lead\\\",\\\"delete deal\\\",\\\"delete form builder\\\",\\\"delete contract\\\",\\\"delete pipeline\\\",\\\"delete stage\\\",\\\"delete source\\\",\\\"delete label\\\",\\\"manage lead\\\",\\\"edit lead\\\",\\\"manage deal\\\",\\\"edit deal\\\",\\\"manage form builder\\\",\\\"edit form builder\\\",\\\"manage contract\\\",\\\"edit contract\\\",\\\"manage pipeline\\\",\\\"edit pipeline\\\",\\\"manage stage\\\",\\\"edit stage\\\",\\\"manage source\\\",\\\"edit source\\\",\\\"manage label\\\",\\\"edit label\\\"],\\\"hrm\\\":[\\\"create employee\\\",\\\"create set salary\\\",\\\"create pay slip\\\",\\\"create leave\\\",\\\"create attendance\\\",\\\"create training\\\",\\\"create award\\\",\\\"create branch\\\",\\\"create department\\\",\\\"create designation\\\",\\\"create document type\\\",\\\"view hrm dashboard\\\",\\\"view employee\\\",\\\"view set salary\\\",\\\"view pay slip\\\",\\\"view leave\\\",\\\"view attendance\\\",\\\"view training\\\",\\\"view award\\\",\\\"view branch\\\",\\\"view department\\\",\\\"view designation\\\",\\\"view document type\\\",\\\"delete employee\\\",\\\"delete set salary\\\",\\\"delete pay slip\\\",\\\"delete leave\\\",\\\"delete attendance\\\",\\\"delete training\\\",\\\"delete award\\\",\\\"delete branch\\\",\\\"delete department\\\",\\\"delete designation\\\",\\\"delete document type\\\",\\\"manage employee\\\",\\\"edit employee\\\",\\\"manage set salary\\\",\\\"edit set salary\\\",\\\"manage pay slip\\\",\\\"edit pay slip\\\",\\\"manage leave\\\",\\\"edit leave\\\",\\\"manage attendance\\\",\\\"edit attendance\\\",\\\"manage training\\\",\\\"edit training\\\",\\\"manage award\\\",\\\"edit award\\\",\\\"manage branch\\\",\\\"edit branch\\\",\\\"manage department\\\",\\\"edit department\\\",\\\"manage designation\\\",\\\"edit designation\\\",\\\"manage document type\\\",\\\"edit document type\\\"],\\\"project\\\":[\\\"create project\\\",\\\"create project task\\\",\\\"create timesheet\\\",\\\"create bug report\\\",\\\"create milestone\\\",\\\"create project stage\\\",\\\"create project task stage\\\",\\\"create project expense\\\",\\\"create activity\\\",\\\"create bug status\\\",\\\"view project dashboard\\\",\\\"view project\\\",\\\"view project task\\\",\\\"view timesheet\\\",\\\"view bug report\\\",\\\"view milestone\\\",\\\"view project stage\\\",\\\"view project task stage\\\",\\\"view project expense\\\",\\\"view activity\\\",\\\"view bug status\\\",\\\"delete project\\\",\\\"delete project task\\\",\\\"delete timesheet\\\",\\\"delete bug report\\\",\\\"delete milestone\\\",\\\"delete project stage\\\",\\\"delete project task stage\\\",\\\"delete project expense\\\",\\\"delete activity\\\",\\\"delete bug status\\\",\\\"manage project\\\",\\\"edit project\\\",\\\"manage project task\\\",\\\"edit project task\\\",\\\"manage timesheet\\\",\\\"edit timesheet\\\",\\\"manage bug report\\\",\\\"edit bug report\\\",\\\"manage milestone\\\",\\\"edit milestone\\\",\\\"manage project stage\\\",\\\"edit project stage\\\",\\\"manage project task stage\\\",\\\"edit project task stage\\\",\\\"manage project expense\\\",\\\"edit project expense\\\",\\\"manage activity\\\",\\\"edit activity\\\",\\\"manage bug status\\\",\\\"edit bug status\\\"],\\\"pos\\\":[\\\"create warehouse\\\",\\\"create purchase\\\",\\\"create quotation\\\",\\\"create pos\\\",\\\"create barcode\\\",\\\"create product\\\",\\\"create product category\\\",\\\"create product unit\\\",\\\"view pos dashboard\\\",\\\"view warehouse\\\",\\\"view purchase\\\",\\\"view quotation\\\",\\\"view pos\\\",\\\"view product\\\",\\\"view product category\\\",\\\"view product unit\\\",\\\"delete warehouse\\\",\\\"delete purchase\\\",\\\"delete quotation\\\",\\\"delete pos\\\",\\\"delete product\\\",\\\"delete product category\\\",\\\"delete product unit\\\",\\\"manage warehouse\\\",\\\"edit warehouse\\\",\\\"manage purchase\\\",\\\"edit purchase\\\",\\\"manage quotation\\\",\\\"edit quotation\\\",\\\"manage pos\\\",\\\"edit pos\\\",\\\"manage product\\\",\\\"edit product\\\",\\\"manage product category\\\",\\\"edit product category\\\",\\\"manage product unit\\\",\\\"edit product unit\\\"],\\\"support\\\":[\\\"create support\\\",\\\"view support dashboard\\\",\\\"view support\\\",\\\"delete support\\\",\\\"manage support\\\",\\\"edit support\\\",\\\"reply support\\\"],\\\"user_management\\\":[\\\"create user\\\",\\\"create client\\\",\\\"view user\\\",\\\"view client\\\",\\\"delete user\\\",\\\"delete client\\\",\\\"manage user\\\",\\\"edit user\\\",\\\"manage client\\\",\\\"edit client\\\"],\\\"booking\\\":[\\\"create booking\\\",\\\"create appointment\\\",\\\"create appointment booking\\\",\\\"create calendar event\\\",\\\"view booking dashboard\\\",\\\"view booking\\\",\\\"show booking\\\",\\\"view appointment\\\",\\\"show appointment\\\",\\\"view appointment booking\\\",\\\"show appointment booking\\\",\\\"view calendar event\\\",\\\"show calendar event\\\",\\\"delete booking\\\",\\\"delete appointment\\\",\\\"delete appointment booking\\\",\\\"delete calendar event\\\",\\\"manage booking\\\",\\\"edit booking\\\",\\\"manage appointment\\\",\\\"edit appointment\\\",\\\"manage appointment booking\\\",\\\"edit appointment booking\\\",\\\"manage calendar event\\\",\\\"edit calendar event\\\"],\\\"omx_flow\\\":[\\\"access omx flow\\\",\\\"whatsapp_flows\\\",\\\"whatsapp_orders\\\",\\\"campaigns\\\",\\\"templates\\\",\\\"chatbot\\\"],\\\"personal_tasks\\\":[\\\"create personal task\\\",\\\"create personal task comment\\\",\\\"create personal task file\\\",\\\"create personal task checklist\\\",\\\"view personal task\\\",\\\"delete personal task\\\",\\\"delete personal task comment\\\",\\\"delete personal task file\\\",\\\"delete personal task checklist\\\",\\\"manage personal task\\\",\\\"edit personal task\\\",\\\"edit personal task comment\\\",\\\"edit personal task checklist\\\",\\\"manage personal task time tracking\\\"],\\\"automatish\\\":[\\\"access automatish\\\"]}', `users`.`updated_at` = '2025-07-31 05:36:18' where `id` = 74", "type": "query", "params": [], "bindings": ["{&quot;crm&quot;:[&quot;create lead&quot;,&quot;create deal&quot;,&quot;create form builder&quot;,&quot;create contract&quot;,&quot;create pipeline&quot;,&quot;create stage&quot;,&quot;create source&quot;,&quot;create label&quot;,&quot;view crm dashboard&quot;,&quot;view lead&quot;,&quot;view deal&quot;,&quot;view form builder&quot;,&quot;view contract&quot;,&quot;view pipeline&quot;,&quot;view stage&quot;,&quot;view source&quot;,&quot;view label&quot;,&quot;delete lead&quot;,&quot;delete deal&quot;,&quot;delete form builder&quot;,&quot;delete contract&quot;,&quot;delete pipeline&quot;,&quot;delete stage&quot;,&quot;delete source&quot;,&quot;delete label&quot;,&quot;manage lead&quot;,&quot;edit lead&quot;,&quot;manage deal&quot;,&quot;edit deal&quot;,&quot;manage form builder&quot;,&quot;edit form builder&quot;,&quot;manage contract&quot;,&quot;edit contract&quot;,&quot;manage pipeline&quot;,&quot;edit pipeline&quot;,&quot;manage stage&quot;,&quot;edit stage&quot;,&quot;manage source&quot;,&quot;edit source&quot;,&quot;manage label&quot;,&quot;edit label&quot;],&quot;hrm&quot;:[&quot;create employee&quot;,&quot;create set salary&quot;,&quot;create pay slip&quot;,&quot;create leave&quot;,&quot;create attendance&quot;,&quot;create training&quot;,&quot;create award&quot;,&quot;create branch&quot;,&quot;create department&quot;,&quot;create designation&quot;,&quot;create document type&quot;,&quot;view hrm dashboard&quot;,&quot;view employee&quot;,&quot;view set salary&quot;,&quot;view pay slip&quot;,&quot;view leave&quot;,&quot;view attendance&quot;,&quot;view training&quot;,&quot;view award&quot;,&quot;view branch&quot;,&quot;view department&quot;,&quot;view designation&quot;,&quot;view document type&quot;,&quot;delete employee&quot;,&quot;delete set salary&quot;,&quot;delete pay slip&quot;,&quot;delete leave&quot;,&quot;delete attendance&quot;,&quot;delete training&quot;,&quot;delete award&quot;,&quot;delete branch&quot;,&quot;delete department&quot;,&quot;delete designation&quot;,&quot;delete document type&quot;,&quot;manage employee&quot;,&quot;edit employee&quot;,&quot;manage set salary&quot;,&quot;edit set salary&quot;,&quot;manage pay slip&quot;,&quot;edit pay slip&quot;,&quot;manage leave&quot;,&quot;edit leave&quot;,&quot;manage attendance&quot;,&quot;edit attendance&quot;,&quot;manage training&quot;,&quot;edit training&quot;,&quot;manage award&quot;,&quot;edit award&quot;,&quot;manage branch&quot;,&quot;edit branch&quot;,&quot;manage department&quot;,&quot;edit department&quot;,&quot;manage designation&quot;,&quot;edit designation&quot;,&quot;manage document type&quot;,&quot;edit document type&quot;],&quot;project&quot;:[&quot;create project&quot;,&quot;create project task&quot;,&quot;create timesheet&quot;,&quot;create bug report&quot;,&quot;create milestone&quot;,&quot;create project stage&quot;,&quot;create project task stage&quot;,&quot;create project expense&quot;,&quot;create activity&quot;,&quot;create bug status&quot;,&quot;view project dashboard&quot;,&quot;view project&quot;,&quot;view project task&quot;,&quot;view timesheet&quot;,&quot;view bug report&quot;,&quot;view milestone&quot;,&quot;view project stage&quot;,&quot;view project task stage&quot;,&quot;view project expense&quot;,&quot;view activity&quot;,&quot;view bug status&quot;,&quot;delete project&quot;,&quot;delete project task&quot;,&quot;delete timesheet&quot;,&quot;delete bug report&quot;,&quot;delete milestone&quot;,&quot;delete project stage&quot;,&quot;delete project task stage&quot;,&quot;delete project expense&quot;,&quot;delete activity&quot;,&quot;delete bug status&quot;,&quot;manage project&quot;,&quot;edit project&quot;,&quot;manage project task&quot;,&quot;edit project task&quot;,&quot;manage timesheet&quot;,&quot;edit timesheet&quot;,&quot;manage bug report&quot;,&quot;edit bug report&quot;,&quot;manage milestone&quot;,&quot;edit milestone&quot;,&quot;manage project stage&quot;,&quot;edit project stage&quot;,&quot;manage project task stage&quot;,&quot;edit project task stage&quot;,&quot;manage project expense&quot;,&quot;edit project expense&quot;,&quot;manage activity&quot;,&quot;edit activity&quot;,&quot;manage bug status&quot;,&quot;edit bug status&quot;],&quot;pos&quot;:[&quot;create warehouse&quot;,&quot;create purchase&quot;,&quot;create quotation&quot;,&quot;create pos&quot;,&quot;create barcode&quot;,&quot;create product&quot;,&quot;create product category&quot;,&quot;create product unit&quot;,&quot;view pos dashboard&quot;,&quot;view warehouse&quot;,&quot;view purchase&quot;,&quot;view quotation&quot;,&quot;view pos&quot;,&quot;view product&quot;,&quot;view product category&quot;,&quot;view product unit&quot;,&quot;delete warehouse&quot;,&quot;delete purchase&quot;,&quot;delete quotation&quot;,&quot;delete pos&quot;,&quot;delete product&quot;,&quot;delete product category&quot;,&quot;delete product unit&quot;,&quot;manage warehouse&quot;,&quot;edit warehouse&quot;,&quot;manage purchase&quot;,&quot;edit purchase&quot;,&quot;manage quotation&quot;,&quot;edit quotation&quot;,&quot;manage pos&quot;,&quot;edit pos&quot;,&quot;manage product&quot;,&quot;edit product&quot;,&quot;manage product category&quot;,&quot;edit product category&quot;,&quot;manage product unit&quot;,&quot;edit product unit&quot;],&quot;support&quot;:[&quot;create support&quot;,&quot;view support dashboard&quot;,&quot;view support&quot;,&quot;delete support&quot;,&quot;manage support&quot;,&quot;edit support&quot;,&quot;reply support&quot;],&quot;user_management&quot;:[&quot;create user&quot;,&quot;create client&quot;,&quot;view user&quot;,&quot;view client&quot;,&quot;delete user&quot;,&quot;delete client&quot;,&quot;manage user&quot;,&quot;edit user&quot;,&quot;manage client&quot;,&quot;edit client&quot;],&quot;booking&quot;:[&quot;create booking&quot;,&quot;create appointment&quot;,&quot;create appointment booking&quot;,&quot;create calendar event&quot;,&quot;view booking dashboard&quot;,&quot;view booking&quot;,&quot;show booking&quot;,&quot;view appointment&quot;,&quot;show appointment&quot;,&quot;view appointment booking&quot;,&quot;show appointment booking&quot;,&quot;view calendar event&quot;,&quot;show calendar event&quot;,&quot;delete booking&quot;,&quot;delete appointment&quot;,&quot;delete appointment booking&quot;,&quot;delete calendar event&quot;,&quot;manage booking&quot;,&quot;edit booking&quot;,&quot;manage appointment&quot;,&quot;edit appointment&quot;,&quot;manage appointment booking&quot;,&quot;edit appointment booking&quot;,&quot;manage calendar event&quot;,&quot;edit calendar event&quot;],&quot;omx_flow&quot;:[&quot;access omx flow&quot;,&quot;whatsapp_flows&quot;,&quot;whatsapp_orders&quot;,&quot;campaigns&quot;,&quot;templates&quot;,&quot;chatbot&quot;],&quot;personal_tasks&quot;:[&quot;create personal task&quot;,&quot;create personal task comment&quot;,&quot;create personal task file&quot;,&quot;create personal task checklist&quot;,&quot;view personal task&quot;,&quot;delete personal task&quot;,&quot;delete personal task comment&quot;,&quot;delete personal task file&quot;,&quot;delete personal task checklist&quot;,&quot;manage personal task&quot;,&quot;edit personal task&quot;,&quot;edit personal task comment&quot;,&quot;edit personal task checklist&quot;,&quot;manage personal task time tracking&quot;],&quot;automatish&quot;:[&quot;access automatish&quot;]}", "2025-07-31 05:36:18", "74"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 35}, {"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.578441, "duration": 0.00851, "duration_str": "8.51ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:35", "source": "app/Observers/PricingPlanObserver.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=35", "ajax": false, "filename": "PricingPlanObserver.php", "line": "35"}, "connection": "radhe_same", "start_percent": 37.202, "width_percent": 6.946}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 76}, {"index": 16, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.597111, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:76", "source": "app/Observers/PricingPlanObserver.php:76", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=76", "ajax": false, "filename": "PricingPlanObserver.php", "line": "76"}, "connection": "radhe_same", "start_percent": 44.148, "width_percent": 2.073}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 91}, {"index": 18, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 19, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}], "start": **********.707769, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 46.221, "width_percent": 0.873}, {"sql": "update `users` set `module_permissions` = '{\\\"crm\\\":[\\\"create lead\\\",\\\"create deal\\\",\\\"create form builder\\\",\\\"create contract\\\",\\\"create pipeline\\\",\\\"create stage\\\",\\\"create source\\\",\\\"create label\\\",\\\"view crm dashboard\\\",\\\"view lead\\\",\\\"view deal\\\",\\\"view form builder\\\",\\\"view contract\\\",\\\"view pipeline\\\",\\\"view stage\\\",\\\"view source\\\",\\\"view label\\\",\\\"delete lead\\\",\\\"delete deal\\\",\\\"delete form builder\\\",\\\"delete contract\\\",\\\"delete pipeline\\\",\\\"delete stage\\\",\\\"delete source\\\",\\\"delete label\\\",\\\"manage lead\\\",\\\"edit lead\\\",\\\"manage deal\\\",\\\"edit deal\\\",\\\"manage form builder\\\",\\\"edit form builder\\\",\\\"manage contract\\\",\\\"edit contract\\\",\\\"manage pipeline\\\",\\\"edit pipeline\\\",\\\"manage stage\\\",\\\"edit stage\\\",\\\"manage source\\\",\\\"edit source\\\",\\\"manage label\\\",\\\"edit label\\\"],\\\"hrm\\\":[\\\"create employee\\\",\\\"create set salary\\\",\\\"create pay slip\\\",\\\"create leave\\\",\\\"create attendance\\\",\\\"create training\\\",\\\"create award\\\",\\\"create branch\\\",\\\"create department\\\",\\\"create designation\\\",\\\"create document type\\\",\\\"view hrm dashboard\\\",\\\"view employee\\\",\\\"view set salary\\\",\\\"view pay slip\\\",\\\"view leave\\\",\\\"view attendance\\\",\\\"view training\\\",\\\"view award\\\",\\\"view branch\\\",\\\"view department\\\",\\\"view designation\\\",\\\"view document type\\\",\\\"delete employee\\\",\\\"delete set salary\\\",\\\"delete pay slip\\\",\\\"delete leave\\\",\\\"delete attendance\\\",\\\"delete training\\\",\\\"delete award\\\",\\\"delete branch\\\",\\\"delete department\\\",\\\"delete designation\\\",\\\"delete document type\\\",\\\"manage employee\\\",\\\"edit employee\\\",\\\"manage set salary\\\",\\\"edit set salary\\\",\\\"manage pay slip\\\",\\\"edit pay slip\\\",\\\"manage leave\\\",\\\"edit leave\\\",\\\"manage attendance\\\",\\\"edit attendance\\\",\\\"manage training\\\",\\\"edit training\\\",\\\"manage award\\\",\\\"edit award\\\",\\\"manage branch\\\",\\\"edit branch\\\",\\\"manage department\\\",\\\"edit department\\\",\\\"manage designation\\\",\\\"edit designation\\\",\\\"manage document type\\\",\\\"edit document type\\\"],\\\"project\\\":[\\\"create project\\\",\\\"create project task\\\",\\\"create timesheet\\\",\\\"create bug report\\\",\\\"create milestone\\\",\\\"create project stage\\\",\\\"create project task stage\\\",\\\"create project expense\\\",\\\"create activity\\\",\\\"create bug status\\\",\\\"view project dashboard\\\",\\\"view project\\\",\\\"view project task\\\",\\\"view timesheet\\\",\\\"view bug report\\\",\\\"view milestone\\\",\\\"view project stage\\\",\\\"view project task stage\\\",\\\"view project expense\\\",\\\"view activity\\\",\\\"view bug status\\\",\\\"delete project\\\",\\\"delete project task\\\",\\\"delete timesheet\\\",\\\"delete bug report\\\",\\\"delete milestone\\\",\\\"delete project stage\\\",\\\"delete project task stage\\\",\\\"delete project expense\\\",\\\"delete activity\\\",\\\"delete bug status\\\",\\\"manage project\\\",\\\"edit project\\\",\\\"manage project task\\\",\\\"edit project task\\\",\\\"manage timesheet\\\",\\\"edit timesheet\\\",\\\"manage bug report\\\",\\\"edit bug report\\\",\\\"manage milestone\\\",\\\"edit milestone\\\",\\\"manage project stage\\\",\\\"edit project stage\\\",\\\"manage project task stage\\\",\\\"edit project task stage\\\",\\\"manage project expense\\\",\\\"edit project expense\\\",\\\"manage activity\\\",\\\"edit activity\\\",\\\"manage bug status\\\",\\\"edit bug status\\\"],\\\"pos\\\":[\\\"create warehouse\\\",\\\"create purchase\\\",\\\"create quotation\\\",\\\"create pos\\\",\\\"create barcode\\\",\\\"create product\\\",\\\"create product category\\\",\\\"create product unit\\\",\\\"view pos dashboard\\\",\\\"view warehouse\\\",\\\"view purchase\\\",\\\"view quotation\\\",\\\"view pos\\\",\\\"view product\\\",\\\"view product category\\\",\\\"view product unit\\\",\\\"delete warehouse\\\",\\\"delete purchase\\\",\\\"delete quotation\\\",\\\"delete pos\\\",\\\"delete product\\\",\\\"delete product category\\\",\\\"delete product unit\\\",\\\"manage warehouse\\\",\\\"edit warehouse\\\",\\\"manage purchase\\\",\\\"edit purchase\\\",\\\"manage quotation\\\",\\\"edit quotation\\\",\\\"manage pos\\\",\\\"edit pos\\\",\\\"manage product\\\",\\\"edit product\\\",\\\"manage product category\\\",\\\"edit product category\\\",\\\"manage product unit\\\",\\\"edit product unit\\\"],\\\"support\\\":[\\\"create support\\\",\\\"view support dashboard\\\",\\\"view support\\\",\\\"delete support\\\",\\\"manage support\\\",\\\"edit support\\\",\\\"reply support\\\"],\\\"user_management\\\":[\\\"create user\\\",\\\"create client\\\",\\\"view user\\\",\\\"view client\\\",\\\"delete user\\\",\\\"delete client\\\",\\\"manage user\\\",\\\"edit user\\\",\\\"manage client\\\",\\\"edit client\\\"],\\\"booking\\\":[\\\"create booking\\\",\\\"create appointment\\\",\\\"create appointment booking\\\",\\\"create calendar event\\\",\\\"view booking dashboard\\\",\\\"view booking\\\",\\\"show booking\\\",\\\"view appointment\\\",\\\"show appointment\\\",\\\"view appointment booking\\\",\\\"show appointment booking\\\",\\\"view calendar event\\\",\\\"show calendar event\\\",\\\"delete booking\\\",\\\"delete appointment\\\",\\\"delete appointment booking\\\",\\\"delete calendar event\\\",\\\"manage booking\\\",\\\"edit booking\\\",\\\"manage appointment\\\",\\\"edit appointment\\\",\\\"manage appointment booking\\\",\\\"edit appointment booking\\\",\\\"manage calendar event\\\",\\\"edit calendar event\\\"],\\\"omx_flow\\\":[\\\"access omx flow\\\",\\\"whatsapp_flows\\\",\\\"whatsapp_orders\\\",\\\"campaigns\\\",\\\"templates\\\",\\\"chatbot\\\"],\\\"personal_tasks\\\":[\\\"create personal task\\\",\\\"create personal task comment\\\",\\\"create personal task file\\\",\\\"create personal task checklist\\\",\\\"view personal task\\\",\\\"delete personal task\\\",\\\"delete personal task comment\\\",\\\"delete personal task file\\\",\\\"delete personal task checklist\\\",\\\"manage personal task\\\",\\\"edit personal task\\\",\\\"edit personal task comment\\\",\\\"edit personal task checklist\\\",\\\"manage personal task time tracking\\\"],\\\"automatish\\\":[\\\"access automatish\\\"]}', `users`.`updated_at` = '2025-07-31 05:36:24' where `id` = 79", "type": "query", "params": [], "bindings": ["{&quot;crm&quot;:[&quot;create lead&quot;,&quot;create deal&quot;,&quot;create form builder&quot;,&quot;create contract&quot;,&quot;create pipeline&quot;,&quot;create stage&quot;,&quot;create source&quot;,&quot;create label&quot;,&quot;view crm dashboard&quot;,&quot;view lead&quot;,&quot;view deal&quot;,&quot;view form builder&quot;,&quot;view contract&quot;,&quot;view pipeline&quot;,&quot;view stage&quot;,&quot;view source&quot;,&quot;view label&quot;,&quot;delete lead&quot;,&quot;delete deal&quot;,&quot;delete form builder&quot;,&quot;delete contract&quot;,&quot;delete pipeline&quot;,&quot;delete stage&quot;,&quot;delete source&quot;,&quot;delete label&quot;,&quot;manage lead&quot;,&quot;edit lead&quot;,&quot;manage deal&quot;,&quot;edit deal&quot;,&quot;manage form builder&quot;,&quot;edit form builder&quot;,&quot;manage contract&quot;,&quot;edit contract&quot;,&quot;manage pipeline&quot;,&quot;edit pipeline&quot;,&quot;manage stage&quot;,&quot;edit stage&quot;,&quot;manage source&quot;,&quot;edit source&quot;,&quot;manage label&quot;,&quot;edit label&quot;],&quot;hrm&quot;:[&quot;create employee&quot;,&quot;create set salary&quot;,&quot;create pay slip&quot;,&quot;create leave&quot;,&quot;create attendance&quot;,&quot;create training&quot;,&quot;create award&quot;,&quot;create branch&quot;,&quot;create department&quot;,&quot;create designation&quot;,&quot;create document type&quot;,&quot;view hrm dashboard&quot;,&quot;view employee&quot;,&quot;view set salary&quot;,&quot;view pay slip&quot;,&quot;view leave&quot;,&quot;view attendance&quot;,&quot;view training&quot;,&quot;view award&quot;,&quot;view branch&quot;,&quot;view department&quot;,&quot;view designation&quot;,&quot;view document type&quot;,&quot;delete employee&quot;,&quot;delete set salary&quot;,&quot;delete pay slip&quot;,&quot;delete leave&quot;,&quot;delete attendance&quot;,&quot;delete training&quot;,&quot;delete award&quot;,&quot;delete branch&quot;,&quot;delete department&quot;,&quot;delete designation&quot;,&quot;delete document type&quot;,&quot;manage employee&quot;,&quot;edit employee&quot;,&quot;manage set salary&quot;,&quot;edit set salary&quot;,&quot;manage pay slip&quot;,&quot;edit pay slip&quot;,&quot;manage leave&quot;,&quot;edit leave&quot;,&quot;manage attendance&quot;,&quot;edit attendance&quot;,&quot;manage training&quot;,&quot;edit training&quot;,&quot;manage award&quot;,&quot;edit award&quot;,&quot;manage branch&quot;,&quot;edit branch&quot;,&quot;manage department&quot;,&quot;edit department&quot;,&quot;manage designation&quot;,&quot;edit designation&quot;,&quot;manage document type&quot;,&quot;edit document type&quot;],&quot;project&quot;:[&quot;create project&quot;,&quot;create project task&quot;,&quot;create timesheet&quot;,&quot;create bug report&quot;,&quot;create milestone&quot;,&quot;create project stage&quot;,&quot;create project task stage&quot;,&quot;create project expense&quot;,&quot;create activity&quot;,&quot;create bug status&quot;,&quot;view project dashboard&quot;,&quot;view project&quot;,&quot;view project task&quot;,&quot;view timesheet&quot;,&quot;view bug report&quot;,&quot;view milestone&quot;,&quot;view project stage&quot;,&quot;view project task stage&quot;,&quot;view project expense&quot;,&quot;view activity&quot;,&quot;view bug status&quot;,&quot;delete project&quot;,&quot;delete project task&quot;,&quot;delete timesheet&quot;,&quot;delete bug report&quot;,&quot;delete milestone&quot;,&quot;delete project stage&quot;,&quot;delete project task stage&quot;,&quot;delete project expense&quot;,&quot;delete activity&quot;,&quot;delete bug status&quot;,&quot;manage project&quot;,&quot;edit project&quot;,&quot;manage project task&quot;,&quot;edit project task&quot;,&quot;manage timesheet&quot;,&quot;edit timesheet&quot;,&quot;manage bug report&quot;,&quot;edit bug report&quot;,&quot;manage milestone&quot;,&quot;edit milestone&quot;,&quot;manage project stage&quot;,&quot;edit project stage&quot;,&quot;manage project task stage&quot;,&quot;edit project task stage&quot;,&quot;manage project expense&quot;,&quot;edit project expense&quot;,&quot;manage activity&quot;,&quot;edit activity&quot;,&quot;manage bug status&quot;,&quot;edit bug status&quot;],&quot;pos&quot;:[&quot;create warehouse&quot;,&quot;create purchase&quot;,&quot;create quotation&quot;,&quot;create pos&quot;,&quot;create barcode&quot;,&quot;create product&quot;,&quot;create product category&quot;,&quot;create product unit&quot;,&quot;view pos dashboard&quot;,&quot;view warehouse&quot;,&quot;view purchase&quot;,&quot;view quotation&quot;,&quot;view pos&quot;,&quot;view product&quot;,&quot;view product category&quot;,&quot;view product unit&quot;,&quot;delete warehouse&quot;,&quot;delete purchase&quot;,&quot;delete quotation&quot;,&quot;delete pos&quot;,&quot;delete product&quot;,&quot;delete product category&quot;,&quot;delete product unit&quot;,&quot;manage warehouse&quot;,&quot;edit warehouse&quot;,&quot;manage purchase&quot;,&quot;edit purchase&quot;,&quot;manage quotation&quot;,&quot;edit quotation&quot;,&quot;manage pos&quot;,&quot;edit pos&quot;,&quot;manage product&quot;,&quot;edit product&quot;,&quot;manage product category&quot;,&quot;edit product category&quot;,&quot;manage product unit&quot;,&quot;edit product unit&quot;],&quot;support&quot;:[&quot;create support&quot;,&quot;view support dashboard&quot;,&quot;view support&quot;,&quot;delete support&quot;,&quot;manage support&quot;,&quot;edit support&quot;,&quot;reply support&quot;],&quot;user_management&quot;:[&quot;create user&quot;,&quot;create client&quot;,&quot;view user&quot;,&quot;view client&quot;,&quot;delete user&quot;,&quot;delete client&quot;,&quot;manage user&quot;,&quot;edit user&quot;,&quot;manage client&quot;,&quot;edit client&quot;],&quot;booking&quot;:[&quot;create booking&quot;,&quot;create appointment&quot;,&quot;create appointment booking&quot;,&quot;create calendar event&quot;,&quot;view booking dashboard&quot;,&quot;view booking&quot;,&quot;show booking&quot;,&quot;view appointment&quot;,&quot;show appointment&quot;,&quot;view appointment booking&quot;,&quot;show appointment booking&quot;,&quot;view calendar event&quot;,&quot;show calendar event&quot;,&quot;delete booking&quot;,&quot;delete appointment&quot;,&quot;delete appointment booking&quot;,&quot;delete calendar event&quot;,&quot;manage booking&quot;,&quot;edit booking&quot;,&quot;manage appointment&quot;,&quot;edit appointment&quot;,&quot;manage appointment booking&quot;,&quot;edit appointment booking&quot;,&quot;manage calendar event&quot;,&quot;edit calendar event&quot;],&quot;omx_flow&quot;:[&quot;access omx flow&quot;,&quot;whatsapp_flows&quot;,&quot;whatsapp_orders&quot;,&quot;campaigns&quot;,&quot;templates&quot;,&quot;chatbot&quot;],&quot;personal_tasks&quot;:[&quot;create personal task&quot;,&quot;create personal task comment&quot;,&quot;create personal task file&quot;,&quot;create personal task checklist&quot;,&quot;view personal task&quot;,&quot;delete personal task&quot;,&quot;delete personal task comment&quot;,&quot;delete personal task file&quot;,&quot;delete personal task checklist&quot;,&quot;manage personal task&quot;,&quot;edit personal task&quot;,&quot;edit personal task comment&quot;,&quot;edit personal task checklist&quot;,&quot;manage personal task time tracking&quot;],&quot;automatish&quot;:[&quot;access automatish&quot;]}", "2025-07-31 05:36:24", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 35}, {"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.995534, "duration": 0.00721, "duration_str": "7.21ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:35", "source": "app/Observers/PricingPlanObserver.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=35", "ajax": false, "filename": "PricingPlanObserver.php", "line": "35"}, "connection": "radhe_same", "start_percent": 47.094, "width_percent": 5.885}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 76}, {"index": 16, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.020875, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:76", "source": "app/Observers/PricingPlanObserver.php:76", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=76", "ajax": false, "filename": "PricingPlanObserver.php", "line": "76"}, "connection": "radhe_same", "start_percent": 52.979, "width_percent": 1.502}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 91}, {"index": 18, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 19, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}], "start": **********.03583, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 54.481, "width_percent": 1.681}, {"sql": "update `users` set `module_permissions` = '{\\\"crm\\\":[\\\"create lead\\\",\\\"create deal\\\",\\\"create form builder\\\",\\\"create contract\\\",\\\"create pipeline\\\",\\\"create stage\\\",\\\"create source\\\",\\\"create label\\\",\\\"view crm dashboard\\\",\\\"view lead\\\",\\\"view deal\\\",\\\"view form builder\\\",\\\"view contract\\\",\\\"view pipeline\\\",\\\"view stage\\\",\\\"view source\\\",\\\"view label\\\",\\\"delete lead\\\",\\\"delete deal\\\",\\\"delete form builder\\\",\\\"delete contract\\\",\\\"delete pipeline\\\",\\\"delete stage\\\",\\\"delete source\\\",\\\"delete label\\\",\\\"manage lead\\\",\\\"edit lead\\\",\\\"manage deal\\\",\\\"edit deal\\\",\\\"manage form builder\\\",\\\"edit form builder\\\",\\\"manage contract\\\",\\\"edit contract\\\",\\\"manage pipeline\\\",\\\"edit pipeline\\\",\\\"manage stage\\\",\\\"edit stage\\\",\\\"manage source\\\",\\\"edit source\\\",\\\"manage label\\\",\\\"edit label\\\"],\\\"hrm\\\":[\\\"create employee\\\",\\\"create set salary\\\",\\\"create pay slip\\\",\\\"create leave\\\",\\\"create attendance\\\",\\\"create training\\\",\\\"create award\\\",\\\"create branch\\\",\\\"create department\\\",\\\"create designation\\\",\\\"create document type\\\",\\\"view hrm dashboard\\\",\\\"view employee\\\",\\\"view set salary\\\",\\\"view pay slip\\\",\\\"view leave\\\",\\\"view attendance\\\",\\\"view training\\\",\\\"view award\\\",\\\"view branch\\\",\\\"view department\\\",\\\"view designation\\\",\\\"view document type\\\",\\\"delete employee\\\",\\\"delete set salary\\\",\\\"delete pay slip\\\",\\\"delete leave\\\",\\\"delete attendance\\\",\\\"delete training\\\",\\\"delete award\\\",\\\"delete branch\\\",\\\"delete department\\\",\\\"delete designation\\\",\\\"delete document type\\\",\\\"manage employee\\\",\\\"edit employee\\\",\\\"manage set salary\\\",\\\"edit set salary\\\",\\\"manage pay slip\\\",\\\"edit pay slip\\\",\\\"manage leave\\\",\\\"edit leave\\\",\\\"manage attendance\\\",\\\"edit attendance\\\",\\\"manage training\\\",\\\"edit training\\\",\\\"manage award\\\",\\\"edit award\\\",\\\"manage branch\\\",\\\"edit branch\\\",\\\"manage department\\\",\\\"edit department\\\",\\\"manage designation\\\",\\\"edit designation\\\",\\\"manage document type\\\",\\\"edit document type\\\"],\\\"project\\\":[\\\"create project\\\",\\\"create project task\\\",\\\"create timesheet\\\",\\\"create bug report\\\",\\\"create milestone\\\",\\\"create project stage\\\",\\\"create project task stage\\\",\\\"create project expense\\\",\\\"create activity\\\",\\\"create bug status\\\",\\\"view project dashboard\\\",\\\"view project\\\",\\\"view project task\\\",\\\"view timesheet\\\",\\\"view bug report\\\",\\\"view milestone\\\",\\\"view project stage\\\",\\\"view project task stage\\\",\\\"view project expense\\\",\\\"view activity\\\",\\\"view bug status\\\",\\\"delete project\\\",\\\"delete project task\\\",\\\"delete timesheet\\\",\\\"delete bug report\\\",\\\"delete milestone\\\",\\\"delete project stage\\\",\\\"delete project task stage\\\",\\\"delete project expense\\\",\\\"delete activity\\\",\\\"delete bug status\\\",\\\"manage project\\\",\\\"edit project\\\",\\\"manage project task\\\",\\\"edit project task\\\",\\\"manage timesheet\\\",\\\"edit timesheet\\\",\\\"manage bug report\\\",\\\"edit bug report\\\",\\\"manage milestone\\\",\\\"edit milestone\\\",\\\"manage project stage\\\",\\\"edit project stage\\\",\\\"manage project task stage\\\",\\\"edit project task stage\\\",\\\"manage project expense\\\",\\\"edit project expense\\\",\\\"manage activity\\\",\\\"edit activity\\\",\\\"manage bug status\\\",\\\"edit bug status\\\"],\\\"pos\\\":[\\\"create warehouse\\\",\\\"create purchase\\\",\\\"create quotation\\\",\\\"create pos\\\",\\\"create barcode\\\",\\\"create product\\\",\\\"create product category\\\",\\\"create product unit\\\",\\\"view pos dashboard\\\",\\\"view warehouse\\\",\\\"view purchase\\\",\\\"view quotation\\\",\\\"view pos\\\",\\\"view product\\\",\\\"view product category\\\",\\\"view product unit\\\",\\\"delete warehouse\\\",\\\"delete purchase\\\",\\\"delete quotation\\\",\\\"delete pos\\\",\\\"delete product\\\",\\\"delete product category\\\",\\\"delete product unit\\\",\\\"manage warehouse\\\",\\\"edit warehouse\\\",\\\"manage purchase\\\",\\\"edit purchase\\\",\\\"manage quotation\\\",\\\"edit quotation\\\",\\\"manage pos\\\",\\\"edit pos\\\",\\\"manage product\\\",\\\"edit product\\\",\\\"manage product category\\\",\\\"edit product category\\\",\\\"manage product unit\\\",\\\"edit product unit\\\"],\\\"support\\\":[\\\"create support\\\",\\\"view support dashboard\\\",\\\"view support\\\",\\\"delete support\\\",\\\"manage support\\\",\\\"edit support\\\",\\\"reply support\\\"],\\\"user_management\\\":[\\\"create user\\\",\\\"create client\\\",\\\"view user\\\",\\\"view client\\\",\\\"delete user\\\",\\\"delete client\\\",\\\"manage user\\\",\\\"edit user\\\",\\\"manage client\\\",\\\"edit client\\\"],\\\"booking\\\":[\\\"create booking\\\",\\\"create appointment\\\",\\\"create appointment booking\\\",\\\"create calendar event\\\",\\\"view booking dashboard\\\",\\\"view booking\\\",\\\"show booking\\\",\\\"view appointment\\\",\\\"show appointment\\\",\\\"view appointment booking\\\",\\\"show appointment booking\\\",\\\"view calendar event\\\",\\\"show calendar event\\\",\\\"delete booking\\\",\\\"delete appointment\\\",\\\"delete appointment booking\\\",\\\"delete calendar event\\\",\\\"manage booking\\\",\\\"edit booking\\\",\\\"manage appointment\\\",\\\"edit appointment\\\",\\\"manage appointment booking\\\",\\\"edit appointment booking\\\",\\\"manage calendar event\\\",\\\"edit calendar event\\\"],\\\"omx_flow\\\":[\\\"access omx flow\\\",\\\"whatsapp_flows\\\",\\\"whatsapp_orders\\\",\\\"campaigns\\\",\\\"templates\\\",\\\"chatbot\\\"],\\\"personal_tasks\\\":[\\\"create personal task\\\",\\\"create personal task comment\\\",\\\"create personal task file\\\",\\\"create personal task checklist\\\",\\\"view personal task\\\",\\\"delete personal task\\\",\\\"delete personal task comment\\\",\\\"delete personal task file\\\",\\\"delete personal task checklist\\\",\\\"manage personal task\\\",\\\"edit personal task\\\",\\\"edit personal task comment\\\",\\\"edit personal task checklist\\\",\\\"manage personal task time tracking\\\"],\\\"automatish\\\":[\\\"access automatish\\\"]}', `users`.`updated_at` = '2025-07-31 05:36:27' where `id` = 82", "type": "query", "params": [], "bindings": ["{&quot;crm&quot;:[&quot;create lead&quot;,&quot;create deal&quot;,&quot;create form builder&quot;,&quot;create contract&quot;,&quot;create pipeline&quot;,&quot;create stage&quot;,&quot;create source&quot;,&quot;create label&quot;,&quot;view crm dashboard&quot;,&quot;view lead&quot;,&quot;view deal&quot;,&quot;view form builder&quot;,&quot;view contract&quot;,&quot;view pipeline&quot;,&quot;view stage&quot;,&quot;view source&quot;,&quot;view label&quot;,&quot;delete lead&quot;,&quot;delete deal&quot;,&quot;delete form builder&quot;,&quot;delete contract&quot;,&quot;delete pipeline&quot;,&quot;delete stage&quot;,&quot;delete source&quot;,&quot;delete label&quot;,&quot;manage lead&quot;,&quot;edit lead&quot;,&quot;manage deal&quot;,&quot;edit deal&quot;,&quot;manage form builder&quot;,&quot;edit form builder&quot;,&quot;manage contract&quot;,&quot;edit contract&quot;,&quot;manage pipeline&quot;,&quot;edit pipeline&quot;,&quot;manage stage&quot;,&quot;edit stage&quot;,&quot;manage source&quot;,&quot;edit source&quot;,&quot;manage label&quot;,&quot;edit label&quot;],&quot;hrm&quot;:[&quot;create employee&quot;,&quot;create set salary&quot;,&quot;create pay slip&quot;,&quot;create leave&quot;,&quot;create attendance&quot;,&quot;create training&quot;,&quot;create award&quot;,&quot;create branch&quot;,&quot;create department&quot;,&quot;create designation&quot;,&quot;create document type&quot;,&quot;view hrm dashboard&quot;,&quot;view employee&quot;,&quot;view set salary&quot;,&quot;view pay slip&quot;,&quot;view leave&quot;,&quot;view attendance&quot;,&quot;view training&quot;,&quot;view award&quot;,&quot;view branch&quot;,&quot;view department&quot;,&quot;view designation&quot;,&quot;view document type&quot;,&quot;delete employee&quot;,&quot;delete set salary&quot;,&quot;delete pay slip&quot;,&quot;delete leave&quot;,&quot;delete attendance&quot;,&quot;delete training&quot;,&quot;delete award&quot;,&quot;delete branch&quot;,&quot;delete department&quot;,&quot;delete designation&quot;,&quot;delete document type&quot;,&quot;manage employee&quot;,&quot;edit employee&quot;,&quot;manage set salary&quot;,&quot;edit set salary&quot;,&quot;manage pay slip&quot;,&quot;edit pay slip&quot;,&quot;manage leave&quot;,&quot;edit leave&quot;,&quot;manage attendance&quot;,&quot;edit attendance&quot;,&quot;manage training&quot;,&quot;edit training&quot;,&quot;manage award&quot;,&quot;edit award&quot;,&quot;manage branch&quot;,&quot;edit branch&quot;,&quot;manage department&quot;,&quot;edit department&quot;,&quot;manage designation&quot;,&quot;edit designation&quot;,&quot;manage document type&quot;,&quot;edit document type&quot;],&quot;project&quot;:[&quot;create project&quot;,&quot;create project task&quot;,&quot;create timesheet&quot;,&quot;create bug report&quot;,&quot;create milestone&quot;,&quot;create project stage&quot;,&quot;create project task stage&quot;,&quot;create project expense&quot;,&quot;create activity&quot;,&quot;create bug status&quot;,&quot;view project dashboard&quot;,&quot;view project&quot;,&quot;view project task&quot;,&quot;view timesheet&quot;,&quot;view bug report&quot;,&quot;view milestone&quot;,&quot;view project stage&quot;,&quot;view project task stage&quot;,&quot;view project expense&quot;,&quot;view activity&quot;,&quot;view bug status&quot;,&quot;delete project&quot;,&quot;delete project task&quot;,&quot;delete timesheet&quot;,&quot;delete bug report&quot;,&quot;delete milestone&quot;,&quot;delete project stage&quot;,&quot;delete project task stage&quot;,&quot;delete project expense&quot;,&quot;delete activity&quot;,&quot;delete bug status&quot;,&quot;manage project&quot;,&quot;edit project&quot;,&quot;manage project task&quot;,&quot;edit project task&quot;,&quot;manage timesheet&quot;,&quot;edit timesheet&quot;,&quot;manage bug report&quot;,&quot;edit bug report&quot;,&quot;manage milestone&quot;,&quot;edit milestone&quot;,&quot;manage project stage&quot;,&quot;edit project stage&quot;,&quot;manage project task stage&quot;,&quot;edit project task stage&quot;,&quot;manage project expense&quot;,&quot;edit project expense&quot;,&quot;manage activity&quot;,&quot;edit activity&quot;,&quot;manage bug status&quot;,&quot;edit bug status&quot;],&quot;pos&quot;:[&quot;create warehouse&quot;,&quot;create purchase&quot;,&quot;create quotation&quot;,&quot;create pos&quot;,&quot;create barcode&quot;,&quot;create product&quot;,&quot;create product category&quot;,&quot;create product unit&quot;,&quot;view pos dashboard&quot;,&quot;view warehouse&quot;,&quot;view purchase&quot;,&quot;view quotation&quot;,&quot;view pos&quot;,&quot;view product&quot;,&quot;view product category&quot;,&quot;view product unit&quot;,&quot;delete warehouse&quot;,&quot;delete purchase&quot;,&quot;delete quotation&quot;,&quot;delete pos&quot;,&quot;delete product&quot;,&quot;delete product category&quot;,&quot;delete product unit&quot;,&quot;manage warehouse&quot;,&quot;edit warehouse&quot;,&quot;manage purchase&quot;,&quot;edit purchase&quot;,&quot;manage quotation&quot;,&quot;edit quotation&quot;,&quot;manage pos&quot;,&quot;edit pos&quot;,&quot;manage product&quot;,&quot;edit product&quot;,&quot;manage product category&quot;,&quot;edit product category&quot;,&quot;manage product unit&quot;,&quot;edit product unit&quot;],&quot;support&quot;:[&quot;create support&quot;,&quot;view support dashboard&quot;,&quot;view support&quot;,&quot;delete support&quot;,&quot;manage support&quot;,&quot;edit support&quot;,&quot;reply support&quot;],&quot;user_management&quot;:[&quot;create user&quot;,&quot;create client&quot;,&quot;view user&quot;,&quot;view client&quot;,&quot;delete user&quot;,&quot;delete client&quot;,&quot;manage user&quot;,&quot;edit user&quot;,&quot;manage client&quot;,&quot;edit client&quot;],&quot;booking&quot;:[&quot;create booking&quot;,&quot;create appointment&quot;,&quot;create appointment booking&quot;,&quot;create calendar event&quot;,&quot;view booking dashboard&quot;,&quot;view booking&quot;,&quot;show booking&quot;,&quot;view appointment&quot;,&quot;show appointment&quot;,&quot;view appointment booking&quot;,&quot;show appointment booking&quot;,&quot;view calendar event&quot;,&quot;show calendar event&quot;,&quot;delete booking&quot;,&quot;delete appointment&quot;,&quot;delete appointment booking&quot;,&quot;delete calendar event&quot;,&quot;manage booking&quot;,&quot;edit booking&quot;,&quot;manage appointment&quot;,&quot;edit appointment&quot;,&quot;manage appointment booking&quot;,&quot;edit appointment booking&quot;,&quot;manage calendar event&quot;,&quot;edit calendar event&quot;],&quot;omx_flow&quot;:[&quot;access omx flow&quot;,&quot;whatsapp_flows&quot;,&quot;whatsapp_orders&quot;,&quot;campaigns&quot;,&quot;templates&quot;,&quot;chatbot&quot;],&quot;personal_tasks&quot;:[&quot;create personal task&quot;,&quot;create personal task comment&quot;,&quot;create personal task file&quot;,&quot;create personal task checklist&quot;,&quot;view personal task&quot;,&quot;delete personal task&quot;,&quot;delete personal task comment&quot;,&quot;delete personal task file&quot;,&quot;delete personal task checklist&quot;,&quot;manage personal task&quot;,&quot;edit personal task&quot;,&quot;edit personal task comment&quot;,&quot;edit personal task checklist&quot;,&quot;manage personal task time tracking&quot;],&quot;automatish&quot;:[&quot;access automatish&quot;]}", "2025-07-31 05:36:27", "82"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 35}, {"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.103518, "duration": 0.0077, "duration_str": "7.7ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:35", "source": "app/Observers/PricingPlanObserver.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=35", "ajax": false, "filename": "PricingPlanObserver.php", "line": "35"}, "connection": "radhe_same", "start_percent": 56.162, "width_percent": 6.285}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 76}, {"index": 16, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.125244, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:76", "source": "app/Observers/PricingPlanObserver.php:76", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=76", "ajax": false, "filename": "PricingPlanObserver.php", "line": "76"}, "connection": "radhe_same", "start_percent": 62.447, "width_percent": 1.437}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 91}, {"index": 18, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 19, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}], "start": **********.1382222, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 63.883, "width_percent": 1.657}, {"sql": "update `users` set `module_permissions` = '{\\\"crm\\\":[\\\"create lead\\\",\\\"create deal\\\",\\\"create form builder\\\",\\\"create contract\\\",\\\"create pipeline\\\",\\\"create stage\\\",\\\"create source\\\",\\\"create label\\\",\\\"view crm dashboard\\\",\\\"view lead\\\",\\\"view deal\\\",\\\"view form builder\\\",\\\"view contract\\\",\\\"view pipeline\\\",\\\"view stage\\\",\\\"view source\\\",\\\"view label\\\",\\\"delete lead\\\",\\\"delete deal\\\",\\\"delete form builder\\\",\\\"delete contract\\\",\\\"delete pipeline\\\",\\\"delete stage\\\",\\\"delete source\\\",\\\"delete label\\\",\\\"manage lead\\\",\\\"edit lead\\\",\\\"manage deal\\\",\\\"edit deal\\\",\\\"manage form builder\\\",\\\"edit form builder\\\",\\\"manage contract\\\",\\\"edit contract\\\",\\\"manage pipeline\\\",\\\"edit pipeline\\\",\\\"manage stage\\\",\\\"edit stage\\\",\\\"manage source\\\",\\\"edit source\\\",\\\"manage label\\\",\\\"edit label\\\"],\\\"hrm\\\":[\\\"create employee\\\",\\\"create set salary\\\",\\\"create pay slip\\\",\\\"create leave\\\",\\\"create attendance\\\",\\\"create training\\\",\\\"create award\\\",\\\"create branch\\\",\\\"create department\\\",\\\"create designation\\\",\\\"create document type\\\",\\\"view hrm dashboard\\\",\\\"view employee\\\",\\\"view set salary\\\",\\\"view pay slip\\\",\\\"view leave\\\",\\\"view attendance\\\",\\\"view training\\\",\\\"view award\\\",\\\"view branch\\\",\\\"view department\\\",\\\"view designation\\\",\\\"view document type\\\",\\\"delete employee\\\",\\\"delete set salary\\\",\\\"delete pay slip\\\",\\\"delete leave\\\",\\\"delete attendance\\\",\\\"delete training\\\",\\\"delete award\\\",\\\"delete branch\\\",\\\"delete department\\\",\\\"delete designation\\\",\\\"delete document type\\\",\\\"manage employee\\\",\\\"edit employee\\\",\\\"manage set salary\\\",\\\"edit set salary\\\",\\\"manage pay slip\\\",\\\"edit pay slip\\\",\\\"manage leave\\\",\\\"edit leave\\\",\\\"manage attendance\\\",\\\"edit attendance\\\",\\\"manage training\\\",\\\"edit training\\\",\\\"manage award\\\",\\\"edit award\\\",\\\"manage branch\\\",\\\"edit branch\\\",\\\"manage department\\\",\\\"edit department\\\",\\\"manage designation\\\",\\\"edit designation\\\",\\\"manage document type\\\",\\\"edit document type\\\"],\\\"project\\\":[\\\"create project\\\",\\\"create project task\\\",\\\"create timesheet\\\",\\\"create bug report\\\",\\\"create milestone\\\",\\\"create project stage\\\",\\\"create project task stage\\\",\\\"create project expense\\\",\\\"create activity\\\",\\\"create bug status\\\",\\\"view project dashboard\\\",\\\"view project\\\",\\\"view project task\\\",\\\"view timesheet\\\",\\\"view bug report\\\",\\\"view milestone\\\",\\\"view project stage\\\",\\\"view project task stage\\\",\\\"view project expense\\\",\\\"view activity\\\",\\\"view bug status\\\",\\\"delete project\\\",\\\"delete project task\\\",\\\"delete timesheet\\\",\\\"delete bug report\\\",\\\"delete milestone\\\",\\\"delete project stage\\\",\\\"delete project task stage\\\",\\\"delete project expense\\\",\\\"delete activity\\\",\\\"delete bug status\\\",\\\"manage project\\\",\\\"edit project\\\",\\\"manage project task\\\",\\\"edit project task\\\",\\\"manage timesheet\\\",\\\"edit timesheet\\\",\\\"manage bug report\\\",\\\"edit bug report\\\",\\\"manage milestone\\\",\\\"edit milestone\\\",\\\"manage project stage\\\",\\\"edit project stage\\\",\\\"manage project task stage\\\",\\\"edit project task stage\\\",\\\"manage project expense\\\",\\\"edit project expense\\\",\\\"manage activity\\\",\\\"edit activity\\\",\\\"manage bug status\\\",\\\"edit bug status\\\"],\\\"pos\\\":[\\\"create warehouse\\\",\\\"create purchase\\\",\\\"create quotation\\\",\\\"create pos\\\",\\\"create barcode\\\",\\\"create product\\\",\\\"create product category\\\",\\\"create product unit\\\",\\\"view pos dashboard\\\",\\\"view warehouse\\\",\\\"view purchase\\\",\\\"view quotation\\\",\\\"view pos\\\",\\\"view product\\\",\\\"view product category\\\",\\\"view product unit\\\",\\\"delete warehouse\\\",\\\"delete purchase\\\",\\\"delete quotation\\\",\\\"delete pos\\\",\\\"delete product\\\",\\\"delete product category\\\",\\\"delete product unit\\\",\\\"manage warehouse\\\",\\\"edit warehouse\\\",\\\"manage purchase\\\",\\\"edit purchase\\\",\\\"manage quotation\\\",\\\"edit quotation\\\",\\\"manage pos\\\",\\\"edit pos\\\",\\\"manage product\\\",\\\"edit product\\\",\\\"manage product category\\\",\\\"edit product category\\\",\\\"manage product unit\\\",\\\"edit product unit\\\"],\\\"support\\\":[\\\"create support\\\",\\\"view support dashboard\\\",\\\"view support\\\",\\\"delete support\\\",\\\"manage support\\\",\\\"edit support\\\",\\\"reply support\\\"],\\\"user_management\\\":[\\\"create user\\\",\\\"create client\\\",\\\"view user\\\",\\\"view client\\\",\\\"delete user\\\",\\\"delete client\\\",\\\"manage user\\\",\\\"edit user\\\",\\\"manage client\\\",\\\"edit client\\\"],\\\"booking\\\":[\\\"create booking\\\",\\\"create appointment\\\",\\\"create appointment booking\\\",\\\"create calendar event\\\",\\\"view booking dashboard\\\",\\\"view booking\\\",\\\"show booking\\\",\\\"view appointment\\\",\\\"show appointment\\\",\\\"view appointment booking\\\",\\\"show appointment booking\\\",\\\"view calendar event\\\",\\\"show calendar event\\\",\\\"delete booking\\\",\\\"delete appointment\\\",\\\"delete appointment booking\\\",\\\"delete calendar event\\\",\\\"manage booking\\\",\\\"edit booking\\\",\\\"manage appointment\\\",\\\"edit appointment\\\",\\\"manage appointment booking\\\",\\\"edit appointment booking\\\",\\\"manage calendar event\\\",\\\"edit calendar event\\\"],\\\"omx_flow\\\":[\\\"access omx flow\\\",\\\"whatsapp_flows\\\",\\\"whatsapp_orders\\\",\\\"campaigns\\\",\\\"templates\\\",\\\"chatbot\\\"],\\\"personal_tasks\\\":[\\\"create personal task\\\",\\\"create personal task comment\\\",\\\"create personal task file\\\",\\\"create personal task checklist\\\",\\\"view personal task\\\",\\\"delete personal task\\\",\\\"delete personal task comment\\\",\\\"delete personal task file\\\",\\\"delete personal task checklist\\\",\\\"manage personal task\\\",\\\"edit personal task\\\",\\\"edit personal task comment\\\",\\\"edit personal task checklist\\\",\\\"manage personal task time tracking\\\"],\\\"automatish\\\":[\\\"access automatish\\\"]}', `users`.`updated_at` = '2025-07-31 05:36:29' where `id` = 83", "type": "query", "params": [], "bindings": ["{&quot;crm&quot;:[&quot;create lead&quot;,&quot;create deal&quot;,&quot;create form builder&quot;,&quot;create contract&quot;,&quot;create pipeline&quot;,&quot;create stage&quot;,&quot;create source&quot;,&quot;create label&quot;,&quot;view crm dashboard&quot;,&quot;view lead&quot;,&quot;view deal&quot;,&quot;view form builder&quot;,&quot;view contract&quot;,&quot;view pipeline&quot;,&quot;view stage&quot;,&quot;view source&quot;,&quot;view label&quot;,&quot;delete lead&quot;,&quot;delete deal&quot;,&quot;delete form builder&quot;,&quot;delete contract&quot;,&quot;delete pipeline&quot;,&quot;delete stage&quot;,&quot;delete source&quot;,&quot;delete label&quot;,&quot;manage lead&quot;,&quot;edit lead&quot;,&quot;manage deal&quot;,&quot;edit deal&quot;,&quot;manage form builder&quot;,&quot;edit form builder&quot;,&quot;manage contract&quot;,&quot;edit contract&quot;,&quot;manage pipeline&quot;,&quot;edit pipeline&quot;,&quot;manage stage&quot;,&quot;edit stage&quot;,&quot;manage source&quot;,&quot;edit source&quot;,&quot;manage label&quot;,&quot;edit label&quot;],&quot;hrm&quot;:[&quot;create employee&quot;,&quot;create set salary&quot;,&quot;create pay slip&quot;,&quot;create leave&quot;,&quot;create attendance&quot;,&quot;create training&quot;,&quot;create award&quot;,&quot;create branch&quot;,&quot;create department&quot;,&quot;create designation&quot;,&quot;create document type&quot;,&quot;view hrm dashboard&quot;,&quot;view employee&quot;,&quot;view set salary&quot;,&quot;view pay slip&quot;,&quot;view leave&quot;,&quot;view attendance&quot;,&quot;view training&quot;,&quot;view award&quot;,&quot;view branch&quot;,&quot;view department&quot;,&quot;view designation&quot;,&quot;view document type&quot;,&quot;delete employee&quot;,&quot;delete set salary&quot;,&quot;delete pay slip&quot;,&quot;delete leave&quot;,&quot;delete attendance&quot;,&quot;delete training&quot;,&quot;delete award&quot;,&quot;delete branch&quot;,&quot;delete department&quot;,&quot;delete designation&quot;,&quot;delete document type&quot;,&quot;manage employee&quot;,&quot;edit employee&quot;,&quot;manage set salary&quot;,&quot;edit set salary&quot;,&quot;manage pay slip&quot;,&quot;edit pay slip&quot;,&quot;manage leave&quot;,&quot;edit leave&quot;,&quot;manage attendance&quot;,&quot;edit attendance&quot;,&quot;manage training&quot;,&quot;edit training&quot;,&quot;manage award&quot;,&quot;edit award&quot;,&quot;manage branch&quot;,&quot;edit branch&quot;,&quot;manage department&quot;,&quot;edit department&quot;,&quot;manage designation&quot;,&quot;edit designation&quot;,&quot;manage document type&quot;,&quot;edit document type&quot;],&quot;project&quot;:[&quot;create project&quot;,&quot;create project task&quot;,&quot;create timesheet&quot;,&quot;create bug report&quot;,&quot;create milestone&quot;,&quot;create project stage&quot;,&quot;create project task stage&quot;,&quot;create project expense&quot;,&quot;create activity&quot;,&quot;create bug status&quot;,&quot;view project dashboard&quot;,&quot;view project&quot;,&quot;view project task&quot;,&quot;view timesheet&quot;,&quot;view bug report&quot;,&quot;view milestone&quot;,&quot;view project stage&quot;,&quot;view project task stage&quot;,&quot;view project expense&quot;,&quot;view activity&quot;,&quot;view bug status&quot;,&quot;delete project&quot;,&quot;delete project task&quot;,&quot;delete timesheet&quot;,&quot;delete bug report&quot;,&quot;delete milestone&quot;,&quot;delete project stage&quot;,&quot;delete project task stage&quot;,&quot;delete project expense&quot;,&quot;delete activity&quot;,&quot;delete bug status&quot;,&quot;manage project&quot;,&quot;edit project&quot;,&quot;manage project task&quot;,&quot;edit project task&quot;,&quot;manage timesheet&quot;,&quot;edit timesheet&quot;,&quot;manage bug report&quot;,&quot;edit bug report&quot;,&quot;manage milestone&quot;,&quot;edit milestone&quot;,&quot;manage project stage&quot;,&quot;edit project stage&quot;,&quot;manage project task stage&quot;,&quot;edit project task stage&quot;,&quot;manage project expense&quot;,&quot;edit project expense&quot;,&quot;manage activity&quot;,&quot;edit activity&quot;,&quot;manage bug status&quot;,&quot;edit bug status&quot;],&quot;pos&quot;:[&quot;create warehouse&quot;,&quot;create purchase&quot;,&quot;create quotation&quot;,&quot;create pos&quot;,&quot;create barcode&quot;,&quot;create product&quot;,&quot;create product category&quot;,&quot;create product unit&quot;,&quot;view pos dashboard&quot;,&quot;view warehouse&quot;,&quot;view purchase&quot;,&quot;view quotation&quot;,&quot;view pos&quot;,&quot;view product&quot;,&quot;view product category&quot;,&quot;view product unit&quot;,&quot;delete warehouse&quot;,&quot;delete purchase&quot;,&quot;delete quotation&quot;,&quot;delete pos&quot;,&quot;delete product&quot;,&quot;delete product category&quot;,&quot;delete product unit&quot;,&quot;manage warehouse&quot;,&quot;edit warehouse&quot;,&quot;manage purchase&quot;,&quot;edit purchase&quot;,&quot;manage quotation&quot;,&quot;edit quotation&quot;,&quot;manage pos&quot;,&quot;edit pos&quot;,&quot;manage product&quot;,&quot;edit product&quot;,&quot;manage product category&quot;,&quot;edit product category&quot;,&quot;manage product unit&quot;,&quot;edit product unit&quot;],&quot;support&quot;:[&quot;create support&quot;,&quot;view support dashboard&quot;,&quot;view support&quot;,&quot;delete support&quot;,&quot;manage support&quot;,&quot;edit support&quot;,&quot;reply support&quot;],&quot;user_management&quot;:[&quot;create user&quot;,&quot;create client&quot;,&quot;view user&quot;,&quot;view client&quot;,&quot;delete user&quot;,&quot;delete client&quot;,&quot;manage user&quot;,&quot;edit user&quot;,&quot;manage client&quot;,&quot;edit client&quot;],&quot;booking&quot;:[&quot;create booking&quot;,&quot;create appointment&quot;,&quot;create appointment booking&quot;,&quot;create calendar event&quot;,&quot;view booking dashboard&quot;,&quot;view booking&quot;,&quot;show booking&quot;,&quot;view appointment&quot;,&quot;show appointment&quot;,&quot;view appointment booking&quot;,&quot;show appointment booking&quot;,&quot;view calendar event&quot;,&quot;show calendar event&quot;,&quot;delete booking&quot;,&quot;delete appointment&quot;,&quot;delete appointment booking&quot;,&quot;delete calendar event&quot;,&quot;manage booking&quot;,&quot;edit booking&quot;,&quot;manage appointment&quot;,&quot;edit appointment&quot;,&quot;manage appointment booking&quot;,&quot;edit appointment booking&quot;,&quot;manage calendar event&quot;,&quot;edit calendar event&quot;],&quot;omx_flow&quot;:[&quot;access omx flow&quot;,&quot;whatsapp_flows&quot;,&quot;whatsapp_orders&quot;,&quot;campaigns&quot;,&quot;templates&quot;,&quot;chatbot&quot;],&quot;personal_tasks&quot;:[&quot;create personal task&quot;,&quot;create personal task comment&quot;,&quot;create personal task file&quot;,&quot;create personal task checklist&quot;,&quot;view personal task&quot;,&quot;delete personal task&quot;,&quot;delete personal task comment&quot;,&quot;delete personal task file&quot;,&quot;delete personal task checklist&quot;,&quot;manage personal task&quot;,&quot;edit personal task&quot;,&quot;edit personal task comment&quot;,&quot;edit personal task checklist&quot;,&quot;manage personal task time tracking&quot;],&quot;automatish&quot;:[&quot;access automatish&quot;]}", "2025-07-31 05:36:29", "83"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 35}, {"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.203965, "duration": 0.00458, "duration_str": "4.58ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:35", "source": "app/Observers/PricingPlanObserver.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=35", "ajax": false, "filename": "PricingPlanObserver.php", "line": "35"}, "connection": "radhe_same", "start_percent": 65.54, "width_percent": 3.738}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 76}, {"index": 16, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.226836, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:76", "source": "app/Observers/PricingPlanObserver.php:76", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=76", "ajax": false, "filename": "PricingPlanObserver.php", "line": "76"}, "connection": "radhe_same", "start_percent": 69.278, "width_percent": 1.918}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 91}, {"index": 18, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 19, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}], "start": **********.246865, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 71.197, "width_percent": 2.089}, {"sql": "update `users` set `module_permissions` = '{\\\"crm\\\":[\\\"create lead\\\",\\\"create deal\\\",\\\"create form builder\\\",\\\"create contract\\\",\\\"create pipeline\\\",\\\"create stage\\\",\\\"create source\\\",\\\"create label\\\",\\\"view crm dashboard\\\",\\\"view lead\\\",\\\"view deal\\\",\\\"view form builder\\\",\\\"view contract\\\",\\\"view pipeline\\\",\\\"view stage\\\",\\\"view source\\\",\\\"view label\\\",\\\"delete lead\\\",\\\"delete deal\\\",\\\"delete form builder\\\",\\\"delete contract\\\",\\\"delete pipeline\\\",\\\"delete stage\\\",\\\"delete source\\\",\\\"delete label\\\",\\\"manage lead\\\",\\\"edit lead\\\",\\\"manage deal\\\",\\\"edit deal\\\",\\\"manage form builder\\\",\\\"edit form builder\\\",\\\"manage contract\\\",\\\"edit contract\\\",\\\"manage pipeline\\\",\\\"edit pipeline\\\",\\\"manage stage\\\",\\\"edit stage\\\",\\\"manage source\\\",\\\"edit source\\\",\\\"manage label\\\",\\\"edit label\\\"],\\\"hrm\\\":[\\\"create employee\\\",\\\"create set salary\\\",\\\"create pay slip\\\",\\\"create leave\\\",\\\"create attendance\\\",\\\"create training\\\",\\\"create award\\\",\\\"create branch\\\",\\\"create department\\\",\\\"create designation\\\",\\\"create document type\\\",\\\"view hrm dashboard\\\",\\\"view employee\\\",\\\"view set salary\\\",\\\"view pay slip\\\",\\\"view leave\\\",\\\"view attendance\\\",\\\"view training\\\",\\\"view award\\\",\\\"view branch\\\",\\\"view department\\\",\\\"view designation\\\",\\\"view document type\\\",\\\"delete employee\\\",\\\"delete set salary\\\",\\\"delete pay slip\\\",\\\"delete leave\\\",\\\"delete attendance\\\",\\\"delete training\\\",\\\"delete award\\\",\\\"delete branch\\\",\\\"delete department\\\",\\\"delete designation\\\",\\\"delete document type\\\",\\\"manage employee\\\",\\\"edit employee\\\",\\\"manage set salary\\\",\\\"edit set salary\\\",\\\"manage pay slip\\\",\\\"edit pay slip\\\",\\\"manage leave\\\",\\\"edit leave\\\",\\\"manage attendance\\\",\\\"edit attendance\\\",\\\"manage training\\\",\\\"edit training\\\",\\\"manage award\\\",\\\"edit award\\\",\\\"manage branch\\\",\\\"edit branch\\\",\\\"manage department\\\",\\\"edit department\\\",\\\"manage designation\\\",\\\"edit designation\\\",\\\"manage document type\\\",\\\"edit document type\\\"],\\\"project\\\":[\\\"create project\\\",\\\"create project task\\\",\\\"create timesheet\\\",\\\"create bug report\\\",\\\"create milestone\\\",\\\"create project stage\\\",\\\"create project task stage\\\",\\\"create project expense\\\",\\\"create activity\\\",\\\"create bug status\\\",\\\"view project dashboard\\\",\\\"view project\\\",\\\"view project task\\\",\\\"view timesheet\\\",\\\"view bug report\\\",\\\"view milestone\\\",\\\"view project stage\\\",\\\"view project task stage\\\",\\\"view project expense\\\",\\\"view activity\\\",\\\"view bug status\\\",\\\"delete project\\\",\\\"delete project task\\\",\\\"delete timesheet\\\",\\\"delete bug report\\\",\\\"delete milestone\\\",\\\"delete project stage\\\",\\\"delete project task stage\\\",\\\"delete project expense\\\",\\\"delete activity\\\",\\\"delete bug status\\\",\\\"manage project\\\",\\\"edit project\\\",\\\"manage project task\\\",\\\"edit project task\\\",\\\"manage timesheet\\\",\\\"edit timesheet\\\",\\\"manage bug report\\\",\\\"edit bug report\\\",\\\"manage milestone\\\",\\\"edit milestone\\\",\\\"manage project stage\\\",\\\"edit project stage\\\",\\\"manage project task stage\\\",\\\"edit project task stage\\\",\\\"manage project expense\\\",\\\"edit project expense\\\",\\\"manage activity\\\",\\\"edit activity\\\",\\\"manage bug status\\\",\\\"edit bug status\\\"],\\\"pos\\\":[\\\"create warehouse\\\",\\\"create purchase\\\",\\\"create quotation\\\",\\\"create pos\\\",\\\"create barcode\\\",\\\"create product\\\",\\\"create product category\\\",\\\"create product unit\\\",\\\"view pos dashboard\\\",\\\"view warehouse\\\",\\\"view purchase\\\",\\\"view quotation\\\",\\\"view pos\\\",\\\"view product\\\",\\\"view product category\\\",\\\"view product unit\\\",\\\"delete warehouse\\\",\\\"delete purchase\\\",\\\"delete quotation\\\",\\\"delete pos\\\",\\\"delete product\\\",\\\"delete product category\\\",\\\"delete product unit\\\",\\\"manage warehouse\\\",\\\"edit warehouse\\\",\\\"manage purchase\\\",\\\"edit purchase\\\",\\\"manage quotation\\\",\\\"edit quotation\\\",\\\"manage pos\\\",\\\"edit pos\\\",\\\"manage product\\\",\\\"edit product\\\",\\\"manage product category\\\",\\\"edit product category\\\",\\\"manage product unit\\\",\\\"edit product unit\\\"],\\\"support\\\":[\\\"create support\\\",\\\"view support dashboard\\\",\\\"view support\\\",\\\"delete support\\\",\\\"manage support\\\",\\\"edit support\\\",\\\"reply support\\\"],\\\"user_management\\\":[\\\"create user\\\",\\\"create client\\\",\\\"view user\\\",\\\"view client\\\",\\\"delete user\\\",\\\"delete client\\\",\\\"manage user\\\",\\\"edit user\\\",\\\"manage client\\\",\\\"edit client\\\"],\\\"booking\\\":[\\\"create booking\\\",\\\"create appointment\\\",\\\"create appointment booking\\\",\\\"create calendar event\\\",\\\"view booking dashboard\\\",\\\"view booking\\\",\\\"show booking\\\",\\\"view appointment\\\",\\\"show appointment\\\",\\\"view appointment booking\\\",\\\"show appointment booking\\\",\\\"view calendar event\\\",\\\"show calendar event\\\",\\\"delete booking\\\",\\\"delete appointment\\\",\\\"delete appointment booking\\\",\\\"delete calendar event\\\",\\\"manage booking\\\",\\\"edit booking\\\",\\\"manage appointment\\\",\\\"edit appointment\\\",\\\"manage appointment booking\\\",\\\"edit appointment booking\\\",\\\"manage calendar event\\\",\\\"edit calendar event\\\"],\\\"omx_flow\\\":[\\\"access omx flow\\\",\\\"whatsapp_flows\\\",\\\"whatsapp_orders\\\",\\\"campaigns\\\",\\\"templates\\\",\\\"chatbot\\\"],\\\"personal_tasks\\\":[\\\"create personal task\\\",\\\"create personal task comment\\\",\\\"create personal task file\\\",\\\"create personal task checklist\\\",\\\"view personal task\\\",\\\"delete personal task\\\",\\\"delete personal task comment\\\",\\\"delete personal task file\\\",\\\"delete personal task checklist\\\",\\\"manage personal task\\\",\\\"edit personal task\\\",\\\"edit personal task comment\\\",\\\"edit personal task checklist\\\",\\\"manage personal task time tracking\\\"],\\\"automatish\\\":[\\\"access automatish\\\"]}', `users`.`updated_at` = '2025-07-31 05:36:31' where `id` = 84", "type": "query", "params": [], "bindings": ["{&quot;crm&quot;:[&quot;create lead&quot;,&quot;create deal&quot;,&quot;create form builder&quot;,&quot;create contract&quot;,&quot;create pipeline&quot;,&quot;create stage&quot;,&quot;create source&quot;,&quot;create label&quot;,&quot;view crm dashboard&quot;,&quot;view lead&quot;,&quot;view deal&quot;,&quot;view form builder&quot;,&quot;view contract&quot;,&quot;view pipeline&quot;,&quot;view stage&quot;,&quot;view source&quot;,&quot;view label&quot;,&quot;delete lead&quot;,&quot;delete deal&quot;,&quot;delete form builder&quot;,&quot;delete contract&quot;,&quot;delete pipeline&quot;,&quot;delete stage&quot;,&quot;delete source&quot;,&quot;delete label&quot;,&quot;manage lead&quot;,&quot;edit lead&quot;,&quot;manage deal&quot;,&quot;edit deal&quot;,&quot;manage form builder&quot;,&quot;edit form builder&quot;,&quot;manage contract&quot;,&quot;edit contract&quot;,&quot;manage pipeline&quot;,&quot;edit pipeline&quot;,&quot;manage stage&quot;,&quot;edit stage&quot;,&quot;manage source&quot;,&quot;edit source&quot;,&quot;manage label&quot;,&quot;edit label&quot;],&quot;hrm&quot;:[&quot;create employee&quot;,&quot;create set salary&quot;,&quot;create pay slip&quot;,&quot;create leave&quot;,&quot;create attendance&quot;,&quot;create training&quot;,&quot;create award&quot;,&quot;create branch&quot;,&quot;create department&quot;,&quot;create designation&quot;,&quot;create document type&quot;,&quot;view hrm dashboard&quot;,&quot;view employee&quot;,&quot;view set salary&quot;,&quot;view pay slip&quot;,&quot;view leave&quot;,&quot;view attendance&quot;,&quot;view training&quot;,&quot;view award&quot;,&quot;view branch&quot;,&quot;view department&quot;,&quot;view designation&quot;,&quot;view document type&quot;,&quot;delete employee&quot;,&quot;delete set salary&quot;,&quot;delete pay slip&quot;,&quot;delete leave&quot;,&quot;delete attendance&quot;,&quot;delete training&quot;,&quot;delete award&quot;,&quot;delete branch&quot;,&quot;delete department&quot;,&quot;delete designation&quot;,&quot;delete document type&quot;,&quot;manage employee&quot;,&quot;edit employee&quot;,&quot;manage set salary&quot;,&quot;edit set salary&quot;,&quot;manage pay slip&quot;,&quot;edit pay slip&quot;,&quot;manage leave&quot;,&quot;edit leave&quot;,&quot;manage attendance&quot;,&quot;edit attendance&quot;,&quot;manage training&quot;,&quot;edit training&quot;,&quot;manage award&quot;,&quot;edit award&quot;,&quot;manage branch&quot;,&quot;edit branch&quot;,&quot;manage department&quot;,&quot;edit department&quot;,&quot;manage designation&quot;,&quot;edit designation&quot;,&quot;manage document type&quot;,&quot;edit document type&quot;],&quot;project&quot;:[&quot;create project&quot;,&quot;create project task&quot;,&quot;create timesheet&quot;,&quot;create bug report&quot;,&quot;create milestone&quot;,&quot;create project stage&quot;,&quot;create project task stage&quot;,&quot;create project expense&quot;,&quot;create activity&quot;,&quot;create bug status&quot;,&quot;view project dashboard&quot;,&quot;view project&quot;,&quot;view project task&quot;,&quot;view timesheet&quot;,&quot;view bug report&quot;,&quot;view milestone&quot;,&quot;view project stage&quot;,&quot;view project task stage&quot;,&quot;view project expense&quot;,&quot;view activity&quot;,&quot;view bug status&quot;,&quot;delete project&quot;,&quot;delete project task&quot;,&quot;delete timesheet&quot;,&quot;delete bug report&quot;,&quot;delete milestone&quot;,&quot;delete project stage&quot;,&quot;delete project task stage&quot;,&quot;delete project expense&quot;,&quot;delete activity&quot;,&quot;delete bug status&quot;,&quot;manage project&quot;,&quot;edit project&quot;,&quot;manage project task&quot;,&quot;edit project task&quot;,&quot;manage timesheet&quot;,&quot;edit timesheet&quot;,&quot;manage bug report&quot;,&quot;edit bug report&quot;,&quot;manage milestone&quot;,&quot;edit milestone&quot;,&quot;manage project stage&quot;,&quot;edit project stage&quot;,&quot;manage project task stage&quot;,&quot;edit project task stage&quot;,&quot;manage project expense&quot;,&quot;edit project expense&quot;,&quot;manage activity&quot;,&quot;edit activity&quot;,&quot;manage bug status&quot;,&quot;edit bug status&quot;],&quot;pos&quot;:[&quot;create warehouse&quot;,&quot;create purchase&quot;,&quot;create quotation&quot;,&quot;create pos&quot;,&quot;create barcode&quot;,&quot;create product&quot;,&quot;create product category&quot;,&quot;create product unit&quot;,&quot;view pos dashboard&quot;,&quot;view warehouse&quot;,&quot;view purchase&quot;,&quot;view quotation&quot;,&quot;view pos&quot;,&quot;view product&quot;,&quot;view product category&quot;,&quot;view product unit&quot;,&quot;delete warehouse&quot;,&quot;delete purchase&quot;,&quot;delete quotation&quot;,&quot;delete pos&quot;,&quot;delete product&quot;,&quot;delete product category&quot;,&quot;delete product unit&quot;,&quot;manage warehouse&quot;,&quot;edit warehouse&quot;,&quot;manage purchase&quot;,&quot;edit purchase&quot;,&quot;manage quotation&quot;,&quot;edit quotation&quot;,&quot;manage pos&quot;,&quot;edit pos&quot;,&quot;manage product&quot;,&quot;edit product&quot;,&quot;manage product category&quot;,&quot;edit product category&quot;,&quot;manage product unit&quot;,&quot;edit product unit&quot;],&quot;support&quot;:[&quot;create support&quot;,&quot;view support dashboard&quot;,&quot;view support&quot;,&quot;delete support&quot;,&quot;manage support&quot;,&quot;edit support&quot;,&quot;reply support&quot;],&quot;user_management&quot;:[&quot;create user&quot;,&quot;create client&quot;,&quot;view user&quot;,&quot;view client&quot;,&quot;delete user&quot;,&quot;delete client&quot;,&quot;manage user&quot;,&quot;edit user&quot;,&quot;manage client&quot;,&quot;edit client&quot;],&quot;booking&quot;:[&quot;create booking&quot;,&quot;create appointment&quot;,&quot;create appointment booking&quot;,&quot;create calendar event&quot;,&quot;view booking dashboard&quot;,&quot;view booking&quot;,&quot;show booking&quot;,&quot;view appointment&quot;,&quot;show appointment&quot;,&quot;view appointment booking&quot;,&quot;show appointment booking&quot;,&quot;view calendar event&quot;,&quot;show calendar event&quot;,&quot;delete booking&quot;,&quot;delete appointment&quot;,&quot;delete appointment booking&quot;,&quot;delete calendar event&quot;,&quot;manage booking&quot;,&quot;edit booking&quot;,&quot;manage appointment&quot;,&quot;edit appointment&quot;,&quot;manage appointment booking&quot;,&quot;edit appointment booking&quot;,&quot;manage calendar event&quot;,&quot;edit calendar event&quot;],&quot;omx_flow&quot;:[&quot;access omx flow&quot;,&quot;whatsapp_flows&quot;,&quot;whatsapp_orders&quot;,&quot;campaigns&quot;,&quot;templates&quot;,&quot;chatbot&quot;],&quot;personal_tasks&quot;:[&quot;create personal task&quot;,&quot;create personal task comment&quot;,&quot;create personal task file&quot;,&quot;create personal task checklist&quot;,&quot;view personal task&quot;,&quot;delete personal task&quot;,&quot;delete personal task comment&quot;,&quot;delete personal task file&quot;,&quot;delete personal task checklist&quot;,&quot;manage personal task&quot;,&quot;edit personal task&quot;,&quot;edit personal task comment&quot;,&quot;edit personal task checklist&quot;,&quot;manage personal task time tracking&quot;],&quot;automatish&quot;:[&quot;access automatish&quot;]}", "2025-07-31 05:36:31", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 35}, {"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.30091, "duration": 0.0075, "duration_str": "7.5ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:35", "source": "app/Observers/PricingPlanObserver.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=35", "ajax": false, "filename": "PricingPlanObserver.php", "line": "35"}, "connection": "radhe_same", "start_percent": 73.286, "width_percent": 6.121}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 76}, {"index": 16, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.322226, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:76", "source": "app/Observers/PricingPlanObserver.php:76", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=76", "ajax": false, "filename": "PricingPlanObserver.php", "line": "76"}, "connection": "radhe_same", "start_percent": 79.407, "width_percent": 1.518}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 91}, {"index": 18, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 19, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}], "start": **********.334737, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 80.926, "width_percent": 1.698}, {"sql": "select * from `users` where `plan` = 10 and `type` = 'company'", "type": "query", "params": [], "bindings": ["10", "company"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 278}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.387613, "duration": 0.00328, "duration_str": "3.28ms", "memory": 0, "memory_str": null, "filename": "PricingPlanController.php:278", "source": "app/Http/Controllers/PricingPlanController.php:278", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=278", "ajax": false, "filename": "PricingPlanController.php", "line": "278"}, "connection": "radhe_same", "start_percent": 82.623, "width_percent": 2.677}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 324}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.407543, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "PricingPlanController.php:324", "source": "app/Http/Controllers/PricingPlanController.php:324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=324", "ajax": false, "filename": "PricingPlanController.php", "line": "324"}, "connection": "radhe_same", "start_percent": 85.3, "width_percent": 1.485}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 339}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.423623, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 86.786, "width_percent": 1.951}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 324}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.472134, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "PricingPlanController.php:324", "source": "app/Http/Controllers/PricingPlanController.php:324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=324", "ajax": false, "filename": "PricingPlanController.php", "line": "324"}, "connection": "radhe_same", "start_percent": 88.737, "width_percent": 1.396}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 339}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.485943, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 90.132, "width_percent": 1.583}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 324}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.535662, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "PricingPlanController.php:324", "source": "app/Http/Controllers/PricingPlanController.php:324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=324", "ajax": false, "filename": "PricingPlanController.php", "line": "324"}, "connection": "radhe_same", "start_percent": 91.716, "width_percent": 0.971}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 339}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.543052, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 92.687, "width_percent": 1.012}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 324}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.587301, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "PricingPlanController.php:324", "source": "app/Http/Controllers/PricingPlanController.php:324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=324", "ajax": false, "filename": "PricingPlanController.php", "line": "324"}, "connection": "radhe_same", "start_percent": 93.699, "width_percent": 1.494}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 339}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6024911, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 95.193, "width_percent": 1.681}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 324}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.652107, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "PricingPlanController.php:324", "source": "app/Http/Controllers/PricingPlanController.php:324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=324", "ajax": false, "filename": "PricingPlanController.php", "line": "324"}, "connection": "radhe_same", "start_percent": 96.874, "width_percent": 1.453}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 339}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6662161, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 98.327, "width_percent": 1.673}]}, "models": {"data": {"App\\Models\\User": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ModuleIntegration": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FModuleIntegration.php&line=1", "ajax": false, "filename": "ModuleIntegration.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}}, "count": 22, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans/10/edit\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7", "success": "Pricing plan updated successfully.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pricing-plans/10", "status_code": "<pre class=sf-dump id=sf-dump-2003266400 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2003266400\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1356796007 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1356796007\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-454813268 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">My Business Flow</span>\"\n  \"<span class=sf-dump-key>plan_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">subaccount</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"8 characters\">80000.00</span>\"\n  \"<span class=sf-dump-key>duration</span>\" => \"<span class=sf-dump-str title=\"6 characters\">yearly</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  \"<span class=sf-dump-key>description</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"1429 characters\">Our Business Flow Plan is designed to streamline and automate critical operations across departments, thereby enhancing productivity, accountability, and growth. This plan focuses on aligning people, processes, and technology through a structured workflow system that minimises manual effort and maximises efficiency.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Key Features:<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Centralised Workflow Management: Coordinate tasks and approvals across teams with clearly defined stages and responsibilities.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Automation Integration: Automate repetitive tasks, notifications, and follow-ups using customizable logic flows.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Real-time Monitoring: Gain visibility into each step of your business processes with dashboards and activity logs.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Data-Driven Decisions: Collect actionable data at each stage to identify bottlenecks and optimise performance.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Collaboration Tools: Enhance team communication and ensure smooth transitions between workflow stages.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Customizable Flow Templates: Build flows tailored to your business model&#8212;sales, support, HR, finance, or operations.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Benefits:<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Reduce operational overhead<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Improve turnaround time on key tasks.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Enhance team accountability<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Increase scalability with repeatable processes.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Deliver consistent experiences for customers and internal teams.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">This Business Flow Plan is ideal for growing teams that want to modernise their operations and adopt a more structured, data-driven approach to working.</span>\n    \"\"\"\n  \"<span class=sf-dump-key>max_users</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>max_customers</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>max_vendors</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>max_clients</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>storage_limit</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n  \"<span class=sf-dump-key>sort_order</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>modules</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>crm</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>hrm</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>project</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>pos</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>support</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>user_management</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>booking</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>omx_flow</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>personal_tasks</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>automatish</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>permissions</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>crm</span>\" => <span class=sf-dump-note>array:41</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">create lead</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">create deal</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"19 characters\">create form builder</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"15 characters\">create contract</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"15 characters\">create pipeline</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">create stage</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"13 characters\">create source</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"12 characters\">create label</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"18 characters\">view crm dashboard</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"9 characters\">view lead</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"9 characters\">view deal</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"17 characters\">view form builder</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"13 characters\">view contract</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"13 characters\">view pipeline</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">view stage</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"11 characters\">view source</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"10 characters\">view label</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">delete lead</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"11 characters\">delete deal</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"19 characters\">delete form builder</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"15 characters\">delete contract</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"15 characters\">delete pipeline</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"12 characters\">delete stage</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"13 characters\">delete source</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">delete label</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"11 characters\">manage lead</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"9 characters\">edit lead</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">manage deal</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"9 characters\">edit deal</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"19 characters\">manage form builder</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"17 characters\">edit form builder</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"15 characters\">manage contract</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"13 characters\">edit contract</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"15 characters\">manage pipeline</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"13 characters\">edit pipeline</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"12 characters\">manage stage</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"10 characters\">edit stage</span>\"\n      <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"13 characters\">manage source</span>\"\n      <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"11 characters\">edit source</span>\"\n      <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"12 characters\">manage label</span>\"\n      <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"10 characters\">edit label</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>hrm</span>\" => <span class=sf-dump-note>array:56</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">create employee</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">create set salary</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">create pay slip</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"12 characters\">create leave</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"17 characters\">create attendance</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"15 characters\">create training</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"12 characters\">create award</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"13 characters\">create branch</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"17 characters\">create department</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"18 characters\">create designation</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"20 characters\">create document type</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"18 characters\">view hrm dashboard</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"13 characters\">view employee</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"15 characters\">view set salary</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"13 characters\">view pay slip</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"10 characters\">view leave</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"15 characters\">view attendance</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">view training</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"10 characters\">view award</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"11 characters\">view branch</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"15 characters\">view department</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"16 characters\">view designation</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"18 characters\">view document type</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"15 characters\">delete employee</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"17 characters\">delete set salary</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"15 characters\">delete pay slip</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"12 characters\">delete leave</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"17 characters\">delete attendance</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"15 characters\">delete training</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">delete award</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"13 characters\">delete branch</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"17 characters\">delete department</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"18 characters\">delete designation</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"20 characters\">delete document type</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"13 characters\">edit employee</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n      <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"15 characters\">edit set salary</span>\"\n      <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"15 characters\">manage pay slip</span>\"\n      <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"13 characters\">edit pay slip</span>\"\n      <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n      <span class=sf-dump-index>41</span> => \"<span class=sf-dump-str title=\"10 characters\">edit leave</span>\"\n      <span class=sf-dump-index>42</span> => \"<span class=sf-dump-str title=\"17 characters\">manage attendance</span>\"\n      <span class=sf-dump-index>43</span> => \"<span class=sf-dump-str title=\"15 characters\">edit attendance</span>\"\n      <span class=sf-dump-index>44</span> => \"<span class=sf-dump-str title=\"15 characters\">manage training</span>\"\n      <span class=sf-dump-index>45</span> => \"<span class=sf-dump-str title=\"13 characters\">edit training</span>\"\n      <span class=sf-dump-index>46</span> => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n      <span class=sf-dump-index>47</span> => \"<span class=sf-dump-str title=\"10 characters\">edit award</span>\"\n      <span class=sf-dump-index>48</span> => \"<span class=sf-dump-str title=\"13 characters\">manage branch</span>\"\n      <span class=sf-dump-index>49</span> => \"<span class=sf-dump-str title=\"11 characters\">edit branch</span>\"\n      <span class=sf-dump-index>50</span> => \"<span class=sf-dump-str title=\"17 characters\">manage department</span>\"\n      <span class=sf-dump-index>51</span> => \"<span class=sf-dump-str title=\"15 characters\">edit department</span>\"\n      <span class=sf-dump-index>52</span> => \"<span class=sf-dump-str title=\"18 characters\">manage designation</span>\"\n      <span class=sf-dump-index>53</span> => \"<span class=sf-dump-str title=\"16 characters\">edit designation</span>\"\n      <span class=sf-dump-index>54</span> => \"<span class=sf-dump-str title=\"20 characters\">manage document type</span>\"\n      <span class=sf-dump-index>55</span> => \"<span class=sf-dump-str title=\"18 characters\">edit document type</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>project</span>\" => <span class=sf-dump-note>array:51</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">create project</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"19 characters\">create project task</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">create timesheet</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"17 characters\">create bug report</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">create milestone</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"20 characters\">create project stage</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"25 characters\">create project task stage</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"22 characters\">create project expense</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"15 characters\">create activity</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"17 characters\">create bug status</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"22 characters\">view project dashboard</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"12 characters\">view project</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">view project task</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"14 characters\">view timesheet</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"15 characters\">view bug report</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">view milestone</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"18 characters\">view project stage</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"23 characters\">view project task stage</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"20 characters\">view project expense</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"13 characters\">view activity</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"15 characters\">view bug status</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"14 characters\">delete project</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"19 characters\">delete project task</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"16 characters\">delete timesheet</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"17 characters\">delete bug report</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"16 characters\">delete milestone</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"20 characters\">delete project stage</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"25 characters\">delete project task stage</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"22 characters\">delete project expense</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"15 characters\">delete activity</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"17 characters\">delete bug status</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">manage project</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"12 characters\">edit project</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"19 characters\">manage project task</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"17 characters\">edit project task</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"16 characters\">manage timesheet</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"14 characters\">edit timesheet</span>\"\n      <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"17 characters\">manage bug report</span>\"\n      <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"15 characters\">edit bug report</span>\"\n      <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"16 characters\">manage milestone</span>\"\n      <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"14 characters\">edit milestone</span>\"\n      <span class=sf-dump-index>41</span> => \"<span class=sf-dump-str title=\"20 characters\">manage project stage</span>\"\n      <span class=sf-dump-index>42</span> => \"<span class=sf-dump-str title=\"18 characters\">edit project stage</span>\"\n      <span class=sf-dump-index>43</span> => \"<span class=sf-dump-str title=\"25 characters\">manage project task stage</span>\"\n      <span class=sf-dump-index>44</span> => \"<span class=sf-dump-str title=\"23 characters\">edit project task stage</span>\"\n      <span class=sf-dump-index>45</span> => \"<span class=sf-dump-str title=\"22 characters\">manage project expense</span>\"\n      <span class=sf-dump-index>46</span> => \"<span class=sf-dump-str title=\"20 characters\">edit project expense</span>\"\n      <span class=sf-dump-index>47</span> => \"<span class=sf-dump-str title=\"15 characters\">manage activity</span>\"\n      <span class=sf-dump-index>48</span> => \"<span class=sf-dump-str title=\"13 characters\">edit activity</span>\"\n      <span class=sf-dump-index>49</span> => \"<span class=sf-dump-str title=\"17 characters\">manage bug status</span>\"\n      <span class=sf-dump-index>50</span> => \"<span class=sf-dump-str title=\"15 characters\">edit bug status</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:37</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">create warehouse</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">create quotation</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">create pos</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"14 characters\">create barcode</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">create product</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"23 characters\">create product category</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"19 characters\">create product unit</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"18 characters\">view pos dashboard</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"14 characters\">view warehouse</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"13 characters\">view purchase</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"14 characters\">view quotation</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"8 characters\">view pos</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"12 characters\">view product</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"21 characters\">view product category</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"17 characters\">view product unit</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"16 characters\">delete warehouse</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"15 characters\">delete purchase</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"16 characters\">delete quotation</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"10 characters\">delete pos</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"14 characters\">delete product</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"23 characters\">delete product category</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"19 characters\">delete product unit</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"14 characters\">edit warehouse</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"15 characters\">manage purchase</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">edit purchase</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"16 characters\">manage quotation</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"14 characters\">edit quotation</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"8 characters\">edit pos</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">manage product</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"12 characters\">edit product</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"23 characters\">manage product category</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"21 characters\">edit product category</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"19 characters\">manage product unit</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"17 characters\">edit product unit</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>support</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">create support</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"22 characters\">view support dashboard</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">view support</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"14 characters\">delete support</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"14 characters\">manage support</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">edit support</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"13 characters\">reply support</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>user_management</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">create user</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">create client</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">view user</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">view client</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"11 characters\">delete user</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">delete client</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">edit user</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"11 characters\">edit client</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>booking</span>\" => <span class=sf-dump-note>array:25</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">create booking</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"18 characters\">create appointment</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"26 characters\">create appointment booking</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"21 characters\">create calendar event</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"22 characters\">view booking dashboard</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">view booking</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"12 characters\">show booking</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"16 characters\">view appointment</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">show appointment</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"24 characters\">view appointment booking</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"24 characters\">show appointment booking</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"19 characters\">view calendar event</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"19 characters\">show calendar event</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"14 characters\">delete booking</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"18 characters\">delete appointment</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"26 characters\">delete appointment booking</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"21 characters\">delete calendar event</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"14 characters\">manage booking</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"12 characters\">edit booking</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"18 characters\">manage appointment</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"16 characters\">edit appointment</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"26 characters\">manage appointment booking</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"24 characters\">edit appointment booking</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"21 characters\">manage calendar event</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"19 characters\">edit calendar event</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>omx_flow</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">access omx flow</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">whatsapp_flows</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">whatsapp_orders</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">campaigns</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">chatbot</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>personal_tasks</span>\" => <span class=sf-dump-note>array:14</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">create personal task</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"28 characters\">create personal task comment</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"25 characters\">create personal task file</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"30 characters\">create personal task checklist</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"18 characters\">view personal task</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"20 characters\">delete personal task</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"28 characters\">delete personal task comment</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"25 characters\">delete personal task file</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"30 characters\">delete personal task checklist</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"20 characters\">manage personal task</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"18 characters\">edit personal task</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"26 characters\">edit personal task comment</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"28 characters\">edit personal task checklist</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"34 characters\">manage personal task time tracking</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>automatish</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">access automatish</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-454813268\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1694077258 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">13607</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pricing-plans/10/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkVFejBLQ1dDT3BIRS9HVUVKaTdPNHc9PSIsInZhbHVlIjoia1VLaTFiNHJtNmVpRlBvYytoWjVsM1pYQ0MxeEd2VFVzWUhSM1dieFptejdyOXVCZHZ1RWE5VE1FWFBZNTJZOWpDV3BtM3FmSmxORG1TcStIcElmWTRRVGdqc0libUFzZ00zdnRhdUZWUHZnZ29JV0tNVlFtRVFpdWZTZkVnbnVVQUVsR0ViWEE3dk1rbmVSdnFwZkZoOW41eDdUc0EzQW1YTTVMR0dmQjUvVExBby9GQkkraGtrR2xTY1N3bDVQZmt1Z0lLbFlzQ0pUei9UdXpCVWJrMmttM3pXVlNWUEJ4cUdIOHVtc3RpNC9RZHpkUjlBRjZ0UXRFeFlmcUhBMHJZZ3FLT0dpN2xyOG5FT1pNRXpLVEI5NTZVbHZYdEg3OHBJZGdhUHkvRWRRZmRyOXdoYjFZZEZrdXlsOFdFMmh2REdnZDdiSEp2N0czNUc0bjhjWFFCdUhuYjhCVVFOZUZFZlRleno3c3ZvK2pXMTNPazNYN2xSeXpkbDlNREJqanh2T0FYSE9nVGFNakpDT1RjZzJkcEEwQU9kblU3MHVGQk9VQ2huNCtGRXNPdVlkOWF0eXljRWF5Z2hjNkhwR1VwaEhGeTh4NEZ4bW1pemJwVnhraUdyY0hvQjNoT09ZVmJ3R2xheEx0anE0d3l5YitmbTdNTGlDek4zbFd2Z3oiLCJtYWMiOiJiZDVhNDNkZGY5N2VmNGE3MzNmYjc4ODA2YzRkNjZiODI1NTc5YTBiYmVmYmE4OGNjNTUxMTVmMmY0N2ZlZTA2IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkNYbWVVbUtRb0hkM3NnWXB1YnZWSEE9PSIsInZhbHVlIjoicVdUa3R3VE5mTzJoNUhGR0FFVXFtZ3ltamprdXREK0xXbHpndkZDaER6R3RPQWV3VkoxdU0xQUM4OG1OeUlxVHRZSzdicWV5YkNSRi8waHgzQkQxL29HZ2JPM3E0ZzljQWpFRjIxU1E0ekZkdzRnVXZJa1FLZzRreit0ZzFRZVhLLzI1cTFMUjREcGtZanIxb21EWTZremVrNTRvQWJNN2prNWNGLzN0T1E1aUQyMTY2ZTJBVnFhMkU4SmVqd1I2TUhPQXhFZVV1VUxvYkZuS0VHOHBLNDJCdnhrZ2VtbjZOZzhPV3NnQTJnaG1pREN5eGxxUjdKRXRUUW1PSkxlTElzemczOU9xZEZHOERHN1pud0g5czQ4b3J4TC9NUE1WOWxMeXI2QmpyWG5nek5kM0ZWaDQ5bllDMURrOXJjQU5LaUhvTXFmWFFHZ0VGVkFraEo2MVhwb3lLMUp0RGJhUzZ1c1J5OWhSUVM4bkpocWpiVkVWWUlENEZSeGExdnlzU1hnbXdMaHR5RDRBcXNrOStrMGdjajZGRXZ5U3BSdHRaVjBrUUdWU215VUY3YWx2KzZKbEdaNE9tekt0aUZyODRxM1I2d20wSHdsekRUTFRNS0ZzaGNUNUFYckRMeGZUWERZV09PUkZxQXV3WE0zU2N3eGovZTFHditvajl5b3YiLCJtYWMiOiI3NmJlN2E0MmUzNGZmNzNkNDYxY2JlMDE3MjhjNjM1YTMyMDQzMTA2ODc2MzgwY2Q5ZjE4MjZjM2EzZDlhZDBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1694077258\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-553160055 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lOWuOQi5K5zalKq6lxC9bmXS2G5Yh3URND5pGg9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-553160055\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-890341183 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:36:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhxNDByNVptVHR4UmJaUE1KangzSUE9PSIsInZhbHVlIjoiMlNwZjgxd2J1V25CVERoRC9KNk1GUnZOSmk5OXYraGJHZDdYQlVqc2tDMkRLVUgvMXlSc3dzbUcrZkZlRkl3YUkxNHFXd2ZFVnVtNHg5SFVMcSsxZkExTVJLRFpWbWZGbHVVaVlMcC9qamljL285ckNQczNXT2Y4UVg1VnJMRlRScUNhbXF0RWxYSnFRL1dtNXRXRExWaXp5ZytOUURTKzVPbUI0eHZDaVRtUFRjcHJieEpQSDhTVUxKVFpZMU9NNWt1eEFXR3VGM3RFMDNTMy9ndFNxcCtnQnlJMHJWR0JtSnk5N2RGd3MyRDJVTWdNWGlpQ1I0d0gvdnFsMDV1NVZpd2ltc2NudVpUWEwzRTBXMWRQNXJSSGp2Ulp5eTB5Qk1hUk9yZ2NkaXZFeDNkNi9iaU8rTVFqRXJVRnk5aEJocWYxMjZqQWRiUDdkQXFsNVdkMy95a3ptNnJKb0VJSGEyeU5jb2J0eEQ4ek00QzI3U2l1SjUzZENUQlFwbVQ5MEZnNTdRLzdNa2JPOGE0ZGZaOFp6RUVseFQwY3VWMUFXM2p1ZkhXTXNrbXhpbm9HMUNBZVoyWXlNVTZtYWlwK0ZmQUZTWUpDY2Y4ZnVkNEpZY3d2TC9jUmJRNlp4ZjNBUDI4bVB6eFVnb2c5NVV4TnV5R3Y3VWxoT2JPK2k2dCsiLCJtYWMiOiJiMzRjNjI1NTZlNzA3MjQwOTEyOWNmYjkzYjZlNWNjNzMwYjJhN2JiNDg0YTljMjFhMzUzNWY4ZGJjNDYyNjI1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:36:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjVtK0NVU0dMOGFNbFBQdWdVR3NwTUE9PSIsInZhbHVlIjoiZExSLzZVUjczUTFmR0oxUzdYUFB3dmoxZUhLd1VYdTd6d0pNaGZVaHB4UkpEcUNPNEdGUzAwUTc4eEQyY1A4Wk84UEpTZ2hNNUZBdnZnZDhZa2lSM3ppcXdtTzFxdDBEYmRpUEtWZHZzM0pISG5kdVA3QnNpbElUdm4vQktkcDhRYmlSdmJVbVBwNy81WDFmK21tV0FNYkJWQUxDRlpxTTZMc2xuek1XUzBldEZEMWNEaThFaDk1QzlmWjlyS3A4YWVCQ1JPaXBzQ2t4bFVad2gxUjNPZFRBcmhDc1EvZ1BBcEZxbUJydVpoSkVrNk5Kd1FTbysvamVwNXZURHJaTjQ3Wkp4dHFvc002OFpDY0dhNlQyQVRPbjlDbzA4RVVkampXa2hHM2g2Q0pIM0hLMytxWDJCOXRFczRCVEdkdHdVWDdyMG05dmswRStQTzRPVUVHS2IvL0QyaHgzWlBaQlZjTGttMytjc2Q1dTh4cmVYTGo1bWRTZWRQOVhudit0eVp6WUF2V1NFaFJTVjVObEY2MjRWYkc4bzVrbXhqUkd4ZnZtQkxUdkNNSGVaSXVjaGhGeDdlSUoyNUJibVBleHd0MXppRjhRL2x5Wml4U0NTVEo3YjNPdm9YWllwMURUT1phdDh6WHh1RXpDc2Z0ZjZGUW9RdUV4MnVkZ3FmR0UiLCJtYWMiOiIzN2JiMmI2NTNhZmJkODlmNDU5MDJiNTE0ZDAyZWM2Zjg4ZDUxN2ViYWE4MWJhOTYwYmY3NjQyODZkMGFhMWRmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:36:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhxNDByNVptVHR4UmJaUE1KangzSUE9PSIsInZhbHVlIjoiMlNwZjgxd2J1V25CVERoRC9KNk1GUnZOSmk5OXYraGJHZDdYQlVqc2tDMkRLVUgvMXlSc3dzbUcrZkZlRkl3YUkxNHFXd2ZFVnVtNHg5SFVMcSsxZkExTVJLRFpWbWZGbHVVaVlMcC9qamljL285ckNQczNXT2Y4UVg1VnJMRlRScUNhbXF0RWxYSnFRL1dtNXRXRExWaXp5ZytOUURTKzVPbUI0eHZDaVRtUFRjcHJieEpQSDhTVUxKVFpZMU9NNWt1eEFXR3VGM3RFMDNTMy9ndFNxcCtnQnlJMHJWR0JtSnk5N2RGd3MyRDJVTWdNWGlpQ1I0d0gvdnFsMDV1NVZpd2ltc2NudVpUWEwzRTBXMWRQNXJSSGp2Ulp5eTB5Qk1hUk9yZ2NkaXZFeDNkNi9iaU8rTVFqRXJVRnk5aEJocWYxMjZqQWRiUDdkQXFsNVdkMy95a3ptNnJKb0VJSGEyeU5jb2J0eEQ4ek00QzI3U2l1SjUzZENUQlFwbVQ5MEZnNTdRLzdNa2JPOGE0ZGZaOFp6RUVseFQwY3VWMUFXM2p1ZkhXTXNrbXhpbm9HMUNBZVoyWXlNVTZtYWlwK0ZmQUZTWUpDY2Y4ZnVkNEpZY3d2TC9jUmJRNlp4ZjNBUDI4bVB6eFVnb2c5NVV4TnV5R3Y3VWxoT2JPK2k2dCsiLCJtYWMiOiJiMzRjNjI1NTZlNzA3MjQwOTEyOWNmYjkzYjZlNWNjNzMwYjJhN2JiNDg0YTljMjFhMzUzNWY4ZGJjNDYyNjI1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:36:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjVtK0NVU0dMOGFNbFBQdWdVR3NwTUE9PSIsInZhbHVlIjoiZExSLzZVUjczUTFmR0oxUzdYUFB3dmoxZUhLd1VYdTd6d0pNaGZVaHB4UkpEcUNPNEdGUzAwUTc4eEQyY1A4Wk84UEpTZ2hNNUZBdnZnZDhZa2lSM3ppcXdtTzFxdDBEYmRpUEtWZHZzM0pISG5kdVA3QnNpbElUdm4vQktkcDhRYmlSdmJVbVBwNy81WDFmK21tV0FNYkJWQUxDRlpxTTZMc2xuek1XUzBldEZEMWNEaThFaDk1QzlmWjlyS3A4YWVCQ1JPaXBzQ2t4bFVad2gxUjNPZFRBcmhDc1EvZ1BBcEZxbUJydVpoSkVrNk5Kd1FTbysvamVwNXZURHJaTjQ3Wkp4dHFvc002OFpDY0dhNlQyQVRPbjlDbzA4RVVkampXa2hHM2g2Q0pIM0hLMytxWDJCOXRFczRCVEdkdHdVWDdyMG05dmswRStQTzRPVUVHS2IvL0QyaHgzWlBaQlZjTGttMytjc2Q1dTh4cmVYTGo1bWRTZWRQOVhudit0eVp6WUF2V1NFaFJTVjVObEY2MjRWYkc4bzVrbXhqUkd4ZnZtQkxUdkNNSGVaSXVjaGhGeDdlSUoyNUJibVBleHd0MXppRjhRL2x5Wml4U0NTVEo3YjNPdm9YWllwMURUT1phdDh6WHh1RXpDc2Z0ZjZGUW9RdUV4MnVkZ3FmR0UiLCJtYWMiOiIzN2JiMmI2NTNhZmJkODlmNDU5MDJiNTE0ZDAyZWM2Zjg4ZDUxN2ViYWE4MWJhOTYwYmY3NjQyODZkMGFhMWRmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:36:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-890341183\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-858007626 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pricing-plans/10/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Pricing plan updated successfully.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-858007626\", {\"maxDepth\":0})</script>\n"}}