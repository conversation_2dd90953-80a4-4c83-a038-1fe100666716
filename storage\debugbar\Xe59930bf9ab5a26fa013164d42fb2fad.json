{"__meta": {"id": "Xe59930bf9ab5a26fa013164d42fb2fad", "datetime": "2025-07-31 07:46:38", "utime": **********.212591, "method": "GET", "uri": "/storage/expense_receipts/1753947979_logo-dark.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753947996.993887, "end": **********.212613, "duration": 1.2187261581420898, "duration_str": "1.22s", "measures": [{"label": "Booting", "start": 1753947996.993887, "relative_start": 0, "end": 1753947997.79797, "relative_end": 1753947997.79797, "duration": 0.8040831089019775, "duration_str": "804ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753947997.797985, "relative_start": 0.****************, "end": **********.212616, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3043\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1871 to 1877\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1871\" onclick=\"\">routes/web.php:1871-1877</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.019260000000000003, "accumulated_duration_str": "19.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0582771, "duration": 0.019260000000000003, "duration_str": "19.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/expense_receipts/1753947979_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/expense_receipts/1753947979_logo-dark.png", "status_code": "<pre class=sf-dump id=sf-dump-966044648 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-966044648\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-1925287740 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1925287740\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1842776290 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1842776290\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-71336520 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlRLVXZySTZDYUFLcnBiZkFJaUp2Y2c9PSIsInZhbHVlIjoiUzEyMHZ4YSt2bFlIKzZ4MXBnT2FQUmVGc0RsbEtTazRaYjFaQWxSTHRQenl5TzVyTW5wMlRibmlYYXRvaVlWOVFXNzY0dmM1dWVJZEo1L29DVzJ3WTZ3YUZRSE5NOC95TXlXN3BVc1hZZmRBNzdpRGtwRUpDdjArcmtWSjYxMnMweVhLTVNyZnRsS3RZWWxnMXU4bVMvZmovdFRXRW1nVkVyemxSMWNkUUdVem1Ea0RBUEJ0cTFpY2x4UHZyUkxsSEVFVjEvUjdLdldTSVlwcENwa2l4YUVnRUhoK3paTnQ2MHhodEYwS3oxTlFPV0Y4S1NQeU1ITmNwcG5PQnlpRWdBT3hydkcvZUZMa3dzMmJQN0RYQUpwRDBjU2p4SXBrY2plc0pscWYzSVNoT3kvVzl3TlNkaTN4a3BHS0Eydmo2aTFwa3RiWTNlN1Z2YjFTLzFkaXJLd2szTVR6UVVtODRYODNRUC9NV0dQMGhWdWxYTjUwWnRxWWkvanF6MHZtb2JzcSsxMW9NOEVZU1RKYjNQNkcrYi8vc3pRSHFGem9Gb25wUzl0aUNzOXlmRWpaU2RPcm9BWlZJUzZPSTRsVWdCTnVZSmF1TytXeWNXYjFFa0VFUHQxNkJsNHR3bVNvZ3UxUlA5c2ZkbjlZc3U3QWdlYnhiOGIwNHptSGJsTHgiLCJtYWMiOiI3MTBkNDQ1ODcyMTM3NTVjOWFkOGJlNjJiMmFhNzg0MDZiYjAxY2ZiYzNhODliZGRiYjgxZTcxYWVmZDQ1ZWNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjIxaTdOT2poamd0VEhBT3FYY0Y0YXc9PSIsInZhbHVlIjoiaWxKOGhORXQvS0VUK3RrcVVBenNZbmpEZ2thNno5eWQxRU96TXYzMmRRV0l6cHFJT3ZTeitseTl1cW50dGp1ODZLRnFHTHN1andOV0VrbzZvVitCZHdPY0JWaVFOR0ZnUWRQT1IrNVRTRWdCemJZRGNrb1UzbUZZd2JtUUJGTU5aV1hYa2dweEg4OFR2MzFpWVVBSndvWC9zaCt2c3U3ZDI3Y0ozQjk2OFI4dVBlV0Y5Q2pRR2ZqU3h4OU50ZDlWeVlnd1VJelN4U0d6cG80WGxEN0V5eHo1ZVdEbGVFMVl2VE40YmdVSjV5eTlKdjBsV3BneS9sR3NDeXp5QmtTWjRoZ2VZN2JNWXJURUltV1RVZWtVd3hOMlVPL1FrUG1COFN6NEgxSnFWZDgzczJYOXhoTncxNStueVJLdmRRY3NTU3BqR0VkdllmenNIT1hlQisvUXRBZlgxQ2VuRW5hcUNhMEFRamxIUGdFa0FyQ0R0bWdzNDVHTi9sbmFBUmVZYVJwOURIams3YWsycGR3UE1rbkphbXl1dGFhOVRFUFFKanJ1Nmh0WjNQdnp0WkpGVGMwbDVGZ3Mxb3krNDM4SnRsOTZMVDVVbExHT0gvRU96dnRudGVRY000VC8rckZrbW9aZGhzYmc4bzZ0aERDcTVMbmZUekJod3REeHVBY3YiLCJtYWMiOiI0MzMyMmI1ZmZkNDIxM2JjZGQxMmYzMTkyMzMwNWM5MjA0NjIzOGUyZDA4NWU4MWU5NzRkM2M4ZDAxMmFiNGYwIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6InRSUkh6OGdFVkFiZlNGaFo1dzNpYlE9PSIsInZhbHVlIjoiR3RZK0RldmdSMDJWSnVGMldkeTZkU0xYYWkyM1g3eFZtUzY2dnpHKzdqUVlBWThLWXlhK0dyN3dkdTlQUFdzOHM0VjVra3hCY1NxTitTNWdPWFo0eXlRQTJrM2JEdi9FRnVJZXpvaWdaUURNZEpnb3BqcnFSL2Y3cFk3cFYwdlBnNERzcHgxeXM1VmJqWWgzQWVTL2ZlR3M5YnQ2RU11TTJraVFzZ1FXd1FCdGI2VjZuYjdPd3haamtlSjMrNFFFdzFEcmxncEhHckFKZUIwQm43MUdmV3orSTRJbnZFdTQ2SnplOE1TVTZZSG1uMkd5ZUJCK0tiakhEdytRZENWRTQ4bzhUVVRmS0dZa0JhdTg4ZEpJQjU5SHhMaHVTWGxvK25lNGo1WHBUWW9iWVFNSm5tWHZHSm1zTXMrb3BhbTJnMFpPZVN6bk5PbGRjMUROeHkvM1NLMzdvSkRhYkU4OGZKeUZWVEYzTUxmMXNRL0M5T05TYXRENG1QVFpUMjdDUWpqb1lFRDIyaU9zY3gwMG5VV2FIR2lTMUJjeE5zaTFlOWVDY0NhdzZNTXlhWjlPR1ZESUVoL2xUVm4vM0U1SlpuaDYwQ0tiSWhrY0Z2QmE3R01LaUtjeHBpd2x5WDRrdkZaSlc1U295R3dSRW5oT1pHTDZmWFlVa2lwQm82SGsiLCJtYWMiOiI5MTM2ZjY2YzcyYjA3NWExNDJhMDFmNThhYTM4MjhlYTA3NjQyYWI5YzQ4Yzg4OTg5ZTVlNDYwNTMxNWU5MmE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-71336520\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1948948760 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21K3ZmFzZyr4s2pH9aKWRqwEKhgdUCnDfhl6jx78</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948948760\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:46:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:46:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25784</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRobmhGZGtxZWRRM2ZuQk5mSklsK2c9PSIsInZhbHVlIjoidjZUME5Wa0RmQlJRTVY3ZFVVZ3JGVzR3MWNJMk5tSlhFR3pGcnRBdTRxL1RaU0I5a1UxU0R5OEhQVG51aWkxaC9DWDdQVDB4MGJxMFFwLzR1K29tMGhqOHlsRjBKL2ROSnM5K2hvTHA2MWNkQmxHSHB0VG84WVFGdU90UDVtZlhWd29sNXNDUklIWEpvS3B0N0ovNTNZb0VnMXZtbU1seUZtWnh6YklMR3ZYMnh3TmduSEJVMFNtQUdzYy9LN21nVVR1S0hKU0NLcDRrWlN2bEM4OXBTU1BkTEwrd3FIVDhKR3lqQVV3UXhQb3lVVXN3Y1dhWGhHVEZtZm5wVUE4NkZKZUY2S3Mra0EySDIwN0VuS3BJdTNwdE5EK1RGaCtnc0pISUFNb0QvUk8xaXZidWM2Nko0OGRPdW9IMGFyMDg1V0NPTGRCNW9aOE9neUhab1d2UzJHTEExS0x1Yi96VEJQcjg3ckJpdGh1cjA3YzFQSW9zQy9XS0xkdVFzWWdXK1NLS3M4cXUvWUQyMWM0YVozd2s3dDYxWGNReCtTZjl3Znk0STFnd09MRTgwVllCS3pWN3hZbER0Wm8xcEJlSUd4cU5Uc0JQTGliaFVTazVEUEFvME5tblBqRDVTbFdSRGZTSHd4M05CMFpxT1pOVkFoSUxQNkY3MkorYytGZm4iLCJtYWMiOiJlN2YyZWEwZmFmYmZiMzc1NTliYTgyZGRlMDA4YTc2NGRhZTk3N2Y5M2RmYThiNzc2OGJkYjU1NmM0YzFlODllIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:46:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IjBrWXlyQXp1cmhJbHF0SDhEZEVEaUE9PSIsInZhbHVlIjoiOE1URkxuQnNwQlB1WHhDMEc2alFkc2NwM09yazBWb2Z5U2JieWZSYVJyYlFiUUU3QmluS1dTaUJqVkp3TzV3QW5sTytlYVVrbzBjV0hxL0JyWmNXNGwyeERJV2VMOW9OTUVVL3o4RnAxdHZ3OXRVV0dBdE1ZMmptZUtKUnQwSkQ4YUJGY3YyL2ZsSGhsVXEwWHI1ZkJLSEFSOGJSRzFzQStmRk4xTkhxRzY2L1Nuek9xUFFsblIvNFFuc3JxQ1FxZWoxeFdhcW8yYmpWWGUvNGlhK3JNVUhSSndNOFo4bjJZQ2ptajlaZjEyMGE5MU96N3NRWkhGSVZ4MGV4UHRyOWo2b09TWGFBaE1zR1hBd2JCVDZHUWQ4S1pzM3c4U3RzVkp0QUJFTE1naG5Ld2lWQTViek9aVHRjSDNYN2VRY1dGTjFIbzZrWjFqTkNGVnFhenBJWExmZVZ4cGo1Ukd2czZSeWRrcEpJNERobUNwTG1uYzg1YkY4NVJlMS9sQ1EzdTdmUUJRVEVOWnNUM1BlZHkwRTNHSnRCTFZjeGJLQmpJdHVoOVNObURjWW0yaitGWFlWclpUQU9hZGdGL0JLQ2pSWmQyVUJkbklFRGxFaXdlZzNkVVhPQVBoRUQ3V3R2Mk9INW02K0ZwS2JjMGtJMHp4NXRFOEdVTkQ4aTVCU08iLCJtYWMiOiJhYzUwNzhmZTA4ZDk0YWZjNDIzYjliOTk2NjkyMGY0M2EwMDdlOGJlOGYzZjI5Zjk2OThkYjA2OGZjZjQ0MDdhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:46:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRobmhGZGtxZWRRM2ZuQk5mSklsK2c9PSIsInZhbHVlIjoidjZUME5Wa0RmQlJRTVY3ZFVVZ3JGVzR3MWNJMk5tSlhFR3pGcnRBdTRxL1RaU0I5a1UxU0R5OEhQVG51aWkxaC9DWDdQVDB4MGJxMFFwLzR1K29tMGhqOHlsRjBKL2ROSnM5K2hvTHA2MWNkQmxHSHB0VG84WVFGdU90UDVtZlhWd29sNXNDUklIWEpvS3B0N0ovNTNZb0VnMXZtbU1seUZtWnh6YklMR3ZYMnh3TmduSEJVMFNtQUdzYy9LN21nVVR1S0hKU0NLcDRrWlN2bEM4OXBTU1BkTEwrd3FIVDhKR3lqQVV3UXhQb3lVVXN3Y1dhWGhHVEZtZm5wVUE4NkZKZUY2S3Mra0EySDIwN0VuS3BJdTNwdE5EK1RGaCtnc0pISUFNb0QvUk8xaXZidWM2Nko0OGRPdW9IMGFyMDg1V0NPTGRCNW9aOE9neUhab1d2UzJHTEExS0x1Yi96VEJQcjg3ckJpdGh1cjA3YzFQSW9zQy9XS0xkdVFzWWdXK1NLS3M4cXUvWUQyMWM0YVozd2s3dDYxWGNReCtTZjl3Znk0STFnd09MRTgwVllCS3pWN3hZbER0Wm8xcEJlSUd4cU5Uc0JQTGliaFVTazVEUEFvME5tblBqRDVTbFdSRGZTSHd4M05CMFpxT1pOVkFoSUxQNkY3MkorYytGZm4iLCJtYWMiOiJlN2YyZWEwZmFmYmZiMzc1NTliYTgyZGRlMDA4YTc2NGRhZTk3N2Y5M2RmYThiNzc2OGJkYjU1NmM0YzFlODllIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:46:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IjBrWXlyQXp1cmhJbHF0SDhEZEVEaUE9PSIsInZhbHVlIjoiOE1URkxuQnNwQlB1WHhDMEc2alFkc2NwM09yazBWb2Z5U2JieWZSYVJyYlFiUUU3QmluS1dTaUJqVkp3TzV3QW5sTytlYVVrbzBjV0hxL0JyWmNXNGwyeERJV2VMOW9OTUVVL3o4RnAxdHZ3OXRVV0dBdE1ZMmptZUtKUnQwSkQ4YUJGY3YyL2ZsSGhsVXEwWHI1ZkJLSEFSOGJSRzFzQStmRk4xTkhxRzY2L1Nuek9xUFFsblIvNFFuc3JxQ1FxZWoxeFdhcW8yYmpWWGUvNGlhK3JNVUhSSndNOFo4bjJZQ2ptajlaZjEyMGE5MU96N3NRWkhGSVZ4MGV4UHRyOWo2b09TWGFBaE1zR1hBd2JCVDZHUWQ4S1pzM3c4U3RzVkp0QUJFTE1naG5Ld2lWQTViek9aVHRjSDNYN2VRY1dGTjFIbzZrWjFqTkNGVnFhenBJWExmZVZ4cGo1Ukd2czZSeWRrcEpJNERobUNwTG1uYzg1YkY4NVJlMS9sQ1EzdTdmUUJRVEVOWnNUM1BlZHkwRTNHSnRCTFZjeGJLQmpJdHVoOVNObURjWW0yaitGWFlWclpUQU9hZGdGL0JLQ2pSWmQyVUJkbklFRGxFaXdlZzNkVVhPQVBoRUQ3V3R2Mk9INW02K0ZwS2JjMGtJMHp4NXRFOEdVTkQ4aTVCU08iLCJtYWMiOiJhYzUwNzhmZTA4ZDk0YWZjNDIzYjliOTk2NjkyMGY0M2EwMDdlOGJlOGYzZjI5Zjk2OThkYjA2OGZjZjQ0MDdhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:46:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-236810294 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"71 characters\">http://127.0.0.1:8000/storage/expense_receipts/1753947979_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-236810294\", {\"maxDepth\":0})</script>\n"}}