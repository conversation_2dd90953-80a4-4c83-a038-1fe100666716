{"__meta": {"id": "X7b1ed088145c9866ea7f70ca59d237b0", "datetime": "2025-07-31 05:11:21", "utime": **********.355944, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753938680.479381, "end": **********.356002, "duration": 0.8766210079193115, "duration_str": "877ms", "measures": [{"label": "Booting", "start": 1753938680.479381, "relative_start": 0, "end": **********.28778, "relative_end": **********.28778, "duration": 0.808398962020874, "duration_str": "808ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.287794, "relative_start": 0.***************, "end": **********.356008, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "68.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "xZpLe3vBz1GnTUAzpIZ5jBw9GCsuwjt1kOgwPp4L", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2028092175 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2028092175\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-455148378 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-455148378\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-863590621 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-863590621\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1750689709 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1750689709\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1441649793 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1441649793\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1566263195 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:11:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFHb0FGNUhLZmgvbTNuSmZqTlpmY1E9PSIsInZhbHVlIjoiaisxYXhlZHBlWVFiR0JmN2x3eTMrQnlIZEZ5aW9ucFMxZllHNDR4ZFlSOFBvUXh3UWY0Ny9qWGZkVjMyVGd2TFNqN3N4S2xUK0xmM3pFV3d0clRDSERVV0pnWW1ONHdhZVVqUllBN3R4bUpKd256V3N1UE8wby9wQVZtaDNkNFMrNnFKZEJ6b3V5ZVhvV09Nekg2dGtrOXNLVmthVitKTHVCdkdBcGZodFNYN0xyUXMwcFgxMHJqUjNOR09Bd2RZWlQrd0V0OURmNXdyUEhHSzlIYVg4SHF2bzRQbkg3dFZRVjJWWDRQYjFTK0pCWGlRa2tkSjM3aSt4WkUzZEFxaEYrZndoYjZsZkdMa2UzM25FVWNUcXpEOHV4TW1OWTNwSmZ0TFhTbUR5dUM0MWd2VEJiaCthaC9PWjJ0OWFlOWdPUDhEb0p1KytHS1MvRkVJby9HMGY3TmlIemRCTDdYSTRVSzN4RTU4ZHFEeVRNU0VpaWxvR21OWVRPL1ovVHRxcTRUTUhkUDFrckNiQnArRWVsU0duL2RPSjQ0NUJEc1loSGFqWlNZVjU1VTNOcThucWtXRDBTQndlTjBqWXBDam1rMGJTOHF6N3NXS1I1OUR0aUtKenFXYjcxYlVheXlxQVlqOFBkQVE4TlJpVDRPcTRUazZ6ZXNLWFlDa25iMlIiLCJtYWMiOiI5YTkxOGNiYjI3ODM4NTFjNGQxNDk0MjViMDQ0YmY2MjAwNzlmN2IyNDg1NjJhMzllM2ViMmE4NGE2N2RlYzEwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:11:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImRtaHNoYUxqZkJNbnJDNEdTVDdpM3c9PSIsInZhbHVlIjoiZEhKYUdibVRxZGlYSjRvS2VmNTY5Q2JNK3dwRU9Wa0JsSER5K0V2NkdlZnZtb0pIMEhtZzJBam5jVUQvbzBLeDlpbEZnTVJwSXh5Y0s2U0dveGVPQ0tudFhzTHNRd2U0a21VMEkzU2RnMVZoRUkyRVh2TFZjblA3WmdNVy95MlRwTDFoaFR1SmQzbEVFRzdKQnRMYUNoaXdacUJYUDZ2c08rTkVJZzI4anZwYnVRUFdiRTBLZnJvR2JYUGFORVRJdzkyZzdDbDhqSUtabmJhbkxZL1l5bit0c3F2eXlNYmJWejN5NEFkYUJiQWQraEM2cXVPNlhoYXRFbUhSM3g1TlpKZC91eUlJSFp3UXkwdC9ZTkxLcUJHbU40eUhWbDBsN3dBNTNYMXVHRE9wTG5sbHdrU0IxcWprelJFQXY2YjdldksyQVdNcFlwZ0g4a1RhaURoUGJEWkNTSEJTdWFDZ3VTYVNTeVV3WGY3cjdkWmpWdWVzVmNyYW00c00wK0hnazNKbDE0K3lJVHpEejNQbTd4TkJSNEx4OWxQbDdPTkJUQ2RSVGhBUWZORkk4Sk5jSmdGV2JjbGVBbnpwRmRIUnVVWGttaFRZbThsK2xCU1lFZ09sU0xrcFNpTXQ2UVcwdkxSSCtXUVM1amRYMk5PMEVwcXU4YXVhUWpoM25LN1ciLCJtYWMiOiI1NTQ2NzNhOTRiMzBlMDFiNjMwNzBjMmUzNzU1OTMyODRiNzY4ZDBjNTZiY2MwM2NkZWJjNTRiMDc5OWYyZjRmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:11:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFHb0FGNUhLZmgvbTNuSmZqTlpmY1E9PSIsInZhbHVlIjoiaisxYXhlZHBlWVFiR0JmN2x3eTMrQnlIZEZ5aW9ucFMxZllHNDR4ZFlSOFBvUXh3UWY0Ny9qWGZkVjMyVGd2TFNqN3N4S2xUK0xmM3pFV3d0clRDSERVV0pnWW1ONHdhZVVqUllBN3R4bUpKd256V3N1UE8wby9wQVZtaDNkNFMrNnFKZEJ6b3V5ZVhvV09Nekg2dGtrOXNLVmthVitKTHVCdkdBcGZodFNYN0xyUXMwcFgxMHJqUjNOR09Bd2RZWlQrd0V0OURmNXdyUEhHSzlIYVg4SHF2bzRQbkg3dFZRVjJWWDRQYjFTK0pCWGlRa2tkSjM3aSt4WkUzZEFxaEYrZndoYjZsZkdMa2UzM25FVWNUcXpEOHV4TW1OWTNwSmZ0TFhTbUR5dUM0MWd2VEJiaCthaC9PWjJ0OWFlOWdPUDhEb0p1KytHS1MvRkVJby9HMGY3TmlIemRCTDdYSTRVSzN4RTU4ZHFEeVRNU0VpaWxvR21OWVRPL1ovVHRxcTRUTUhkUDFrckNiQnArRWVsU0duL2RPSjQ0NUJEc1loSGFqWlNZVjU1VTNOcThucWtXRDBTQndlTjBqWXBDam1rMGJTOHF6N3NXS1I1OUR0aUtKenFXYjcxYlVheXlxQVlqOFBkQVE4TlJpVDRPcTRUazZ6ZXNLWFlDa25iMlIiLCJtYWMiOiI5YTkxOGNiYjI3ODM4NTFjNGQxNDk0MjViMDQ0YmY2MjAwNzlmN2IyNDg1NjJhMzllM2ViMmE4NGE2N2RlYzEwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:11:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImRtaHNoYUxqZkJNbnJDNEdTVDdpM3c9PSIsInZhbHVlIjoiZEhKYUdibVRxZGlYSjRvS2VmNTY5Q2JNK3dwRU9Wa0JsSER5K0V2NkdlZnZtb0pIMEhtZzJBam5jVUQvbzBLeDlpbEZnTVJwSXh5Y0s2U0dveGVPQ0tudFhzTHNRd2U0a21VMEkzU2RnMVZoRUkyRVh2TFZjblA3WmdNVy95MlRwTDFoaFR1SmQzbEVFRzdKQnRMYUNoaXdacUJYUDZ2c08rTkVJZzI4anZwYnVRUFdiRTBLZnJvR2JYUGFORVRJdzkyZzdDbDhqSUtabmJhbkxZL1l5bit0c3F2eXlNYmJWejN5NEFkYUJiQWQraEM2cXVPNlhoYXRFbUhSM3g1TlpKZC91eUlJSFp3UXkwdC9ZTkxLcUJHbU40eUhWbDBsN3dBNTNYMXVHRE9wTG5sbHdrU0IxcWprelJFQXY2YjdldksyQVdNcFlwZ0g4a1RhaURoUGJEWkNTSEJTdWFDZ3VTYVNTeVV3WGY3cjdkWmpWdWVzVmNyYW00c00wK0hnazNKbDE0K3lJVHpEejNQbTd4TkJSNEx4OWxQbDdPTkJUQ2RSVGhBUWZORkk4Sk5jSmdGV2JjbGVBbnpwRmRIUnVVWGttaFRZbThsK2xCU1lFZ09sU0xrcFNpTXQ2UVcwdkxSSCtXUVM1amRYMk5PMEVwcXU4YXVhUWpoM25LN1ciLCJtYWMiOiI1NTQ2NzNhOTRiMzBlMDFiNjMwNzBjMmUzNzU1OTMyODRiNzY4ZDBjNTZiY2MwM2NkZWJjNTRiMDc5OWYyZjRmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:11:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566263195\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-136240743 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xZpLe3vBz1GnTUAzpIZ5jBw9GCsuwjt1kOgwPp4L</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-136240743\", {\"maxDepth\":0})</script>\n"}}