{"__meta": {"id": "X157bf99c6ccc744ff0133006ac6f71fc", "datetime": "2025-07-31 06:13:58", "utime": **********.892634, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:13:58] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.883687, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753942436.480447, "end": **********.892702, "duration": 2.412255048751831, "duration_str": "2.41s", "measures": [{"label": "Booting", "start": 1753942436.480447, "relative_start": 0, "end": **********.65962, "relative_end": **********.65962, "duration": 2.179172992706299, "duration_str": "2.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.659643, "relative_start": 2.1791958808898926, "end": **********.892707, "relative_end": 5.0067901611328125e-06, "duration": 0.2330641746520996, "duration_str": "233ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45942832, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01244, "accumulated_duration_str": "12.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7646148, "duration": 0.00851, "duration_str": "8.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 68.408}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.813681, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 68.408, "width_percent": 12.701}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.827092, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 81.109, "width_percent": 9.646}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.838243, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 90.756, "width_percent": 9.244}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1230258158 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1230258158\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1390294488 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1390294488\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-194840436 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-194840436\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1009646147 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im9xT1BBOElDL0pZcTJvZ2ZBOWhoaFE9PSIsInZhbHVlIjoiS0JtS25XNFF3UlQyVFNoVUVrd2xjbDd1cmU5ajVpaVY1THM4ekJ5UVU2eGlnQkp3OGdvSVNhaGhjbnFGZG9qVFlSVklGTTB1b1BnelVZYkRtV2ZsTm1sRDFGOU45QTh2OUtIcWRmeUNYb0Y5aSsxS3ROT3diMkpITHByT3N0UUFBbUtZZGlTTURKMTFaWktrRU9zN2pBUFM1UVJFeS9DRDYxUUpFaENZMGg3TFZFK3pQbFNwUk9MTnpZNlFjMm1Yc0ppZnIremZpR3Q1Vkk3VWJUMTgrQkltOVh1SGhUZGx3cVRlWHVQbkJoNGRlVmpKd1U4Q0N6SFNrR0NrRmtqOTdCaG9ybW9jQXBhN1lPSkg5VXJMY2ErcXE3S283RktZdFJPTUluZ3ZCTW85Y0hvQ3ZaZFVqZ0ducDJYQ1hDc2crVE9ZZzhvRjdpWXNuS2xmck45UUNDOFBaTzM1ZjlTTU92eHNmbzZjM2hLOU8zTTIvRURmVktpMkVjeVBKSnZEQ0VLdnZSRVNnRmJXQmRubzRJL3A3RTY0dDU4ZHRYajNwV0MrSW9sYWhUcHR1UnBrM1dXNTFnL3F5dVNCTThCNzBZeGhDeFRkV3R2Rm14WWxialNkMWU2YllSR1o3dWJDVi8wUHpHamdqL1ZoMm05TlNJV3RqMW5Tajc2R1RWSGgiLCJtYWMiOiIzMDRlNTVhMDZhMWEzZGQ2Y2M2MGJkYzRhMjQwMzFkNmM2ODY4ZDk1NjBiNGM3MjQ4YzEwMmM2ZDMyNWRmODNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InBlTzZ1QzV4RmtLb1U2YVFrdThTSFE9PSIsInZhbHVlIjoibEZ1cVV3RU9MbHphTENGUkpkVCt6Kzc1K0V2UmZTaFNhVm5aOUtVSVpsVDNsOHRqQ2dRTG8rcFVlRWlIblRLV2xid2c0RjM3ZlM0SlIzTmgxTy93WXorYkVXMy9BeXZuVll3eHdUV1diVEdIRnMyaDFZOENlRkdIT2d6L0Q4TktpU3JUWnZKYXN2R2diTDFWbEFmUnJlWHh2TmtzWEVhN3FOVjZtcGxtbW5Ld0tTTGRodGdXczFNZk5ocVhtb3I3Qll3NFhOdEhvczdDODRqVEM1T0prMXpYaWxuaExPSWd5NjRqb3JUdlJUbzJXQ3ErcWtIS1JRVmZ3VzJnV1RCY01Od1FkcWIrazVCRWJSUWhhOTBNTFZ5TExxTFVPR2hWaEY5VE96STF0bVpnTDFaU3FKK1daM1ZLYnh1TEhiTXdwUWQrc0h5dkNsTzZwTmNRSm5TQVJvV2lDNWJEREdGR0czNnlLYnN2VVkzNmV3SjNJbzBiNUdGc3lESXB6OStlSWoxaVBrQTRhbEk2eHdhblF1SWRLKzhQUnVmSFZFbzcwbTVGNWdUdW8yQzQzeEhGLzRwbC9XV0MvZjJBSFdaTWxkOXBENnU1M3dzWVhNM1JkRnlpMTRhOXNhR0J2Z2dqNWlyYVY3L1NLWlFpdEZuWWZhQWUrNXlBUldsMHB0SmQiLCJtYWMiOiJhYzAzNjE4MDZmOWZhMmU2OTI3M2QwMjFhMjUyMWM4NzU4NmNjNzQ3ZDUxNmFiNGRlMzk1OWIwYjEzMjk2MjVlIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Im5SRnhtVW9EMnloelZRbVJUd0FKZ0E9PSIsInZhbHVlIjoiaUpmRDgzWk4vUTUxcVo0RERlMnp0eVJlUENkSlR4RUh1TTZCenlXNExMclNkTUNJK2w1Qndsa0Q3NDRXNEtXY0h4cTRtcVRoWmhoZTJZVEgwREVna3p0ZDNnNTk5a29xUFBSeDhpSFlad3JBeEVveUc2ZFlZbGU3cVkzZUp3UkxFLzRkejlXeEZnTytmZ1NrcERuU2ZwbEtQMi83blYyVmJJK3lKTzJWcDN1ZGJqVkhnRVlwMlUvRnFkWGVJTERPTzRBd0tXVVF1dnFIc3hSMjhKbDNMYXRVaGphTHFqT3ZKYlNKNVUzcTlPMXAvUnNHcmNzTUQranFVNDBJNWx2VUxMYXltNnpBVnpvWTVuMGtFc2VTN3IyN2VtVTZ0SW1GSXpmWjczRHViN29icEdOVVJhYkY3dEFFWmJKSWhWQXc1aThEVXB1UjhHc0Z6bTVIT3Z5bHBEUlltc2IwNFd5UklaOFVJdHRLOFlzY2t5QkpaN3N0Sm5zS3VZRHQzQ1FEL2ZyMWhUTlc0TzkrNXBEaHhrZ25NdlhtSUpOZVlqUFdlZTlMb0pGc0hCS2FtNnVaQjdWaktiUENibXZTREZ1TFJmbkNvQloyRExQYkZtV0UyVFc4T1dtdERUQU1XVS9SanpYMUpCek9yTlk4eVhGa004MUlremhJbHBoRHNTK2oiLCJtYWMiOiJjODE4ZDBjYWM3M2Y1OTFjYTBhMDdiYzRkZDM1N2ZhZWY0ZTI3YTE3ZDI2N2E4MTZiMmVlOWM3NDIwOWM3YjdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1009646147\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5LJND5bUclZnLzzClCMEaxKzZ5Yt8AAmGjprz0I</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sUbnPperdA1I3PaRxBpMGV4YsR5AVGq0ERh2LBdJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1537797112 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:13:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikl4Tmt1L2xtRVhBTFlTMUZvM1drTFE9PSIsInZhbHVlIjoicGxxZEVuYVpUSXF4VDJvU29vWllkc0k1bEUzblppbWIrZUJtbWszdHpRVDRNTjlkRzRRcDlFeFk4dWZrVGtvNy9RV3hrVlZKWWpBS0ZqVVdLcHB5cFNjZXB4a1JlcEtscWxscmJ0cXh2algwVGZOWDh5VCtxOU9uOWg3Sm1xdDgwY09lSE5qaFc5dGlmMnkxN0pVcWRvOXI4WnhpMmRQMElaK2dZb2lrOHlkeDNRN24yL254VmJqUTdpS200SHFWV3FHTEZlYU03dVZEV1h0cFBTSjRFaVRzbko4Sk9JMVlvR2VOTHF5YzdXNXJLMW8rTngzSmJWTlF0d2NSOXl4UEpWeDZ6SEcxaFRKOXpBb0ltdkNpcWxmcDNIMVJCKzFpS2Z5TTFtc2dnV284YVhBMGZYYkI2Zk8rV1BJcXlWMXpCRTNWdFcwMk83ZVA3VjR4WTRqVk1GTWVQZFcxSEtWQ0docFdMaFM2ZFdraWpVVlNHRnUvZlpucnZXQWQ1a2xDbUFSMW5ZejN2MTdJSU5iVlJoV09Bc1dnSVlYSERDWHc0d2Jmd1VlNXBWK29TdzhtMFF5K3dKV00wMnpLcjhsSllid0t3RGRQaC9FSUg3WjJXWnYybmdGZmpORmpLU2RqZnA4enZ0aVdERVBOSU4vMm0zS202c0NSN29kU2ZRNU8iLCJtYWMiOiI2YTQwNzFlYjIzYzg1MjkwOGE1NDVhYTgzNTgyZGNhMmRjZWVlNDNmZTkyMDlmZmQ4NjgxZmZiYzRmMDhiZGNkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:13:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IkRCcFhWa2dKU2pPMWFMZ1U0czdQR0E9PSIsInZhbHVlIjoiaEV0eU1ZR2RnVjBjVlpidFBFakJCOERwNnBmY1RHMVRucFpwMCtBeEFMa21CSDl0ZEdUbzJNbFprYTVNak90VGJiRG9YbVdXSkVScGwzOHo4TWsyQXRtbjRNTlIxa0N3SmZGTS95M2JlZU0wajd1U0FDWVFQbjRWSTcrUFZyMG5EaklJeVJ5T0N0aVpjb05aZFlnV0NlOXl0MkxhcnVSMmg4aGxrUGdINVBPSWo2MWxicUtGNStXTC81RER0VEFranVEZHVEUDNSckpCSnYxeVJDNUtGTTMxQWM1WnBTaXJSaGFFbXcxbGlYelJKQ0lzNURCVklReWxXZHZic1cwVUFlelEvZ2pSN2dCamF2ZVpjb2hFUVBqUlVrTmZZeU9UT0N1aEdCRFZJazg4WEJhTDJNblBKQjEvcTRicXlQNzUzWU9idlpFU2lRTEpNblJsUlJuaTJPVzE1dUs3T05CRzhlODBVVHRROEgzRHBQQjQwT0gyeUoxRkJJSXU0UGlQQjlaQ25PczdwRGhMS09uU2wyK3BESHM5US9jTC9PMGREU3RoTERrNG8rS0todDgrYWtyYkZHWDBuck9DemF2M3EzTWNITzVPejlsQjFOd0FlSklRcklEUnB3VmNlSHRyYnF1UTJxTzhFcVpkNVN6SS9reFhHVzUyVEw2Yy9rSmMiLCJtYWMiOiI0ZmM0MmMxOGNmOWJkNjEwY2JmNzAyZmYyNmU1OGRmZDYwYTdiNTZkN2EyN2QyMmZmMzc3NWQ2MWQ0ZTcxNWI3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:13:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikl4Tmt1L2xtRVhBTFlTMUZvM1drTFE9PSIsInZhbHVlIjoicGxxZEVuYVpUSXF4VDJvU29vWllkc0k1bEUzblppbWIrZUJtbWszdHpRVDRNTjlkRzRRcDlFeFk4dWZrVGtvNy9RV3hrVlZKWWpBS0ZqVVdLcHB5cFNjZXB4a1JlcEtscWxscmJ0cXh2algwVGZOWDh5VCtxOU9uOWg3Sm1xdDgwY09lSE5qaFc5dGlmMnkxN0pVcWRvOXI4WnhpMmRQMElaK2dZb2lrOHlkeDNRN24yL254VmJqUTdpS200SHFWV3FHTEZlYU03dVZEV1h0cFBTSjRFaVRzbko4Sk9JMVlvR2VOTHF5YzdXNXJLMW8rTngzSmJWTlF0d2NSOXl4UEpWeDZ6SEcxaFRKOXpBb0ltdkNpcWxmcDNIMVJCKzFpS2Z5TTFtc2dnV284YVhBMGZYYkI2Zk8rV1BJcXlWMXpCRTNWdFcwMk83ZVA3VjR4WTRqVk1GTWVQZFcxSEtWQ0docFdMaFM2ZFdraWpVVlNHRnUvZlpucnZXQWQ1a2xDbUFSMW5ZejN2MTdJSU5iVlJoV09Bc1dnSVlYSERDWHc0d2Jmd1VlNXBWK29TdzhtMFF5K3dKV00wMnpLcjhsSllid0t3RGRQaC9FSUg3WjJXWnYybmdGZmpORmpLU2RqZnA4enZ0aVdERVBOSU4vMm0zS202c0NSN29kU2ZRNU8iLCJtYWMiOiI2YTQwNzFlYjIzYzg1MjkwOGE1NDVhYTgzNTgyZGNhMmRjZWVlNDNmZTkyMDlmZmQ4NjgxZmZiYzRmMDhiZGNkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:13:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IkRCcFhWa2dKU2pPMWFMZ1U0czdQR0E9PSIsInZhbHVlIjoiaEV0eU1ZR2RnVjBjVlpidFBFakJCOERwNnBmY1RHMVRucFpwMCtBeEFMa21CSDl0ZEdUbzJNbFprYTVNak90VGJiRG9YbVdXSkVScGwzOHo4TWsyQXRtbjRNTlIxa0N3SmZGTS95M2JlZU0wajd1U0FDWVFQbjRWSTcrUFZyMG5EaklJeVJ5T0N0aVpjb05aZFlnV0NlOXl0MkxhcnVSMmg4aGxrUGdINVBPSWo2MWxicUtGNStXTC81RER0VEFranVEZHVEUDNSckpCSnYxeVJDNUtGTTMxQWM1WnBTaXJSaGFFbXcxbGlYelJKQ0lzNURCVklReWxXZHZic1cwVUFlelEvZ2pSN2dCamF2ZVpjb2hFUVBqUlVrTmZZeU9UT0N1aEdCRFZJazg4WEJhTDJNblBKQjEvcTRicXlQNzUzWU9idlpFU2lRTEpNblJsUlJuaTJPVzE1dUs3T05CRzhlODBVVHRROEgzRHBQQjQwT0gyeUoxRkJJSXU0UGlQQjlaQ25PczdwRGhMS09uU2wyK3BESHM5US9jTC9PMGREU3RoTERrNG8rS0todDgrYWtyYkZHWDBuck9DemF2M3EzTWNITzVPejlsQjFOd0FlSklRcklEUnB3VmNlSHRyYnF1UTJxTzhFcVpkNVN6SS9reFhHVzUyVEw2Yy9rSmMiLCJtYWMiOiI0ZmM0MmMxOGNmOWJkNjEwY2JmNzAyZmYyNmU1OGRmZDYwYTdiNTZkN2EyN2QyMmZmMzc3NWQ2MWQ0ZTcxNWI3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:13:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1537797112\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1811090184 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1811090184\", {\"maxDepth\":0})</script>\n"}}