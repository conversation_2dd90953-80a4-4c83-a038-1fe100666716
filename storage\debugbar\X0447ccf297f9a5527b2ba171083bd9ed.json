{"__meta": {"id": "X0447ccf297f9a5527b2ba171083bd9ed", "datetime": "2025-07-31 05:11:57", "utime": **********.391968, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753938716.161844, "end": **********.392018, "duration": 1.2301740646362305, "duration_str": "1.23s", "measures": [{"label": "Booting", "start": 1753938716.161844, "relative_start": 0, "end": **********.272594, "relative_end": **********.272594, "duration": 1.1107499599456787, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.27262, "relative_start": 1.****************, "end": **********.392023, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "119ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "6iSO4BTGvE9I9aPmupSbti7DJzMO2v0GaXqEX5qs", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1623559603 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1623559603\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1953931048 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1953931048\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1166231868 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1166231868\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-605085840 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-605085840\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1719718515 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1719718515\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1325471056 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:11:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNPMFAyVjZ5cWVUU3c5VGNRM1FrYmc9PSIsInZhbHVlIjoiVUdML3o1cDJDVENVelFUV1V1dmZBVnFYa3RkVGc0NHl0SURZcGtIcXYyS29XZXYxZUU0a2JZTldVSVk1SllVQUZWdWhMbzFETGNyeDRYYUFreC9NUHBUZ2JiNXZGYXlSSktZaEtLbTJCR2ZXeHdwSEw2SEhxZmxkN0xqYXE1djhhc00xZkdCSUllbUtmRWJNYmdMakx1MjRRV3lCSkxyUGlaSVhldjZDak56RFlZdFdVdU0wMkl6VjFNeGlVOGg5MjZlMkZHZGhMVk8rbUVlYmNzSGdrS0Z3cmVWY1VBVmYrUFdWVEVQWEhkQlVWRlhiTk9ZVE5lb3VvNHlLa0MwTEZLY25MbGxRUlFra3c1aGsvL3Rua2g5RHhTVUpxZDZiS0lzL1p1RzNncGpNaVZOOWJuVUtoS3VzMWl3cHBQbkkvTWJ6NTNYSWtYbW9ONVdqclVCZDAwdVZtcVJpQjRaN1AwS1M2bWZPa2tqUXJwRGl4TjZ2a044LzRRS0U3SDRmQUg5SjkxeE9HNDQrK01rQ2padmF5QzRQRVFZNm43RXhYd08wWFRFL0JiUGhndDY1KzhWaGJxQlZyUnNzTFZaOUZyZ1dQdXErcURUemMzWFljbzJvQlplMExscmt0cUEzRDBma2pGLy8xa0psMG1JYXIwdVA0R1hUS0NEdC82Z2siLCJtYWMiOiI0NzdmMDFkZWM3ZWFiZjI0NmRiN2M4YTI5Y2Q4MWUyYjhkMGI0Njk4OGZlNTgwYTY0ZTk5NzYzMmZjZTBiYWIyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:11:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im5jaGFVRWJucU9Dem55eHdJN2h2VWc9PSIsInZhbHVlIjoiUmtRVmd1d05wWnR2OGZkNVlheDZUckpqd3pOeGN1ZWVIcjZQZTJJRitYbTFFeWVJb2xHeEg5NkR5RVZ5WUFWaEg1Rm44QkJGdkY4cWo2dUhYNzROUUVtRjFzMVJRSXI0eDBucFpVcCtvQVBKdTNPdlUvN1Vpa1RxT1B0czdxT2p5bndBR1AxdFYyRm9DakU3V3JZZEpUeWRjZTYzOGdrY1ByMEU0ZXBGZ2Zib0ZwR3pNQjlDUE0rVXk4VFZvUG5kbHBoN3g3MHZOQUlBZHJSMTZlSU5tbzl0WnhIOS8wUWhzZ1hzQWlTcE9rQTNqM2tGTUZOdVJGc0RwcFB2SERYN1JsTnUyNWNlNmk4UmZVMlhycC9QWWlkdFRFd2RyNVVRRlVsVm9NUFViZkdIOXBZdERabWQzQ0poR3kzeGdhSVVEaHpwV05YQ2JWNG9UN2RiZ1IwV0F4MHdtdlpXSEZkc1RSYkYzWFQzRkt0NG5ET0pYYVl4bXpOUDZzdEVBR0RUT3VhZllyVW1WL25samdGZXR1ZDV3MVVGSFluR08rRnlhVHA1U0xlSGtjR2FKMS9IaDhTczRVNGw2cDZCNTA3SnpMM0w2ZTBXTUlrNVVwYU1JeUtkcldlRnlEclNXSisyL1Y2UnB2d21ORHZqQjY3YnJleStkYU1yYW1UcTJxTXEiLCJtYWMiOiI2YmMxYjkxMmZiMzUxMDRjNWM0NmU1NWY1ZjgxMmM1MGFkOTY5NzI1ODk0OWJjMDNmZjhhNmJlZGIwMzFlMzZlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:11:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNPMFAyVjZ5cWVUU3c5VGNRM1FrYmc9PSIsInZhbHVlIjoiVUdML3o1cDJDVENVelFUV1V1dmZBVnFYa3RkVGc0NHl0SURZcGtIcXYyS29XZXYxZUU0a2JZTldVSVk1SllVQUZWdWhMbzFETGNyeDRYYUFreC9NUHBUZ2JiNXZGYXlSSktZaEtLbTJCR2ZXeHdwSEw2SEhxZmxkN0xqYXE1djhhc00xZkdCSUllbUtmRWJNYmdMakx1MjRRV3lCSkxyUGlaSVhldjZDak56RFlZdFdVdU0wMkl6VjFNeGlVOGg5MjZlMkZHZGhMVk8rbUVlYmNzSGdrS0Z3cmVWY1VBVmYrUFdWVEVQWEhkQlVWRlhiTk9ZVE5lb3VvNHlLa0MwTEZLY25MbGxRUlFra3c1aGsvL3Rua2g5RHhTVUpxZDZiS0lzL1p1RzNncGpNaVZOOWJuVUtoS3VzMWl3cHBQbkkvTWJ6NTNYSWtYbW9ONVdqclVCZDAwdVZtcVJpQjRaN1AwS1M2bWZPa2tqUXJwRGl4TjZ2a044LzRRS0U3SDRmQUg5SjkxeE9HNDQrK01rQ2padmF5QzRQRVFZNm43RXhYd08wWFRFL0JiUGhndDY1KzhWaGJxQlZyUnNzTFZaOUZyZ1dQdXErcURUemMzWFljbzJvQlplMExscmt0cUEzRDBma2pGLy8xa0psMG1JYXIwdVA0R1hUS0NEdC82Z2siLCJtYWMiOiI0NzdmMDFkZWM3ZWFiZjI0NmRiN2M4YTI5Y2Q4MWUyYjhkMGI0Njk4OGZlNTgwYTY0ZTk5NzYzMmZjZTBiYWIyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:11:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im5jaGFVRWJucU9Dem55eHdJN2h2VWc9PSIsInZhbHVlIjoiUmtRVmd1d05wWnR2OGZkNVlheDZUckpqd3pOeGN1ZWVIcjZQZTJJRitYbTFFeWVJb2xHeEg5NkR5RVZ5WUFWaEg1Rm44QkJGdkY4cWo2dUhYNzROUUVtRjFzMVJRSXI0eDBucFpVcCtvQVBKdTNPdlUvN1Vpa1RxT1B0czdxT2p5bndBR1AxdFYyRm9DakU3V3JZZEpUeWRjZTYzOGdrY1ByMEU0ZXBGZ2Zib0ZwR3pNQjlDUE0rVXk4VFZvUG5kbHBoN3g3MHZOQUlBZHJSMTZlSU5tbzl0WnhIOS8wUWhzZ1hzQWlTcE9rQTNqM2tGTUZOdVJGc0RwcFB2SERYN1JsTnUyNWNlNmk4UmZVMlhycC9QWWlkdFRFd2RyNVVRRlVsVm9NUFViZkdIOXBZdERabWQzQ0poR3kzeGdhSVVEaHpwV05YQ2JWNG9UN2RiZ1IwV0F4MHdtdlpXSEZkc1RSYkYzWFQzRkt0NG5ET0pYYVl4bXpOUDZzdEVBR0RUT3VhZllyVW1WL25samdGZXR1ZDV3MVVGSFluR08rRnlhVHA1U0xlSGtjR2FKMS9IaDhTczRVNGw2cDZCNTA3SnpMM0w2ZTBXTUlrNVVwYU1JeUtkcldlRnlEclNXSisyL1Y2UnB2d21ORHZqQjY3YnJleStkYU1yYW1UcTJxTXEiLCJtYWMiOiI2YmMxYjkxMmZiMzUxMDRjNWM0NmU1NWY1ZjgxMmM1MGFkOTY5NzI1ODk0OWJjMDNmZjhhNmJlZGIwMzFlMzZlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:11:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1325471056\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-609262673 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6iSO4BTGvE9I9aPmupSbti7DJzMO2v0GaXqEX5qs</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-609262673\", {\"maxDepth\":0})</script>\n"}}