{"__meta": {"id": "X36d106e727868ad04a79d433e858d7a4", "datetime": "2025-07-31 06:09:14", "utime": **********.166308, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753942152.353226, "end": **********.166369, "duration": 1.813143014907837, "duration_str": "1.81s", "measures": [{"label": "Booting", "start": 1753942152.353226, "relative_start": 0, "end": **********.006303, "relative_end": **********.006303, "duration": 1.6530771255493164, "duration_str": "1.65s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.006337, "relative_start": 1.****************, "end": **********.166375, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "160ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dgvU0kVGJDo7yPfBxaSdPFdjvsBngECClcCYxsSJ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1420622052 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1420622052\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1949067933 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1949067933\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1133133801 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1133133801\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1566409214 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566409214\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-295716426 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-295716426\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-716070592 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:09:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZJT1RUZGNpOGhsTysvVjFnYXRqbFE9PSIsInZhbHVlIjoiNEFFbC9ZdmVyd29Cc2xaRkgvcHR0eGtlK0ZZWm44aGxIdGlsZW5Qb1llQldiV0FMVXBQd1dpTmVneWNvZ0VDMHduSnRTcFBnSjlyR1hUVE9pemlFK1N6YzdLM1h6aTl6TUdqSUI3OTR1VFVNZk9BamlKNWh4Y1ljWUxHb1d2emVlZEJabVEvZGh5WFYvc0VSajhEQWQ5SnQ3eVpiZTJuc2Iwd2p2VTNuMWIyQ0tYMUJFOE1CeG9VSFIyaVl3Sm9qc0VzMnhIazRaWTJxVWJib09GZ1NvSDNVQzkwdGQyWk1RRFJPR1NWd01lNlN0L21CazN3Y0xBdXhHWGVpYjRtN0NQaUJPVHBvWFg0OS9DU2NGUmphNG1ORTRmWEIvKzJxcDhaUXpUMjZqS1V3dGJHMzhnOWozWnlSSXZjQlhGV0dVTzlMa3JkaFEySUVsdHpZNVoxZEQyYlJJQW9yWldQV0hIRWQ4WlBEK2JtUVhPZCtPNzFOQ3V1NGc2dFZmZUp6cGVtcWw1RUErY0tGZUFCK0RCbXFLVElCbXpRK1ZPNENiUy91bGQ4MHgyUlIvZU9pZEhqcGtHbnFCaTd0YnRRSnNNanBJVXNxaDRXNHEzSWoyc1dqNWVjVnU5b3VRSXhBWHZnSjhnREJaellKdFVOZWdjRWZBS2sva2MrM2xkYXMiLCJtYWMiOiJiYmM5ZTMyNjUxNGFiYjI5MDdhZTdmZDA1NmIxNGUwNDgzZDFmYzNmNjAxMzgyOWFiNTY0ZjFhZDNiMzJmN2M2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:09:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlFvZmk4czRWK2NIS0ZaZUw3cUVkd1E9PSIsInZhbHVlIjoieGJQYW1wWHY2RWdyUW0yWVNTSU5rYW16UUVSeEswTGdnN3h3Q2l5V0ZBSFFGUnkvaUtKaEVKRndlNTVYVVBINExJbFBRR3pIV3hienlHQXE5cS84ZlN0c0hmNDJ0ZUsyeFVkOG1DSFd0U2Zna3J2dWxZK2xSc2lJL1RHNTNsL3R3bmFGRmNNTXo1em9XeW55WFdPamVtYUppQVlibEpiR1Y1RlEzRzdBVElmeXQrMi9SOFBndDNHOFAzb3RUSXFlUmVnd1dXbjE5N1BmRXZuclU2NHFlcnBHd3lmdzVLRUZUa1RSQUtHa0liNmNEU3J3NzlaVGdmR0w5SCtTblVsOTdZVVhyLy9WenhLWmlzeUdBTVZqVlJQc2cvdm94SGsyZ0V0bkFEZ2ZpVFVURjBJSVJLSmFrYzlnMjBPYWorM2hTRlRmODR0OG9aUytkYVMwWjNPa0gzd3c0Vko4RUF5TUJvdlk1OUxOeWh6QkJ4UWVmZWMxdEpwVnlsUmVKL1ZseVlIQ0R1Y2J0UmNPTFdSc2dKQTlBbmQ3ZjZXL2l1bnllN3pOblNNODRveEZGOUVEWko5TDVuRk9mS1UzV1l2eFRPK2FNSGVpTm5Mcy8ydm03czEvV25xSWZSSXR1TXZoUGNqMGhhR0JueG5KQzdCMklTRmx6YVZSYVoyWXhWZVYiLCJtYWMiOiI0NmQwN2FlOWNlODk0NDA1Y2M5MTA5NGExMDljYmVlOTFiYWVmNGU5YjYwMWFlNmFlZmViNjFjOWE0MDkyYmViIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:09:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZJT1RUZGNpOGhsTysvVjFnYXRqbFE9PSIsInZhbHVlIjoiNEFFbC9ZdmVyd29Cc2xaRkgvcHR0eGtlK0ZZWm44aGxIdGlsZW5Qb1llQldiV0FMVXBQd1dpTmVneWNvZ0VDMHduSnRTcFBnSjlyR1hUVE9pemlFK1N6YzdLM1h6aTl6TUdqSUI3OTR1VFVNZk9BamlKNWh4Y1ljWUxHb1d2emVlZEJabVEvZGh5WFYvc0VSajhEQWQ5SnQ3eVpiZTJuc2Iwd2p2VTNuMWIyQ0tYMUJFOE1CeG9VSFIyaVl3Sm9qc0VzMnhIazRaWTJxVWJib09GZ1NvSDNVQzkwdGQyWk1RRFJPR1NWd01lNlN0L21CazN3Y0xBdXhHWGVpYjRtN0NQaUJPVHBvWFg0OS9DU2NGUmphNG1ORTRmWEIvKzJxcDhaUXpUMjZqS1V3dGJHMzhnOWozWnlSSXZjQlhGV0dVTzlMa3JkaFEySUVsdHpZNVoxZEQyYlJJQW9yWldQV0hIRWQ4WlBEK2JtUVhPZCtPNzFOQ3V1NGc2dFZmZUp6cGVtcWw1RUErY0tGZUFCK0RCbXFLVElCbXpRK1ZPNENiUy91bGQ4MHgyUlIvZU9pZEhqcGtHbnFCaTd0YnRRSnNNanBJVXNxaDRXNHEzSWoyc1dqNWVjVnU5b3VRSXhBWHZnSjhnREJaellKdFVOZWdjRWZBS2sva2MrM2xkYXMiLCJtYWMiOiJiYmM5ZTMyNjUxNGFiYjI5MDdhZTdmZDA1NmIxNGUwNDgzZDFmYzNmNjAxMzgyOWFiNTY0ZjFhZDNiMzJmN2M2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:09:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlFvZmk4czRWK2NIS0ZaZUw3cUVkd1E9PSIsInZhbHVlIjoieGJQYW1wWHY2RWdyUW0yWVNTSU5rYW16UUVSeEswTGdnN3h3Q2l5V0ZBSFFGUnkvaUtKaEVKRndlNTVYVVBINExJbFBRR3pIV3hienlHQXE5cS84ZlN0c0hmNDJ0ZUsyeFVkOG1DSFd0U2Zna3J2dWxZK2xSc2lJL1RHNTNsL3R3bmFGRmNNTXo1em9XeW55WFdPamVtYUppQVlibEpiR1Y1RlEzRzdBVElmeXQrMi9SOFBndDNHOFAzb3RUSXFlUmVnd1dXbjE5N1BmRXZuclU2NHFlcnBHd3lmdzVLRUZUa1RSQUtHa0liNmNEU3J3NzlaVGdmR0w5SCtTblVsOTdZVVhyLy9WenhLWmlzeUdBTVZqVlJQc2cvdm94SGsyZ0V0bkFEZ2ZpVFVURjBJSVJLSmFrYzlnMjBPYWorM2hTRlRmODR0OG9aUytkYVMwWjNPa0gzd3c0Vko4RUF5TUJvdlk1OUxOeWh6QkJ4UWVmZWMxdEpwVnlsUmVKL1ZseVlIQ0R1Y2J0UmNPTFdSc2dKQTlBbmQ3ZjZXL2l1bnllN3pOblNNODRveEZGOUVEWko5TDVuRk9mS1UzV1l2eFRPK2FNSGVpTm5Mcy8ydm03czEvV25xSWZSSXR1TXZoUGNqMGhhR0JueG5KQzdCMklTRmx6YVZSYVoyWXhWZVYiLCJtYWMiOiI0NmQwN2FlOWNlODk0NDA1Y2M5MTA5NGExMDljYmVlOTFiYWVmNGU5YjYwMWFlNmFlZmViNjFjOWE0MDkyYmViIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:09:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-716070592\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-686101397 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dgvU0kVGJDo7yPfBxaSdPFdjvsBngECClcCYxsSJ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-686101397\", {\"maxDepth\":0})</script>\n"}}