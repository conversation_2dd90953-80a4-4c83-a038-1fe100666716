{"__meta": {"id": "X6277835c8824eb162cd0f3962cb24477", "datetime": "2025-07-31 06:32:24", "utime": **********.915203, "method": "GET", "uri": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943542.712833, "end": **********.91529, "duration": 2.2024571895599365, "duration_str": "2.2s", "measures": [{"label": "Booting", "start": 1753943542.712833, "relative_start": 0, "end": **********.620707, "relative_end": **********.620707, "duration": 1.9078741073608398, "duration_str": "1.91s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.620766, "relative_start": 1.***************, "end": **********.915299, "relative_end": 8.821487426757812e-06, "duration": 0.*****************, "duration_str": "295ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wb6kdPPMN4GI8EzoJX2OWvBhv8Cq5mW0mlPXDww2", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "status_code": "<pre class=sf-dump id=sf-dump-448705627 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-448705627\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1520673955 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1520673955\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1520955011 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1520955011\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-788853302 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-788853302\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1614254944 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1614254944\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1749197522 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:32:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBGdXJXaHhhaVUyZGVLbDc0a0h2ZEE9PSIsInZhbHVlIjoidUwyVGZtdXd1RnJzMWhzMlFOT3lYOElOaUl2T0sraWdoYTZHcmNGdFBLYXVObTZ6cGxscFI2NWpzZFlLWnhNN0tDUjhYZWFSdmhQbjJtaFNHZjBOaVQ5UEVUUVBXNC8yK25zR1VpMnM4d1p4MURkeVNtaFh5UEp5VFdJaTh0MWV6RlV1OXVwZURRRVJPWFBUazJodElVUDAreksyTjNmRis5a3RBSWlYMnJPKzdGWFB0d1poMkZna2dEK1NqaXhJU1A0Q2NEYmVyVE9hY01ISFhrN3RCZlFwMTdKN0EvWFFlVVdNS2w1REtqcUV0OHJ6Ulh5U2N5VnhCWnJpVXFzN053bGdrRDZUVVdRUjBWcXc5SC9LTVFGUmM2OEJKYnJVbExWNldxK0tPNE1QOThORmR0K0k3T3ZzdEsxeVp3bTlaUWJQaWwvSmgvSnVkOWRsQUptQkVsOGFEVUpMajh1RGVnTWt5VkpKZ0xXd0R1cW1jU1hPVk1Oc1hIRlNUQU84dG55U3l5VExWbGlZTVF6aURlVUNvRmI2Uy9xQ2QwYlhhZ3R0UVhvQXZvdjIrckNCWnpkdExTYU9lWStUNHE2djEwMEgyenJpeTZSZldhOUFoSUVsRkhKVlByWFN3RThQWlg3ampnM0MrNGtWTzFmNCtXTC84RFZKa1ZFN1FTZTkiLCJtYWMiOiJiYmYyYTc3ZjI3N2EzMDc4OTEzZTMwYTFhM2E4N2FlYzFmZWQ3MjgwMDIyNDEzNTVhNWRmM2QzNDUwM2I0OTM4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ijhqa3NPVysxQ1d3UnAwOEp0MlhpYWc9PSIsInZhbHVlIjoiNm5Xckd3OWR6cDRTMlU2S0ZTVURjWWRJejQ1M3lNeTc3TDA3cTNxWjVIUlM4ZWw2WE0xdC95LzVmSzVhK3gxVWtTT1dJQWxoaUdoUVlUKzB3NkhtN3hnVWpCUlZPai9RRHRJSXpQTTB4WHczd0NQc0xmbmVra0dpZkVCMm5qa0FnQ0o4Z0tFZENqcFhueHJDVkwxRGl0QSsxMHZVNDd6RW5hWWpZbHhpbGs3UjlZKzhpKzdNL2JKMExrMWRBSlRhOVZnZFVOcG0vQkhleTJGYTRmbnRFQ0djTW5XTURPSVcrWVFKK2RnOFpUaEJ0S2xRZDdWZCs0TTlSR0VGaEhLWWxUaFRCcFM1dnZkSThwOGZWSFZXTmNTUUYrbEtnVVNnUlVyTzRpOUVhamxBZjZaamE4VzhacGgxd3VLbGxWa00wNzVDcmhjMmJSRHhMRnFFWVNUeDA4TXBaeWk1czg1TG1vV21STWNLK3JnT0hoVGlnL0xTVk85YnNqRExaamphWnpxUDlPMElMNVJmaWJOdzUzZ2w0bnl1cHlTV1VET0dsOUlLYmVzcTBwYlpwQXZ0b0NXend3RFRibmMyUzRrZmNPczhaNVBEME1ZUW1xTWhPWFQyQTFUVEZlZVdsRE04WFRpblg4VWNVZFE0dFoydzJScXJPeFVPUi9GVGg5Um0iLCJtYWMiOiI5YmE3MTAzNjM0YTdmNWYwYzFiODcyNWVkMDk3NjBkNGU3NTA5YjI0OWZhYjY1NGE0YzhjZWI1YmE0NzE2MDNkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBGdXJXaHhhaVUyZGVLbDc0a0h2ZEE9PSIsInZhbHVlIjoidUwyVGZtdXd1RnJzMWhzMlFOT3lYOElOaUl2T0sraWdoYTZHcmNGdFBLYXVObTZ6cGxscFI2NWpzZFlLWnhNN0tDUjhYZWFSdmhQbjJtaFNHZjBOaVQ5UEVUUVBXNC8yK25zR1VpMnM4d1p4MURkeVNtaFh5UEp5VFdJaTh0MWV6RlV1OXVwZURRRVJPWFBUazJodElVUDAreksyTjNmRis5a3RBSWlYMnJPKzdGWFB0d1poMkZna2dEK1NqaXhJU1A0Q2NEYmVyVE9hY01ISFhrN3RCZlFwMTdKN0EvWFFlVVdNS2w1REtqcUV0OHJ6Ulh5U2N5VnhCWnJpVXFzN053bGdrRDZUVVdRUjBWcXc5SC9LTVFGUmM2OEJKYnJVbExWNldxK0tPNE1QOThORmR0K0k3T3ZzdEsxeVp3bTlaUWJQaWwvSmgvSnVkOWRsQUptQkVsOGFEVUpMajh1RGVnTWt5VkpKZ0xXd0R1cW1jU1hPVk1Oc1hIRlNUQU84dG55U3l5VExWbGlZTVF6aURlVUNvRmI2Uy9xQ2QwYlhhZ3R0UVhvQXZvdjIrckNCWnpkdExTYU9lWStUNHE2djEwMEgyenJpeTZSZldhOUFoSUVsRkhKVlByWFN3RThQWlg3ampnM0MrNGtWTzFmNCtXTC84RFZKa1ZFN1FTZTkiLCJtYWMiOiJiYmYyYTc3ZjI3N2EzMDc4OTEzZTMwYTFhM2E4N2FlYzFmZWQ3MjgwMDIyNDEzNTVhNWRmM2QzNDUwM2I0OTM4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ijhqa3NPVysxQ1d3UnAwOEp0MlhpYWc9PSIsInZhbHVlIjoiNm5Xckd3OWR6cDRTMlU2S0ZTVURjWWRJejQ1M3lNeTc3TDA3cTNxWjVIUlM4ZWw2WE0xdC95LzVmSzVhK3gxVWtTT1dJQWxoaUdoUVlUKzB3NkhtN3hnVWpCUlZPai9RRHRJSXpQTTB4WHczd0NQc0xmbmVra0dpZkVCMm5qa0FnQ0o4Z0tFZENqcFhueHJDVkwxRGl0QSsxMHZVNDd6RW5hWWpZbHhpbGs3UjlZKzhpKzdNL2JKMExrMWRBSlRhOVZnZFVOcG0vQkhleTJGYTRmbnRFQ0djTW5XTURPSVcrWVFKK2RnOFpUaEJ0S2xRZDdWZCs0TTlSR0VGaEhLWWxUaFRCcFM1dnZkSThwOGZWSFZXTmNTUUYrbEtnVVNnUlVyTzRpOUVhamxBZjZaamE4VzhacGgxd3VLbGxWa00wNzVDcmhjMmJSRHhMRnFFWVNUeDA4TXBaeWk1czg1TG1vV21STWNLK3JnT0hoVGlnL0xTVk85YnNqRExaamphWnpxUDlPMElMNVJmaWJOdzUzZ2w0bnl1cHlTV1VET0dsOUlLYmVzcTBwYlpwQXZ0b0NXend3RFRibmMyUzRrZmNPczhaNVBEME1ZUW1xTWhPWFQyQTFUVEZlZVdsRE04WFRpblg4VWNVZFE0dFoydzJScXJPeFVPUi9GVGg5Um0iLCJtYWMiOiI5YmE3MTAzNjM0YTdmNWYwYzFiODcyNWVkMDk3NjBkNGU3NTA5YjI0OWZhYjY1NGE0YzhjZWI1YmE0NzE2MDNkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1749197522\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1933205338 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wb6kdPPMN4GI8EzoJX2OWvBhv8Cq5mW0mlPXDww2</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933205338\", {\"maxDepth\":0})</script>\n"}}