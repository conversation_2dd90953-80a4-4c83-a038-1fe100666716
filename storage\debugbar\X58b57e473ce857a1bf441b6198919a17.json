{"__meta": {"id": "X58b57e473ce857a1bf441b6198919a17", "datetime": "2025-07-31 06:31:58", "utime": **********.415889, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:31:58] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.402965, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943516.448228, "end": **********.415948, "duration": 1.9677200317382812, "duration_str": "1.97s", "measures": [{"label": "Booting", "start": 1753943516.448228, "relative_start": 0, "end": **********.12448, "relative_end": **********.12448, "duration": 1.6762521266937256, "duration_str": "1.68s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.124507, "relative_start": 1.676279067993164, "end": **********.415954, "relative_end": 6.198883056640625e-06, "duration": 0.29144716262817383, "duration_str": "291ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50618648, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.370619, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.05613, "accumulated_duration_str": "56.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2310572, "duration": 0.02208, "duration_str": "22.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 39.337}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.286242, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 39.337, "width_percent": 3.118}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.299634, "duration": 0.030719999999999997, "duration_str": "30.72ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 42.455, "width_percent": 54.73}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.340922, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 97.185, "width_percent": 2.815}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies/79/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1122402382 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1122402382\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-380507711 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-380507711\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1746349655 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1746349655\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-215224053 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/system-admin/companies/79/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im02RW9yRThpWVp6VlgzbmpNaXpOb2c9PSIsInZhbHVlIjoieHV5WUk4V1NKZWNLTkhvS0ZqdkhXQlNYMlRidGk1dTJ1eFZTZnpLdWZNTWtBamRHallJcTlUM3AxekN4dXA1blFpQzJWMjE2bmMyZXBYYVZxMkVEZk8ycklodTdCMGFKL0xSRU9PRm5kQWRpa3pXTXFoQVhiVXVaNW9Ed2dqdERrdDBiUjNZMUVBcisxNzVDbG14ckFEZHRiOUpXaE11K3ZURGNLMW1VU0U1OWRjeTFlYS9IaTVQOUxCSmsva0hWbHc0c1BQNTNLcnRiWlpvNTRTZUtmM2x5b3ZxRDNEdUk2Mk0vVWJ0SHp3NzNGbnZ5RWRHU1BtQnlmWkJnVkVzeGhYNlJCL3VrVFFUc3RIVWM4ZzJ4NEpRckkyMW1mRWtRRmlrVGEyRFQ5d0dWRWxzbDZXL3FiOUtJdXlWVXVTMGxGZ2JYYWl4Ylk1ZEs0b0xuekl1Q2d0eWl0KzNqSEZwb2owQUk1U1FaSVRJeGxYTXlMRHN1UUt4K1RVOGNSRnZhWEl1TXRLZVpzbUZvcDJIK0h2NzNmS3EvWW1DNDhuV1hVVnRxb2dHSnd0TGhmZzg1SmNuUFRBTGNQcklEWWRSem1YdjYxM0JEMGhObU1UTXB0Y1NBSEJDOTlheTR0Y0NnRzJRVTM0WjVYWjhDdXJWSnQzZi9ldHM5cURMWDhnUUYiLCJtYWMiOiI3ZGUxZjRlMDM4N2YzZTA5NTIyMGYyZWMwZjMzY2Y0YzgwMzE0ZjljYTc0MjMzYjY3YzNhMDNhNDFhNDFmMzg0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik1OMkFkdnU3a1F6MHhhb083WVdaNEE9PSIsInZhbHVlIjoiYzVrbWRGSCtBZkFLcFRMc3BQZXkrbmo1b0lUZ2xUMS8vb3V3N1FtNklvQTcyYUVYQnZodDErM2FrT1pFVHhIeG8wWHBLV000OGt3ck8zVDdpRHY5UWNQaFB1Q01HMER0cEVua2MvcHhNMU1SNWtmS29Mb24yUWxVdzV3ZTVUbWtvMXRzRmk0a3FXYlE0dm9OWFpGMFpqeHMwaFBFMlIxN3NFcVZTQUxmQUhORHQ4bWhORnFjSGxSamRPRXZCdEFuV1RadmRJMkE4RWZKaC9CUTdsbG1HMnVMd3YrRzZGRHhxWlJqbk9hQysyTVpZYTIxb3NkRWQ4T2lYYXdzajNyQ0x1RUVBU3NaWldOM2FVQVNhSTFmRklKRXh6eFhaSWpNZ1o3Tnk3dEZRVUlmYlh5T01nQ0pYaThBbzR2dE5jY0p1dGVsSVFGdmxRYXp4T1U2bFh1VW4xOWE3RkxzZmVORkV6RnpEakUyUXVKWkg4QjdBa3lHUW5lOExzZEs3Z1lYcUVFM3A5OEdBNjl2SkNYZ0IxemJwaFYxdWFBeXh5aHA1WUdsOUVlMXRqY2hKY3BWTEsraWg5Ym1LU0NVWHpheE96dC9HMGVIQVdTWWNGTUJGcDlrU2huS3NzbmZ5RkF4WW9IMlpENzN3aXVkcldpZllIZW9mTnBPdFE5UEFqMEQiLCJtYWMiOiJlYzllMTJiZjAzYjE1M2YwZDc2ZTA5OTZhYTliZWU1NDNhMzc2MmU1YzI2OWY0ZGVhY2FmNDJhYTY4NjIxMTI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-215224053\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-452569285 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:31:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndJWk9Wd1RIcFQvc0ZXdHZKZjN2M3c9PSIsInZhbHVlIjoiYWd1SW14Y0J3amVobk1lOENCNXk4clBCZnFOZzYwYVYwaitzQ0dRcERyTjY2WUN4YjloU3NCSFRhM3VEM1VIZ054Y3F6MjA4TmZhNDBKYnU2RnhkRkdvenNGQy9PRDQ4aWpvN0F6Sm4zZ2Z3NUo2R1cvZ1ZKSVdLa3VUUUFPRHZnQ3RtOGMxQStTTGZPSkZ5ZWdac1lvWE4yQjdZZm9ONmNWaURYNFl2MDB4Q0FNUVFPTFNYaEMyMndoUnNxYVM3ejQyZWlYV3Y4ZzFWYVlHM3ZDWkQyY3N5cUxKSVQxOWhyR0htRVFvRnBIbSt4YmxKTkxOamkvelJQUVQydk8xTy8wNVNhdmwyY0gwN0VQMkI0QWFLc2t5dDFDRGRiZlIrSHFNTGxNU1JsQ2hlTGlBWFQ4czI4NFkrVXNUZTNFYTIvVzZuMWgyM2lWclFpL1FiTlFqZWZ4NUJoWTlzYzJjNjlGZk1tYkpLK1pqVjdtcTVBQ2FDWkFuMnZ5dGN4Ymx0QWxNSTJ0c2JhNGFOKzJwRlpPYnh4ZVhJWE9LMlBaVVM0eGdpQW1rdFM0WUZoellRYTh4ZmhpbWhVMzhLcmFnczZaQkZGYnVUVXpaZE1NeDlGSGFVdVE5eVhNbU9KaHg5STVjK3poTjVjdlVGekhlWDE1NERkeWptNXR1SWNqNVEiLCJtYWMiOiI4MjkwNGM3ZTk2YTk0NjNjNjIxMTRlYTc2M2U3ZWJjM2UxYmIyYjRjODA0ZGMzMGM0ZTdiNzA3YWQ4M2I3YTE0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImlidUNFelpZdHRqSVZnWWhlVkRnZWc9PSIsInZhbHVlIjoiMmxXNzNGbGVsUkRVaCtMcmVPWTU1TlRzQ0RJV0Y0bDRsR29WU2VFaFl1d3U2Um8zRGIwbk5odHVXYVJCMmJ0SjIzVTJGbDBSSSsweitpbk1hSUh6bWdlOGl6V3JHZ3RXaGNWR3BRRXRCS2Z1UG84M1pPYk5LamJNQUxEb05hL1Ivdkk2b1BEOC8zVi8wUUx6MzFTN0ZJcUZzUm1rZzdGNWZ2UG1ycUNvd1Z4WnRPM3RpNGxVcXJRK2dqclN2K2QySW1WTVI4R0dzQUkwc2xkakZJMDhPRGJ3eGZCSlRvaUp6RVhtUXE0TEhLM0o3QzhIbDJJa0R5ZmpXNUR6bnkrbkVmSzA5d1JZK0hCdXlWUDVTN3pvVWM5WkdvcGhRSnBRQmFDK21MUWV3NUxCekdjSzZrZ1kyVFNMU3RUSzRPcm9BNml5cTd5T3p1SWhNSSsxWlNOZzR1cXh0bVd6YlVxenNhTHdhSFdoVlQ4RFd6SmRiK2JsYW5QbE9nZzZ4RU1CZ08wMWZCK3p1SXBUaStSbnRYbVpmNDBSbkxZUkF1MXRnQkliZ05iZnZqTzRieXpTQ0xHQTNDME5mbUQvTXowVXRjSkxWZHZmQUZTODRmcFBzUmJ4cG15K1NVU1RSY1Vra085Tlo2blU2bWxkb3BnMHhERXhNTndWSnNQVjhvYmIiLCJtYWMiOiIwZWFiNzJjMzA5N2FiZTVmMDk1ZmZjODJiOTNkMmM2NWM2ODllZDFhYWY0NzA0ZmJkZThiMGNjNzEzY2YzM2Q0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndJWk9Wd1RIcFQvc0ZXdHZKZjN2M3c9PSIsInZhbHVlIjoiYWd1SW14Y0J3amVobk1lOENCNXk4clBCZnFOZzYwYVYwaitzQ0dRcERyTjY2WUN4YjloU3NCSFRhM3VEM1VIZ054Y3F6MjA4TmZhNDBKYnU2RnhkRkdvenNGQy9PRDQ4aWpvN0F6Sm4zZ2Z3NUo2R1cvZ1ZKSVdLa3VUUUFPRHZnQ3RtOGMxQStTTGZPSkZ5ZWdac1lvWE4yQjdZZm9ONmNWaURYNFl2MDB4Q0FNUVFPTFNYaEMyMndoUnNxYVM3ejQyZWlYV3Y4ZzFWYVlHM3ZDWkQyY3N5cUxKSVQxOWhyR0htRVFvRnBIbSt4YmxKTkxOamkvelJQUVQydk8xTy8wNVNhdmwyY0gwN0VQMkI0QWFLc2t5dDFDRGRiZlIrSHFNTGxNU1JsQ2hlTGlBWFQ4czI4NFkrVXNUZTNFYTIvVzZuMWgyM2lWclFpL1FiTlFqZWZ4NUJoWTlzYzJjNjlGZk1tYkpLK1pqVjdtcTVBQ2FDWkFuMnZ5dGN4Ymx0QWxNSTJ0c2JhNGFOKzJwRlpPYnh4ZVhJWE9LMlBaVVM0eGdpQW1rdFM0WUZoellRYTh4ZmhpbWhVMzhLcmFnczZaQkZGYnVUVXpaZE1NeDlGSGFVdVE5eVhNbU9KaHg5STVjK3poTjVjdlVGekhlWDE1NERkeWptNXR1SWNqNVEiLCJtYWMiOiI4MjkwNGM3ZTk2YTk0NjNjNjIxMTRlYTc2M2U3ZWJjM2UxYmIyYjRjODA0ZGMzMGM0ZTdiNzA3YWQ4M2I3YTE0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImlidUNFelpZdHRqSVZnWWhlVkRnZWc9PSIsInZhbHVlIjoiMmxXNzNGbGVsUkRVaCtMcmVPWTU1TlRzQ0RJV0Y0bDRsR29WU2VFaFl1d3U2Um8zRGIwbk5odHVXYVJCMmJ0SjIzVTJGbDBSSSsweitpbk1hSUh6bWdlOGl6V3JHZ3RXaGNWR3BRRXRCS2Z1UG84M1pPYk5LamJNQUxEb05hL1Ivdkk2b1BEOC8zVi8wUUx6MzFTN0ZJcUZzUm1rZzdGNWZ2UG1ycUNvd1Z4WnRPM3RpNGxVcXJRK2dqclN2K2QySW1WTVI4R0dzQUkwc2xkakZJMDhPRGJ3eGZCSlRvaUp6RVhtUXE0TEhLM0o3QzhIbDJJa0R5ZmpXNUR6bnkrbkVmSzA5d1JZK0hCdXlWUDVTN3pvVWM5WkdvcGhRSnBRQmFDK21MUWV3NUxCekdjSzZrZ1kyVFNMU3RUSzRPcm9BNml5cTd5T3p1SWhNSSsxWlNOZzR1cXh0bVd6YlVxenNhTHdhSFdoVlQ4RFd6SmRiK2JsYW5QbE9nZzZ4RU1CZ08wMWZCK3p1SXBUaStSbnRYbVpmNDBSbkxZUkF1MXRnQkliZ05iZnZqTzRieXpTQ0xHQTNDME5mbUQvTXowVXRjSkxWZHZmQUZTODRmcFBzUmJ4cG15K1NVU1RSY1Vra085Tlo2blU2bWxkb3BnMHhERXhNTndWSnNQVjhvYmIiLCJtYWMiOiIwZWFiNzJjMzA5N2FiZTVmMDk1ZmZjODJiOTNkMmM2NWM2ODllZDFhYWY0NzA0ZmJkZThiMGNjNzEzY2YzM2Q0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452569285\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2101246558 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/system-admin/companies/79/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2101246558\", {\"maxDepth\":0})</script>\n"}}