{"__meta": {"id": "Xd742a68309f1736bbf9121431eca8375", "datetime": "2025-07-31 05:17:23", "utime": **********.779206, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939042.801478, "end": **********.779252, "duration": 0.9777741432189941, "duration_str": "978ms", "measures": [{"label": "Booting", "start": 1753939042.801478, "relative_start": 0, "end": **********.710212, "relative_end": **********.710212, "duration": 0.9087340831756592, "duration_str": "909ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.71023, "relative_start": 0.****************, "end": **********.779255, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "69.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jVaj5x8LAAm4hxhJrQKQv7TUhZ7MtZ5otDWaHH1Y", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-815570783 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-815570783\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1603835693 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1603835693\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-932943827 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-932943827\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-16182727 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16182727\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1474589722 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1474589722\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-103866186 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:17:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBPd1ZobmRaZTZjK2RoY2s4S3Q0anc9PSIsInZhbHVlIjoiZXNqMFVRaGhkMmY4cjFFMEczTHVvYVh2NkZqN3pnRm04WDdSMEVCc0VxQ1Y1VkVOanpNamczWTIrQ2s2Y3VCVTgvbFVodDVTak1sY0JFWWU0eGhpRHVSRFRlWm9LTWtTMnpTa0dvVkkrblNXMXpTdFRFN1V2dUJGdENOaEZERW9Kb1NWbHZrV05EWHVoUFdJZUpmNjViZ2o3TWl5ZXIyZytCR0F2VENTQlZnYWtucEJmQlVTQytjRWVHRXNpK1BpNUZzN2RZVFNFdGU5c3ZyZElDN3RFOXQ0ZW5QaU9oNFhab29SRGNuSEFBVG1rdXhQNzhWMUt0YkRkcjJreWJmOThMYy9nYldXMUVDSWt4Z09RTkRndXFwTnRyUlpsVDN0UHNNK01EVGR2SzdJcGx6V01MM1VRMHFOYkxjMGFIWXI1bFlYSWxVZ0FySUFaTjh2RkRjREhncXBFeHVoN0x2VWQ0aXZTOWlqY212L0s3WE9xcEllRXJhSHNoRUVTamhkaUFjUk1BanFxR3kxVVJFVThCUEs3RjlDdWd3SEI5cnJQZzJJTlExN0h4Z3p1K3pUKzhJcytFckgvVDE5Qno5SEoyMEx0WWgxbUx1c0VpNDZYd2lZZEFuNDJMYUt5M2lJTlZCamFhc0NIa1FDYTZmSmE4d1ZpM3hVWWVDanBIM1IiLCJtYWMiOiI5YTQ5MzcyYTZjNzcyYzlhOGY1MWI4YmI4ZGEwYzVkZmNkZTBmNmM3YzUwYjVkYWYxYjAyNjNlMTk4ZGFmZWZjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:17:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlVBTUdaTkJaN2FicHo4ZzVBNmhZcmc9PSIsInZhbHVlIjoibUY3VDNoKzkrRWpMVWtXVVUrTzB6QXhJaXlwMjRJT1FzS3pkajZnK3Z4dWtPL1J6b2orb1FXS1poS0x6NzRMTm8rZmNNS0x5YVU2QjFydEZVaVh0MU5rUTQ1MFdLTktSVXFLa21laDJHTllSbEdRY1dhOVFIZHZENGtPNENiZVcwWnZ5R0J6cFRUMS9qdFBNc1Y0ZExMMy9xdkdYV20zbEhza0pNQVE3dENsMnUvcWFPZG9BbTI2Z2xQekxqdTdXaG9VdElQVVo0TGJtWUovZmo4Y3RqVlVpZUR0ajdqSzVnMlFuOEhyS1FWMW10blVtdVR6eGhETzlxQ3NBRmlrdGhPaHp2SCtHcUl2TVRsdkhyRzhBc0poMTlEZ0VDVTgzTDkvMVEzazdXazdmenhLOU4wLzFpb1FlVTNobk5kUVFuOGhVSzFuN0ZMclRSQkFTVVhZbVlUYzh3aGtmMENlQkpGUDF6T1p2RE9SMGFOZS9vczcwb1oza1lENUhVNmMzS3JHUjk4QXBZbHcyeEFnSHZMSzZ1NTFPS2hBa2JGWmFvQnc5YjBkd1d6dyt2L3pWaG80UjlpV3g2SFQwb1phTU9abVZjWUVramhudmZGaElEUXZTNFFEZ0dkdVg3bnBscjVQWEtoRWRETkNCbDF5dVl6d3kxcnQzcTlaSG1nOWEiLCJtYWMiOiI5YTdhNzZiOWYyYmM4MmQ5YWQ2MzRhZDU1ZjVhYWM5YzRhNjgzODI1ZDg4NTViN2Y4MDViNmExY2VjZjNiMWYwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:17:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBPd1ZobmRaZTZjK2RoY2s4S3Q0anc9PSIsInZhbHVlIjoiZXNqMFVRaGhkMmY4cjFFMEczTHVvYVh2NkZqN3pnRm04WDdSMEVCc0VxQ1Y1VkVOanpNamczWTIrQ2s2Y3VCVTgvbFVodDVTak1sY0JFWWU0eGhpRHVSRFRlWm9LTWtTMnpTa0dvVkkrblNXMXpTdFRFN1V2dUJGdENOaEZERW9Kb1NWbHZrV05EWHVoUFdJZUpmNjViZ2o3TWl5ZXIyZytCR0F2VENTQlZnYWtucEJmQlVTQytjRWVHRXNpK1BpNUZzN2RZVFNFdGU5c3ZyZElDN3RFOXQ0ZW5QaU9oNFhab29SRGNuSEFBVG1rdXhQNzhWMUt0YkRkcjJreWJmOThMYy9nYldXMUVDSWt4Z09RTkRndXFwTnRyUlpsVDN0UHNNK01EVGR2SzdJcGx6V01MM1VRMHFOYkxjMGFIWXI1bFlYSWxVZ0FySUFaTjh2RkRjREhncXBFeHVoN0x2VWQ0aXZTOWlqY212L0s3WE9xcEllRXJhSHNoRUVTamhkaUFjUk1BanFxR3kxVVJFVThCUEs3RjlDdWd3SEI5cnJQZzJJTlExN0h4Z3p1K3pUKzhJcytFckgvVDE5Qno5SEoyMEx0WWgxbUx1c0VpNDZYd2lZZEFuNDJMYUt5M2lJTlZCamFhc0NIa1FDYTZmSmE4d1ZpM3hVWWVDanBIM1IiLCJtYWMiOiI5YTQ5MzcyYTZjNzcyYzlhOGY1MWI4YmI4ZGEwYzVkZmNkZTBmNmM3YzUwYjVkYWYxYjAyNjNlMTk4ZGFmZWZjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:17:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlVBTUdaTkJaN2FicHo4ZzVBNmhZcmc9PSIsInZhbHVlIjoibUY3VDNoKzkrRWpMVWtXVVUrTzB6QXhJaXlwMjRJT1FzS3pkajZnK3Z4dWtPL1J6b2orb1FXS1poS0x6NzRMTm8rZmNNS0x5YVU2QjFydEZVaVh0MU5rUTQ1MFdLTktSVXFLa21laDJHTllSbEdRY1dhOVFIZHZENGtPNENiZVcwWnZ5R0J6cFRUMS9qdFBNc1Y0ZExMMy9xdkdYV20zbEhza0pNQVE3dENsMnUvcWFPZG9BbTI2Z2xQekxqdTdXaG9VdElQVVo0TGJtWUovZmo4Y3RqVlVpZUR0ajdqSzVnMlFuOEhyS1FWMW10blVtdVR6eGhETzlxQ3NBRmlrdGhPaHp2SCtHcUl2TVRsdkhyRzhBc0poMTlEZ0VDVTgzTDkvMVEzazdXazdmenhLOU4wLzFpb1FlVTNobk5kUVFuOGhVSzFuN0ZMclRSQkFTVVhZbVlUYzh3aGtmMENlQkpGUDF6T1p2RE9SMGFOZS9vczcwb1oza1lENUhVNmMzS3JHUjk4QXBZbHcyeEFnSHZMSzZ1NTFPS2hBa2JGWmFvQnc5YjBkd1d6dyt2L3pWaG80UjlpV3g2SFQwb1phTU9abVZjWUVramhudmZGaElEUXZTNFFEZ0dkdVg3bnBscjVQWEtoRWRETkNCbDF5dVl6d3kxcnQzcTlaSG1nOWEiLCJtYWMiOiI5YTdhNzZiOWYyYmM4MmQ5YWQ2MzRhZDU1ZjVhYWM5YzRhNjgzODI1ZDg4NTViN2Y4MDViNmExY2VjZjNiMWYwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:17:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-103866186\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1525903500 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jVaj5x8LAAm4hxhJrQKQv7TUhZ7MtZ5otDWaHH1Y</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1525903500\", {\"maxDepth\":0})</script>\n"}}