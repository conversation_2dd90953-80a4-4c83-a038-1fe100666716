{"__meta": {"id": "X0f0cf736453359a65c30e3f1db5ff939", "datetime": "2025-07-31 06:16:01", "utime": **********.094358, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:16:01] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.084232, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753942559.088531, "end": **********.0944, "duration": 2.005868911743164, "duration_str": "2.01s", "measures": [{"label": "Booting", "start": 1753942559.088531, "relative_start": 0, "end": **********.837197, "relative_end": **********.837197, "duration": 1.7486660480499268, "duration_str": "1.75s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.837224, "relative_start": 1.7486929893493652, "end": **********.094404, "relative_end": 4.0531158447265625e-06, "duration": 0.25717997550964355, "duration_str": "257ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50612688, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.054545, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.04961, "accumulated_duration_str": "49.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.935657, "duration": 0.02222, "duration_str": "22.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 44.789}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.9906628, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 44.789, "width_percent": 2.6}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0009718, "duration": 0.02466, "duration_str": "24.66ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 47.39, "width_percent": 49.708}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.032094, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 97.097, "width_percent": 2.903}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-971442859 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-971442859\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1084746783 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1084746783\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2092352868 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2092352868\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im9xT1BBOElDL0pZcTJvZ2ZBOWhoaFE9PSIsInZhbHVlIjoiS0JtS25XNFF3UlQyVFNoVUVrd2xjbDd1cmU5ajVpaVY1THM4ekJ5UVU2eGlnQkp3OGdvSVNhaGhjbnFGZG9qVFlSVklGTTB1b1BnelVZYkRtV2ZsTm1sRDFGOU45QTh2OUtIcWRmeUNYb0Y5aSsxS3ROT3diMkpITHByT3N0UUFBbUtZZGlTTURKMTFaWktrRU9zN2pBUFM1UVJFeS9DRDYxUUpFaENZMGg3TFZFK3pQbFNwUk9MTnpZNlFjMm1Yc0ppZnIremZpR3Q1Vkk3VWJUMTgrQkltOVh1SGhUZGx3cVRlWHVQbkJoNGRlVmpKd1U4Q0N6SFNrR0NrRmtqOTdCaG9ybW9jQXBhN1lPSkg5VXJMY2ErcXE3S283RktZdFJPTUluZ3ZCTW85Y0hvQ3ZaZFVqZ0ducDJYQ1hDc2crVE9ZZzhvRjdpWXNuS2xmck45UUNDOFBaTzM1ZjlTTU92eHNmbzZjM2hLOU8zTTIvRURmVktpMkVjeVBKSnZEQ0VLdnZSRVNnRmJXQmRubzRJL3A3RTY0dDU4ZHRYajNwV0MrSW9sYWhUcHR1UnBrM1dXNTFnL3F5dVNCTThCNzBZeGhDeFRkV3R2Rm14WWxialNkMWU2YllSR1o3dWJDVi8wUHpHamdqL1ZoMm05TlNJV3RqMW5Tajc2R1RWSGgiLCJtYWMiOiIzMDRlNTVhMDZhMWEzZGQ2Y2M2MGJkYzRhMjQwMzFkNmM2ODY4ZDk1NjBiNGM3MjQ4YzEwMmM2ZDMyNWRmODNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImM3eElSMVZVaWxrTUFZd016ajFRamc9PSIsInZhbHVlIjoiV1JtanY1cGRqNkNLaWVRb3lWRnl5a3ZDdmRmK0VLM0dwZHVuNC90RjFzZUllV2NiN21HVk1TeXppWnl5R0NTSHc3SjhFeEE4VklkTmhRWEt4N2dCd0VsUVM2RXNOZWdDNnpEc2J5WEhpM1hSbXB2QVhyVk83YlJYeVpQZ2lxNlo0VnJneWlORG9TWE9VRHI5Rlc1Rzc3QmZ0VkRyTUpZK2pmUXRDSEVCTEtZaC9jeHNLN0NDQlViTDV1ZkluNVBmQjB5WnJkbVdhUzVEaDBzS09ZSHYrQkMza3lWYkNkUWlFYmVXTmg1TVNyRGNpMWhKSXJoZFUzVlhTRjE5ZFcrMGdSemNZc2h4akZvVDl2Qk8wNi9sYjZJZjlvL3VVbmdld3IvYU1iVzVRNmwvdGQvcDVmNHNITHUrN1ZCbHFVL3QxRC94WVpNK2dkd3BQS291WEhaL3JnVFh4Tis5dkVzOEJMZVZRNjNJNFBmUFdlUkE0RUFicGEwVjFpRnpTTTMrQWxSR00xYU5EL0FWUnBrYlRiQ3Nqb09NaFlLVDQ1NWpUYTZtRHJKalJXT1Q5clBKbUtPb1hGWHA0RHZRaWZZeFpGL3pYS3FlekJBTVN5NlBoZnlqTERBNkhobXNtWnpmaWU1QUlUaGgwb0VsakxuY2dGQmxmWTQ5Y1lYakp0dVEiLCJtYWMiOiI4ZmU2MTk2OTRlNDIwNGJkYmVjY2UwNjg4MjFhOTYzZmZjNTM3ZjMyNWZmMmY3YjNkNzYyZWZiOGM1MzJjZGQ3IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Ik9kc2JOZ0NrMEVtdUFKMDYwb0dzQVE9PSIsInZhbHVlIjoiODlHZnhSRy9BU05rM1duUzZWbk9Dc3ZXT0ZyY3FjVzZ3SHlkSXROc1lUaUhmRGpSdEFlbVR3di9GL3pBTmxPbGoyUnFHYWtTbEhiK25LZ1UzbFM1VlJ0eXN3Yjk1ZURLcDluU2wzUGZhUVovVkpnUHR5cjlsa1V3YmlYUjB5ZHRTNmh4cTRINkYzeG9ZYWJ5NVhnVmo4UHg4Ri95Ujg3V0pMMFF6bGhENkpRZUxkTlBFVlo0T1pocW9najRMVEZYWUcvZW5xbmM2ZDI3TzBGT0RRODFqUkxQNGlyeW9YZCtsa0ZxMC80d0pSd1pUcFF5d3dKUGl5TmxSajJPeHBjbWlpa05FMDR2ZnE3V0NlemJOclk2TWJRWTJKdEM5MFoyVFlMZ2ZnY29aSXhpWFlrc3UrRnlpbWxEdXpobEZkQlMxaHh3Vlk0b3FEMVQwbEdsYStnMFlHall3ajFnaUNONm1UZG5LT2Q2K1FjYWpQNk0wWmZmN1ZqSXJ4UkVacjRjT0ZPSjd3dGJGblZGNVJaOFRLTG5hd3p4V25Hekwvc3pvMmZ5N3grbU9GS3VybGp5bG5aeHE0M1ZpbWxCRngyRXN5TEp1WjNpcUdZNnk4MDMvckVEK09TTHZtVGtPYzR2ajBqR1lkeE8zNm5jV2s5YlZsUDQxODRoNytIM3FZK2ciLCJtYWMiOiJlMWZiMDYxNzdiOGNmNjdlYzNlNzYyZjNjOWUzNmY2ZTNhMjhiMmRmYzE1MTM4ZmVmNmMwZjJhODMyMWRmZDJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-802411572 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5LJND5bUclZnLzzClCMEaxKzZ5Yt8AAmGjprz0I</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sUbnPperdA1I3PaRxBpMGV4YsR5AVGq0ERh2LBdJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-802411572\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:16:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxuMXB1dEc4R0tOQWkrS280QWg5bUE9PSIsInZhbHVlIjoiSm1ENkxXeFYxUnBTWXB6UHZGaHRsb3M2S0R3OHNMU0dNSzlhQW1SdU5VeFV5dHdXL0EyYjhadkhmTHNNR3cvVmp2OWg2RlducFc2NVIwL2Z5NDcveDVrSUpOYSt0cDgyNWNUN0N0QWMycGFSOHZGK1ZnVC9ocTY2RDQxYkxBa2JLeHhDdGYwT1ZWd3dnOWgza2dKaWRkQkFKWDA3YzdLaHczSm0vT21vOEswZkNZc0dyRkVTQjkwTVVQL2JWeTgxV1FPNS9BbW9lZ0dmUVNjaDhrR2YxcUs3QkNNZUs0MS83UXJZd0orRFpJb0NDK2FUZW1ubUErQTI0MjNVK1RzTUVheTRRTHN0OEx1QXlsTEpaV2wrbDhGQ0Vlc2J5TS82eXhqdGpUR1lxZG1tVi83bThxZ1ZXY0pPaFh2RHVUdXFubWNLckNqa3RhNW54MFprdEdPaHBJYTltSDFHWlhheHQ2a0R2Z05oSFJEaGZ1bGFoWlNkdDR0aVI0Zy9TcTFTOEpPZENBWVZFaFkvV3Axa2hRUlhtRDBWTG8xT0hkTE9hU0xUZDVwOGsvMUdBVm01SEsxWUljL3drTEdqYnFOYUhQUHNrUkg3M0pueHRzd3pnQVJzUHFPbDZnLzQ0a0JOSlJDeE41YVkvY21qNW8vUjJWTFp1OHUrZEN2OThSQVQiLCJtYWMiOiI2Mzg3NzljMDQ4NDgxMmZmMmExM2IzYzdjYTRjMTNlZjMwYWZmOTUyYjE3NDJlZjk4MjY2ZWE3OTViOWNlYzY4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:16:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6Ikl2SFU1TTBOUWEwc3R6V3dwNjROb3c9PSIsInZhbHVlIjoiVGdnVkRRNHRIL3Jmdnd6SzJOV1FXVEtVenhvaUp4UFlzdjlNQzhvL2pMUUFyRWtrcTByZmR2Slhrcmw5d0ZGaEJWM0RaUUdSM1gxQUNpWjRFdFdwTFZVbWMyY1pIbWsvTm5iRHRLM242aHd0aTVza3NVMDFsN2lMSVZGSkNCNEJPbThBTkdHSXUzclpLa3VnOTkrWURLZnE2eDFDdjJmOUhXSVdEbi9sWUJUdTl6SGRLekNjZkdTemZoZkk0TS9yTlUrUVdsZ3hQWVNSNnEvd1hsaG1ITG9Ub0Y4emhuUEliQXRPb1lmU29aMVdFelRQT1k1b1JwSGFCYURIMXhkUHVxWEFDTFR2VElpc0VYRFJlTmRicWwyWjlvTldTRW14dExwSE9RS01Zd0RmS3ArK3ZGUkpsZDIwaXU0RVE2RzI1Q3lzSHhYaThpZnlmT3FrYWtFRDRCRER6V213TWZmTXJpWVR1L0FXcVlmQTZrZndoRTZrQmIrYnU2Yk4xaWV3MVdlM2JZSjhMbVZ1TEJobjlOcEl0ZStkblo1Tkk3RDhRQ0xaK2VSTTFDMzhESjdhQTFXV2ZHSS81MzRoclZPaWgxWnhzQ3VOcFN6SW1CZnJia3hiS29UeGhiQzRKRVFRakF4eVZNWmF2b253TXI2TGFQOU5EcFdkVUhRWFU5YWoiLCJtYWMiOiIxMDFkMGIwMWZlNjZlMDY5YmM2NzExYWJlZjhjNDJlYWUyNTNjZGE2NjMzZjNkMDE4Yzg0NjI0Njk3ZmM5YmNmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:16:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxuMXB1dEc4R0tOQWkrS280QWg5bUE9PSIsInZhbHVlIjoiSm1ENkxXeFYxUnBTWXB6UHZGaHRsb3M2S0R3OHNMU0dNSzlhQW1SdU5VeFV5dHdXL0EyYjhadkhmTHNNR3cvVmp2OWg2RlducFc2NVIwL2Z5NDcveDVrSUpOYSt0cDgyNWNUN0N0QWMycGFSOHZGK1ZnVC9ocTY2RDQxYkxBa2JLeHhDdGYwT1ZWd3dnOWgza2dKaWRkQkFKWDA3YzdLaHczSm0vT21vOEswZkNZc0dyRkVTQjkwTVVQL2JWeTgxV1FPNS9BbW9lZ0dmUVNjaDhrR2YxcUs3QkNNZUs0MS83UXJZd0orRFpJb0NDK2FUZW1ubUErQTI0MjNVK1RzTUVheTRRTHN0OEx1QXlsTEpaV2wrbDhGQ0Vlc2J5TS82eXhqdGpUR1lxZG1tVi83bThxZ1ZXY0pPaFh2RHVUdXFubWNLckNqa3RhNW54MFprdEdPaHBJYTltSDFHWlhheHQ2a0R2Z05oSFJEaGZ1bGFoWlNkdDR0aVI0Zy9TcTFTOEpPZENBWVZFaFkvV3Axa2hRUlhtRDBWTG8xT0hkTE9hU0xUZDVwOGsvMUdBVm01SEsxWUljL3drTEdqYnFOYUhQUHNrUkg3M0pueHRzd3pnQVJzUHFPbDZnLzQ0a0JOSlJDeE41YVkvY21qNW8vUjJWTFp1OHUrZEN2OThSQVQiLCJtYWMiOiI2Mzg3NzljMDQ4NDgxMmZmMmExM2IzYzdjYTRjMTNlZjMwYWZmOTUyYjE3NDJlZjk4MjY2ZWE3OTViOWNlYzY4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:16:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6Ikl2SFU1TTBOUWEwc3R6V3dwNjROb3c9PSIsInZhbHVlIjoiVGdnVkRRNHRIL3Jmdnd6SzJOV1FXVEtVenhvaUp4UFlzdjlNQzhvL2pMUUFyRWtrcTByZmR2Slhrcmw5d0ZGaEJWM0RaUUdSM1gxQUNpWjRFdFdwTFZVbWMyY1pIbWsvTm5iRHRLM242aHd0aTVza3NVMDFsN2lMSVZGSkNCNEJPbThBTkdHSXUzclpLa3VnOTkrWURLZnE2eDFDdjJmOUhXSVdEbi9sWUJUdTl6SGRLekNjZkdTemZoZkk0TS9yTlUrUVdsZ3hQWVNSNnEvd1hsaG1ITG9Ub0Y4emhuUEliQXRPb1lmU29aMVdFelRQT1k1b1JwSGFCYURIMXhkUHVxWEFDTFR2VElpc0VYRFJlTmRicWwyWjlvTldTRW14dExwSE9RS01Zd0RmS3ArK3ZGUkpsZDIwaXU0RVE2RzI1Q3lzSHhYaThpZnlmT3FrYWtFRDRCRER6V213TWZmTXJpWVR1L0FXcVlmQTZrZndoRTZrQmIrYnU2Yk4xaWV3MVdlM2JZSjhMbVZ1TEJobjlOcEl0ZStkblo1Tkk3RDhRQ0xaK2VSTTFDMzhESjdhQTFXV2ZHSS81MzRoclZPaWgxWnhzQ3VOcFN6SW1CZnJia3hiS29UeGhiQzRKRVFRakF4eVZNWmF2b253TXI2TGFQOU5EcFdkVUhRWFU5YWoiLCJtYWMiOiIxMDFkMGIwMWZlNjZlMDY5YmM2NzExYWJlZjhjNDJlYWUyNTNjZGE2NjMzZjNkMDE4Yzg0NjI0Njk3ZmM5YmNmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:16:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-239268610 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-239268610\", {\"maxDepth\":0})</script>\n"}}