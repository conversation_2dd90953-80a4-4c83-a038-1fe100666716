{"__meta": {"id": "Xcc0f5005754ba94989203fcfe5c0f15a", "datetime": "2025-07-31 06:04:44", "utime": **********.076641, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753941882.028558, "end": **********.076711, "duration": 2.0481529235839844, "duration_str": "2.05s", "measures": [{"label": "Booting", "start": 1753941882.028558, "relative_start": 0, "end": 1753941883.933606, "relative_end": 1753941883.933606, "duration": 1.90504789352417, "duration_str": "1.91s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753941883.933631, "relative_start": 1.****************, "end": **********.076717, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "143ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "52ujf3KkNnldiLQAx8uZKj4gs8N42wQDeBSnvh4G", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2120620813 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2120620813\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2010598017 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2010598017\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-222110151 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-222110151\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-377307184 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-377307184\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1696719898 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1696719898\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1192361948 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:04:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRmNGJNSGVWeHQ5ckRkVlRhRUJPVUE9PSIsInZhbHVlIjoiQW9GU3MvUUh4M2dOZlJ2OVp1SjV4TG1aTGtJUWJ3bzR3R0hyNnF2a2UwWUtYU0lUcFA2Zkd2b2Q3M2tPbVhaMmg3b0liRnJqcHR1U2lhUWVoVFBpdHA4RE9NUlBvN0xJaHdSZFkwSTFNWFJVaEdDcXMwZ1BTUjFmaXJyZHNUVUxia3Rzc2pXRGNOcWcrc2NhaVUwSFJVRUNnZ1JJTDk5VmxHTlovU1FmU29xeEgzU21UZkdxZkUxWXplaXdjTW5vT0E4MDBJQ3VWdUNaNTVFTnJxdWZaS3J5RkRPUmFPVGg0YVpyTU1SbldzYmQrUnRFUFcwaHJncStsSmM5U1ZqQ08rVjVyd3NxVkF6RVF5dVNRWU5FTWR1bFY4Q0JpQ1hpNDNveTlFSldINXpvZzRHZU42WXRoT3ZZN1hyV0lyM3FIRkx4V3FxTXI3Mk91RVR4cVh1cGFZN3hkRlhJNmM0VWhvSWlBWTBNdWlCTHJOZ00veWl6MHBnSE5QRUIvbElQdEI1c05ob3JuYS9hN01KT1pJUEVLMzJKbVZqUFNBQ3BZVmk2TG1IM3dQOGltNkdmdDNodmc0cFZjR3JCTVFzeUhOeEJPQVZlMmd6c0tWanp1RXkvakhYeXpQb3gwUm4rbE9JUGo4YUQ3bC92V1JqSk5oM3hLSVZlTDFjQVhINU0iLCJtYWMiOiI5YTk3MDlhMzkzOTRjMTI4YTNmYWE1Y2MxNDBmMzk3YWRmZDFlZTU3NjA4YzQ0NTUwMDRkMDY0ZTM4N2M3MDM2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:04:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjVkUVE1azNUbE5QZllYMWJxeHRURUE9PSIsInZhbHVlIjoiY2c0SHduNGxNVmNva1RlQ1lmVStxMEdFWXlnSzBidlpNV094ZWxxVS9KRHpsQkNHd0huMEtBY2JPd2dWMWZWU0U2MUNZQ01sY0dXSC8xUUNmOFRnaWEzd3ZkNEpFRmUwd2daMnFVWk0zLzhRN0RkdmlUcTJGR2dIN2JwQXNmaGNxN3RaWE0wN2lFRS9ZRGNBTWIxZzVDRXdadXV1MGdqVUVEa05VYjZENzkva0ozQ2FSNEZqdFR1MmtaYlFTQm5sVm9aWk94TzdudmkxYkxOSjlDb2ZPU04vT1NxY0tBWnFLNDlMY3JLby9odGhiTWQ0cEZzUnBjbmtscUVzR0Q2bWJOR3Z5dDgzUk5LK3lwQUIrenoyK0FsYUdLR1daUUN1NTQzS0tzM3VOZENaM2wzL0VOMTJNRk1jOW5saFRuK25Wb0w1QjhuK2RnSEdPTmdyMnNZbXpJR2xjcUMxc2R2TXRLaHZTZmFOUTcvYW1LT0d0c0NBTk9wR09RSU9XTFgyclpFRytYSFk1eDhlNDkrWE4wUjJ1UEJ1VUhweCs5WFhqRE1Rd3JYd1E2WGVJNUdzS3JBZXRyRjE2VEc1VWd5K1EvUVFob0xRWHduNDlrRlZIOTgweGlYSXp6V3RSTnVhZHBpeTFKU0N6UmZjVk4yanFRc2owUUM3NEpoRmlBMkUiLCJtYWMiOiI4ZjMxMWRhMTZlYmI4MzJkYjk0MjRlOWEzY2ViNTA4N2JlYTMwYTQxMjM2N2I4MzMwYjVhNzM5NWE0MGNmNjMwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:04:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRmNGJNSGVWeHQ5ckRkVlRhRUJPVUE9PSIsInZhbHVlIjoiQW9GU3MvUUh4M2dOZlJ2OVp1SjV4TG1aTGtJUWJ3bzR3R0hyNnF2a2UwWUtYU0lUcFA2Zkd2b2Q3M2tPbVhaMmg3b0liRnJqcHR1U2lhUWVoVFBpdHA4RE9NUlBvN0xJaHdSZFkwSTFNWFJVaEdDcXMwZ1BTUjFmaXJyZHNUVUxia3Rzc2pXRGNOcWcrc2NhaVUwSFJVRUNnZ1JJTDk5VmxHTlovU1FmU29xeEgzU21UZkdxZkUxWXplaXdjTW5vT0E4MDBJQ3VWdUNaNTVFTnJxdWZaS3J5RkRPUmFPVGg0YVpyTU1SbldzYmQrUnRFUFcwaHJncStsSmM5U1ZqQ08rVjVyd3NxVkF6RVF5dVNRWU5FTWR1bFY4Q0JpQ1hpNDNveTlFSldINXpvZzRHZU42WXRoT3ZZN1hyV0lyM3FIRkx4V3FxTXI3Mk91RVR4cVh1cGFZN3hkRlhJNmM0VWhvSWlBWTBNdWlCTHJOZ00veWl6MHBnSE5QRUIvbElQdEI1c05ob3JuYS9hN01KT1pJUEVLMzJKbVZqUFNBQ3BZVmk2TG1IM3dQOGltNkdmdDNodmc0cFZjR3JCTVFzeUhOeEJPQVZlMmd6c0tWanp1RXkvakhYeXpQb3gwUm4rbE9JUGo4YUQ3bC92V1JqSk5oM3hLSVZlTDFjQVhINU0iLCJtYWMiOiI5YTk3MDlhMzkzOTRjMTI4YTNmYWE1Y2MxNDBmMzk3YWRmZDFlZTU3NjA4YzQ0NTUwMDRkMDY0ZTM4N2M3MDM2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:04:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjVkUVE1azNUbE5QZllYMWJxeHRURUE9PSIsInZhbHVlIjoiY2c0SHduNGxNVmNva1RlQ1lmVStxMEdFWXlnSzBidlpNV094ZWxxVS9KRHpsQkNHd0huMEtBY2JPd2dWMWZWU0U2MUNZQ01sY0dXSC8xUUNmOFRnaWEzd3ZkNEpFRmUwd2daMnFVWk0zLzhRN0RkdmlUcTJGR2dIN2JwQXNmaGNxN3RaWE0wN2lFRS9ZRGNBTWIxZzVDRXdadXV1MGdqVUVEa05VYjZENzkva0ozQ2FSNEZqdFR1MmtaYlFTQm5sVm9aWk94TzdudmkxYkxOSjlDb2ZPU04vT1NxY0tBWnFLNDlMY3JLby9odGhiTWQ0cEZzUnBjbmtscUVzR0Q2bWJOR3Z5dDgzUk5LK3lwQUIrenoyK0FsYUdLR1daUUN1NTQzS0tzM3VOZENaM2wzL0VOMTJNRk1jOW5saFRuK25Wb0w1QjhuK2RnSEdPTmdyMnNZbXpJR2xjcUMxc2R2TXRLaHZTZmFOUTcvYW1LT0d0c0NBTk9wR09RSU9XTFgyclpFRytYSFk1eDhlNDkrWE4wUjJ1UEJ1VUhweCs5WFhqRE1Rd3JYd1E2WGVJNUdzS3JBZXRyRjE2VEc1VWd5K1EvUVFob0xRWHduNDlrRlZIOTgweGlYSXp6V3RSTnVhZHBpeTFKU0N6UmZjVk4yanFRc2owUUM3NEpoRmlBMkUiLCJtYWMiOiI4ZjMxMWRhMTZlYmI4MzJkYjk0MjRlOWEzY2ViNTA4N2JlYTMwYTQxMjM2N2I4MzMwYjVhNzM5NWE0MGNmNjMwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:04:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1192361948\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-55157206 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">52ujf3KkNnldiLQAx8uZKj4gs8N42wQDeBSnvh4G</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-55157206\", {\"maxDepth\":0})</script>\n"}}