{"__meta": {"id": "X3515d520e8437f45d0836e49c3d99b05", "datetime": "2025-07-31 05:10:59", "utime": **********.366756, "method": "GET", "uri": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753938658.374749, "end": **********.366805, "duration": 0.992056131362915, "duration_str": "992ms", "measures": [{"label": "Booting", "start": 1753938658.374749, "relative_start": 0, "end": **********.284659, "relative_end": **********.284659, "duration": 0.9099099636077881, "duration_str": "910ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.284674, "relative_start": 0.****************, "end": **********.366808, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "82.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cy1brQ86uB4JG3Q0FXl5DyOSGIaG3NhHu23Ph5ap", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "status_code": "<pre class=sf-dump id=sf-dump-132001422 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-132001422\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-808841683 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-808841683\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-745509357 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-745509357\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1046261591 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046261591\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-363779947 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-363779947\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-251460787 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:10:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFtWGVXdG1kOGdaMGNQWk1hZGVIUmc9PSIsInZhbHVlIjoieFo2OW1PUzdDd3MzbVZYYXl6ZnBjQTFQZlhPVHFSSXkvKy8wTVpsSldqazlSM3NKMTVQZ015dDBGZmhjWGdFdVkwalg5cU9DTEU3eDBQaldpMjRZUGZ5ZEtpb1RVSzVIZFBOdzFTdGp5SHNmdjJManRrd2F3cjhkWi94OU41V2paaTBHZDFQQjlaSEEwQ1pUZ01mY0FGYzF1SVpOOXYyWFNCY1ZSSmwzazM0REQ2cUVxT1prZmozSnVRRGFJSEphbFh2amthNDNMVTBWTk5JMjdwbGhvVUNiUTM5eEUxR01heklHakdSRVFpdG15K1g2WUZjTkt4VlZveUN2T2VnS0NCRDU2enRRMWJTdmowT3FEMlBiN3JWNzJuN0hDTG9YekdJQ2hWdDVtU2Y2akVkZDBRbTJjdTFFUkNZL29XdEJNMTAySjAvUmpFK0szUXBDL20xcThMTEtzOFBFYmZZdU0yTzJYSXk0SnlyQTNRZGtEOHl3MnhFZUtrbjQ5T3VONkZnN0FteTVUZXhrYjJGd2sremx3WnVOell5Wnl4ZGttVm5NbVBpK3V4YlJVRExPRGhDQUlhK0tLZndhL2hDZTFHZG93bUNubis5NlN2OUtsTm5rUSt6aWJ5amlTSk4xamJFZ01YQlM0dWJGZXdDbzlBMncyTlRuRTljVG1SV2UiLCJtYWMiOiI4ZWE0OTRjNDE0NzgzYjA4Y2I3YmQyMGU4OWJiYTIzMTZiZGI2NTIwODRiMjk1MjZmMTc1YjkzYjVlYmIyMjNhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:10:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImV6Mk1sUndkazA2NDFsb205L3RXT3c9PSIsInZhbHVlIjoiMGxUK090UkNvcEZnaXFMRXZWejVJSG05K2c3REhhTlErN1Juemw5di9xWmpVYkdlZVZLNVZpVWtSclE4MVkrb1ZJb0R4S25aN1VXMUJ4QndCRFNqd3dnclhLSk5seGVzTHFGRVpJYUlRcGMxYTdOQzJ0WjhYSTVXd1o3c0U5UE9kcGlMRGhJQ0JnZFZva0Rnc2g1c0RYdWFQSTRrckN4bGFwU3FwTGNOQ3pPSC9kZ2t6NXcvYzV5d3V5VVNJU1E3WHFsVnMrZkRtWGEvWEZDVEZUQXlIOG9JT3lIWS9uY0JGYkZvbG5ES1lZVFJ4YkZpOEhkL0JmMFphT1ZDSkcyQkxiWGxKc1dsZ2VvWkd2VHVFREV2Ni9SWnZNRnIzWUJMTVlkRSswTVBLSjFOZnpsaUJvM1BkRkV2dVpMd0lmREZxK2VpK2x3bkFCaTRYVEIrQVpScVFCa2RTdTluTFBFblNreU0xdEh2Zk9ORjJpaWVCRUNyYTAzNjV0OU42MzVGcTB0aUxaQVEvV1dHbStuZ2dDZnZvSjE3bXBLRW1xb01IQ2VnNTJiQ1c5QzZ2VnM0dXRzcEticmtuSXp5WG4rWVVsWXVtSFRBei9jT0VSZ3ZNaHFMQXV3dFpBZlE0ak5hQUdBNHVBMmY0THFrdWJldlhUeENNeTUxVnVZQ1lFdXciLCJtYWMiOiI2NmVjZmRkNDM3MjVlM2VkZjIzMTIwZDBkMDU2MmM2ZjQ1NzAyZTkyZGJiN2I5ZDRmNTQ3MjZiZjg0MzBhNTkyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:10:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFtWGVXdG1kOGdaMGNQWk1hZGVIUmc9PSIsInZhbHVlIjoieFo2OW1PUzdDd3MzbVZYYXl6ZnBjQTFQZlhPVHFSSXkvKy8wTVpsSldqazlSM3NKMTVQZ015dDBGZmhjWGdFdVkwalg5cU9DTEU3eDBQaldpMjRZUGZ5ZEtpb1RVSzVIZFBOdzFTdGp5SHNmdjJManRrd2F3cjhkWi94OU41V2paaTBHZDFQQjlaSEEwQ1pUZ01mY0FGYzF1SVpOOXYyWFNCY1ZSSmwzazM0REQ2cUVxT1prZmozSnVRRGFJSEphbFh2amthNDNMVTBWTk5JMjdwbGhvVUNiUTM5eEUxR01heklHakdSRVFpdG15K1g2WUZjTkt4VlZveUN2T2VnS0NCRDU2enRRMWJTdmowT3FEMlBiN3JWNzJuN0hDTG9YekdJQ2hWdDVtU2Y2akVkZDBRbTJjdTFFUkNZL29XdEJNMTAySjAvUmpFK0szUXBDL20xcThMTEtzOFBFYmZZdU0yTzJYSXk0SnlyQTNRZGtEOHl3MnhFZUtrbjQ5T3VONkZnN0FteTVUZXhrYjJGd2sremx3WnVOell5Wnl4ZGttVm5NbVBpK3V4YlJVRExPRGhDQUlhK0tLZndhL2hDZTFHZG93bUNubis5NlN2OUtsTm5rUSt6aWJ5amlTSk4xamJFZ01YQlM0dWJGZXdDbzlBMncyTlRuRTljVG1SV2UiLCJtYWMiOiI4ZWE0OTRjNDE0NzgzYjA4Y2I3YmQyMGU4OWJiYTIzMTZiZGI2NTIwODRiMjk1MjZmMTc1YjkzYjVlYmIyMjNhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:10:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImV6Mk1sUndkazA2NDFsb205L3RXT3c9PSIsInZhbHVlIjoiMGxUK090UkNvcEZnaXFMRXZWejVJSG05K2c3REhhTlErN1Juemw5di9xWmpVYkdlZVZLNVZpVWtSclE4MVkrb1ZJb0R4S25aN1VXMUJ4QndCRFNqd3dnclhLSk5seGVzTHFGRVpJYUlRcGMxYTdOQzJ0WjhYSTVXd1o3c0U5UE9kcGlMRGhJQ0JnZFZva0Rnc2g1c0RYdWFQSTRrckN4bGFwU3FwTGNOQ3pPSC9kZ2t6NXcvYzV5d3V5VVNJU1E3WHFsVnMrZkRtWGEvWEZDVEZUQXlIOG9JT3lIWS9uY0JGYkZvbG5ES1lZVFJ4YkZpOEhkL0JmMFphT1ZDSkcyQkxiWGxKc1dsZ2VvWkd2VHVFREV2Ni9SWnZNRnIzWUJMTVlkRSswTVBLSjFOZnpsaUJvM1BkRkV2dVpMd0lmREZxK2VpK2x3bkFCaTRYVEIrQVpScVFCa2RTdTluTFBFblNreU0xdEh2Zk9ORjJpaWVCRUNyYTAzNjV0OU42MzVGcTB0aUxaQVEvV1dHbStuZ2dDZnZvSjE3bXBLRW1xb01IQ2VnNTJiQ1c5QzZ2VnM0dXRzcEticmtuSXp5WG4rWVVsWXVtSFRBei9jT0VSZ3ZNaHFMQXV3dFpBZlE0ak5hQUdBNHVBMmY0THFrdWJldlhUeENNeTUxVnVZQ1lFdXciLCJtYWMiOiI2NmVjZmRkNDM3MjVlM2VkZjIzMTIwZDBkMDU2MmM2ZjQ1NzAyZTkyZGJiN2I5ZDRmNTQ3MjZiZjg0MzBhNTkyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:10:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-251460787\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-820670290 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cy1brQ86uB4JG3Q0FXl5DyOSGIaG3NhHu23Ph5ap</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-820670290\", {\"maxDepth\":0})</script>\n"}}