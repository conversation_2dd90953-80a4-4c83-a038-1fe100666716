{"__meta": {"id": "Xda48081a51814e7621ae2be5bd4afa23", "datetime": "2025-07-31 07:45:54", "utime": **********.181123, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[07:45:54] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.174415, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753947952.759173, "end": **********.181185, "duration": 1.4220120906829834, "duration_str": "1.42s", "measures": [{"label": "Booting", "start": 1753947952.759173, "relative_start": 0, "end": **********.872693, "relative_end": **********.872693, "duration": 1.1135201454162598, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.872709, "relative_start": 1.1135361194610596, "end": **********.18119, "relative_end": 5.0067901611328125e-06, "duration": 0.30848097801208496, "duration_str": "308ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48413480, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.043719999999999995, "accumulated_duration_str": "43.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.959995, "duration": 0.00555, "duration_str": "5.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 12.694}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.9868379, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 12.694, "width_percent": 1.693}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.993085, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 14.387, "width_percent": 2.836}, {"sql": "select * from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.002533, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 17.223, "width_percent": 2.173}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.0328708, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 19.396, "width_percent": 5.009}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.057876, "duration": 0.0126, "duration_str": "12.6ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 24.405, "width_percent": 28.82}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0784109, "duration": 0.00283, "duration_str": "2.83ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 53.225, "width_percent": 6.473}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.091524, "duration": 0.01762, "duration_str": "17.62ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 59.698, "width_percent": 40.302}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1816838952 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1816838952\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1865804701 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1865804701\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1566700905 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566700905\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-470298700 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlRLVXZySTZDYUFLcnBiZkFJaUp2Y2c9PSIsInZhbHVlIjoiUzEyMHZ4YSt2bFlIKzZ4MXBnT2FQUmVGc0RsbEtTazRaYjFaQWxSTHRQenl5TzVyTW5wMlRibmlYYXRvaVlWOVFXNzY0dmM1dWVJZEo1L29DVzJ3WTZ3YUZRSE5NOC95TXlXN3BVc1hZZmRBNzdpRGtwRUpDdjArcmtWSjYxMnMweVhLTVNyZnRsS3RZWWxnMXU4bVMvZmovdFRXRW1nVkVyemxSMWNkUUdVem1Ea0RBUEJ0cTFpY2x4UHZyUkxsSEVFVjEvUjdLdldTSVlwcENwa2l4YUVnRUhoK3paTnQ2MHhodEYwS3oxTlFPV0Y4S1NQeU1ITmNwcG5PQnlpRWdBT3hydkcvZUZMa3dzMmJQN0RYQUpwRDBjU2p4SXBrY2plc0pscWYzSVNoT3kvVzl3TlNkaTN4a3BHS0Eydmo2aTFwa3RiWTNlN1Z2YjFTLzFkaXJLd2szTVR6UVVtODRYODNRUC9NV0dQMGhWdWxYTjUwWnRxWWkvanF6MHZtb2JzcSsxMW9NOEVZU1RKYjNQNkcrYi8vc3pRSHFGem9Gb25wUzl0aUNzOXlmRWpaU2RPcm9BWlZJUzZPSTRsVWdCTnVZSmF1TytXeWNXYjFFa0VFUHQxNkJsNHR3bVNvZ3UxUlA5c2ZkbjlZc3U3QWdlYnhiOGIwNHptSGJsTHgiLCJtYWMiOiI3MTBkNDQ1ODcyMTM3NTVjOWFkOGJlNjJiMmFhNzg0MDZiYjAxY2ZiYzNhODliZGRiYjgxZTcxYWVmZDQ1ZWNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik5HZFJ2SHMvUnRHM01qOVZjbXdoQ3c9PSIsInZhbHVlIjoiL2ZQdTN5S0Z1VE84UGZnNndxM0ovZk80ZGJnWjVIRVRxUXJGUGpBbXZsOGxWQyt6akRZMXpOVFZCekI4L0NNMDQzYWJyTmxYcU54TXo2T1RwSTlaaXJaWmhORkhCaDI3VjQ2SWRxWUdTaE9JUHBEOGVGQkU1NEh3ZDVXQjNJeUsvVzF3RUt0Q2dueDRtVUdQazFnL1E3Y2g1OWJaQWxXOW14YUNWTVhCUVBIanBmY2FJa3VGZWpURS96WUZGWWFVVmtScjdlSEY4Z3U1aTB4YzZLZUtoM1Vxa1dOb1VNbXcrUzRMcUtqeUpkUHU4R3QyZTBtOXNHZVlUaWJoUmYwVHRPd0NKNmI0U3ZnMjZlOGlkbFo4WHNSWTQxQzBXR2VTSDlRVGN5SnF3WGhsdjdJOVMyaHUyYXNOdlRqYzZKcjJLZ3d1ZVc2dUh0c21EWEE3c1BybUZwUm82UHJjNTUrQlhQVWV5R0poV0luNVlTSUZwTlpuaDVkVmVoZ09hZ3lNbUgwdVpVam1UL3NDTnh0dDBuYWt4cFlQaG5ueDhBUDBlc2E2WEdodXFzZ0ozTlFsTVpaVVoveUl0aTFLQzh3TmQ1NTQ1Qk5vRnhQUEhrMjJMTHZGUzhLTThNOG5abWNwandmN1FKRUU3Q1lvbWVIQmR4Y3Zyc1VvVExVVWlBTEIiLCJtYWMiOiIzMjA1ODc0YTM5M2E2NGRhZTQyOWVmZTM5M2MzYzY2ZDM0Y2VkMTQwZTNkMjUzM2E1YWE1YTZlMTIzNjAyMDliIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlhGWnBCQjAxb1lFRlJLR2V5NHNMN1E9PSIsInZhbHVlIjoicnFiU05QYVRHUlFESFVUeHhOTlM0U0hSSWRXVTNnL3dlVUhnL2NyaFRjNUU5clJQbWRXdHYrWlAxMFlEcFhiczczUVRhUVFjejNBWlNYVTlWL3FBM3ZBcFVuZGZrKzhiQVRtRFlJYVAxaE02eGg4RVZqeG9JbXYySmZCZENLeXFmYUZsSDlOa2JuTkxoSEE2SmtDb0x6alN0dWV5YVRrM2g3WHk5anhTczR2eEVxNjM1ZFNBRHkySkg4cUFUT1A0RzEwSDBua3ZHK256eG9mZ0huQmwrODcxM2JEbVU4bEl2UzlRWVZnSlNSalhpUFhRYlJWOGFLOEVOanNZRGFjSExQU0w0QkI3Q05ZcEtTVGhWa0svS3ZkR3dwTkZuMTZXRUw4VEZlM1UvdHJzMDNRNk1xM0hEYVJQRkJtSUo3aHJNZGZ5MEdZZjhpc0pvOE5LRXREOE1oK1BOWnpkd3ZqZ3ZCY3c1UEdhcEZObnMzZnZuOVlrd0ZMMVRQbStuckl4ZS85SDJIcVMySTRtRm1neDFPdjlKayt3N1RWdXZ6NlM1bTc3U3I2WUxocXdNdy9BemxjSWFhSGJVdU0vU2IvNVEzZjlkaUNrMGlQYW1UYitNOTZzU2JXWHFwMGFRZ0d4YmRxQVFiQkdRSGlYMFRmK3VHRmwzdjFnVXVpbmkxNU0iLCJtYWMiOiJhN2I5NTkzMzU0MjJlMWE3NDBmZDFiMDdkZjg5YWFlMDFmODQzNGVlYTZjMzhmZDFhMzc3ZDIxMTFkNGM0MThmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-470298700\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-780753924 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21K3ZmFzZyr4s2pH9aKWRqwEKhgdUCnDfhl6jx78</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-780753924\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:45:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRKcjJnQm9RMVJ5K2Vld0Vva1pIVHc9PSIsInZhbHVlIjoiYURwbElOTHZVQmIyNXV6STZaMmkrc2N4V2hqYWNpQjNOVmZsUERjbkgyem5QT3B5QlVsd29ZUDFEdDBpb1ZwN3ZraTRxUzc1cjlEek9waUlJd0NHMGhYcXR1RzhmV1NqeUVuemJaN2cwSGk2QUVIVWFxdC8xK281YzhjL3JZL3hkb2pES2xnbjNIT0ovcVZhQmptallKeHU2Sy9XZDEwSUhEY1lyREs2Q01SdlhnSGdwN25WRE5zMVhGZUN1TVBXc2YvUUJ3dkJnL01BanFmQ0VpUFpQc0RZYmdQOFVOcWtPek9vNXkxUzlZUEVmNkVvSFRSOUpiSjhucmdFSTAvdkpDZmt4Q1VDcXJWbTJrcXRYMnNLMlVqVmo0eStPRUY4aUY0Qm5OWnNJNjl0YXJObDUrRitUWktqcjdFTWV4cWdXWnlzT1JnOEEwKzM2NzU5Qnl2V1dnRHlxN2Zwc0VwL3pDbFQ2VVFLOE9BY0plajBMR2xHNnl1RkVEOXp5ZlptVG91ZWZvNHFBSHQzeWtjYU9IM0JmV2FrcTJ5SkZUeENYVmtZTzFBek1QakZBdGlDT0p4UFBnQWVCOUFFVS9VUkRVU2w2SE5iZXhxVUE3QzZnbWZ1MEJqaVZGdm9OZlVhR1ZvbFZveHJtQTVlOFM3QnRVR0JpN01QNjRZWmNNRnoiLCJtYWMiOiIzZTEwNzcyY2ViYzk1ZTI4NGVlYzc1ZjE5OTJhOThmMTEzODFhZjM5YmRhNDk4ZmUzOTkxNzdlOGRlMzQwZjVmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:45:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6ImlUU0F6NFJyTTk4blVNa2lGOS9NZFE9PSIsInZhbHVlIjoiTzM5bTBzNHJGV05HV2VUNXRWNUxVbFFzTGs1YWRlSkdpaXlySi9zUnBDOXM2bXg2d2tSVENYT0dPUy9Zc1I5NWRLODNVcWtiKzJqd2VrUGpWQVRzR2RNODBNcG41RXJvMW5Cd1ptRTNVemxWblE3a09rMTA0LzVyMkZPNnMzWkJUeWh6SVRYejVSWXVtbHNhWVlJRHVrWWxqZjZucWlQdHZnajlZMktscU1YVjJ5V3U0M2tTNzJvbFJ1ZmpKaXRxZncrZWhMSEpKWFB0SnpXTCtFZEd3UFpBWjNuL3lZbjZNYzZ6TVpOVkY2ekQySldRa0dmazd3NmNEeWFvOVQ2b2VpMU1iRGxrMW9Md2pVajVpaHpOWG81TUtYVWZpNlV6cHc0dkJjWHNobDBxbU9vUW5OcVhiU1drd1RkRU13Vkd4UittY1hNZFI2RkpGNXMwcEF5ZFFJMXRNRW02U0lQNXpSWjYycmNxaktyUDB6SHJjTXIrNkNMRGRnWkNNWHI1YnV2dE9sV0h6WHRnRTFnVkVZb213VlEwVDZjT1NYMU44VW1lY1dDQVJhQmU2QlBTUGJSZXlmVHB1RUpwaTBSZWUvMXczbk1wYWtRbzFQaCs0VWpNRS95d2ptcWNRV25KR1dLNW5mZ3hhSkpkakFEK0VRNlVuUjEvTEV3WGVNYUoiLCJtYWMiOiIzMmVmNjJhMTAxMTA5ZmUyOTE2NTA1ZjdlNmUzNDI4ODM4ZWU4M2E5MGZlN2M3ZWI1NDY3Y2E0NDFiZjE5YTJhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:45:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRKcjJnQm9RMVJ5K2Vld0Vva1pIVHc9PSIsInZhbHVlIjoiYURwbElOTHZVQmIyNXV6STZaMmkrc2N4V2hqYWNpQjNOVmZsUERjbkgyem5QT3B5QlVsd29ZUDFEdDBpb1ZwN3ZraTRxUzc1cjlEek9waUlJd0NHMGhYcXR1RzhmV1NqeUVuemJaN2cwSGk2QUVIVWFxdC8xK281YzhjL3JZL3hkb2pES2xnbjNIT0ovcVZhQmptallKeHU2Sy9XZDEwSUhEY1lyREs2Q01SdlhnSGdwN25WRE5zMVhGZUN1TVBXc2YvUUJ3dkJnL01BanFmQ0VpUFpQc0RZYmdQOFVOcWtPek9vNXkxUzlZUEVmNkVvSFRSOUpiSjhucmdFSTAvdkpDZmt4Q1VDcXJWbTJrcXRYMnNLMlVqVmo0eStPRUY4aUY0Qm5OWnNJNjl0YXJObDUrRitUWktqcjdFTWV4cWdXWnlzT1JnOEEwKzM2NzU5Qnl2V1dnRHlxN2Zwc0VwL3pDbFQ2VVFLOE9BY0plajBMR2xHNnl1RkVEOXp5ZlptVG91ZWZvNHFBSHQzeWtjYU9IM0JmV2FrcTJ5SkZUeENYVmtZTzFBek1QakZBdGlDT0p4UFBnQWVCOUFFVS9VUkRVU2w2SE5iZXhxVUE3QzZnbWZ1MEJqaVZGdm9OZlVhR1ZvbFZveHJtQTVlOFM3QnRVR0JpN01QNjRZWmNNRnoiLCJtYWMiOiIzZTEwNzcyY2ViYzk1ZTI4NGVlYzc1ZjE5OTJhOThmMTEzODFhZjM5YmRhNDk4ZmUzOTkxNzdlOGRlMzQwZjVmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:45:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6ImlUU0F6NFJyTTk4blVNa2lGOS9NZFE9PSIsInZhbHVlIjoiTzM5bTBzNHJGV05HV2VUNXRWNUxVbFFzTGs1YWRlSkdpaXlySi9zUnBDOXM2bXg2d2tSVENYT0dPUy9Zc1I5NWRLODNVcWtiKzJqd2VrUGpWQVRzR2RNODBNcG41RXJvMW5Cd1ptRTNVemxWblE3a09rMTA0LzVyMkZPNnMzWkJUeWh6SVRYejVSWXVtbHNhWVlJRHVrWWxqZjZucWlQdHZnajlZMktscU1YVjJ5V3U0M2tTNzJvbFJ1ZmpKaXRxZncrZWhMSEpKWFB0SnpXTCtFZEd3UFpBWjNuL3lZbjZNYzZ6TVpOVkY2ekQySldRa0dmazd3NmNEeWFvOVQ2b2VpMU1iRGxrMW9Md2pVajVpaHpOWG81TUtYVWZpNlV6cHc0dkJjWHNobDBxbU9vUW5OcVhiU1drd1RkRU13Vkd4UittY1hNZFI2RkpGNXMwcEF5ZFFJMXRNRW02U0lQNXpSWjYycmNxaktyUDB6SHJjTXIrNkNMRGRnWkNNWHI1YnV2dE9sV0h6WHRnRTFnVkVZb213VlEwVDZjT1NYMU44VW1lY1dDQVJhQmU2QlBTUGJSZXlmVHB1RUpwaTBSZWUvMXczbk1wYWtRbzFQaCs0VWpNRS95d2ptcWNRV25KR1dLNW5mZ3hhSkpkakFEK0VRNlVuUjEvTEV3WGVNYUoiLCJtYWMiOiIzMmVmNjJhMTAxMTA5ZmUyOTE2NTA1ZjdlNmUzNDI4ODM4ZWU4M2E5MGZlN2M3ZWI1NDY3Y2E0NDFiZjE5YTJhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:45:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1321623484 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1321623484\", {\"maxDepth\":0})</script>\n"}}