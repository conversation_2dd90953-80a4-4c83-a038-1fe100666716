{"__meta": {"id": "X68b70787c7bc73a6303c0f8df3a12240", "datetime": "2025-07-31 05:57:30", "utime": **********.671073, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753941449.851637, "end": **********.671116, "duration": 0.81947922706604, "duration_str": "819ms", "measures": [{"label": "Booting", "start": 1753941449.851637, "relative_start": 0, "end": **********.586504, "relative_end": **********.586504, "duration": 0.7348670959472656, "duration_str": "735ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.586534, "relative_start": 0.****************, "end": **********.671119, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "84.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KxWyl4MSf0l9oNGOGSF1HEcG5JGA3NICRYBRoxtU", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-168443823 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-168443823\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-945854279 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-945854279\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1690278577 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1690278577\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-866428191 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-866428191\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1131453535 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1131453535\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1478937238 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:57:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkIrRS9yejYzaG40cFZMMGRDZ0hmaXc9PSIsInZhbHVlIjoiZTZqcXgxNUxKL3BHWG5mR2U2NGZEb0FJSU4wN1BuSDFUbWl1em52SzJ4NVorY3pwRW9CcmVLMzE1bktocEVXcVowYjlTUk9oS1BibHlvWGljdVZtU1F5T2tLR0dUR2FaLzlpRkJSdTBxN2VKQ21rVXhqY0tSMVB5TFloaWVIOTU3MGtqRnNOVDJoZEFaZkFjaEsyZ291MUozcnpwa2RoZVV2WnM2MVA3eVEwNEU0emNmekoxUzQvVnVHYTdyeldzZ2hrWEZRWFVMNThZTmM5SXR0ZHBaaEhnVVQwRE9Rbklwc3FQOGFmaTU0L2hvUysvVDE3cjNFR2hUQnRxYUN3a1kxbS8rYlBEbVJKU3VwKzNWUU54Mnd2SmVoRityUHFSSnhWV2dYNFZTdVZSOThwU2RhNWNxUnMwcnA4bjlKMngwWkh5cWIxTjZWeTlpWGtKZnZqNittenlhY0ZzUklBYU1UbGV0NnJINHRnYUF0NDRPL0dYYlZ3Y2FUK1lQeGVnU1NDNjE4N3V6NnozWUFVR21BWFdPL3FHMVVFTkdVa2dSdGQydUN2WXhYV1BWWDRVMzQ1MThONXJRZmJYMUh4c0xOZTVvTkNhVHgzUjgvU0dnSWVZcm5mRTlzbUk4VW44bGpFRFdIcWNHTVdlL1NSL1FkNWg4NjBJTWZ3THczUjMiLCJtYWMiOiIxYzdlYzBlNGE1ZTI1OTE4YTZkNTA4NDMwNTNhYTk2MDkzZjJiMjRmODRiNTFmZGQ1Y2I1MTIyMjE2YmVkNDYzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:57:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlFGK2NYY29odTBxSlpBRnNNSlQ5M3c9PSIsInZhbHVlIjoiNDMyRE1zTWhQZHN6R1F6eU50QmhuTVNPeWM2TWZFU25SNW5vOXZ1VjZhM0F4SGJ2YXZZVUdUK21PM1lqcHJSMmdhUzJGamJEMzQ3ZzJ0Vzlsc0dJcVNpUGh6VmZmOVlldnFnZzB3czRLUkw2L2NMSHlhZkh3aGxMMTZPKzhxL0xHRDJIZXE3djFsUkZMODgydlRyNVkyUEZURTZZVlkxQzc4RG85MHQ0clRYNjJHREJlcE83akhGdGljSHBFdmRDTThmUHJ4SGtMYk5Ea0xuUGNkL3UzdjV4NTZlcFg3V2I3QkFDcFFxTFk1VnVZWDRqQnVQZDJDQzU5cGk4N1NBNkFobVpVTUxISDZnMk1UWGR2WkpLTU4rSjJBLzFzSG1EYUQyNm9WVStWUFQ4bUpMNm5rSmhQU2dBS0g3eS92ZXozeTBJMUNab3dDSlF1dnh3NkpKcWRVNlhFSzY5Rng1elNBYWZrejlITWIxSGZRTjFwU0trL0JSOGZJVHphd2FVN3p3UWY1V0pGZkVUb3YvNmZhb3NBbk14QzFoR1VmaXhLZFhmbE9LQjFNeTRUVWVVZmpvelhOYStGWm9hb1UzaE5KcFZrVEZrRDZnbTVnYWZ6YUtGbDNFbURaK1daZTV4cDVuMHNkOHpVVE9YNzdtZmRlbStwZmtlQUhCenE4WjQiLCJtYWMiOiIwNGRiMWIwOTgwZmY0MjAyNzUwMjlkOTUxODZhZGFmNDkzZWZiMjIzNGE4ZjkzODI1MDgzYjgxN2Y1NWExMjUwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:57:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkIrRS9yejYzaG40cFZMMGRDZ0hmaXc9PSIsInZhbHVlIjoiZTZqcXgxNUxKL3BHWG5mR2U2NGZEb0FJSU4wN1BuSDFUbWl1em52SzJ4NVorY3pwRW9CcmVLMzE1bktocEVXcVowYjlTUk9oS1BibHlvWGljdVZtU1F5T2tLR0dUR2FaLzlpRkJSdTBxN2VKQ21rVXhqY0tSMVB5TFloaWVIOTU3MGtqRnNOVDJoZEFaZkFjaEsyZ291MUozcnpwa2RoZVV2WnM2MVA3eVEwNEU0emNmekoxUzQvVnVHYTdyeldzZ2hrWEZRWFVMNThZTmM5SXR0ZHBaaEhnVVQwRE9Rbklwc3FQOGFmaTU0L2hvUysvVDE3cjNFR2hUQnRxYUN3a1kxbS8rYlBEbVJKU3VwKzNWUU54Mnd2SmVoRityUHFSSnhWV2dYNFZTdVZSOThwU2RhNWNxUnMwcnA4bjlKMngwWkh5cWIxTjZWeTlpWGtKZnZqNittenlhY0ZzUklBYU1UbGV0NnJINHRnYUF0NDRPL0dYYlZ3Y2FUK1lQeGVnU1NDNjE4N3V6NnozWUFVR21BWFdPL3FHMVVFTkdVa2dSdGQydUN2WXhYV1BWWDRVMzQ1MThONXJRZmJYMUh4c0xOZTVvTkNhVHgzUjgvU0dnSWVZcm5mRTlzbUk4VW44bGpFRFdIcWNHTVdlL1NSL1FkNWg4NjBJTWZ3THczUjMiLCJtYWMiOiIxYzdlYzBlNGE1ZTI1OTE4YTZkNTA4NDMwNTNhYTk2MDkzZjJiMjRmODRiNTFmZGQ1Y2I1MTIyMjE2YmVkNDYzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:57:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlFGK2NYY29odTBxSlpBRnNNSlQ5M3c9PSIsInZhbHVlIjoiNDMyRE1zTWhQZHN6R1F6eU50QmhuTVNPeWM2TWZFU25SNW5vOXZ1VjZhM0F4SGJ2YXZZVUdUK21PM1lqcHJSMmdhUzJGamJEMzQ3ZzJ0Vzlsc0dJcVNpUGh6VmZmOVlldnFnZzB3czRLUkw2L2NMSHlhZkh3aGxMMTZPKzhxL0xHRDJIZXE3djFsUkZMODgydlRyNVkyUEZURTZZVlkxQzc4RG85MHQ0clRYNjJHREJlcE83akhGdGljSHBFdmRDTThmUHJ4SGtMYk5Ea0xuUGNkL3UzdjV4NTZlcFg3V2I3QkFDcFFxTFk1VnVZWDRqQnVQZDJDQzU5cGk4N1NBNkFobVpVTUxISDZnMk1UWGR2WkpLTU4rSjJBLzFzSG1EYUQyNm9WVStWUFQ4bUpMNm5rSmhQU2dBS0g3eS92ZXozeTBJMUNab3dDSlF1dnh3NkpKcWRVNlhFSzY5Rng1elNBYWZrejlITWIxSGZRTjFwU0trL0JSOGZJVHphd2FVN3p3UWY1V0pGZkVUb3YvNmZhb3NBbk14QzFoR1VmaXhLZFhmbE9LQjFNeTRUVWVVZmpvelhOYStGWm9hb1UzaE5KcFZrVEZrRDZnbTVnYWZ6YUtGbDNFbURaK1daZTV4cDVuMHNkOHpVVE9YNzdtZmRlbStwZmtlQUhCenE4WjQiLCJtYWMiOiIwNGRiMWIwOTgwZmY0MjAyNzUwMjlkOTUxODZhZGFmNDkzZWZiMjIzNGE4ZjkzODI1MDgzYjgxN2Y1NWExMjUwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:57:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478937238\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-695416022 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KxWyl4MSf0l9oNGOGSF1HEcG5JGA3NICRYBRoxtU</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-695416022\", {\"maxDepth\":0})</script>\n"}}