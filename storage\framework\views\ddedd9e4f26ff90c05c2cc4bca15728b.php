
<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Dashboard')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('action-btn'); ?>
<div class="float-end">
    <a href="#"
       class="btn btn-sm text-white d-flex align-items-center gap-1"
       data-bs-toggle="modal"
       data-bs-target="#dashboardSwitcherModal"
       data-bs-toggle="tooltip"
       title="<?php echo e(__('Change Dashboard')); ?>"
       style="background: linear-gradient(135deg, #1b5e20, #0d47a1); border: none;">
        <i class="ti ti-layout-dashboard"></i>
        <span class="d-none d-md-inline"><i class="ti ti-switch-vertical" style="margin-right: 5px;"></i><?php echo e(__('Switch')); ?></span>
    </a>
</div>

    <!-- Dashboard Switcher Modal -->
    <div class="modal fade" id="dashboardSwitcherModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><?php echo e(__('Select Dashboard')); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="account">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-wallet fs-1 text-primary"></i>
                                    </div>
                                    <h5 class="mb-0"><?php echo e(__('Account Dashboard')); ?></h5>
                                    <small class="text-muted"><?php echo e(__('View financial overview')); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="crm">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-users fs-1 text-info"></i>
                                    </div>
                                    <h5 class="mb-0"><?php echo e(__('CRM Dashboard')); ?></h5>
                                    <small class="text-muted"><?php echo e(__('Customer relationship management')); ?></small>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="hrm">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-id fs-1 text-warning"></i>
                                    </div>
                                    <h5 class="mb-0"><?php echo e(__('HRM Dashboard')); ?></h5>
                                    <small class="text-muted"><?php echo e(__('Human resource management')); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="pos">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-cash fs-1 text-success"></i>
                                    </div>
                                    <h5 class="mb-0"><?php echo e(__('POS Dashboard')); ?></h5>
                                    <small class="text-muted"><?php echo e(__('Point of sale system')); ?></small>
                                </div>
                            </div>
                        </div> -->
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="project">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="fas fa-project-diagram fs-1 text-primary"></i>
                                    </div>
                                    <h5 class="mb-0"><?php echo e(__('Project Dashboard')); ?></h5>
                                    <small class="text-muted"><?php echo e(__('Project management')); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('script-page'); ?>
<script>
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Dashboard Switcher
    document.addEventListener('DOMContentLoaded', function() {
        // Get all dashboard options
        const dashboardOptions = document.querySelectorAll('.dashboard-option');
        
        // Add click event to each option
        dashboardOptions.forEach(option => {
            option.addEventListener('click', function() {
                const dashboardType = this.getAttribute('data-dashboard');
                
                // Store the selected dashboard in localStorage
                localStorage.setItem('selectedDashboard', dashboardType);
                
                // Determine the route based on dashboard type
                let route = '<?php echo e(route("dashboard")); ?>';
                
                switch(dashboardType) {
                    case 'crm':
                        route = '<?php echo e(route("crm.dashboard")); ?>';
                        break;
                    case 'hrm':
                        route = '<?php echo e(route("hrm.dashboard")); ?>';
                        break;
                    case 'pos':
                        route = '<?php echo e(route("pos.dashboard")); ?>';
                        break;
                    case 'project':
                        route = '<?php echo e(route("project.dashboard")); ?>';
                        break;
                    // Default is account dashboard
                }
                
                // Redirect to the selected dashboard
                window.location.href = route;
            });
        });
        
        // Highlight current dashboard in modal when opened
        const modal = document.getElementById('dashboardSwitcherModal');
        if (modal) {
            modal.addEventListener('show.bs.modal', function () {
                const currentPath = window.location.pathname;
                dashboardOptions.forEach(option => {
                    const dashboardType = option.getAttribute('data-dashboard');
                    if (
                        (dashboardType === 'account' && currentPath.includes('account-dashboard')) ||
                        (dashboardType === 'crm' && currentPath.includes('crm-dashboard')) ||
                        (dashboardType === 'hrm' && currentPath.includes('hrm-dashboard')) ||
                        (dashboardType === 'pos' && currentPath.includes('pos-dashboard')) ||
                        (dashboardType === 'project' && currentPath.includes('project-dashboard'))
                    ) {
                        option.classList.add('border-primary');
                    } else {
                        option.classList.remove('border-primary');
                    }
                });
            });
        }
    });
</script>
<script>
    // --- FIX: Prevent double rendering of ApexCharts ---
    let taskOverviewChart = null;
    (function () {
        var options = {
            chart: {
                height: 180,
                type: 'area',
                toolbar: {
                    show: false,
                },
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                width: 2,
                curve: 'smooth'
            },
            series: [{
                name: 'Refferal',
                data:<?php echo json_encode(array_values($home_data['task_overview'])); ?>

            },],
            xaxis: {
                categories:<?php echo json_encode(array_keys($home_data['task_overview'])); ?>,
            },
            colors: ['#3ec9d6'],
            fill: {
                type: 'solid',
            },
            grid: {
                strokeDashArray: 4,
            },
            legend: {
                show: true,
                position: 'top',
                horizontalAlign: 'right',
            },
        };
        // Destroy previous chart if exists
        if (window.taskOverviewChart && window.taskOverviewChart.destroy) {
            window.taskOverviewChart.destroy();
        }
        window.taskOverviewChart = new ApexCharts(document.querySelector("#task_overview"), options);
        window.taskOverviewChart.render();
    })();

    (function () {
        var options = {
            chart: {
                height: 300,
                type: 'bar',
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    horizontal: true,
                    borderRadius: 10,
                    dataLabels: {
                        position: 'top',
                    },
                }
            },
            colors: ["#3ec9d6"],
            dataLabels: {
                enabled: true,
                offsetX: -6,
                style: {
                    fontSize: '12px',
                    colors: ['#fff']
                }
            },
            stroke: {
                show: true,
                width: 1,
                colors: ['#fff']
            },
            grid: {
                strokeDashArray: 4,
            },
            series: [{
                data: <?php echo json_encode(array_values($home_data['timesheet_logged'])); ?>

            }],
            xaxis: {
                categories: <?php echo json_encode(array_keys($home_data['timesheet_logged'])); ?>,
            },
        };
        var chart = new ApexCharts(document.querySelector("#timesheet_logged"), options);
        chart.render();
    })();

</script>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Project')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
<div class="row mb-4">
    <!-- Projects Card -->
    <div class="col-xxl-4 col-md-4 col-12">
    <div class="card h-100 shadow-sm"
         style="border: 1px solid #2e7d32; border-radius: 12px;">
        <div class="card-body p-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0 d-flex align-items-center">
                    <i class="fas fa-project-diagram me-2" style="color: #2e7d32;"></i>
                    <a href="<?php echo e(route('projects.index')); ?>" class="text-dark text-decoration-none">
                        <?php echo e(__('Projects')); ?>

                    </a>
                </h5>
                <span class="badge rounded-pill"
                      style="
                          background-color: rgba(46, 125, 50, 0.1);
                          color: #2e7d32;
                          font-size: 1rem;
                          padding: 10px 20px;
                          font-weight: 800;">
                    <?php echo e($home_data['total_project']['total']); ?> Total
                </span>
            </div>
            <div class="row align-items-center">
                <div class="col-7">
                    <div class="d-flex flex-column">
                        <span class="text-muted small">Completed</span>
                        <h3 class="mb-0" style="color: var(--theme-color);"><?php echo e($home_data['total_project']['completed'] ?? 0); ?></h3>
                        <div class="progress mt-2" style="height: 6px;">
                            <div class="progress-bar" role="progressbar"
                                 style="width: <?php echo e($home_data['total_project']['percentage']); ?>%; background-color: var(--theme-color);"
                                 aria-valuenow="<?php echo e($home_data['total_project']['percentage']); ?>"
                                 aria-valuemin="0" aria-valuemax="100">
                            </div>
                        </div>
                        <span class="text-muted small mt-1"><?php echo e($home_data['total_project']['percentage']); ?>% Complete</span>
                    </div>
                </div>
                <div class="col-5 text-center">
                    <div style="position: relative; width: 110px; height: 110px; margin: 0 auto; border: 1px solid #55CFDB; border-radius: 50%; background: #ffffff;">
                        <div class="position-absolute top-50 start-50 translate-middle text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#2e7d32" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21.21 15.89A10 10 0 1 1 12 2v10z"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


    <!-- Tasks Card -->
    <div class="col-xxl-4 col-md-4 col-12">
    <div class="card h-100 shadow-sm" 
         style="border: 1px solid #2e7d32; border-radius: 12px;">
        <div class="card-body p-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0 d-flex align-items-center">
                    <i class="fas fa-tasks me-2" style="color: #2e7d32;"></i>
                    <a href="<?php echo e(route('taskBoard.view', 'list')); ?>" class="text-dark text-decoration-none">
                        <?php echo e(__('Tasks')); ?>

                    </a>
                </h5>
                <span class="badge rounded-pill"
                      style="background-color: rgba(46, 125, 50, 0.1);
                             color: #2e7d32;
                             font-size: 1rem;
                             padding: 10px 20px;
                             font-weight: 800;">
                    <?php echo e($home_data['total_task']['total']); ?> Total
                </span>
            </div>
            <div class="row align-items-center">
                <div class="col-7">
                    <div class="d-flex flex-column">
                        <!-- Done Tasks -->
                        <div class="mb-3">
                            <span class="text-muted small">Done Tasks</span>
                            <h3 class="mb-0" style="color: #2e7d32;">
                                <?php echo e($home_data['total_task']['completed'] ?? 0); ?>

                            </h3>
                        </div>

                        <!-- Pending Tasks -->
                        <div class="mb-3">
                            <span class="text-muted small">Pending Tasks</span>
                            <h3 class="mb-0" style="color: #ff6b35;">
                                <?php echo e($home_data['total_task']['pending'] ?? 0); ?>

                            </h3>
                        </div>

                        <div class="progress mt-2" style="height: 6px; background-color: #f1f5f9; border-radius: 6px;">
                            <div class="progress-bar" role="progressbar"
                                 style="width: <?php echo e($home_data['total_task']['percentage']); ?>%; background-color: var(--theme-color); border-radius: 6px;"
                                 aria-valuenow="<?php echo e($home_data['total_task']['percentage']); ?>"
                                 aria-valuemin="0" aria-valuemax="100">
                            </div>
                        </div>
                        <span class="text-muted small mt-1">
                            <?php echo e($home_data['total_task']['percentage']); ?>% Complete
                        </span>
                    </div>
                </div>
                <div class="col-5 text-center">
                    <div style="position: relative; width: 110px; height: 110px; margin: 0 auto; border: 1px solid #55CFDB; border-radius: 50%; background: #ffffff;">
                        <div class="position-absolute top-50 start-50 translate-middle text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 24 24" fill="none" stroke="#2e7d32" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M9 11l3 3L22 4"></path>
                                <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


    <!-- Expenses Card -->
    <div class="col-xxl-4 col-md-4 col-12">
        <div class="card h-100 shadow-sm" 
            style="border: 1px solid #2e7d32; border-radius: 12px;">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="mb-0 d-flex align-items-center">
                        <i class="fas fa-money-bill-wave me-2" style="color: #2e7d32;"></i>
                        <a href="<?php echo e(route('expense.index')); ?>" class="text-dark text-decoration-none">
                            <?php echo e(__('Expenses')); ?>

                        </a>
                    </h5>
                    <span class="badge rounded-pill"    
                        style="background-color: rgba(46, 125, 50, 0.1);
                                color: #2e7d32;
                                font-size: 1rem;
                                padding: 10px 20px;
                                font-weight: 800;">
                        <?php echo e($home_data['total_expense']['total']); ?> Total
                    </span>
                </div>

                <div class="row align-items-center">
                    <div class="col-7">
                        <div class="d-flex flex-column">
                            <span class="text-muted small">Completed</span>
                            <h3 class="mb-0" style="color: #2e7d32;">
                                <?php echo e($home_data['total_expense']['completed'] ?? 0); ?>

                            </h3>
                            <div class="progress mt-2" style="height: 6px;">
                                <div class="progress-bar" role="progressbar" 
                                    style="width: <?php echo e($home_data['total_expense']['percentage']); ?>%; background-color: #2e7d32;" 
                                    aria-valuenow="<?php echo e($home_data['total_expense']['percentage']); ?>" 
                                    aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                            <span class="text-muted small mt-1">
                                <?php echo e($home_data['total_expense']['percentage']); ?>% Complete
                            </span>
                        </div>
                    </div>

                    <div class="col-5 text-center">
                        <div style="position: relative; width: 110px; height: 110px; margin: 0 auto; border: 1px solid #55CFDB; border-radius: 50%; background: #ffffff;">
                            <div class="position-absolute top-50 start-50 translate-middle text-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 24 24" fill="none" stroke="#2e7d32" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12 1v22"></path>
                                    <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('script-page'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to create pie chart with improved styling
        function createPieChart(elementId, completed, total, color) {
            const ctx = document.getElementById(elementId).getContext('2d');
            const remaining = total - completed;
            const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
            
            return new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['Completed', 'Remaining'],
                    datasets: [{
                        data: [completed, remaining],
                        backgroundColor: [
                            color,
                            'rgba(200, 200, 200, 0.3)'
                        ],
                        borderColor: '#fff',
                        borderWidth: 2,
                        hoverOffset: 10
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                pointStyle: 'circle',
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            },
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleFont: { size: 14 },
                            bodyFont: { size: 13 },
                            padding: 12,
                            displayColors: true,
                            usePointStyle: true
                        },
                        datalabels: {
                            formatter: (value, ctx) => {
                                const total = ctx.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return percentage > 10 ? `${percentage}%` : '';
                            },
                            color: '#fff',
                            font: {
                                weight: 'bold',
                                size: 14
                            }
                        }
                    },
                    layout: {
                        padding: 10
                    },
                    elements: {
                        arc: {
                            borderWidth: 0,
                            borderRadius: 10
                        }
                    },
                    animation: {
                        animateScale: true,
                        animateRotate: true
                    }
                },
                plugins: [{
                    id: 'centerText',
                    beforeDraw: function(chart) {
                        if (chart.config.options.elements.center) {
                            const centerConfig = chart.config.options.elements.center;
                            const ctx = chart.ctx;
                            

                            // Get center coordinates
                            const centerX = (chart.chartArea.left + chart.chartArea.right) / 2;
                            const centerY = (chart.chartArea.top + chart.chartArea.bottom) / 2;
                            

                            // Draw text in center
                            ctx.save();
                            ctx.font = centerConfig.font;
                            ctx.fillStyle = centerConfig.color;
                            ctx.textAlign = 'center';
                            ctx.textBaseline = 'middle';
                            ctx.fillText(centerConfig.text, centerX, centerY);
                            ctx.restore();
                        }
                    }
                }]
            });
        }

        // Initialize charts with actual data
        createPieChart('projectsChart',
            <?php echo e($home_data['total_project']['completed'] ?? 0); ?>,
            <?php echo e($home_data['total_project']['total'] ?? 0); ?>,
            getComputedStyle(document.documentElement).getPropertyValue('--theme-color').trim()
        );

        createPieChart('tasksChart',
            <?php echo e($home_data['total_task']['completed'] ?? 0); ?>,
            <?php echo e($home_data['total_task']['total'] ?? 0); ?>,
            getComputedStyle(document.documentElement).getPropertyValue('--theme-color').trim()
        );

        createPieChart('expensesChart',
            <?php echo e($home_data['total_expense']['completed'] ?? 0); ?>,
            <?php echo e($home_data['total_expense']['total'] ?? 0); ?>,
            getComputedStyle(document.documentElement).getPropertyValue('--theme-color').trim()
        );
    });
</script>
<?php $__env->stopPush(); ?>
</div>
    <div class="row" style="margin-left: 20px; margin-right: 20px;">
        <div class="col-lg-4 mb-4">
            <div class="card h-100 mb-0">
                <div class="card-header" style="border-top: 4px solid #2e7d32;">
                    <h5><i class="fas fa-project-diagram me-1 text-warning" style="margin-right: 5px; color: #2e7d32;"></i><?php echo e(__('Project Status')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row gy-5">
                        <?php $__currentLoopData = $home_data['project_status']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status => $val): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-6 col-sm-6">
                                <div class="align-items-start">

                                    <div class="ms-2">
                                        <p class="text-sm mb-2"><?php echo e(__(\App\Models\Project::$project_status[$status])); ?></p>
                                        <h3 class="mb-2 text-<?php echo e(\App\Models\Project::$status_color[$status]); ?>"><?php echo e($val['total']); ?>%</h3>
                                        <div class="progress mb-0">
                                            <div class="progress-bar bg-<?php echo e(\App\Models\Project::$status_color[$status]); ?>" style="width: <?php echo e($val['percentage']); ?>%;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header" style="border-top: 4px solid var(--theme-color);">
                    <h5><i class="fas fa-chart-pie me-1" style="margin-right: 5px; color: var(--theme-color);"></i><?php echo e(__('Tasks Overview')); ?> <span class="float-end"> <small class="text-muted"><?php echo e(__('Total Completed task in last 7 days')); ?></small></span></h5>
                </div>
                <div class="card-body">
                    <div id="task_overview"></div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header" style="border-top: 4px solid #2e7d32;">
                    <h5><i class="fas fa-project-diagram me-1 text-warning" style="margin-right: 5px; color: #2e7d32;"></i><?php echo e(__('Top Due Projects')); ?></h5>
                </div>
                <div class="card-body project_table">
                    <div class="table-responsive ">
                        <table class="table table-hover mb-0">
                            <thead>
                            <tr>
                                <th><?php echo e(__('Name')); ?></th>
                                <th><?php echo e(__('End Date')); ?></th>
                                <th ><?php echo e(__('Status')); ?></th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php if($home_data['due_project']->count() > 0): ?>
                                <?php $__currentLoopData = $home_data['due_project']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $due_project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="<?php echo e(asset(Storage::url('/'.$due_project->project_image ))); ?>"
                                                     class="wid-40 rounded border-2 border border-primary me-3" >
                                                <div>
                                                    <h6 class="mb-0"><?php echo e($due_project->project_name); ?></h6>
                                                    <p class="mb-0"><span class="text-success"><?php echo e(\Auth::user()->priceFormat($due_project->budget)); ?></p>

                                                </div>
                                            </div>
                                        </td>
                                        <td ><?php echo e(Utility::getDateFormated($due_project->end_date)); ?></td>
                                        <td class="">
                                            <span class=" status_badge p-2 px-3 rounded badge bg-<?php echo e(\App\Models\Project::$status_color[$due_project->status]); ?>"><?php echo e(__(\App\Models\Project::$project_status[$due_project->status])); ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <tr class="py-5">
                                    <td class="text-center mb-0" colspan="3"><?php echo e(__('No Due Projects Found.')); ?></td>
                                </tr>
                            <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header" style="border-top: 4px solid #2e7d32;">
                    <h5><i class="fas fa-clock me-1 text-warning" style="margin-right: 5px; color: #2e7d32;"></i><?php echo e(__('Timesheet Logged Hours')); ?> <span>  <small class="float-end text-muted flo"><?php echo e(__('Last 7 days')); ?></small></span></h5>
                </div>
                <div class="card-body project_table">
                    <div id="timesheet_logged"></div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="card" style="margin-top: 10px;">
                <div class="card-header" style="border-top: 4px solid #2e7d32;">
                    <h5><i class="fas fa-clock me-1 text-warning" style="margin-right: 5px; color: #2e7d32;"></i><?php echo e(__('Top Due Tasks')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <tbody>
                            <?php $__currentLoopData = $home_data['due_tasks']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $due_task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="">
                                                <span class="text-muted d-block mb-2"><?php echo e(__('Task')); ?>:</span>
                                                <h6 class="m-0"><a href="<?php echo e(route('projects.tasks.index',$due_task->project->id)); ?>" class="name mb-0 h6"><?php echo e($due_task->name); ?></a></h6>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted d-block mb-2"><?php echo e(__('Project')); ?>:</span>
                                        <h6 class="m-0"><?php echo e($due_task->project->project_name); ?></h6>
                                    </td>
                                    <td>

                                        <span class="text-muted d-block mb-2"><?php echo e(__('Stage')); ?>:</span>
                                        <div class="d-flex align-items-center h6">
                                            <span class="full-circle bg-<?php echo e(\App\Models\ProjectTask::$priority_color[$due_task->priority]); ?>"></span>
                                            <span class="ms-1"><?php echo e(\App\Models\ProjectTask::$priority[$due_task->priority]); ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted d-block mb-2"><?php echo e(__('Completion')); ?>:</span>
                                        <h6 class="m-0"><?php echo e($due_task->taskProgress($due_task)['percentage']); ?></h6>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/dashboard/project-dashboard.blade.php ENDPATH**/ ?>