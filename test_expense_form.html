<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Expense Form</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .alert { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>Test Expense Form</h1>
    <div id="alerts"></div>
    
    <form id="testExpenseForm">
        <div class="form-group">
            <label for="category_id">Category:</label>
            <select id="category_id" name="category_id" required>
                <option value="">Select Category</option>
                <option value="1">Office Supplies</option>
                <option value="2">Travel & Transportation</option>
                <option value="3">Meals & Entertainment</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="amount">Amount:</label>
            <input type="number" id="amount" name="amount" step="0.01" min="0" required>
        </div>
        
        <div class="form-group">
            <label for="vendor_name">Vendor Name:</label>
            <input type="text" id="vendor_name" name="vendor_name" required>
        </div>
        
        <div class="form-group">
            <label for="bill_date">Date:</label>
            <input type="date" id="bill_date" name="bill_date" required>
        </div>
        
        <div class="form-group">
            <label for="description">Description:</label>
            <textarea id="description" name="description" rows="3"></textarea>
        </div>
        
        <div class="form-group">
            <label for="attachment">Receipt (optional):</label>
            <input type="file" id="attachment" name="attachment" accept="image/*,.pdf,.doc,.docx">
        </div>
        
        <button type="submit">Submit Expense</button>
    </form>

    <script>
        // Set today's date as default
        document.getElementById('bill_date').value = new Date().toISOString().split('T')[0];
        
        document.getElementById('testExpenseForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            // Log form data for debugging
            console.log('Form data:');
            for (let [key, value] of formData.entries()) {
                console.log(key, value);
            }
            
            // You would replace this URL with your actual expense creation endpoint
            const url = '/omx-new-saas/public/expense/store-ajax';
            
            fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': 'your-csrf-token-here' // Replace with actual CSRF token
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message || 'Expense created successfully');
                    this.reset();
                    document.getElementById('bill_date').value = new Date().toISOString().split('T')[0];
                } else {
                    showAlert('error', data.message || 'Error creating expense');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'An error occurred while saving the expense');
            });
        });
        
        function showAlert(type, message) {
            const alertsContainer = document.getElementById('alerts');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass}`;
            alertDiv.textContent = message;
            
            alertsContainer.appendChild(alertDiv);
            
            // Remove alert after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
    </script>
</body>
</html>
