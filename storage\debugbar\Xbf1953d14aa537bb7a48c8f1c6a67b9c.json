{"__meta": {"id": "Xbf1953d14aa537bb7a48c8f1c6a67b9c", "datetime": "2025-07-31 06:23:33", "utime": **********.691014, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943011.465882, "end": **********.691073, "duration": 2.2251908779144287, "duration_str": "2.23s", "measures": [{"label": "Booting", "start": 1753943011.465882, "relative_start": 0, "end": **********.490711, "relative_end": **********.490711, "duration": 2.0248289108276367, "duration_str": "2.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.490749, "relative_start": 2.***************, "end": **********.691099, "relative_end": 2.5987625122070312e-05, "duration": 0.*****************, "duration_str": "200ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nvMonP8bgNRijcmPNB2iB9i5bc8tMPSDIIeqWxPY", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1313746994 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1313746994\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1054644208 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1054644208\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2054125164 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2054125164\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1921557733 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921557733\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2046233080 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2046233080\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-810168384 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:23:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlI2L2w1YTMzUWpkazhKSWRqank2dFE9PSIsInZhbHVlIjoienc1VGNsUFUreStrSGNBRVNZSHB1RmlwaXhkd0o5Q2JucVQxd2JaYmR0YUZhKzJ6V1Q2RHJwS01GYU1DVzRCL0lIa05Wd0VrMytyYm53QlV4UTZmUnhTa0ZkTTFWQTJmeHF1NllnY1NPU1NhdmY4d3EzVDc1MDFLT2ZXUWx4ekxSWU9BRWdLa0UreTQ5SHNTaVpQZE92aDFwMjBCbFJQaHNnTm9pVGE4Mm5EMjM5VTJmNkh1RXBmY1MvZ0VvN3dRV3dLdWtudlpQUDRDVnBod2IwNkZTYkdiczBUeHN4RmJlU1lpdUJzWEFyTjdaWlFCTmswMVJraVBIRENkVTZmbXFMT3BxQ0ZDcFRJN240U3A0bFlnT1ZtVUR1anpZSE5zZXRUMHJ1am5pcTI0WXFJcnBnWE9kUm5DeEJ6VUEvZXVSTHN3MlFaRkxVMXV0SStEVUhOaUtubm9OVUk2VndRK3NZbkxneCs2bzc5Z3M4YWx2NWRiYTBCb1VlRVlheW9LRUZka28vL0Qzb0hXb2VKcklLRGVac0diMlVFQXdFdVFRR2I1RzNvZTU0c21mQm0wTzQrbTRQVmxmS3NpMmhHNS9lcnZWNFpTQmZFekFyc1BCRHhsWlBwWkdVTUlkdFpnZTVKcHdZdVhuTDhMM2lEOG5TRU94YXBwa1lSK0hNTW4iLCJtYWMiOiIyZWRiN2E5NGM5ZGZhNTM2ZGQ0MjgxMWUzNTc3MGE4N2ZkZWI4MDYyNzllNDMxNjFkNWQyOWI1ZWM0NWYwZjlhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:23:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlROaG1LT1RVaHdZK0hLM1crWXl5WXc9PSIsInZhbHVlIjoiTTl6NUgzSkJjaG8xS1RmbUVEUFlrd1Uzb3liSHZPazNIajBmekpiZEZQa3VLU1c1VEFyZDhUMUI4TUl5a2l0QkFQS0hpV1VpVEFjWGxaWVBSMTZPWjEyU25uSml6cE4xRE5JdUxqYmp1N21hcXRrYWVDN21saTdiUy9LRmdTNVdwd3JJT0FrOW90ank0RjFoNG1mOEJac1dtV2pMb0tjWTFUTmRhZWl2SkY2WTRPWTJWWkRmQlJsM0FSWDlWSk9oY2lta0h4UWtCcEZ6RjhseDcwdUgrUXpBTDZDa2M3UmNBdEQ2MnVMRE9RSmhENmlubXZwZDlwaWE4aGljQllRZjhDbnhObUV5ZlBwYU5Yb2d5WVdVQ1F2Wnd5ZXFybzlBVmdDV0I5UjRlS3lIOFJMTzFlZW9jQzM0RjhmRW8rbXJPdVQ2WHZmUmUyUTI4ZDlNUFcvK1lOZGpZOHBEeFlhK2M0YjJXVUtrVjA4VGJiTHdsTy9wZENFZjBFYnFmeUNBUHVaRlF5bDVLTGJsdzAxZUhuWkF1RTFqa1Qrd2NEb0RSbFQrOENIeE41M1l2Ym5BZWxtR3Q1MlJlWWVEbnF4TjlHRG14c1JTdGdQYmtMUHdFbS9keEVVajlNcHkycFI0YTFDbHI5SnF4bDF2TEhNdnlpYmppOGVUYlVQclZOOGEiLCJtYWMiOiJjZDUwNWQ1ZWEwYzI0YjdlMDRiN2RjN2VlZWMwMDFkODAzNGI1YWU4YmNlNGY2M2ZkZDRhZGY3ZTU3NzkzYzgwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:23:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlI2L2w1YTMzUWpkazhKSWRqank2dFE9PSIsInZhbHVlIjoienc1VGNsUFUreStrSGNBRVNZSHB1RmlwaXhkd0o5Q2JucVQxd2JaYmR0YUZhKzJ6V1Q2RHJwS01GYU1DVzRCL0lIa05Wd0VrMytyYm53QlV4UTZmUnhTa0ZkTTFWQTJmeHF1NllnY1NPU1NhdmY4d3EzVDc1MDFLT2ZXUWx4ekxSWU9BRWdLa0UreTQ5SHNTaVpQZE92aDFwMjBCbFJQaHNnTm9pVGE4Mm5EMjM5VTJmNkh1RXBmY1MvZ0VvN3dRV3dLdWtudlpQUDRDVnBod2IwNkZTYkdiczBUeHN4RmJlU1lpdUJzWEFyTjdaWlFCTmswMVJraVBIRENkVTZmbXFMT3BxQ0ZDcFRJN240U3A0bFlnT1ZtVUR1anpZSE5zZXRUMHJ1am5pcTI0WXFJcnBnWE9kUm5DeEJ6VUEvZXVSTHN3MlFaRkxVMXV0SStEVUhOaUtubm9OVUk2VndRK3NZbkxneCs2bzc5Z3M4YWx2NWRiYTBCb1VlRVlheW9LRUZka28vL0Qzb0hXb2VKcklLRGVac0diMlVFQXdFdVFRR2I1RzNvZTU0c21mQm0wTzQrbTRQVmxmS3NpMmhHNS9lcnZWNFpTQmZFekFyc1BCRHhsWlBwWkdVTUlkdFpnZTVKcHdZdVhuTDhMM2lEOG5TRU94YXBwa1lSK0hNTW4iLCJtYWMiOiIyZWRiN2E5NGM5ZGZhNTM2ZGQ0MjgxMWUzNTc3MGE4N2ZkZWI4MDYyNzllNDMxNjFkNWQyOWI1ZWM0NWYwZjlhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:23:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlROaG1LT1RVaHdZK0hLM1crWXl5WXc9PSIsInZhbHVlIjoiTTl6NUgzSkJjaG8xS1RmbUVEUFlrd1Uzb3liSHZPazNIajBmekpiZEZQa3VLU1c1VEFyZDhUMUI4TUl5a2l0QkFQS0hpV1VpVEFjWGxaWVBSMTZPWjEyU25uSml6cE4xRE5JdUxqYmp1N21hcXRrYWVDN21saTdiUy9LRmdTNVdwd3JJT0FrOW90ank0RjFoNG1mOEJac1dtV2pMb0tjWTFUTmRhZWl2SkY2WTRPWTJWWkRmQlJsM0FSWDlWSk9oY2lta0h4UWtCcEZ6RjhseDcwdUgrUXpBTDZDa2M3UmNBdEQ2MnVMRE9RSmhENmlubXZwZDlwaWE4aGljQllRZjhDbnhObUV5ZlBwYU5Yb2d5WVdVQ1F2Wnd5ZXFybzlBVmdDV0I5UjRlS3lIOFJMTzFlZW9jQzM0RjhmRW8rbXJPdVQ2WHZmUmUyUTI4ZDlNUFcvK1lOZGpZOHBEeFlhK2M0YjJXVUtrVjA4VGJiTHdsTy9wZENFZjBFYnFmeUNBUHVaRlF5bDVLTGJsdzAxZUhuWkF1RTFqa1Qrd2NEb0RSbFQrOENIeE41M1l2Ym5BZWxtR3Q1MlJlWWVEbnF4TjlHRG14c1JTdGdQYmtMUHdFbS9keEVVajlNcHkycFI0YTFDbHI5SnF4bDF2TEhNdnlpYmppOGVUYlVQclZOOGEiLCJtYWMiOiJjZDUwNWQ1ZWEwYzI0YjdlMDRiN2RjN2VlZWMwMDFkODAzNGI1YWU4YmNlNGY2M2ZkZDRhZGY3ZTU3NzkzYzgwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:23:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-810168384\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1046706145 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nvMonP8bgNRijcmPNB2iB9i5bc8tMPSDIIeqWxPY</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046706145\", {\"maxDepth\":0})</script>\n"}}