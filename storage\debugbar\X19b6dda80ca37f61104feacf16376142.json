{"__meta": {"id": "X19b6dda80ca37f61104feacf16376142", "datetime": "2025-07-31 06:24:37", "utime": **********.289956, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:24:37] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.281629, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943075.336688, "end": **********.289997, "duration": 1.9533090591430664, "duration_str": "1.95s", "measures": [{"label": "Booting", "start": 1753943075.336688, "relative_start": 0, "end": **********.03586, "relative_end": **********.03586, "duration": 1.699172019958496, "duration_str": "1.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.03588, "relative_start": 1.6991920471191406, "end": **********.290001, "relative_end": 3.814697265625e-06, "duration": 0.2541208267211914, "duration_str": "254ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50618576, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.258373, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.035100000000000006, "accumulated_duration_str": "35.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.129647, "duration": 0.00507, "duration_str": "5.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 14.444}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.186697, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 14.444, "width_percent": 3.504}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.201819, "duration": 0.027370000000000002, "duration_str": "27.37ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 17.949, "width_percent": 77.977}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.235914, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 95.926, "width_percent": 4.074}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1085176662 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1085176662\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1850369638 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1850369638\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1545380098 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1545380098\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1662936590 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InNPU2lBVEg1ZzVNOFYyTGtRc2tzbmc9PSIsInZhbHVlIjoiRE5VTGJiNzI1Qk92RWdaYjR6ZmZQSFhrV1E0OTR1SXh5dUFYcXdZUXh3TkJrWFpxNFRUQ2lNVTFnZ3l2S0ltS1VXOEE2ZVhQQjNEWmVUU3VIMldWbDY0eExVK2ZPajZwcm5oZWxyYTlsY0ZmQk9HSjl1aTRrOG9rNTZzcGJlNXZMdmFPYUFTNEF0U2EzRHhtaEZvc2U5eTNvL291UzRyK0ZGMjdxZk4xYTFJNEdiSFdhWXVqRTdWVlhmOU52bDBYZDY1d1hJc0lLeHQrRXRacUgwY0FaR01kVWJVRGhZTDgvSjd0bE9WVnQrTkZFZUR5Z3pLRmtuOG5hVFhsMG9RNmR5Y0p5TW5KU1BpaWJYNGZjNlh6RDhxcm1NLzgzYjZjQTE5WTEvTzg4RDl5VVFiSE0wT1ZURURPYW4rei83SExGa3NNZ0p5K2kvOWJDMk5lWXVndU9EZUhjUFpRVXhla093UURRRjNQWkJJbGdXcGNOcDJkZFFIMTdVNXg0WUNiMTRRYXc2aEFmaWUrcC9uTG4wMklOZkNPZCtaQmQzeHgrVitlQTJwbDAyVmxjQjR4TlNYeW1tWE1oOUpmNWlraWZ0ODJKQmZMQmpzdEwxcUxzSFFGTjg5b1o0QU5HRHFGZVZJT3BvVXFxNUVaVEYwaWQxTlAxZVJDVzlCUU5Bc24iLCJtYWMiOiJiMWNkZDIwZWQyYmE5MTZkZTc3YjRlNTExYjdiMGZhNTdmYjY0OGJkYzMwOTEwYTM3YmU5MzQ4ODM1NzA0YjE5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ing2VCtqYmMvVUE4RCtOem9tKzNXU3c9PSIsInZhbHVlIjoiQVV1TTl0NW1VeUd0bG9IUG1NTlR0S0hXaURQODB6emYwT3ZOb1pZNWtzaGQ4TEFpbkN6c09OYXhvTE9mUSsxbmFjQnNqSitGR2RkT0x3RUI3b0xTV2pTSUJVSnNzdWRlUVZZR0VFTUVkcU1BZW9Vb2kyeWFIb1F1MlBxaWpXTFhJMTFHQ2RKV0ZobWJ2TGFSdmtKZWhhaHM1S0h2M3lpMDlDa3lLcGxzWkphcnJMZHdoWjh2NWdxSC9hN21QeXFHTUI0czJ3TndSTXkzMlg1RDJXbXdmZm5HYXhwOHVHaEl0NmEybVRCQXo0UGp1UWl3c0VxQ1RmTE16Q0dpQXBuRW1WUXpVVWZXUlVkL09UbGMwQUx2dWo5S0s1Z2psN012YmhjMS9nM2VDOC9ZZ2xjTnZYTnJRdzNHcDA1Y0tqZGs0S1gyY3BldzRYZ1JjSW95RllmVW1RRzJtNE5nd21UZ1pjdzQydWUvdE81RStPZnJHY2lpN1d2Z3p1c3cyTUtYR2U0WGFjRkFVdnhnOTFqQlp1Vlc4bVZwei8zc2ZPSUNicVpxNmZYTEdpWWVkc2pSM1RIZzk5akF3Q3h6aUxzRmFkaUQwWFpLWG1KMHdPV3RxWkQ2ZEIxUFZ6c21YNzFramVDZkRUSCtoTERYNFZRTW5hR3h2VFRqT01UcVJPSG0iLCJtYWMiOiJmOGY5YTIyYTk3MmM3ZjQwMzE1MjZkMWVmM2RkOTMzYTgyNDBhYjM5ZWEyNTMwOTVjYzhlNDk1NGMwZDkyOTI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1662936590\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-715955159 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-715955159\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-575471600 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:24:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5FK0RCZy9JbEhNUmpBUUpRNE45Smc9PSIsInZhbHVlIjoia3BqaWRvNVV3dDdQNE4zVy96TUs3NFppN1V5QllaV0E3SlNNTVV6TGppc0hBZSttY0l4MmlzenR0TDNFdGQxa0kyb1pYbzRTYS9od0J2cVpNWG0wNXJzbDUybWo2SzhnZFJzaWRkTEozSGVVME1IbHB1Q0hzT3A5YzFEWEVMb2diajVQNjlqOWdxcnVxTG9hTXJVT3F5N2MwREpIYWcrVE9SRkNSdnZiTmVSY29rbUJXSGo2azI5aUtjUFpKVUF5WXIzNXRzUGRnSitUN3ljdGVvWmFjcXdBUWdvemdDdU5tNjV4aEwrZzVJLzNUdE9WdStqTzVselN0eEd0WG9FVEp6R3Z6K0wzLzdpUTRmaWlHbWVaMjhEUTRvdmdWZFN4RWI3alArbGxLTGpaUm84WWxzTXVBb1hhUStidXJZcHlCZHowKy81VkpPREpyOGRGVTJVMVhXQWdoSXpXc3RBNkdEUHpGcHp6Y2M1WW1qS3EyYUR0NnEvTXlJQ0xQUnRhcXA4LzJ4dlNRREI3SnExMEFaSVovallUWklWK2RiYlZ3MER0eW9kTXVGVWZlL0RZdmNJV0xHTzlmczdkbkJvZjVEUjQrT1F0dUpaUXp3Z1NQbG5Nbi9PNHR6U0dTYXdzT1R6ZUovZWpUSlpra3VyTHRscmJFN29KZG9GU0hBS3giLCJtYWMiOiJjNWJhYzE3YzkyY2MxYTMzYWY2NWFhYTNiNjY1NmRkNDA0ODk0ZDBhZGMxNDc0YzBhYmUyYzMyN2JmNjA3MmI5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:24:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjFJOHBYZ25CQVdQSG9tbWJwR21CbGc9PSIsInZhbHVlIjoiR3E5WlpSSFUvTlFRNE94UkdvQytLVVA4QUhSMHdVYjR5R2pVYjJMZHEweVZYWDltM1U2NHZVODFFMTU3ekg2RzZQS3pPaUFnakRLUG5MdmczaG43c2pINkw3ZmRrRGYvZGphNGovZjJZN3l5RTYxZC85M3N2L0l2V1F2TkcrWE5pMkt3OXZCcVZoVWJNUEpUSThRaERCdVd3alJuTDN3SmgrdXNPTGdCQzY1eWJ3ZDQwWjU5aEFQbjhoMWwzYVRUVlN3VUd4OTV3bXdUMlA0cnNaVjM2eWhXajBqN1JxcnR4THkyK3RsYXp3MzlrY2NUenBWZ1M4blc3VGpjOWNUck9HWS91Y0pIa1lKdGVmNXFLcmdDd1JlSTE1Q1BlS3FKU2FEekNUN09lQjlVVTBxYzErVVRraTkzNW44UDFDaDliRUs1V1ZLMEpUVTIyUTFHY0ltbHNkOHZoM0c5SzF1LzFXN3dTYjNiV0J6RjZ3M05nWDhaSXRCRWw1UW9tRFFBRnl1WTZNU25oS3Q2YkpsTGtHVGgrb0xqT0c5bk41ay9NUHdoMzlTVnA3YzFsRzdkbmc4b0RTTGxUajFZS2IvS0tXVTdYVUE2VjR2SHBML3l1MXFKQTJyT2Rpb2g3a2tNYlNQWERlWWVUZWo2eFUyWjhQZ280SDZhbVlxNTJkcHciLCJtYWMiOiIwNTM5YTE1NDU4NzYyZWQ0ODQxYmU4OTE4OGRhZTQ2YzUzNDZjNmYxYzZlYmIzNWM5NzVlMDQ5YTgwMDRjMjQwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:24:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5FK0RCZy9JbEhNUmpBUUpRNE45Smc9PSIsInZhbHVlIjoia3BqaWRvNVV3dDdQNE4zVy96TUs3NFppN1V5QllaV0E3SlNNTVV6TGppc0hBZSttY0l4MmlzenR0TDNFdGQxa0kyb1pYbzRTYS9od0J2cVpNWG0wNXJzbDUybWo2SzhnZFJzaWRkTEozSGVVME1IbHB1Q0hzT3A5YzFEWEVMb2diajVQNjlqOWdxcnVxTG9hTXJVT3F5N2MwREpIYWcrVE9SRkNSdnZiTmVSY29rbUJXSGo2azI5aUtjUFpKVUF5WXIzNXRzUGRnSitUN3ljdGVvWmFjcXdBUWdvemdDdU5tNjV4aEwrZzVJLzNUdE9WdStqTzVselN0eEd0WG9FVEp6R3Z6K0wzLzdpUTRmaWlHbWVaMjhEUTRvdmdWZFN4RWI3alArbGxLTGpaUm84WWxzTXVBb1hhUStidXJZcHlCZHowKy81VkpPREpyOGRGVTJVMVhXQWdoSXpXc3RBNkdEUHpGcHp6Y2M1WW1qS3EyYUR0NnEvTXlJQ0xQUnRhcXA4LzJ4dlNRREI3SnExMEFaSVovallUWklWK2RiYlZ3MER0eW9kTXVGVWZlL0RZdmNJV0xHTzlmczdkbkJvZjVEUjQrT1F0dUpaUXp3Z1NQbG5Nbi9PNHR6U0dTYXdzT1R6ZUovZWpUSlpra3VyTHRscmJFN29KZG9GU0hBS3giLCJtYWMiOiJjNWJhYzE3YzkyY2MxYTMzYWY2NWFhYTNiNjY1NmRkNDA0ODk0ZDBhZGMxNDc0YzBhYmUyYzMyN2JmNjA3MmI5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:24:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjFJOHBYZ25CQVdQSG9tbWJwR21CbGc9PSIsInZhbHVlIjoiR3E5WlpSSFUvTlFRNE94UkdvQytLVVA4QUhSMHdVYjR5R2pVYjJMZHEweVZYWDltM1U2NHZVODFFMTU3ekg2RzZQS3pPaUFnakRLUG5MdmczaG43c2pINkw3ZmRrRGYvZGphNGovZjJZN3l5RTYxZC85M3N2L0l2V1F2TkcrWE5pMkt3OXZCcVZoVWJNUEpUSThRaERCdVd3alJuTDN3SmgrdXNPTGdCQzY1eWJ3ZDQwWjU5aEFQbjhoMWwzYVRUVlN3VUd4OTV3bXdUMlA0cnNaVjM2eWhXajBqN1JxcnR4THkyK3RsYXp3MzlrY2NUenBWZ1M4blc3VGpjOWNUck9HWS91Y0pIa1lKdGVmNXFLcmdDd1JlSTE1Q1BlS3FKU2FEekNUN09lQjlVVTBxYzErVVRraTkzNW44UDFDaDliRUs1V1ZLMEpUVTIyUTFHY0ltbHNkOHZoM0c5SzF1LzFXN3dTYjNiV0J6RjZ3M05nWDhaSXRCRWw1UW9tRFFBRnl1WTZNU25oS3Q2YkpsTGtHVGgrb0xqT0c5bk41ay9NUHdoMzlTVnA3YzFsRzdkbmc4b0RTTGxUajFZS2IvS0tXVTdYVUE2VjR2SHBML3l1MXFKQTJyT2Rpb2g3a2tNYlNQWERlWWVUZWo2eFUyWjhQZ280SDZhbVlxNTJkcHciLCJtYWMiOiIwNTM5YTE1NDU4NzYyZWQ0ODQxYmU4OTE4OGRhZTQ2YzUzNDZjNmYxYzZlYmIzNWM5NzVlMDQ5YTgwMDRjMjQwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:24:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-575471600\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1156614131 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1156614131\", {\"maxDepth\":0})</script>\n"}}