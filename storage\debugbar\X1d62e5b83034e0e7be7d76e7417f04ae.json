{"__meta": {"id": "X1d62e5b83034e0e7be7d76e7417f04ae", "datetime": "2025-07-31 05:37:23", "utime": **********.753834, "method": "GET", "uri": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940242.153148, "end": **********.753906, "duration": 1.6007580757141113, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": 1753940242.153148, "relative_start": 0, "end": **********.596917, "relative_end": **********.596917, "duration": 1.4437689781188965, "duration_str": "1.44s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.596946, "relative_start": 1.****************, "end": **********.753912, "relative_end": 5.9604644775390625e-06, "duration": 0.***************, "duration_str": "157ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eDIddVIG1ueGYpmeYaIdlmskjcaJ4pWKnVkaOZQq", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "status_code": "<pre class=sf-dump id=sf-dump-2007694620 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2007694620\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2047564204 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2047564204\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1701493437 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1701493437\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2044117886 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2044117886\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-853952662 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-853952662\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2147128816 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:37:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlIramgwR3ZEajlzdngyakk2ZzdaalE9PSIsInZhbHVlIjoiTUg1d1ZOaGVHQ1RuSlhEc3I3ek5UeWZnZ2xVdjBDekZrMXNXd1ByUW50SkI2dnNwZ0doZE1NWTljZVRZdG0vbzFVKzh1ZjFHL0VzUm96TGFwaGpWeVBFRnNFTTlPbDJ0MytiajVGWVpQY2Jxa0U5TFRKNFBkYWxKU3lGNjRBSG53L29TR1BGWXdVcnFqZTg5cmN5ZHN0d0h5ajRWVnV1R0lUcC9vSVNDeXZZYVhrTXpFMndrRE45MHJ6eEFHRVJFRnlTSUZPeWZpOWoyOVVybXJlblpHakFJRndMd3VweEkyR0RydUpKdHJxM1QyWDJEQlluR09NNTB1YXVDY3RCWWxVMVMyUithMEFiUzJ0bS9UclJNeWJOYmgxNm9KOHV2QVRsSkJ5VUpBZlVHQ3g5NlYyYXdtaTJHNUYyckFDTkorZDJmR3FYeWZCZFJvNEcyNExUWURsNjZyYWN5U2lJbW1lUUJzdUNiblZFRWMyWEk0NVJ4SUdCeXV3VzJ2SmpvTUw4NzdoSFhmQWhYK2FUd0luZmYvZ0U1ZVdvKzJDUUtmYVpqR1RQYVY0eE42MkRwMTlYMkNCOWd6enFSVHNwUTRjQVVlZDNMR3l2MFJBcXBZQjhTNmFaNVlmcG5MMkdGTW5oVTdxMzNHZ2dVbzhXUmtYWnNDTTJZUFFyemxXdHYiLCJtYWMiOiJlMzY5NjM5ZDJkNmY3MGI2ZDY2NTA3NmI1ZWYwMTA1Zjk2ZGJlNmUwOWQ2Y2MyYTUzOTI0MDhmNjc5ZDE4ODAwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:37:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjVzUHNPZmlZUnVvQjRYNHR2T2xCakE9PSIsInZhbHVlIjoiYXNQZko0eEptcVBJbldraGRCaERaSDlucjBEc2J1bjNVQWJpQU5kaTdWS3M3TmduZ0Voekt6aDN1anNVOERqTUZaZktyWk9VL1NVN25INzNGeko3dkZkc2h5ZTBTR0k2V1VHakZ6MmtrVXBwdzYxajJJZWdBMkh2N1pSR0ZRWVhqWUYwS2FCOWg0UjdQMEtGMmRaRmVSMnlBZTFETXRJYkZWSWpLK2FHRDI1WkNURVJ3UlRTb3B4TDk3MjNvSFlHUFR2WUEvcmg1Y2djc3ZQd2NhUFM3TlhMaFM1YXNyaGk3VFQ4V3FNUjRRYzlReWlrbWpoMDY1ZDlCVWJWeVRGbVcyOXNWRi9VdlRCa3JBMEl4c3lUeHZhVi95WDNnUisyVUJIaGxmZXUxaVZmbGtCd1VDcjlZZlhxeDl0Qkl5ekQ1WHBncmdmc3Y5VGFSeTBOcFNUNGpTY0s1OWVBQ2t4TUtyNHVwOHFwT0VZZlVsNUZVendEelBCdUxxMTZkdURFOCs2cHdJWTA2dXhUc01SM1dwajh2dUZYU2FueGdmaDV0ODMrQ0hJNXZKZVplbU9MOTFoSHIrSUswT25zbzJENW1PclI2dEFlZU5qUWhiMXBCK2xxUDYyYU5XcTZHNjhrNlNIR3VCYlc0TFkzQnlucVdJR2VuaW5FL2pqdXdMZUciLCJtYWMiOiJkYjI0MWQ3M2IwN2FjMzRkNzRhNWJiY2MwNTEzZjM3YTIwYmRiMjVlZjFlYmM2ZWRhYTZjMmNlZjY4MmNhODBiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:37:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlIramgwR3ZEajlzdngyakk2ZzdaalE9PSIsInZhbHVlIjoiTUg1d1ZOaGVHQ1RuSlhEc3I3ek5UeWZnZ2xVdjBDekZrMXNXd1ByUW50SkI2dnNwZ0doZE1NWTljZVRZdG0vbzFVKzh1ZjFHL0VzUm96TGFwaGpWeVBFRnNFTTlPbDJ0MytiajVGWVpQY2Jxa0U5TFRKNFBkYWxKU3lGNjRBSG53L29TR1BGWXdVcnFqZTg5cmN5ZHN0d0h5ajRWVnV1R0lUcC9vSVNDeXZZYVhrTXpFMndrRE45MHJ6eEFHRVJFRnlTSUZPeWZpOWoyOVVybXJlblpHakFJRndMd3VweEkyR0RydUpKdHJxM1QyWDJEQlluR09NNTB1YXVDY3RCWWxVMVMyUithMEFiUzJ0bS9UclJNeWJOYmgxNm9KOHV2QVRsSkJ5VUpBZlVHQ3g5NlYyYXdtaTJHNUYyckFDTkorZDJmR3FYeWZCZFJvNEcyNExUWURsNjZyYWN5U2lJbW1lUUJzdUNiblZFRWMyWEk0NVJ4SUdCeXV3VzJ2SmpvTUw4NzdoSFhmQWhYK2FUd0luZmYvZ0U1ZVdvKzJDUUtmYVpqR1RQYVY0eE42MkRwMTlYMkNCOWd6enFSVHNwUTRjQVVlZDNMR3l2MFJBcXBZQjhTNmFaNVlmcG5MMkdGTW5oVTdxMzNHZ2dVbzhXUmtYWnNDTTJZUFFyemxXdHYiLCJtYWMiOiJlMzY5NjM5ZDJkNmY3MGI2ZDY2NTA3NmI1ZWYwMTA1Zjk2ZGJlNmUwOWQ2Y2MyYTUzOTI0MDhmNjc5ZDE4ODAwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:37:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjVzUHNPZmlZUnVvQjRYNHR2T2xCakE9PSIsInZhbHVlIjoiYXNQZko0eEptcVBJbldraGRCaERaSDlucjBEc2J1bjNVQWJpQU5kaTdWS3M3TmduZ0Voekt6aDN1anNVOERqTUZaZktyWk9VL1NVN25INzNGeko3dkZkc2h5ZTBTR0k2V1VHakZ6MmtrVXBwdzYxajJJZWdBMkh2N1pSR0ZRWVhqWUYwS2FCOWg0UjdQMEtGMmRaRmVSMnlBZTFETXRJYkZWSWpLK2FHRDI1WkNURVJ3UlRTb3B4TDk3MjNvSFlHUFR2WUEvcmg1Y2djc3ZQd2NhUFM3TlhMaFM1YXNyaGk3VFQ4V3FNUjRRYzlReWlrbWpoMDY1ZDlCVWJWeVRGbVcyOXNWRi9VdlRCa3JBMEl4c3lUeHZhVi95WDNnUisyVUJIaGxmZXUxaVZmbGtCd1VDcjlZZlhxeDl0Qkl5ekQ1WHBncmdmc3Y5VGFSeTBOcFNUNGpTY0s1OWVBQ2t4TUtyNHVwOHFwT0VZZlVsNUZVendEelBCdUxxMTZkdURFOCs2cHdJWTA2dXhUc01SM1dwajh2dUZYU2FueGdmaDV0ODMrQ0hJNXZKZVplbU9MOTFoSHIrSUswT25zbzJENW1PclI2dEFlZU5qUWhiMXBCK2xxUDYyYU5XcTZHNjhrNlNIR3VCYlc0TFkzQnlucVdJR2VuaW5FL2pqdXdMZUciLCJtYWMiOiJkYjI0MWQ3M2IwN2FjMzRkNzRhNWJiY2MwNTEzZjM3YTIwYmRiMjVlZjFlYmM2ZWRhYTZjMmNlZjY4MmNhODBiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:37:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2147128816\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-188796606 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eDIddVIG1ueGYpmeYaIdlmskjcaJ4pWKnVkaOZQq</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-188796606\", {\"maxDepth\":0})</script>\n"}}