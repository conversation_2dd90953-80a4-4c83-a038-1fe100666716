{"__meta": {"id": "X2577186881a91becf837729e4b600914", "datetime": "2025-07-31 05:34:37", "utime": **********.867429, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:34:37] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.860911, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753940076.143369, "end": **********.867467, "duration": 1.7240979671478271, "duration_str": "1.72s", "measures": [{"label": "Booting", "start": 1753940076.143369, "relative_start": 0, "end": **********.666401, "relative_end": **********.666401, "duration": 1.5230319499969482, "duration_str": "1.52s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.666425, "relative_start": 1.5230560302734375, "end": **********.86747, "relative_end": 3.0994415283203125e-06, "duration": 0.20104503631591797, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45914952, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00857, "accumulated_duration_str": "8.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.765614, "duration": 0.0057599999999999995, "duration_str": "5.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 67.211}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.811348, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 67.211, "width_percent": 14.702}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.82565, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 81.914, "width_percent": 9.802}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.832388, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 91.715, "width_percent": 8.285}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-203115703 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-203115703\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-972328799 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-972328799\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-102953748 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-102953748\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1528662821 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkhzSUlUa0VUUVJlb1VMSDcvUDlaaVE9PSIsInZhbHVlIjoiWE12a296Zyt5N1dGMEhvbkdIVDZUWW9mRDZoZmx4N0sxT0JLRzZiSW9lUG5sVU5JeFl2dmdUbU5FeW1WbnlnMXVlM2Zid2wrc2VlZHBLckRtaEx4YkZ0bWJDOHc4NjUrWjMrc2NzOFNSdHk5S2FEb2hYUmd2SU91eEpxUjUraHhkc3dKTlpLaUk3dGxzbE1iQUtOWUVnN3RWd3ozSlRKZTBkd2ZRZjBOV1k5WDVEcmlxVlliU1hBMmpxNGp5QnM3L1BuQWVYbXFkY2hWSHBXZXc4S2xzTk53MFNTYXk1Uk9zRUtiUDU5WkRTQSswdmlVZ09MS2NJMndDRWZjQzlKbG56T0hyTHdzbzF0QVFNSnROOWNLeVE3c2p6ZU8xT2VpcStZNUJhakVIZU9talJXRkZnZ2VncUpQVldjNHc0QWdFMXpCZUdsVndRbWVaSkcydUswWWZFMmVmT1ZBcWdocHVtZERxNmkxMEgzVXd2UHIybEUvZ2lyaXlBMGRjc2ZXV1RCV000MjdLOW5nWGltWXg2clJXWHQ4Z0tmRGVOdzk5aUlCd2dBbFE3bkQxNFdDVXJ6cWQ0S2NVd2hvU0F1aEpabG5LWDBxenNKNzhYVVRWQkN2cHNPdmRjTmZFbVhLbkRiVnNKbzU5M3p4THpPb0VndDNGTlhLTFZrWlBzWEUiLCJtYWMiOiI5NTZkZjliODI4N2JmYzJjZDczZmI2OWY5MTliN2Q4MDk5OTI4NTAwOTMxNGE5NWM2ODMzMDM2Yjc3MWZiYmFiIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkU1alM3c3ppcVFiMmk0OTZRZ2lFeWc9PSIsInZhbHVlIjoiSmIwY0FkbjNvWnJKNzVFT05ZdlVzUUpQS0NWMnJzWE5aaUx4WG55Um80OUZVMWhPUGkzZUtlWHZYaXpFdi82ajQ3Mk5yclVQVDZ6YXNMWS93MW02MEtTY1BBTTdzQURxVUdVZkJUMzdvaWRub0JWQjFsRkpWYjNJSFlETWJjQ0MzVnNyRUlVMGRRV1c5eWpmSnAvMmtKOFgzU0NobXFPQ1dmbktYTkhXQlZSMEpDaWVUREY5N2paT3BCS3l3QzhTUS85anFFanFNNk1KMDdaK2lmbkU3VUMrb3o1YUJEWWVnTVRGR2dyRzhoOUU4L2VaZnNPaGVRUStJRXBHK0ZXMnpud3R5empZbzRPTWNrWE54YytZRmp6b3hEQjlEaS91NHRwdEJScloyWnB1SFZhZUdsUEQ2RFYwaUNWeXFWMEloT1hBUDM5MEV2cW9BNlBvWjNWRzdXVmhWZU8vTnNrNXVVOC9XVHZseDVubmN6Ty9CQnlNaUlMN0dMNk5rRURIOWJTTUNsT0hKRUphLzRCMW1mOFZDWjYxdm5mVTVZM2FSUkxsWnI4Szh0VjE2UHR4NU9Ca2JNNGJoZ0MzWWRzVTZKSDRmaTZzQlBIblBMMlI2alNhQW83RS9rLzhoaUFjd0Q1Q2Y4Y1ZSQ3FNUXorcE1DLzM2RXNFRll2NkIzdGsiLCJtYWMiOiI2N2NhZTk3MjA5ZTdjZjkzMmNlNGNiMmJlOWQxOTE3ODU2ZmY2MWYwNTYxZTkyOTMzYzc4N2FiYjIzNWYxMmUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1528662821\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1769853735 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lOWuOQi5K5zalKq6lxC9bmXS2G5Yh3URND5pGg9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1769853735\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1117126642 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:34:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNWYVk3Z2x2aWVEK3hVcXI1b2Fyb1E9PSIsInZhbHVlIjoiL1FZb0RMUllFYStVbm1xUkdTRC9Jd0YwQm1FdWpnVXM5c0Q5WWNoaUsvVUtYWmtVd1U5NTFOSVJHbWhlaWRFRGVuOTEzYXFBcjExLzgxS1E2bXNrL2J1OVhBaUZhTG9ybm5QUTlmRjB5ek9CSnlJSkpGVlQxZlpPWUZIWHJPM1JTWDEzQVRjRFRDTzJQUGROZSsySnI4MVh1TnhJaXphbjdDTURaZ2diK2djQ0h3QWhYZDlXWXZrTysxQy82OE1SMHZ5UjM1Tll2cVhSRTFYQlAxMVJiZWVaWHZuaVFPUVNIZVZSZFBFNlMwREkyYmxzalMwekliWDZtTmtRK0ZoZFBmbXlMZUhHTXZtMWRhYno2T0V0SHcxekJiNU1wQ0g5M1ZZcVJOK0dGZ0owSGMyWXFYMWY4WmtnQnNodWdsQlBXK3V1TXR3YzNvMlRCVUJ1YWNEZHFQbFpKUzZkaktUQ3ZmOFJZWmdqNlpVaXB2WVoyVkFXTkNuMXkvQ2R0alRrU0FYWWpqWTQ1RmUrOGJQejVtb2wxMjNldGdjWmE3OTA5UHh1TDlpKzlpNmw4MHR4ZlpVVkhPRmJYMW5DM2d5SlYrVERJMWhjeUsybkNqdnFMRkZGYzFKYXd6dUNDbWVHVUZxSWdCSVlEclpjaDF6NEo3NkVUTnU4UUNyK0N2YnUiLCJtYWMiOiJjODE5ZDdjYzgwYmZiNTFjYTVkMGFkYjViNjViZDc0OWZjNTY2NjkxZjQxY2ZhZWEzNDViMTY4YmYwMTI3MTdiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImpLTHN4cGJVL2FTK3dkNUF0TkVLZ0E9PSIsInZhbHVlIjoiMlZDemNIRzZwUnRSZEtGdHJuMXFaMUJxdXdUTVJBcmN4anhYQWVJVkNheWpBeU5aZzJ6M016bnpRWC9LYXptUjUreXVtQ28rc25xc1o0NXkzKzRlaEN1d293SVpUQkVub0JUcVFyWjRLZFdYOVltMHg3S2lrY0tFT0hPTEFGelNBblJ1R0M5Q1gvUnkveDFsUVVHSFBSNHBuOFM4dGNIZ0ZSYkxnRHpDRXZrUmN2SVQ5NTdEckFpcFZOQzBXcUp6SWNPY1kybmszMVBCWmJSLzBKTHJLWnFOTWhZSGU2b0hUVVJqWlRQdmxlcjdxdGd4cHcwRVovbmQwQTFVdzRleTkzcEVPMjN4SFNhYjZuamM2aXcycjJwMW1qc2FaZmFNcVdjUEI0OWQwdk1CdXo1VVNPdTMzY1hpM1NsMW92L29EYklsTUJhdjdaUy9KZFFndWdXNGpjRlJSeElGV3NpYmVVRlVZdFZDNjZwdDdJeUlJQSt5dDcwMC8rd1d5ekY0SGVRUVF2UGVvWkhGUzYySk1YNmpUeUplYjgramk0MkRSM1FXUUJMNVVXSEo2cEtUNmZXZWxqVmJPRStaanRpUkhoekxEMEdUaXhvNEREM3pHcW5adzR1bmlOSy9wM3RrYzZjUGRaOVoyTHg2bWxxOCt2eXNsUkZMQS9RQkNPdUciLCJtYWMiOiI4OWQ0MGQxODUwMDM0YjI1ZDhiOTJlMTNiY2JmMzgzMjIwN2RmMTBlZWE5ZGJmOGFkY2ZkYTMwZWE3OGVkYzc0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNWYVk3Z2x2aWVEK3hVcXI1b2Fyb1E9PSIsInZhbHVlIjoiL1FZb0RMUllFYStVbm1xUkdTRC9Jd0YwQm1FdWpnVXM5c0Q5WWNoaUsvVUtYWmtVd1U5NTFOSVJHbWhlaWRFRGVuOTEzYXFBcjExLzgxS1E2bXNrL2J1OVhBaUZhTG9ybm5QUTlmRjB5ek9CSnlJSkpGVlQxZlpPWUZIWHJPM1JTWDEzQVRjRFRDTzJQUGROZSsySnI4MVh1TnhJaXphbjdDTURaZ2diK2djQ0h3QWhYZDlXWXZrTysxQy82OE1SMHZ5UjM1Tll2cVhSRTFYQlAxMVJiZWVaWHZuaVFPUVNIZVZSZFBFNlMwREkyYmxzalMwekliWDZtTmtRK0ZoZFBmbXlMZUhHTXZtMWRhYno2T0V0SHcxekJiNU1wQ0g5M1ZZcVJOK0dGZ0owSGMyWXFYMWY4WmtnQnNodWdsQlBXK3V1TXR3YzNvMlRCVUJ1YWNEZHFQbFpKUzZkaktUQ3ZmOFJZWmdqNlpVaXB2WVoyVkFXTkNuMXkvQ2R0alRrU0FYWWpqWTQ1RmUrOGJQejVtb2wxMjNldGdjWmE3OTA5UHh1TDlpKzlpNmw4MHR4ZlpVVkhPRmJYMW5DM2d5SlYrVERJMWhjeUsybkNqdnFMRkZGYzFKYXd6dUNDbWVHVUZxSWdCSVlEclpjaDF6NEo3NkVUTnU4UUNyK0N2YnUiLCJtYWMiOiJjODE5ZDdjYzgwYmZiNTFjYTVkMGFkYjViNjViZDc0OWZjNTY2NjkxZjQxY2ZhZWEzNDViMTY4YmYwMTI3MTdiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImpLTHN4cGJVL2FTK3dkNUF0TkVLZ0E9PSIsInZhbHVlIjoiMlZDemNIRzZwUnRSZEtGdHJuMXFaMUJxdXdUTVJBcmN4anhYQWVJVkNheWpBeU5aZzJ6M016bnpRWC9LYXptUjUreXVtQ28rc25xc1o0NXkzKzRlaEN1d293SVpUQkVub0JUcVFyWjRLZFdYOVltMHg3S2lrY0tFT0hPTEFGelNBblJ1R0M5Q1gvUnkveDFsUVVHSFBSNHBuOFM4dGNIZ0ZSYkxnRHpDRXZrUmN2SVQ5NTdEckFpcFZOQzBXcUp6SWNPY1kybmszMVBCWmJSLzBKTHJLWnFOTWhZSGU2b0hUVVJqWlRQdmxlcjdxdGd4cHcwRVovbmQwQTFVdzRleTkzcEVPMjN4SFNhYjZuamM2aXcycjJwMW1qc2FaZmFNcVdjUEI0OWQwdk1CdXo1VVNPdTMzY1hpM1NsMW92L29EYklsTUJhdjdaUy9KZFFndWdXNGpjRlJSeElGV3NpYmVVRlVZdFZDNjZwdDdJeUlJQSt5dDcwMC8rd1d5ekY0SGVRUVF2UGVvWkhGUzYySk1YNmpUeUplYjgramk0MkRSM1FXUUJMNVVXSEo2cEtUNmZXZWxqVmJPRStaanRpUkhoekxEMEdUaXhvNEREM3pHcW5adzR1bmlOSy9wM3RrYzZjUGRaOVoyTHg2bWxxOCt2eXNsUkZMQS9RQkNPdUciLCJtYWMiOiI4OWQ0MGQxODUwMDM0YjI1ZDhiOTJlMTNiY2JmMzgzMjIwN2RmMTBlZWE5ZGJmOGFkY2ZkYTMwZWE3OGVkYzc0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1117126642\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-223170491 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-223170491\", {\"maxDepth\":0})</script>\n"}}