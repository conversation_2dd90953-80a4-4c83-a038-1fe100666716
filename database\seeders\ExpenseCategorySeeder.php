<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ProductServiceCategory;
use App\Models\User;

class ExpenseCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get all users to create categories for each
        $users = User::all();

        $defaultCategories = [
            'Office Supplies',
            'Travel & Transportation',
            'Meals & Entertainment',
            'Utilities',
            'Marketing & Advertising',
            'Professional Services',
            'Equipment & Software',
            'Miscellaneous'
        ];

        foreach ($users as $user) {
            foreach ($defaultCategories as $categoryName) {
                // Check if category already exists for this user
                $exists = ProductServiceCategory::where('name', $categoryName)
                    ->where('type', 'expense')
                    ->where('created_by', $user->creatorId())
                    ->exists();

                if (!$exists) {
                    ProductServiceCategory::create([
                        'name' => $categoryName,
                        'color' => '#fc544b',
                        'type' => 'expense',
                        'chart_account_id' => 0,
                        'created_by' => $user->creatorId()
                    ]);
                }
            }
        }

        $this->command->info('Expense categories seeded successfully!');
    }
}
