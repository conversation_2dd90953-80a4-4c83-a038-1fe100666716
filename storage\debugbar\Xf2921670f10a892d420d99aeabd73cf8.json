{"__meta": {"id": "Xf2921670f10a892d420d99aeabd73cf8", "datetime": "2025-07-31 05:10:57", "utime": **********.404703, "method": "GET", "uri": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753938656.597383, "end": **********.404728, "duration": 0.807344913482666, "duration_str": "807ms", "measures": [{"label": "Booting", "start": 1753938656.597383, "relative_start": 0, "end": **********.332199, "relative_end": **********.332199, "duration": 0.7348160743713379, "duration_str": "735ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.332218, "relative_start": 0.****************, "end": **********.40473, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "72.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "RNyX9WCN4reYcT0DCZ4OL8AMB8ycAJl0mxP1oS1E", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "status_code": "<pre class=sf-dump id=sf-dump-53072837 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-53072837\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-932056833 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-932056833\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-330798334 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-330798334\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-799814773 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799814773\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1632394013 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1632394013\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-432934481 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:10:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNmRlJrZkovOThpcFNsdTVPUnhHR1E9PSIsInZhbHVlIjoiSlBWWXFpak42RFQ0NERiL0R0azFxVVVLbVFDVmVXaUJxMDhGUHpGVXdFS1c2Y2FQZlpRR2NWcER0OS9hT3oxT0lROHROOTJuVVpTNW40QnpQVWQ0dnZ0SkNrU3VIWllsaVlYa0VUTktoVTJWUEErTk9YK25ieTFrVXpTa1VBQUdsLzZiOVNCWUwwNnRSR3lOMFRFMW9EdWg1WjZNZzJYTmxVWWFzMlp2aFhDdElZME8vcUlMeWFqRHdDV3JOcEoxbVpUTTVtdkZFN0cwYS9Zd3NvOVRydEh2R2dqN2JDaEZWTDNVaVc4bTgyOHo2NEtxalU3dUp0aS8wMGpmUmcrMXlEcFZmR1lHdWh0Qm1zRWw4U3lUd3RWRlkrQmZEVG52eEV1NjlsZHBnQzRPYXNMWFFVM2tpdXYxWENGSFY3MlBMejcyZG1TYnRVYmxMRFZpalkybzlFYXBYU2hGNExaQlBlNFV0am5HQ3Q2N1A2ZGNyYUxtdGVVaXYvcXk3d3lmaGNZdkU2MXRFb0k1V2ZuWGxQVXJYM3lPdndmeTdRYlN3YnVXaUlwSXM0RTYzNTJvYzQyM3ZHamowdUxYR0Zrbm5obkJFSzdPL0ZRVk1PejRFUTNKRzJXQkdiQVdFU2VpSVJzemdHSUN0alh5b2xlSGE5OFUvQkczbXcxMHR3bjciLCJtYWMiOiI3ZDFhYWE4NGUyMjQzMWM3MGQzMThkMDI0ZjhlMTlkOWYxYmE5Zjk4M2Q1ODMxMTVlYTE1MzQwYjY3YzI4M2UwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:10:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImdLa3N6WUpHOVlqQVllZGp4ZjZmR0E9PSIsInZhbHVlIjoiUTFBcldSaDB6aHhFd1Vwek1MY3FGUEorS3ZTZzVqTXFrby9Ud1IrOWFvVmVUSW1JU1NUS2JNS0pXZThpa3o2dmN1a2N6eHJTSVc4ODE5MlJuQlBZUHkzWVJmWmVjaVhtbVV3OW5BUVdSRmtuMUlIMlVjdCtLK0pJb1FxdFU5ZVlJRW9vbklMN3ptRmRwbldWMmFCZ3N1L3lTakFUMnkzS2wvQlVVL3VFdUxxZzRYak43SnBYL1ZYYTRIOUZHSklvRTc4ZVZJNEZVSGlEeExFK2J4THM1dk5mUkdPaW5PUzU1bGs3WTloQUdNTU53SUVHS3Q3TEoyaFR0NmJ0WmRucGJFQlhlYTU0aTJvWUtIazJ3bG9nWDBCdG4yRWNZblZEQmk3eHg2UDVpM2xNN21pN3pNSW16YW5KYTBabFR5TEgwMTljY0drQ3VrUTFtWVpjbG5qRFpkTlRhMnQ2RmFBNTBIb2xyUU1yeGxoNDRPOFFTWFJhbWNTSEF5SDl1Ri9FNzFtWjhrM0lnRnI2SVBzaTlpYzZHak1EejZSUS90RFhPaGxjYTl2N1JjRnNWdXJyaUY0MytnUFVsWlB2ZVpCN1p0OFMwZjVUcXdoT2UzaEI5YWh5K0RWMHIzWFgvMVpsRUtJL090dERTbjhnL3YyVi9yRG9ua0JzbHVGUUF6OXUiLCJtYWMiOiIxYWVkN2FlM2I1Njg4MjIyZTQzOTIxNDg5MzAwNTljYTM0ZjRkNDIyZGY5M2M4M2FhMWQyMDY0Yjc4ZGQwNmM5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:10:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNmRlJrZkovOThpcFNsdTVPUnhHR1E9PSIsInZhbHVlIjoiSlBWWXFpak42RFQ0NERiL0R0azFxVVVLbVFDVmVXaUJxMDhGUHpGVXdFS1c2Y2FQZlpRR2NWcER0OS9hT3oxT0lROHROOTJuVVpTNW40QnpQVWQ0dnZ0SkNrU3VIWllsaVlYa0VUTktoVTJWUEErTk9YK25ieTFrVXpTa1VBQUdsLzZiOVNCWUwwNnRSR3lOMFRFMW9EdWg1WjZNZzJYTmxVWWFzMlp2aFhDdElZME8vcUlMeWFqRHdDV3JOcEoxbVpUTTVtdkZFN0cwYS9Zd3NvOVRydEh2R2dqN2JDaEZWTDNVaVc4bTgyOHo2NEtxalU3dUp0aS8wMGpmUmcrMXlEcFZmR1lHdWh0Qm1zRWw4U3lUd3RWRlkrQmZEVG52eEV1NjlsZHBnQzRPYXNMWFFVM2tpdXYxWENGSFY3MlBMejcyZG1TYnRVYmxMRFZpalkybzlFYXBYU2hGNExaQlBlNFV0am5HQ3Q2N1A2ZGNyYUxtdGVVaXYvcXk3d3lmaGNZdkU2MXRFb0k1V2ZuWGxQVXJYM3lPdndmeTdRYlN3YnVXaUlwSXM0RTYzNTJvYzQyM3ZHamowdUxYR0Zrbm5obkJFSzdPL0ZRVk1PejRFUTNKRzJXQkdiQVdFU2VpSVJzemdHSUN0alh5b2xlSGE5OFUvQkczbXcxMHR3bjciLCJtYWMiOiI3ZDFhYWE4NGUyMjQzMWM3MGQzMThkMDI0ZjhlMTlkOWYxYmE5Zjk4M2Q1ODMxMTVlYTE1MzQwYjY3YzI4M2UwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:10:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImdLa3N6WUpHOVlqQVllZGp4ZjZmR0E9PSIsInZhbHVlIjoiUTFBcldSaDB6aHhFd1Vwek1MY3FGUEorS3ZTZzVqTXFrby9Ud1IrOWFvVmVUSW1JU1NUS2JNS0pXZThpa3o2dmN1a2N6eHJTSVc4ODE5MlJuQlBZUHkzWVJmWmVjaVhtbVV3OW5BUVdSRmtuMUlIMlVjdCtLK0pJb1FxdFU5ZVlJRW9vbklMN3ptRmRwbldWMmFCZ3N1L3lTakFUMnkzS2wvQlVVL3VFdUxxZzRYak43SnBYL1ZYYTRIOUZHSklvRTc4ZVZJNEZVSGlEeExFK2J4THM1dk5mUkdPaW5PUzU1bGs3WTloQUdNTU53SUVHS3Q3TEoyaFR0NmJ0WmRucGJFQlhlYTU0aTJvWUtIazJ3bG9nWDBCdG4yRWNZblZEQmk3eHg2UDVpM2xNN21pN3pNSW16YW5KYTBabFR5TEgwMTljY0drQ3VrUTFtWVpjbG5qRFpkTlRhMnQ2RmFBNTBIb2xyUU1yeGxoNDRPOFFTWFJhbWNTSEF5SDl1Ri9FNzFtWjhrM0lnRnI2SVBzaTlpYzZHak1EejZSUS90RFhPaGxjYTl2N1JjRnNWdXJyaUY0MytnUFVsWlB2ZVpCN1p0OFMwZjVUcXdoT2UzaEI5YWh5K0RWMHIzWFgvMVpsRUtJL090dERTbjhnL3YyVi9yRG9ua0JzbHVGUUF6OXUiLCJtYWMiOiIxYWVkN2FlM2I1Njg4MjIyZTQzOTIxNDg5MzAwNTljYTM0ZjRkNDIyZGY5M2M4M2FhMWQyMDY0Yjc4ZGQwNmM5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:10:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432934481\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-218799226 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RNyX9WCN4reYcT0DCZ4OL8AMB8ycAJl0mxP1oS1E</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-218799226\", {\"maxDepth\":0})</script>\n"}}