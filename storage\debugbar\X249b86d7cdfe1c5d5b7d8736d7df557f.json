{"__meta": {"id": "X249b86d7cdfe1c5d5b7d8736d7df557f", "datetime": "2025-07-31 05:37:29", "utime": **********.832248, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:37:29] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.820535, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753940248.177436, "end": **********.832369, "duration": 1.654932975769043, "duration_str": "1.65s", "measures": [{"label": "Booting", "start": 1753940248.177436, "relative_start": 0, "end": **********.607281, "relative_end": **********.607281, "duration": 1.429844856262207, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.607308, "relative_start": 1.4298717975616455, "end": **********.832378, "relative_end": 8.821487426757812e-06, "duration": 0.22506999969482422, "duration_str": "225ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45913856, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.009649999999999999, "accumulated_duration_str": "9.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.704981, "duration": 0.0058, "duration_str": "5.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 60.104}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.739954, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 60.104, "width_percent": 13.886}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.750942, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 73.99, "width_percent": 12.539}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7602048, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 86.528, "width_percent": 13.472}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2074087127 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2074087127\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-716154920 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-716154920\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1891833416 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1891833416\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1257011647 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImQ4S3hQUkVEekpFa0pUWmxqRFdKWFE9PSIsInZhbHVlIjoiUzZSdXNqMlVuK3RnZCt5UUZEYmhLOEFDS1AyanVsMlVOdlMzQ2dNQWRmQjRFQURyeDc1bE10d3pZWjFnK1hYdmJaV0I5OFRES1VnYjBuVitRaUlyU0ZNMVhhYklMUHl1bU5SRm81d0VNY2o0UTd6SW5mOXc4eGVDZWhCZyttT3YrMDMrZXRLQmRvVlhCRjFXOXlRM2svcjBScXlrRXd1WUxNeVdkV1RScG1MZkMzWEsyQjZvc1NOeTFheXdkY0YvN29BZWtmTnRXWlBYaXJaaW5ydW9HcnQ2Nm44aWlKalo4M1ZzSjZYNnZFVTlrWitCTmdyaU15NlJzLy9vZEJWWDN6RUhsdlh3dC8vQjNSekQ3eG5JNGtUMzMvV1pEKzRiMldwb2l6UnliTDViZVRNL0krZ2h2a1hTMEV3c1llbHUzNmlvN2NZaDRLWFFhSlFGSFlFOE9ZU05hYjA0Z1NHYXYyVWVBaStYdmZLbGFocXo4U0NYaC9CMDZrZVA3UzVzRVR2ampOdXdMb2srejkzWm1TVDdrelV4d0MwRk1ibTBVN2l3MlhXRmxQWGlvZmtSVWhycW5wUnZGc3J0RlNBWTZDVDBoY3ZPVXk3UXJGdE4xVExxMXFmN1orWGJDNGtjcU5Kd2dtTE9sZVZoclNTQWh1czBBSEVIa3hMcXRBdUciLCJtYWMiOiI4MmI2MjQyOGMwMTMzZTJhNzVlZDY4YjI1NmVmOWM3MTgwZTE3ZmUzMWM0MzljZWYwMzc4NDkzNWZjMWUwODNjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImVVdGZJTGM5WGJBb0IydUZoTUM5UlE9PSIsInZhbHVlIjoiekJzSEZjQ3gyYnU1SlFLZVBLSFZvUXdsN0t1L1dUSjlLMTZFY3l4VVVJU05xb0p0dkcxUTJjL1ZyYUQrc0RleHhZK1BHQ1R1YVgrZmF3WGtoaVJpUDhCamtSUVR1WDlyMHZUOGhOS005dmJnTDNoTmdYbE5nSFI0RHpGYnlyMDMzUEJMa3hmQXBOSTZxbEdjTWZsOXFVN2xvR1BtdnpyZFp0M3dnbEgxd0tFelhUT0dIcjU1V2FLQ2U0V0k4RVZkazBaTmJPdTVlMWkxV0xMLzFFUDJadytVWVRCK1gycDFiU2kycGx5QStPbHBaRWdrVTNHTWIwcEdac1dYbnR4QWI4UXY5LzJya0dEdzZWMzB0cGZhQWQyYmwrUnVjb1BIMTVpekdjOUVjZlQ5TGpFTzh3akFHdnJYdkR2STlKNHJ1NG1tck40K1VDOFR2UFFDa2twV2xodTMwWlhZYkRsZlNtY1ZORnhyNzNhQm5LMGdNRFFPQkVEZlV1K21BUkdtRTMrMWFVTWIyTkRrT2paNTRUTkMyd0R6OWJhd2JFYW1jeURXWXdoUjlXR0lrd3dpd21XQjIxYnltMlpoaDk4b2k5bVlHeEZmWWpLSWdKSWRscXJaN0Fxd3lBbXJUZGZ2Ni9RaW1yeC9XWVRFMi9BY3NHUDZNQVVYcTdYWUp0TlEiLCJtYWMiOiJlYzU5NGQ1MjNkNjUyMzdkNzA1MjdmODZjNjQ3NmY5YmNmM2JmMjkzMzgyYmNhNjhlNjE1YjEyNjVkNDI0ZTMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1257011647\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-627729803 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lOWuOQi5K5zalKq6lxC9bmXS2G5Yh3URND5pGg9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-627729803\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-152364954 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:37:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5XeVBFSXcvNW5OODE1T3BVRWowSkE9PSIsInZhbHVlIjoiWFRXOHBLTkh5czQzd2lsamR2NUlqcXhuTmZ5MUZpSzBicXBnUzJOMkdLMEQ4dEVrbS9IYTB6STlrVWxHMEhTdjVLN3Z6NTBqekFpMlhWYmZyN0gzZkZrU2tET09EbXYxenhjaW1QTkw1M0RGU2Q1UnkwNUhwbVZzdTd0MjQvYXlwZTZTR0UxQVVqMDhIVkFhZkJvV1JWYXpqbDlNRDFLQzF5R0dMME5nL0NGMUJXYmxjaXVDTFRLWno3TXBTTTh5M3AwQUl1Q21kcDVrSmt6dmcrYStEVVhnMTFqZzdzSCthUUVTRTVXU3R6dEtybHVZRFlJSFpIallVWDVhNTBTVGc1RVArVThFT3VHK3Z3aERHQVNEL3Q1b0FrRi9tTkdNcUNkRDhyci9WMVpGMjNUWHc1OXMvSnZJMHMzMnVyalI4a1RCbUxsQ2VvMG5IWWJsMTZoTEpzWE8wUGswZ2c4eUdVMEpPdHhtSk41QUd5N0Zob24yZjFrWHJWVFRhKzJrTlBtQ08zUStCR1BpQmRiamdneDN1ci91Mm00WXZOUXN3UGJya0pURGFudU5QQ2ZpY3RzTCtTaU4vUUhyeE1iZHdxUE9JMjBJcGNKSkNvT2RRUUVUQlZjTWdva2pHbVhxcHN6WmQ1eHMwd0YrTC9IZkJTRkJ6UVR1Rjd5YzNicjUiLCJtYWMiOiIwYzNiYzkwZWYwOGE3MDQ5MjI3OTM3YjRlZDM3MzMyMGVlNTQyMmU1M2M5OWFmYzBlZDk1MzM1ZjNjOTNmOGM1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:37:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Imd4TWpha3Z4WnBXOGliTTB4MlZhenc9PSIsInZhbHVlIjoiTlFDQTZueFd1dGZnNGc2VWxOT0pHSGZ1NkNtbjZPTExFR2VKMml0TDM2aVd2YVorYUQwZTlMVE1sVDhMRUhWOW9xQmNlY3NaUGo0WWEvNy80THR2bU9zSFFkUGxIMldOMmFCYnVkRGt3M2Q2UmIyRlEwbnQ2SkhSbnl4MFd0TjJTUTNDdnNoWXdNbDNXRUYyWTN5bzhIU3FyZ2pQTHYxcjNRZmp5TlRCcWlZNTJQN3dpaGF5eUM4cllLN1d6TVdMOTlBWXd3ZXlRUW9uWThiMUJkcjQweU9ZYU1velZaYXpMNzhieElHaExBQTh5dFN0WnpRVUU4cDRtdFZqZ296anlwQ2k4SVRiakluaGtCT2JkL053dm15VzRib2QzaU5ueGZLcm51bkFqZkdZOFYwcWo4ZzNBSWxKRnpYOVlBMkY3SDdNbDh6cVM0WlFhOW00RU5MOWtONFh2OG8zNzhUdXlJWUZwa3EwNmdVMk5nNmtxcXZSRXljanVUeXFNdnB5R2JnMnJBeGtmUUp5YU53VTRRWVA3a0dXOWZkdVBYYjdJUDlHM0x0VmN1ZllEZGc0RkV5Y1N1RzRRM3EreWtjZHBGTkpickI2eGJyeHA5VXF2V1ZRa2JsRDdGd1RHMHIxb0thZmNLQXFlR2pYeHJiTm9udEp6cy9WRC8yS0ZGTG0iLCJtYWMiOiIwMzMyOGE4NjAxMjgwMTg2ZjU0M2JhODc2YmJmMzNmOGMzNThhZGM2MWI1NjIwYjdiZDA1NTA4NDY0YjljZWYxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:37:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5XeVBFSXcvNW5OODE1T3BVRWowSkE9PSIsInZhbHVlIjoiWFRXOHBLTkh5czQzd2lsamR2NUlqcXhuTmZ5MUZpSzBicXBnUzJOMkdLMEQ4dEVrbS9IYTB6STlrVWxHMEhTdjVLN3Z6NTBqekFpMlhWYmZyN0gzZkZrU2tET09EbXYxenhjaW1QTkw1M0RGU2Q1UnkwNUhwbVZzdTd0MjQvYXlwZTZTR0UxQVVqMDhIVkFhZkJvV1JWYXpqbDlNRDFLQzF5R0dMME5nL0NGMUJXYmxjaXVDTFRLWno3TXBTTTh5M3AwQUl1Q21kcDVrSmt6dmcrYStEVVhnMTFqZzdzSCthUUVTRTVXU3R6dEtybHVZRFlJSFpIallVWDVhNTBTVGc1RVArVThFT3VHK3Z3aERHQVNEL3Q1b0FrRi9tTkdNcUNkRDhyci9WMVpGMjNUWHc1OXMvSnZJMHMzMnVyalI4a1RCbUxsQ2VvMG5IWWJsMTZoTEpzWE8wUGswZ2c4eUdVMEpPdHhtSk41QUd5N0Zob24yZjFrWHJWVFRhKzJrTlBtQ08zUStCR1BpQmRiamdneDN1ci91Mm00WXZOUXN3UGJya0pURGFudU5QQ2ZpY3RzTCtTaU4vUUhyeE1iZHdxUE9JMjBJcGNKSkNvT2RRUUVUQlZjTWdva2pHbVhxcHN6WmQ1eHMwd0YrTC9IZkJTRkJ6UVR1Rjd5YzNicjUiLCJtYWMiOiIwYzNiYzkwZWYwOGE3MDQ5MjI3OTM3YjRlZDM3MzMyMGVlNTQyMmU1M2M5OWFmYzBlZDk1MzM1ZjNjOTNmOGM1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:37:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Imd4TWpha3Z4WnBXOGliTTB4MlZhenc9PSIsInZhbHVlIjoiTlFDQTZueFd1dGZnNGc2VWxOT0pHSGZ1NkNtbjZPTExFR2VKMml0TDM2aVd2YVorYUQwZTlMVE1sVDhMRUhWOW9xQmNlY3NaUGo0WWEvNy80THR2bU9zSFFkUGxIMldOMmFCYnVkRGt3M2Q2UmIyRlEwbnQ2SkhSbnl4MFd0TjJTUTNDdnNoWXdNbDNXRUYyWTN5bzhIU3FyZ2pQTHYxcjNRZmp5TlRCcWlZNTJQN3dpaGF5eUM4cllLN1d6TVdMOTlBWXd3ZXlRUW9uWThiMUJkcjQweU9ZYU1velZaYXpMNzhieElHaExBQTh5dFN0WnpRVUU4cDRtdFZqZ296anlwQ2k4SVRiakluaGtCT2JkL053dm15VzRib2QzaU5ueGZLcm51bkFqZkdZOFYwcWo4ZzNBSWxKRnpYOVlBMkY3SDdNbDh6cVM0WlFhOW00RU5MOWtONFh2OG8zNzhUdXlJWUZwa3EwNmdVMk5nNmtxcXZSRXljanVUeXFNdnB5R2JnMnJBeGtmUUp5YU53VTRRWVA3a0dXOWZkdVBYYjdJUDlHM0x0VmN1ZllEZGc0RkV5Y1N1RzRRM3EreWtjZHBGTkpickI2eGJyeHA5VXF2V1ZRa2JsRDdGd1RHMHIxb0thZmNLQXFlR2pYeHJiTm9udEp6cy9WRC8yS0ZGTG0iLCJtYWMiOiIwMzMyOGE4NjAxMjgwMTg2ZjU0M2JhODc2YmJmMzNmOGMzNThhZGM2MWI1NjIwYjdiZDA1NTA4NDY0YjljZWYxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:37:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-152364954\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1222955665 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222955665\", {\"maxDepth\":0})</script>\n"}}