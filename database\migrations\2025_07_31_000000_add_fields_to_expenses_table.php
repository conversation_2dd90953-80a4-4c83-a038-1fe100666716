<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldsToExpensesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('expenses', function (Blueprint $table) {
            if (!Schema::hasColumn('expenses', 'category_id')) {
                $table->integer('category_id')->default(0)->after('name');
            }
            if (!Schema::hasColumn('expenses', 'vendor_name')) {
                $table->string('vendor_name')->nullable()->after('category_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('expenses', function (Blueprint $table) {
            if (Schema::hasColumn('expenses', 'category_id')) {
                $table->dropColumn('category_id');
            }
            if (Schema::hasColumn('expenses', 'vendor_name')) {
                $table->dropColumn('vendor_name');
            }
        });
    }
}
