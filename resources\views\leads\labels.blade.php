{{ Form::open(array('route' => ['leads.labels.store',$lead->id], 'class' => 'tags-form')) }}
<div class="modal-body">
    <!-- Header Section -->
    <div class="mb-4">
        <h5 class="mb-2">{{ __('Manage Tags') }}</h5>
        <p class="text-muted mb-0">{{ __('Select tags to organize your lead') }}</p>
    </div>

    <!-- Selected Tags Display -->
    <div class="mb-4">
        <label class="form-label fw-semibold">
            {{ __('Selected Tags') }}
            <span class="badge bg-primary ms-1" id="selected-count">0</span>
        </label>
        <div id="selected-tags-container" class="border rounded p-3 bg-light" style="min-height: 60px;">
            <div class="text-muted" id="no-tags-message">
                {{ __('No tags selected') }}
            </div>
        </div>
    </div>

    <!-- Available Tags -->
    <div class="mb-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <label class="form-label fw-semibold mb-0">
                {{ __('Available Tags') }}
                <span class="badge bg-info ms-1">{{ count($labels) }}</span>
            </label>
            <button type="button" class="btn btn-success btn-sm" id="toggleCreateForm">
                <i class="ti ti-plus me-1"></i>{{ __('Create Tag') }}
            </button>
        </div>

        <!-- Create Tag Form (Hidden by default) -->
        <div id="createTagForm" class="card border-success mb-3" style="display: none;">
            <div class="card-header bg-success text-white py-2">
                <h6 class="mb-0">
                    <i class="ti ti-plus me-2"></i>{{ __('Create New Tag') }}
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    <div class="col-8">
                        <input type="text" id="newTagNameInput" class="form-control" placeholder="{{ __('Enter tag name...') }}" maxlength="50">
                    </div>
                    <div class="col-4">
                        <div class="d-flex gap-1">
                            <button type="button" class="btn btn-success btn-sm flex-fill" id="saveNewTagBtn">
                                <i class="ti ti-check"></i> {{ __('Create') }}
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm" id="cancelCreateBtn">
                                <i class="ti ti-x"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div id="createTagError" class="text-danger small mt-2" style="display: none;"></div>
            </div>
        </div>

        <!-- Search Input -->
        <div class="mb-3">
            <input type="text" id="tag-search" class="form-control" placeholder="{{ __('Search tags...') }}">
        </div>

        <!-- Tags List -->
        <div class="border rounded" style="max-height: 300px; overflow-y: auto;">
            @foreach ($labels as $label)
                <div class="tag-item d-flex align-items-center justify-content-between p-3 border-bottom"
                     data-tag-id="{{ $label->id }}"
                     data-tag-name="{{ ucfirst($label->name) }}"
                     data-selected="{{ array_key_exists($label->id, $selected) ? 'true' : 'false' }}">
                    <div class="d-flex align-items-center">
                        <i class="ti ti-tag me-2 text-primary"></i>
                        <span>{{ ucfirst($label->name) }}</span>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-primary select-tag-btn">
                        <i class="ti ti-plus"></i>
                    </button>
                </div>
            @endforeach
        </div>
    </div>



    <!-- Hidden inputs for form submission -->
    <input type="hidden" name="labels" id="selected-tags-input" value="">
    <input type="hidden" name="new_labels" id="new-labels-input" value="">
</div>

<div class="modal-footer bg-light border-0 p-4">
    <div class="d-flex justify-content-between align-items-center w-100">
        <div class="text-muted small">
            <i class="ti ti-info-circle me-1"></i>
            {{ __('Changes will be saved immediately') }}
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">
                <i class="ti ti-x me-1"></i>{{ __('Cancel') }}
            </button>
            <button type="submit" class="btn btn-primary px-4">
                <i class="ti ti-check me-1"></i>{{ __('Save Tags') }}
            </button>
        </div>
    </div>
</div>

{{Form::close()}}

<!-- Simple Tag UI CSS -->
<style>
/* Modal Styling */
.modal-dialog {
    max-width: 600px;
}

/* Tag Items */
.tag-item {
    cursor: pointer;
}

.tag-item:hover {
    background-color: #f8f9fa;
}

.tag-item:last-child {
    border-bottom: none !important;
}

.tag-item.selected {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

/* Selected Tags */
.selected-tag {
    background-color: #2196f3;
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    margin: 2px;
}

.selected-tag .remove-tag {
    cursor: pointer;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
}

.selected-tag .remove-tag:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

/* Buttons */
.select-tag-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.select-tag-btn.selected {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}



/* Search Input */
#tag-search:focus {
    border-color: #2196f3;
    box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
}

/* Create Tag Form */
#createTagForm {
    border: 2px solid #28a745;
}

#createTagForm .card-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

#newTagNameInput:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.create-tag-slide-down {
    animation: slideDown 0.3s ease-out;
}

.create-tag-slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .modal-dialog {
        max-width: 95%;
        margin: 10px;
    }

    .selected-tag {
        font-size: 13px;
        padding: 5px 10px;
    }
}
</style>

<script>
$(document).ready(function() {
    let selectedTags = new Map();
    let allTags = new Map();
    let newTagCounter = 0;

    // Initialize tags
    function initializeTags() {
        $('.tag-item[data-tag-id]').each(function() {
            const id = $(this).data('tag-id');
            const name = $(this).data('tag-name');
            const isSelected = $(this).data('selected') === 'true';

            allTags.set(id, {id: id, name: name, isNew: false});

            if (isSelected) {
                selectedTags.set(id, {id: id, name: name, isNew: false});
                updateTagItemState(id, true);
            }
        });

        updateSelectedTagsDisplay();
        updateSelectedCount();
    }

    // Update selected tags display
    function updateSelectedTagsDisplay() {
        const container = $('#selected-tags-container');
        const noTagsMessage = $('#no-tags-message');

        container.find('.selected-tag').remove();

        if (selectedTags.size === 0) {
            noTagsMessage.show();
        } else {
            noTagsMessage.hide();

            selectedTags.forEach((tag, id) => {
                const tagElement = $(`
                    <span class="selected-tag" data-tag-id="${id}">
                        ${tag.name}
                        ${tag.isNew ? ' <small>(new)</small>' : ''}
                        <span class="remove-tag ms-1" data-tag-id="${id}">
                            <i class="ti ti-x"></i>
                        </span>
                    </span>
                `);
                container.append(tagElement);
            });
        }
    }

    // Update selected count
    function updateSelectedCount() {
        $('#selected-count').text(selectedTags.size);
    }

    // Update tag item state
    function updateTagItemState(tagId, isSelected) {
        const tagItem = $(`.tag-item[data-tag-id="${tagId}"]`);
        const button = tagItem.find('.select-tag-btn');

        if (isSelected) {
            tagItem.addClass('selected');
            button.removeClass('btn-outline-primary').addClass('btn-danger selected');
            button.html('<i class="ti ti-x"></i>');
        } else {
            tagItem.removeClass('selected');
            button.removeClass('btn-danger selected').addClass('btn-outline-primary');
            button.html('<i class="ti ti-plus"></i>');
        }
    }

    // Add tag
    function addTag(id, name, isNew = false) {
        if (!selectedTags.has(id)) {
            selectedTags.set(id, {id: id, name: name, isNew: isNew});

            if (!isNew) {
                updateTagItemState(id, true);
            }

            updateSelectedTagsDisplay();
            updateSelectedCount();
            updateFormInputs();
        }
    }

    // Remove tag
    function removeTag(id) {
        if (selectedTags.has(id)) {
            const tag = selectedTags.get(id);
            selectedTags.delete(id);

            if (!tag.isNew) {
                updateTagItemState(id, false);
            }

            updateSelectedTagsDisplay();
            updateSelectedCount();
            updateFormInputs();
        }
    }

    // Update form inputs
    function updateFormInputs() {
        const existingTags = [];
        const newTags = [];

        selectedTags.forEach((tag) => {
            if (tag.isNew) {
                newTags.push(tag.name);
            } else {
                existingTags.push(tag.id);
            }
        });

        $('#selected-tags-input').val(existingTags.join(','));
        $('#new-labels-input').val(newTags.join(','));
    }

    // Filter tags
    function filterTags(searchTerm) {
        const lowerSearchTerm = searchTerm.toLowerCase().trim();

        $('.tag-item[data-tag-id]').each(function() {
            const tagName = $(this).data('tag-name').toLowerCase();
            if (lowerSearchTerm === '' || tagName.includes(lowerSearchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }

    // Event handlers
    $('#tag-search').on('input', function() {
        filterTags($(this).val());
    });

    $(document).on('click', '.select-tag-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const tagItem = $(this).closest('.tag-item');
        const tagId = tagItem.data('tag-id');
        const tagName = tagItem.data('tag-name');
        const isSelected = tagItem.hasClass('selected');

        if (isSelected) {
            removeTag(tagId);
        } else {
            addTag(tagId, tagName, false);
        }
    });

    $(document).on('click', '.remove-tag', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const tagId = $(this).data('tag-id');
        removeTag(tagId);
    });

    // Toggle create tag form
    $('#toggleCreateForm').on('click', function(e) {
        e.preventDefault();
        const form = $('#createTagForm');
        const button = $(this);

        if (form.is(':visible')) {
            form.removeClass('create-tag-slide-down').addClass('create-tag-slide-up');
            setTimeout(() => {
                form.hide();
                button.html('<i class="ti ti-plus me-1"></i>{{ __("Create Tag") }}');
            }, 300);
        } else {
            form.show().removeClass('create-tag-slide-up').addClass('create-tag-slide-down');
            button.html('<i class="ti ti-x me-1"></i>{{ __("Cancel") }}');
            $('#newTagNameInput').focus();
        }

        // Clear form
        $('#newTagNameInput').val('');
        $('#createTagError').hide();
    });

    // Cancel create tag
    $('#cancelCreateBtn').on('click', function(e) {
        e.preventDefault();
        $('#toggleCreateForm').click(); // Trigger the toggle to close
    });

    // Save new tag
    $('#saveNewTagBtn').on('click', function(e) {
        e.preventDefault();

        const newTagName = $('#newTagNameInput').val().trim();
        const errorDiv = $('#createTagError');

        // Clear previous errors
        errorDiv.hide();

        // Validation
        if (!newTagName) {
            errorDiv.text('{{ __("Please enter a tag name") }}').show();
            $('#newTagNameInput').focus();
            return;
        }

        if (newTagName.length > 50) {
            errorDiv.text('{{ __("Tag name must be 50 characters or less") }}').show();
            $('#newTagNameInput').focus();
            return;
        }

        // Check if tag already exists
        let tagExists = false;
        allTags.forEach((tag) => {
            if (tag.name.toLowerCase() === newTagName.toLowerCase()) {
                tagExists = true;
                return false;
            }
        });

        if (tagExists) {
            errorDiv.text('{{ __("Tag already exists") }}').show();
            $('#newTagNameInput').focus();
            return;
        }

        // Create the tag
        const newId = 'new_' + (++newTagCounter);
        addTag(newId, newTagName, true);

        // Close form and reset
        $('#toggleCreateForm').click();

        // Show success message
        if (typeof show_toastr === 'function') {
            show_toastr('success', '{{ __("Tag created and selected successfully!") }}');
        }
    });

    // Handle Enter key in tag name input
    $('#newTagNameInput').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            e.preventDefault();
            $('#saveNewTagBtn').click();
        }
    });



    $('form').on('submit', function(e) {
        updateFormInputs();

        const $submitBtn = $(this).find('button[type="submit"]');
        $submitBtn.html('<i class="spinner-border spinner-border-sm me-1"></i>{{ __("Saving...") }}').prop('disabled', true);
    });

    // Reset main modal when closed
    $('.modal').on('hidden.bs.modal', function() {
        selectedTags.clear();
        $('#tag-search').val('');
        $('.tag-item[data-tag-id]').show();
        updateSelectedTagsDisplay();
        updateSelectedCount();
    });

    // Initialize
    initializeTags();
});
</script>

