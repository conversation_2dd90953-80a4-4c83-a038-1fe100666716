{"__meta": {"id": "Xdbd3e82f4400035ebe82dd59177013cb", "datetime": "2025-07-31 05:22:46", "utime": **********.580399, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939365.521291, "end": **********.580427, "duration": 1.059135913848877, "duration_str": "1.06s", "measures": [{"label": "Booting", "start": 1753939365.521291, "relative_start": 0, "end": **********.491149, "relative_end": **********.491149, "duration": 0.969857931137085, "duration_str": "970ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.491161, "relative_start": 0.****************, "end": **********.58043, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "89.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "w0m6EfCX6kPdpe0J3Gh98FD2qNKOx7pIStUJVbtH", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1972159930 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1972159930\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1350857910 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1350857910\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1451262352 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1451262352\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2142684993 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2142684993\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1881374420 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1881374420\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1680801124 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:22:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijljd09NMXpON28vOTNuclVsNzNjaXc9PSIsInZhbHVlIjoibXRmRmYrT0UzWStjU1ljUDBDY01PNThpejNSUU1vaTBsV2wyZVVlRDA3OHc0clJuR3ZQUWNOaHZaaVVVMUhiZ1RVM3huTnRZRFV4SnlOM3A1V05wdk85NDZuRUJYQm5iOG1VQklEQ2o5NlAvczU5cWxjNUVrSzRHWXZCQ3llVVdKYUE0OVdId25QdUpzL2ZOdGduSEtqWGFJVjNNRWxhSFZodlZNZHM1NTcwc2doTkJobG5RZDlLUFBWcXQyMENUMnRzTVNDYU1HaFoxQ04xOGg4Uk52SHBlNG83VlpUWERXWHg5ZGVidFUrcVJET1dNMzA3dS9Ub3g5KzlkU0dLdHEzTEVQaldXWTVuTlBlQVBMUGhGbEZyc2hscUJYQnF3S3JhT1N1Umk2d2drR3FRTFI5R3V2cHNXSjVVV2h4ZU5TSWpZQ3FzU0tMSzI5SEhRcjRHYVp6Y3hqV3dOSGJEbG82dG52SmJxVmhNRjhMR0E4T04rcTZ0dWdML1UwN3hpQWU0UzM0MWN0djk4Nmd3MXhJVGJSN0hlQmVpNktqemd5TFN3eUlQTk0vdEtWOFZkVUZsSnowUWFIZUoxclNiR1JHVTRjM1M3SXdBdDM0YzBKbzU1eDVHL2dhYS9WcmYzbWcxV0VmZG1tYnhlYmdSNXJYbEU3Ukh6KzNNUWZGMEwiLCJtYWMiOiI0MWNhYzI1ZmE2MjI3MWEyMmY1MjJmMTkxY2ZkOTU5YjQ1M2RiZDkzYTFkMzc2NjRjNWQxZjkzOGMwNGYyOTQ4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:22:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im8zQ3hUUTRxNU9aR0pic1QwdldYK3c9PSIsInZhbHVlIjoiajRHdkhCT05nWHRLU3ZyRVZsaSttMUZORDVOYlVxUUNGTm0rRjRzS01RZVRsSnR6RVF4ZTMyeVBlZ09wVCt4NUdOY0VXQm4vdGJOSEUrZ3didHZtM3B2N25sa0V3cXI0NnRvZHNmN2NDTGZSb3dVWDRSSWlzRXd6RERhUkZzQ2g0TU1XbFZFb2dLQmlpZ1BTRThXZlovSnFWRzl0KzR5TVFRNjZNMkI3Ny9pY1lIby9YMEpWUGFCQmVpTDRkb0xOZkxoa2d2QlBmMkJiUUMwOE5qblIyRFZSQStLemRiU3lvclcrcDVGeFFnSVYwRHRhS0t4enJlQ3dTTzdmNFlBa05IK1FycC9DczNPQzJTNUgyckw5b0dXYjFBbGFlbVhXVUVtTlQzLzErTG1CL3hDOHZ6YmRIQkV3VUw4UTJDd0FuVXRFK0NFb1dUWVcvcmJUdVF6djZSS0Ywc2lCeFA4dGlDOHUrUW4weUxpeFlIaWNzelRYNDJ6amhNNmRHUjdCM1NEa1lBWDJGMTRWL3AvYm9udSt5R21ZM1VJdml6WUNlREJCOXFyd2loS3NKckg0dHE4OGpSY05ObkxkQXg5Rlh3aUZLQVFZNGFNbEJTQVZsNTRaM2p1dklxMzNQSWtrMjgzZS90UlhJRjc5dlhMeVZHa09Rc1J3Y0tCekc0VS8iLCJtYWMiOiIyOTQ3ZGNhMGUwMzI4ZGU0MmRjYmJkNWJmYmExODE2MjE3YjkxNGUxMjJlZWVhZWRiMWNhMTc3NjgwZjkwOGY2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:22:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijljd09NMXpON28vOTNuclVsNzNjaXc9PSIsInZhbHVlIjoibXRmRmYrT0UzWStjU1ljUDBDY01PNThpejNSUU1vaTBsV2wyZVVlRDA3OHc0clJuR3ZQUWNOaHZaaVVVMUhiZ1RVM3huTnRZRFV4SnlOM3A1V05wdk85NDZuRUJYQm5iOG1VQklEQ2o5NlAvczU5cWxjNUVrSzRHWXZCQ3llVVdKYUE0OVdId25QdUpzL2ZOdGduSEtqWGFJVjNNRWxhSFZodlZNZHM1NTcwc2doTkJobG5RZDlLUFBWcXQyMENUMnRzTVNDYU1HaFoxQ04xOGg4Uk52SHBlNG83VlpUWERXWHg5ZGVidFUrcVJET1dNMzA3dS9Ub3g5KzlkU0dLdHEzTEVQaldXWTVuTlBlQVBMUGhGbEZyc2hscUJYQnF3S3JhT1N1Umk2d2drR3FRTFI5R3V2cHNXSjVVV2h4ZU5TSWpZQ3FzU0tMSzI5SEhRcjRHYVp6Y3hqV3dOSGJEbG82dG52SmJxVmhNRjhMR0E4T04rcTZ0dWdML1UwN3hpQWU0UzM0MWN0djk4Nmd3MXhJVGJSN0hlQmVpNktqemd5TFN3eUlQTk0vdEtWOFZkVUZsSnowUWFIZUoxclNiR1JHVTRjM1M3SXdBdDM0YzBKbzU1eDVHL2dhYS9WcmYzbWcxV0VmZG1tYnhlYmdSNXJYbEU3Ukh6KzNNUWZGMEwiLCJtYWMiOiI0MWNhYzI1ZmE2MjI3MWEyMmY1MjJmMTkxY2ZkOTU5YjQ1M2RiZDkzYTFkMzc2NjRjNWQxZjkzOGMwNGYyOTQ4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:22:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im8zQ3hUUTRxNU9aR0pic1QwdldYK3c9PSIsInZhbHVlIjoiajRHdkhCT05nWHRLU3ZyRVZsaSttMUZORDVOYlVxUUNGTm0rRjRzS01RZVRsSnR6RVF4ZTMyeVBlZ09wVCt4NUdOY0VXQm4vdGJOSEUrZ3didHZtM3B2N25sa0V3cXI0NnRvZHNmN2NDTGZSb3dVWDRSSWlzRXd6RERhUkZzQ2g0TU1XbFZFb2dLQmlpZ1BTRThXZlovSnFWRzl0KzR5TVFRNjZNMkI3Ny9pY1lIby9YMEpWUGFCQmVpTDRkb0xOZkxoa2d2QlBmMkJiUUMwOE5qblIyRFZSQStLemRiU3lvclcrcDVGeFFnSVYwRHRhS0t4enJlQ3dTTzdmNFlBa05IK1FycC9DczNPQzJTNUgyckw5b0dXYjFBbGFlbVhXVUVtTlQzLzErTG1CL3hDOHZ6YmRIQkV3VUw4UTJDd0FuVXRFK0NFb1dUWVcvcmJUdVF6djZSS0Ywc2lCeFA4dGlDOHUrUW4weUxpeFlIaWNzelRYNDJ6amhNNmRHUjdCM1NEa1lBWDJGMTRWL3AvYm9udSt5R21ZM1VJdml6WUNlREJCOXFyd2loS3NKckg0dHE4OGpSY05ObkxkQXg5Rlh3aUZLQVFZNGFNbEJTQVZsNTRaM2p1dklxMzNQSWtrMjgzZS90UlhJRjc5dlhMeVZHa09Rc1J3Y0tCekc0VS8iLCJtYWMiOiIyOTQ3ZGNhMGUwMzI4ZGU0MmRjYmJkNWJmYmExODE2MjE3YjkxNGUxMjJlZWVhZWRiMWNhMTc3NjgwZjkwOGY2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:22:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1680801124\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-362683011 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">w0m6EfCX6kPdpe0J3Gh98FD2qNKOx7pIStUJVbtH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-362683011\", {\"maxDepth\":0})</script>\n"}}