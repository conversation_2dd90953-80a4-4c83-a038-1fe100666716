{"__meta": {"id": "X26032015a19e21b4cc4a2758334c4684", "datetime": "2025-07-31 05:10:16", "utime": **********.484963, "method": "GET", "uri": "/login-with-company/exit", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753938615.298817, "end": **********.485004, "duration": 1.1861870288848877, "duration_str": "1.19s", "measures": [{"label": "Booting", "start": 1753938615.298817, "relative_start": 0, "end": **********.375786, "relative_end": **********.375786, "duration": 1.0769691467285156, "duration_str": "1.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.375803, "relative_start": 1.0769860744476318, "end": **********.485007, "relative_end": 3.0994415283203125e-06, "duration": 0.10920405387878418, "duration_str": "109ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44673080, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login-with-company/exit", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@ExitCompany", "namespace": null, "prefix": "", "where": [], "as": "exit.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1476\" onclick=\"\">app/Http/Controllers/UserController.php:1476-1480</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0065, "accumulated_duration_str": "6.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.436746, "duration": 0.00532, "duration_str": "5.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 81.846}, {"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Services/ImpersonateManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\lab404\\laravel-impersonate\\src\\Services\\ImpersonateManager.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Services/ImpersonateManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\lab404\\laravel-impersonate\\src\\Services\\ImpersonateManager.php", "line": 137}, {"index": 19, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Models/Impersonate.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\lab404\\laravel-impersonate\\src\\Models\\Impersonate.php", "line": 64}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\UserController.php", "line": 1478}], "start": **********.453506, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 81.846, "width_percent": 18.154}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login-with-company/exit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login-with-company/exit", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1531874851 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1531874851\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1090040019 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1090040019\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InZSN21pL3dBVHEzQkRRTVduWFJzS0E9PSIsInZhbHVlIjoiN2ZVVWFDVkNXQjFoSUMwTFlmNlk3N3JCNkR1U2JYMkVlUEpFanZHTHFiSEZJNEUxcnVaSW12bmZMakFKbTh3YjdjL3pPM2RkNWtkcnA5ZDNOdzFEbW1kWEdoZDRzeHpDd2xnR2VHZ2hOV2ZjQUJSQzdOVVl1RFN1UW1hQmpTTi8vbEpJamtBL2orTklnejJuVGFZUlhibkd4dUg5c1hBR0hIRklOY01nOWZtU0V5SDRqTzFnZFFWbWtBRjZoSlU5QWlMZVZvQlgrMFBDZEVxTDRIY2ljOXo3bkxJSXNaMEM2MlN6UWgyUDdOTVN6Wmd0OWR2Y0lIWlB5dVhUYzlEUnlxUldEeE5ZUWV3RTJ3cElnZ3NyWStZaTRMY0FHZFBpUGZZYi9iSnBmLzJ0aUw4V0JjK3pvS1hBaDVKRk9iaXZxUnNzTjNQZnJlaXkwVEU5SWg1VjYwbTNjelcrZkpRTk5ad2FML1h4U2liQnVMSGN4anhLVXV2dWVYM0lHQ1ZEMEJ4azVVKzhjZmg1UkFoSGlONEFFZzBxNnVwUmtFQkZTWjRadHVQUUFjSGlHRDRTRloyeGExL1luNEE5am9OeXhETHNGNGFwRE1BUS90bEsxQVNKcGQxZVYyQVdpQUNSb1BwSmtaamlZZlY3WXBMU0dBS2Y2dk5RNk9uRG9qYWMiLCJtYWMiOiIwNzk0MmU0YjA3NmMwYTM3MTk4NDU3ZDQ2MWQ1ZWM1M2QwNTJjZDY0NDk4ZTlkZjA4MmUxOTMwMGNmNTI5ZDA4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Imhha2poVnhoWG5taG5zdVFvYm1QUEE9PSIsInZhbHVlIjoicEg5UnlybUNTdXlnaTZid210eVBsZDR6aStIaUVuS0szR2pJSE1YVVJweTFKR1U5MW9JNXl3WGJoU1haUXoyRzlQMDNYOUgrSjM0Rzl5V2pYbjgwaXI3WjhLOHNYUFFWZUtJV2QzWEdsWk9LSkFaVkREeDVUc3ZsM0lpOXdWZ2R3QWpUNGFRYXkweHZZemhOV2hueHY3VWgzTVhJNHBPTXNlWTBoZFlrRno1RUYwbkphYzJNakEreGpXU25ObnRvTDl4cGFvSU1Wd2xtTnRwT3ZDVnhWQm9xVTVOUFBKbnhhL1V2ZUgrTzhjNGl5TmtSS3Fud0o1VzdxdEg3N3N1WjhUcDB1VlBMc1prUHdZQ05pd0l2VXBqVzAwQXVYM0NiS3Vzc1ZLblhPRmpLM3RkNzdHK2QzT3FwMVF6OWtSTTlEbXdmaGRXbHlDT3Y1R3JhdG1rVkFYRnNvWTlCQUF0SDZFUVJHMXVoZXlSN2JzRXp0aFRMWkp1VTdBUjhRYWpCVkdTY2NkVHM1NzlGRTRBbXphTDlsaDVWQjF5c2FTb2Rjdkxmbitya2Ntd2czc3ZQNHRUZEV3aS9nY1pqeXdmZXA4QXkvRVlnT0syd2E0SnVVMXhESDZSdkpLQXpFMGRsUGp6U1RWeXBkaUVwUXFtSE50UVVjNFJEVVhVYXMyb0oiLCJtYWMiOiIyZmNlY2Y3ZTRhNmYwN2M5NzBiZmExNWJlZWMyOWUyOWNiYThiYzgxNjQyYTUzNjM0ZWNmMWYyNGVmMzJhYzU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1234197184 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ox3wNzbpEsYdcIjBj7CrMqAYjNh67dKHlf9jl7A4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1234197184\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1955550479 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:10:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im55UVBOMng1ZkNpTmVSMzNnN2NvNVE9PSIsInZhbHVlIjoiSy9RaEdKdVBGc0FVRGlqYitXbit3WTZrRUxWb2ZTUnBzOGhsWGx1SzBCdG9HN3lMZDhZNFJ5eXBQMlEyT2ZvU3h0Szk0cjl5bE5zZ3dkcjZ4b01jMXZEWmQyTHdtSkVJaEw0VE9XdmhMRFBnUW0wZjNLY1NLcG1LejdFUmVpeG41d0MxSWVycWFnOTlacG53MUN2dWtFVS9qVmNFN01WRDRTZkxXYkNQa0l0RzBuWDRNQU5neHJEVWxMa2xYb2ZJNHRMM3Y0ZTh3WWlnSDdEWHdySnNPRUQyZFl0K0lZY0lFTCtiTDYxNGdOVUFMdGRhYVNQUS9wUWZkeGkvTlVCK2F5Y3VqaStiWFdPY3RNNDVCZ1FZTU93S2pLaWJvMy9mS0dWaE9iN2hUWStzNXJqV2tIZHdrVEF5RlVKbXJ5QmxTZWpTMUx4SlJSMm9ZQmx2UHNXODhHT1BheUY3bm1rLy9tMUxLbXJvWXg2M0lTeDZ3SVJSWjBFQW1RbXFDZ2QxejZQZGtaSnJGK3A5RW1wa1VWZVorQzhPYVpCUGtNdVozS0E3OHZLbDJhVy8zV05pY3JpR3ozcGhFNkltc0JVRnBIYy8xUm11c0NsSWk0Z1krVVptQjNqSFVYbWdtMTJEVVpaYkRUZml0b3V5RmVWK3dYVDBaTTRJeVlVL3dvb0EiLCJtYWMiOiJkZjNjM2E3NzFiODllYjJhOGM0NjczZGY3ODAzYTAzMzcwZGZiODQ2YTFlMjkxZDdiYjUwYzcyNDg1Mjk5ZTUyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:10:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IitZcXVVVlozcHFHYVlaUlRpeVdOTXc9PSIsInZhbHVlIjoickFlTU9BWGdkdUJyemNYMVJ4WExmb1JOTXczZ29ZdWcxWG10eHI0MXdOYUVyUGtIMW1Bd2JvSHJzb05ETGtHa3dQeXBBMWJKQUhtMUVzZ1FPY2FPUlpIT3lTVHJNNGJRYTZjTktRa0Z4VWhhWXMvZHkvS2tHblV4TTh6MjdHcTBmS2NqbWVDL0RuaW9xcE1QZ0x1ZWorTm4zYWc1S1NKQkNXTnRhSnFuVWo1WitlNWNTdFBuQ1dqRjl6ai9aS0VKMDYwWTRsbUJTYXlRRVJVK3JwV1A5YzRoZFNNbXRveGQxSW5KZUhmZ1daeWY0UC9ydU8yeVpITUlMSzI2UjJra21hUHk4ZUtUejhZUUwrUS9IcnBSYXdDYjE2d296MVMrMlpPc1VXN0lEdUQzRGRIVnJiKzNxSDNnQVhJNlNBa2FwcGZZQk5pNTQwRXBGeVJ4b29BWEJwSVpJRGllSjIxTW1VeHJ1QXU5eVB2YktSVUs3R2dabFl1U0hXUTNaSi90S256YXJqT0gwRFAzbGhMNFBjVm1jWVJFOXVaeXZNcDNWRlVCVjZZb3BkdkVwUGpQSERnSkJVOG9IUkl6RzU5UzRheVpHTVplWkY5UHBZczQvaTBkdlM4WWhQVVBoOVpPQmR1YzlNQjZxd1dVOG1Bd2xvSzZMdkdEKzlPL3RCcVUiLCJtYWMiOiI4ZGQ1OWUyODJjOTA1MWIwNzYxZDJlZWFkNGM1OGRmMzg4Yjc2MWY5ODdhNjAwMGFkNTQzYmRiYjIzNGUwNWI2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:10:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im55UVBOMng1ZkNpTmVSMzNnN2NvNVE9PSIsInZhbHVlIjoiSy9RaEdKdVBGc0FVRGlqYitXbit3WTZrRUxWb2ZTUnBzOGhsWGx1SzBCdG9HN3lMZDhZNFJ5eXBQMlEyT2ZvU3h0Szk0cjl5bE5zZ3dkcjZ4b01jMXZEWmQyTHdtSkVJaEw0VE9XdmhMRFBnUW0wZjNLY1NLcG1LejdFUmVpeG41d0MxSWVycWFnOTlacG53MUN2dWtFVS9qVmNFN01WRDRTZkxXYkNQa0l0RzBuWDRNQU5neHJEVWxMa2xYb2ZJNHRMM3Y0ZTh3WWlnSDdEWHdySnNPRUQyZFl0K0lZY0lFTCtiTDYxNGdOVUFMdGRhYVNQUS9wUWZkeGkvTlVCK2F5Y3VqaStiWFdPY3RNNDVCZ1FZTU93S2pLaWJvMy9mS0dWaE9iN2hUWStzNXJqV2tIZHdrVEF5RlVKbXJ5QmxTZWpTMUx4SlJSMm9ZQmx2UHNXODhHT1BheUY3bm1rLy9tMUxLbXJvWXg2M0lTeDZ3SVJSWjBFQW1RbXFDZ2QxejZQZGtaSnJGK3A5RW1wa1VWZVorQzhPYVpCUGtNdVozS0E3OHZLbDJhVy8zV05pY3JpR3ozcGhFNkltc0JVRnBIYy8xUm11c0NsSWk0Z1krVVptQjNqSFVYbWdtMTJEVVpaYkRUZml0b3V5RmVWK3dYVDBaTTRJeVlVL3dvb0EiLCJtYWMiOiJkZjNjM2E3NzFiODllYjJhOGM0NjczZGY3ODAzYTAzMzcwZGZiODQ2YTFlMjkxZDdiYjUwYzcyNDg1Mjk5ZTUyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:10:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IitZcXVVVlozcHFHYVlaUlRpeVdOTXc9PSIsInZhbHVlIjoickFlTU9BWGdkdUJyemNYMVJ4WExmb1JOTXczZ29ZdWcxWG10eHI0MXdOYUVyUGtIMW1Bd2JvSHJzb05ETGtHa3dQeXBBMWJKQUhtMUVzZ1FPY2FPUlpIT3lTVHJNNGJRYTZjTktRa0Z4VWhhWXMvZHkvS2tHblV4TTh6MjdHcTBmS2NqbWVDL0RuaW9xcE1QZ0x1ZWorTm4zYWc1S1NKQkNXTnRhSnFuVWo1WitlNWNTdFBuQ1dqRjl6ai9aS0VKMDYwWTRsbUJTYXlRRVJVK3JwV1A5YzRoZFNNbXRveGQxSW5KZUhmZ1daeWY0UC9ydU8yeVpITUlMSzI2UjJra21hUHk4ZUtUejhZUUwrUS9IcnBSYXdDYjE2d296MVMrMlpPc1VXN0lEdUQzRGRIVnJiKzNxSDNnQVhJNlNBa2FwcGZZQk5pNTQwRXBGeVJ4b29BWEJwSVpJRGllSjIxTW1VeHJ1QXU5eVB2YktSVUs3R2dabFl1U0hXUTNaSi90S256YXJqT0gwRFAzbGhMNFBjVm1jWVJFOXVaeXZNcDNWRlVCVjZZb3BkdkVwUGpQSERnSkJVOG9IUkl6RzU5UzRheVpHTVplWkY5UHBZczQvaTBkdlM4WWhQVVBoOVpPQmR1YzlNQjZxd1dVOG1Bd2xvSzZMdkdEKzlPL3RCcVUiLCJtYWMiOiI4ZGQ1OWUyODJjOTA1MWIwNzYxZDJlZWFkNGM1OGRmMzg4Yjc2MWY5ODdhNjAwMGFkNTQzYmRiYjIzNGUwNWI2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:10:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1955550479\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1595966099 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://127.0.0.1:8000/login-with-company/exit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1595966099\", {\"maxDepth\":0})</script>\n"}}