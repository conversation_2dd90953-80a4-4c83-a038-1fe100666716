{"__meta": {"id": "X4692008e23b6e73b8a2dfa8ac38c217e", "datetime": "2025-07-31 07:37:21", "utime": **********.880377, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[07:37:21] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.874841, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753947440.759557, "end": **********.8804, "duration": 1.1208429336547852, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": 1753947440.759557, "relative_start": 0, "end": **********.660662, "relative_end": **********.660662, "duration": 0.9011049270629883, "duration_str": "901ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.660684, "relative_start": 0.9011270999908447, "end": **********.880403, "relative_end": 3.0994415283203125e-06, "duration": 0.21971893310546875, "duration_str": "220ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48411656, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.025939999999999998, "accumulated_duration_str": "25.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.723669, "duration": 0.01584, "duration_str": "15.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 61.064}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.7574348, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 61.064, "width_percent": 3.624}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.762894, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 64.688, "width_percent": 2.699}, {"sql": "select * from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7676802, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 67.386, "width_percent": 3.739}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.788363, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 71.126, "width_percent": 3.161}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.801564, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 74.287, "width_percent": 5.513}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8107, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 79.8, "width_percent": 3.354}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.817311, "duration": 0.00437, "duration_str": "4.37ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 83.153, "width_percent": 16.847}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-349999109 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-349999109\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-12606495 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-12606495\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2064732447 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2064732447\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1702010961 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlRLVXZySTZDYUFLcnBiZkFJaUp2Y2c9PSIsInZhbHVlIjoiUzEyMHZ4YSt2bFlIKzZ4MXBnT2FQUmVGc0RsbEtTazRaYjFaQWxSTHRQenl5TzVyTW5wMlRibmlYYXRvaVlWOVFXNzY0dmM1dWVJZEo1L29DVzJ3WTZ3YUZRSE5NOC95TXlXN3BVc1hZZmRBNzdpRGtwRUpDdjArcmtWSjYxMnMweVhLTVNyZnRsS3RZWWxnMXU4bVMvZmovdFRXRW1nVkVyemxSMWNkUUdVem1Ea0RBUEJ0cTFpY2x4UHZyUkxsSEVFVjEvUjdLdldTSVlwcENwa2l4YUVnRUhoK3paTnQ2MHhodEYwS3oxTlFPV0Y4S1NQeU1ITmNwcG5PQnlpRWdBT3hydkcvZUZMa3dzMmJQN0RYQUpwRDBjU2p4SXBrY2plc0pscWYzSVNoT3kvVzl3TlNkaTN4a3BHS0Eydmo2aTFwa3RiWTNlN1Z2YjFTLzFkaXJLd2szTVR6UVVtODRYODNRUC9NV0dQMGhWdWxYTjUwWnRxWWkvanF6MHZtb2JzcSsxMW9NOEVZU1RKYjNQNkcrYi8vc3pRSHFGem9Gb25wUzl0aUNzOXlmRWpaU2RPcm9BWlZJUzZPSTRsVWdCTnVZSmF1TytXeWNXYjFFa0VFUHQxNkJsNHR3bVNvZ3UxUlA5c2ZkbjlZc3U3QWdlYnhiOGIwNHptSGJsTHgiLCJtYWMiOiI3MTBkNDQ1ODcyMTM3NTVjOWFkOGJlNjJiMmFhNzg0MDZiYjAxY2ZiYzNhODliZGRiYjgxZTcxYWVmZDQ1ZWNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkxCdWNsWUdtSWlNemFqcGxiU2cxcmc9PSIsInZhbHVlIjoiN0I1WDJ6VnNIRmdYdVIwMzMzRDNRUWtjYktDQndNQitIL0duOVRXblpSS0M3YUtLTVBlSkpLaHhWV1RMN1licnRYY3pkQjlNLzhJcE5pcEJoSkltTjVjd09mMzRaM2VncnVqcGYwaFBJa0ZjRmVRWWtHb3YzQTRlWjNzQXJHZElmSXlTbk9RRFFaQ3JUZFJxYzRJQjM3T1pMdFBrMS9PN3RvS1ZPbmU5eFRrSnpJeGpDUXQwcit4Ym91QTRLRnB2ZFBNUHN2enRvVWxhZmdaR2xIMWgrcG5KMHRrdFU0ZjRPb3hRd0FrUUV2ekFkS0E5dmhsNjhtWEN6MkdvanZrbmtIQjRBQlJKcmp4bkFYeTRmblZEUUhtN0JmZ2VNSC9abHpjd0JaSXNibFQ5WFNGL1h4Z1ZmcnlmMEZOclhxU0k3UVNoSVIxS21Tazc0S0UvVXQ5SC8rKzFVNlBJSXBBVFhHd2lYdzVlZUpleDhUc0JrN2EwMzJDQ1dORWZMOXVMVlhoeEdaZENxRE9tUHE0VEZkVGlaZzllZzNPaHVoSmg3dytEVVpYb1d6WXptL1JYVStpOHFpazFPRWIxNUJMb3hVVTBaNW4zQmZsWHR2bUVBSnRvNWtXRWV1cUxIRzdONGI4TXE1bDFkNjJtb1RUWlVYdGVHLzdzTGdqbk1jdXoiLCJtYWMiOiIzNTgxMTc1ZGMyZjAzMzc5M2RhY2YxOWVkNjI4YmFjYTA5MDI3MjQzNDNhZTVjN2YwYTRlMzBkYWNjMzlhYjVlIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlMwTHpuMjVPb0E4UnYybXhYQjRVMHc9PSIsInZhbHVlIjoieUR2SHRSVzZySi9tWFNXaTY4NEdHRytPQU9wSEYyRGthZU1KSzFFVkRSRGZpK3hwaGNBU3JGbjBsTHBwWDlNK3dkS1FhUlNwS0E0YXg1Y01IRDdkQ0lpanRmdHEvdVlSeWQwV1VJQkFHWTh3NXplNFlVek9sUzRZaEVMbGlHV0xiUTFLMm1VZmcvNkR5SHllTDViM3lCSTBtZ2xwWHIvSDJQVDMyc1hlVEE5VWJQdExOWGNhVFlrZTYvcUEwT2VyVVhQcmFhSHF5Z1lIU21laDhaMEVPN3J5RDlLbTFwT2x0Zk56T3BGYkk3Y29aS2dBeVYxRlVCVnBXb2FydmtRT2ZJY0NJRGE4S05MNi9hWWFzR0ZmRllIb1lqQnNLWC9rOUpPdE5XbEZrMUNpNEV3Y0RWRkEyRzNzTUtLNkkxSmU5QjBMdXVUT213bkQ5c1RIVXBST3o0aDMrTDhEeWRKM21hc2tRbkNyeVJjcXZ6Zy9mTXRIbjc1REx5TXl4bis4U2dRalpMQnBTc0d3MWExbFNMZHlyNG5IQXVVV1NGVEpVVGI1S1F6NmNWT0EwNWU1MEtvTmZzcmNyU0lucTFPcHVvaHVaU3RoVUlBci93VFVXTzZuNk51N2VXbVo1WE01SXE5UXJydGVSd1FSWEhjM2t0VDBYTmFRWEY4SGdCL3AiLCJtYWMiOiI3MzBjMTZjZTUwYTMwN2QxMmU1MTBlZDQ2Nzc2N2NhMDI0OGM0ZjE0NTc4MDZiM2U0MzQxYjIzYmI3ZDA2YTEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1702010961\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1503353650 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21K3ZmFzZyr4s2pH9aKWRqwEKhgdUCnDfhl6jx78</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1503353650\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1039080567 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:37:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVWQ2xDMzUyUUJHWTZKeHZCekFlaUE9PSIsInZhbHVlIjoielVQQ0UxVzBBUGNhRExBUU5aY294SnVCZ05JSEkwT0IvdVNlQ0ZIelJtOGcrRzB0ejhMcEtMV1l2a1FHSlFQR3h2YzVOcTZwd3BJc3BGY0NIMGNYNGdFOXZGc3o2MytvbFppVnRNc1J4N2haQWpQRlVUUHpXdEdnWGVISmR1dW10b1VrcXJtQjNZTXlKa2VDS09KcFJrd3hqUlUvNS9reUVSOWI1b0l5ZC81cVdVRnJQSDFxTFRoRE05SUNJdSs4ai9KU0Q3ZTF3ak85dngrcnk3QUFOZXVDZUpFTFdqUG1GV2NRWGYzV0lZSnYzWG5vczhHQmxIQUl6ZGk2QVV6ZitiVWlEdnhEWkk5blZRZUpSQ0QyMzRUcGoxMnFMN3h6cy94MnpLTWxkSmJIeVhIMmdXd0xZQXRSbTJPcHplNXdOZTYwMG1KWjdUUDF4UzRNNHlOVGxiZnc4M3JPQWd6V1p3RWRrbEVOUEkyeUEyN3VxL3pTZVA1ZXFTMklFOVQ3aG9PQkhsVjVscmVqTm9xVkxTNDRqYis2UFErdWsxdW5aVEtzaStxZ0t5RitzVmFBWU5PQXZHRnlXdkdHZkNGeERvNWF3cWZ2YTJqZUVjTCtmTXdIYnFjWTdMSkdMem81M0dub3F4bTRWdWluK0p0dXVENlBUUnBhZmVaYlArMG4iLCJtYWMiOiI0YWY2ODk5NGNiOWIxYjdjOTFjZWEyZjUwM2VkYTg5NGRjYmFiZjIwODBjOTZlYjE1ODQ2ODc5NDQzNTFjYTYxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:37:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IkFUNVVSeW1mNURFWXN3Q2lyMGZCZkE9PSIsInZhbHVlIjoiblNKaEN0UVJGWVdZYmY0cE5mQWVYbkx6NTRXejNTU1M1b3JKQmhJWERFOXZpZEVXR25Kc3RrNFNYV1NTT3hLOTNvSkNTaEJPYTlVNDFlT1c5b3VVakNISTJSQTViNG5nNHE4bHlaNkhQMzZLdUlmc0phbmhPUUo1TWFzWEpmNDJmUmsxbGl5QnQvVS9WTWFyL1Y1RmtBRzQwcDRUQ0tqc0tPVS9ZVGFieDl5THdjdndYRGF5MWhnY2ZjRWtxMGxSSmZpSlBrMTFSL09yMjBCMTRPVmhWVHJ6cjRFc3JuZU82amo0NE1WUDdwMko2MzNRc3ErQ1NlazVvSUkweW5DeWEwQWhxUmROa2pGS3RsMC9IR1FHcHJucmR5Z21qaS9wRlpPd0duSkk3WVFaQk5rbmV0T3ZUMldwOHhQVGVQNDNFY2J1VUU0dS9BUXVKR2IvZ3dWS0tlZEpLVkVDTDdzaytIYktyT1dGdnY3WWlPbk1vNEcyd1F1dlo1NSt1TDV1ZHJIR2dhZXJnWUxXY3AxRkpXZ0Z2VGlzR1docHBVdlFQeFpkMW1JaEdQY3NUazJRbHhVdmxSMmNMTG0yRHNuRitISnQyMkVKNEVzOUgrTHFOTVVqdGQwM0drazk3RHlwN3FISWFENkMvaXQ5WHlnSEJEOWNFU0JrMUdkUE1NaUkiLCJtYWMiOiI1Y2ZkMzdjMjkzMDZmZWRhYmFhYThkYTc0YjY4ODZiMmFmYjFiNjk4ZmU2MmI2NjcxYjg3MGE5YTY2ZDZjYzUzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:37:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVWQ2xDMzUyUUJHWTZKeHZCekFlaUE9PSIsInZhbHVlIjoielVQQ0UxVzBBUGNhRExBUU5aY294SnVCZ05JSEkwT0IvdVNlQ0ZIelJtOGcrRzB0ejhMcEtMV1l2a1FHSlFQR3h2YzVOcTZwd3BJc3BGY0NIMGNYNGdFOXZGc3o2MytvbFppVnRNc1J4N2haQWpQRlVUUHpXdEdnWGVISmR1dW10b1VrcXJtQjNZTXlKa2VDS09KcFJrd3hqUlUvNS9reUVSOWI1b0l5ZC81cVdVRnJQSDFxTFRoRE05SUNJdSs4ai9KU0Q3ZTF3ak85dngrcnk3QUFOZXVDZUpFTFdqUG1GV2NRWGYzV0lZSnYzWG5vczhHQmxIQUl6ZGk2QVV6ZitiVWlEdnhEWkk5blZRZUpSQ0QyMzRUcGoxMnFMN3h6cy94MnpLTWxkSmJIeVhIMmdXd0xZQXRSbTJPcHplNXdOZTYwMG1KWjdUUDF4UzRNNHlOVGxiZnc4M3JPQWd6V1p3RWRrbEVOUEkyeUEyN3VxL3pTZVA1ZXFTMklFOVQ3aG9PQkhsVjVscmVqTm9xVkxTNDRqYis2UFErdWsxdW5aVEtzaStxZ0t5RitzVmFBWU5PQXZHRnlXdkdHZkNGeERvNWF3cWZ2YTJqZUVjTCtmTXdIYnFjWTdMSkdMem81M0dub3F4bTRWdWluK0p0dXVENlBUUnBhZmVaYlArMG4iLCJtYWMiOiI0YWY2ODk5NGNiOWIxYjdjOTFjZWEyZjUwM2VkYTg5NGRjYmFiZjIwODBjOTZlYjE1ODQ2ODc5NDQzNTFjYTYxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:37:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IkFUNVVSeW1mNURFWXN3Q2lyMGZCZkE9PSIsInZhbHVlIjoiblNKaEN0UVJGWVdZYmY0cE5mQWVYbkx6NTRXejNTU1M1b3JKQmhJWERFOXZpZEVXR25Kc3RrNFNYV1NTT3hLOTNvSkNTaEJPYTlVNDFlT1c5b3VVakNISTJSQTViNG5nNHE4bHlaNkhQMzZLdUlmc0phbmhPUUo1TWFzWEpmNDJmUmsxbGl5QnQvVS9WTWFyL1Y1RmtBRzQwcDRUQ0tqc0tPVS9ZVGFieDl5THdjdndYRGF5MWhnY2ZjRWtxMGxSSmZpSlBrMTFSL09yMjBCMTRPVmhWVHJ6cjRFc3JuZU82amo0NE1WUDdwMko2MzNRc3ErQ1NlazVvSUkweW5DeWEwQWhxUmROa2pGS3RsMC9IR1FHcHJucmR5Z21qaS9wRlpPd0duSkk3WVFaQk5rbmV0T3ZUMldwOHhQVGVQNDNFY2J1VUU0dS9BUXVKR2IvZ3dWS0tlZEpLVkVDTDdzaytIYktyT1dGdnY3WWlPbk1vNEcyd1F1dlo1NSt1TDV1ZHJIR2dhZXJnWUxXY3AxRkpXZ0Z2VGlzR1docHBVdlFQeFpkMW1JaEdQY3NUazJRbHhVdmxSMmNMTG0yRHNuRitISnQyMkVKNEVzOUgrTHFOTVVqdGQwM0drazk3RHlwN3FISWFENkMvaXQ5WHlnSEJEOWNFU0JrMUdkUE1NaUkiLCJtYWMiOiI1Y2ZkMzdjMjkzMDZmZWRhYmFhYThkYTc0YjY4ODZiMmFmYjFiNjk4ZmU2MmI2NjcxYjg3MGE5YTY2ZDZjYzUzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:37:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1039080567\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-814562315 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-814562315\", {\"maxDepth\":0})</script>\n"}}