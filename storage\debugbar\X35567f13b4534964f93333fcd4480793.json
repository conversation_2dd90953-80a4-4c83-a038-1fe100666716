{"__meta": {"id": "X35567f13b4534964f93333fcd4480793", "datetime": "2025-07-31 06:29:45", "utime": **********.650739, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943383.423287, "end": **********.650792, "duration": 2.2275049686431885, "duration_str": "2.23s", "measures": [{"label": "Booting", "start": 1753943383.423287, "relative_start": 0, "end": **********.444288, "relative_end": **********.444288, "duration": 2.021001100540161, "duration_str": "2.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.44432, "relative_start": 2.****************, "end": **********.650797, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "6XiKPjBgrXYbErZ1GkmqRu1XYoWcefeLRUsLgJWY", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-525621714 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-525621714\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1844144889 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1844144889\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1257215755 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1257215755\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1958336292 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1958336292\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-748895392 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-748895392\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-74853878 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:29:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijh4TGZhcnBsMEwwUjU4bVJIUjArMUE9PSIsInZhbHVlIjoiV1FRNi9sd3gzSmFpb2pDWXJKcEduSkEzbGlQZWtwWjVRQ3BrbkprNklWNmxxVUdpbzZ1dGdoTzhxZ0tDRU9jd2Fta3NvVUxZVitOam9NK2lvclhBd2hLcTdxZ1lHaDVxb3A1dGNlRFVaZ1hwU3dUaUdUQU5zZTdQQ0tNbkE4STNBWXJzYTRDdnFTWWZVM2k2MG85L3Mrc0xTaVluNVNoRFJNZU5aMzZ5WW9hVU1UclBpMklKUE81bmtKSC9HZXpNUmlLaGlOc20raytQMkVkcmdKN2lFOEJUTmtxYXJtYzNhOVA1VDhiMUlpaDc4YmNzYWkxcUFOWUFuMVBSY2ROeFZVYU8yL2FrRk9yM29XTm9yemtEM2c4L3cwSm5FY3BkSmFJbFc5aFpKTVp1ZmZJTjdNOFFURFQ4WFBHMTBqRmY0V2ViRVI1cDJtSUs2a3lzY1RhcmZPVnI1ZmNYWGhOS1c4VklTQ3JNQXFVeWlMTzM1ejZQL1Fsc0VMWU5zYWUzSFdDQ0Y4cXB1Z3JwMkRuL0dScmVTVkVIV1d6V3V1UzloR1laS3RJRG1KVDlwZXFaQ3o3TkhxbElQVW5Nb3ZIdnNoczh3bEl3a2Y4Ui9tTHhSYy9mYk5xdHJUVVlwRk55bmlZL1VRcm9rZDdjUFNGTHhqSGVBZk9mQjNnRlVIdzMiLCJtYWMiOiI4YjVmZDU4MDM3MzM0YTA1NWZiMTFhNjFiY2M5MzUyYjI0NDgzMmM1ZTc3MTI5Mzg5MTdiZTJiYjdhY2ViYjY1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjdiRW5mSWw2SHgxYUFjVzg3SmZXTGc9PSIsInZhbHVlIjoiZGduZUJSWHB6T2ovYXYra0xaYnlVMnBuVXRWbE9DUWdrMFhUcnVwWmN3dkV3alJPU0tOWXN4c21QN2ZMSnkvZUlpbzQwMi9QNkFDeVR0THpaRUpaNWtyd1F5VlBha2R5aThUMjhocFNGTTRyR21kQk1vRncyaDBLaDdTeEh3THRyVFpHZFVNZ2FmdlU3UDc0blpqZURkU2pCV1kyc3FnNy85Z3BRMW4wa083WnF6ZXlxYWhPZkZhV3RGSkNMbHc2elV0Y3k5VjBOa0NWc0NXL0hsMGd1a09VQnZBMXRINE12amxJNnVnK2lvN3l0eUNDRDhlZ25RMWVTbmdzcUlLY2RKZkhndlhUaEtQOXZ5Q21lZTdYc21ZWFJEakRMU3VSMDk2MmxTTnkxVlFNWm4ra1lmMURBbmRJT3BubW9QTmNDRkVmRmRDb2F1UG1rMkhKbnRUWVF4RC9LWWxQZmx0MkU0ejJzcmQ3bExxMXdhMjBRM2dKdTZqSnVja0xUUC9wUkQ5R3ZIQlF6R3pCY0Nhc1UzSmVkNE1WdExCdmt1REdEaUh1NkNOc255aTNYNG16cVlON3lNU2FuN1hFZllNTm5temxUSnVZWXhCK3Y0Zi9ZT0JPZm5Cd2F5N09Vd2VDbm5NSXpPL3dna1JCMWZVWnlvVkZkTEhKTFBQOXo1cHIiLCJtYWMiOiIwY2I5NTk0NDFlYzY4YzQ1OWU2MjA0YmJmZDdhOWNjZTNlYTJhNjM2YzRjOTdlOWFhMmQyZjEwMTEwOTEwYmIwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijh4TGZhcnBsMEwwUjU4bVJIUjArMUE9PSIsInZhbHVlIjoiV1FRNi9sd3gzSmFpb2pDWXJKcEduSkEzbGlQZWtwWjVRQ3BrbkprNklWNmxxVUdpbzZ1dGdoTzhxZ0tDRU9jd2Fta3NvVUxZVitOam9NK2lvclhBd2hLcTdxZ1lHaDVxb3A1dGNlRFVaZ1hwU3dUaUdUQU5zZTdQQ0tNbkE4STNBWXJzYTRDdnFTWWZVM2k2MG85L3Mrc0xTaVluNVNoRFJNZU5aMzZ5WW9hVU1UclBpMklKUE81bmtKSC9HZXpNUmlLaGlOc20raytQMkVkcmdKN2lFOEJUTmtxYXJtYzNhOVA1VDhiMUlpaDc4YmNzYWkxcUFOWUFuMVBSY2ROeFZVYU8yL2FrRk9yM29XTm9yemtEM2c4L3cwSm5FY3BkSmFJbFc5aFpKTVp1ZmZJTjdNOFFURFQ4WFBHMTBqRmY0V2ViRVI1cDJtSUs2a3lzY1RhcmZPVnI1ZmNYWGhOS1c4VklTQ3JNQXFVeWlMTzM1ejZQL1Fsc0VMWU5zYWUzSFdDQ0Y4cXB1Z3JwMkRuL0dScmVTVkVIV1d6V3V1UzloR1laS3RJRG1KVDlwZXFaQ3o3TkhxbElQVW5Nb3ZIdnNoczh3bEl3a2Y4Ui9tTHhSYy9mYk5xdHJUVVlwRk55bmlZL1VRcm9rZDdjUFNGTHhqSGVBZk9mQjNnRlVIdzMiLCJtYWMiOiI4YjVmZDU4MDM3MzM0YTA1NWZiMTFhNjFiY2M5MzUyYjI0NDgzMmM1ZTc3MTI5Mzg5MTdiZTJiYjdhY2ViYjY1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjdiRW5mSWw2SHgxYUFjVzg3SmZXTGc9PSIsInZhbHVlIjoiZGduZUJSWHB6T2ovYXYra0xaYnlVMnBuVXRWbE9DUWdrMFhUcnVwWmN3dkV3alJPU0tOWXN4c21QN2ZMSnkvZUlpbzQwMi9QNkFDeVR0THpaRUpaNWtyd1F5VlBha2R5aThUMjhocFNGTTRyR21kQk1vRncyaDBLaDdTeEh3THRyVFpHZFVNZ2FmdlU3UDc0blpqZURkU2pCV1kyc3FnNy85Z3BRMW4wa083WnF6ZXlxYWhPZkZhV3RGSkNMbHc2elV0Y3k5VjBOa0NWc0NXL0hsMGd1a09VQnZBMXRINE12amxJNnVnK2lvN3l0eUNDRDhlZ25RMWVTbmdzcUlLY2RKZkhndlhUaEtQOXZ5Q21lZTdYc21ZWFJEakRMU3VSMDk2MmxTTnkxVlFNWm4ra1lmMURBbmRJT3BubW9QTmNDRkVmRmRDb2F1UG1rMkhKbnRUWVF4RC9LWWxQZmx0MkU0ejJzcmQ3bExxMXdhMjBRM2dKdTZqSnVja0xUUC9wUkQ5R3ZIQlF6R3pCY0Nhc1UzSmVkNE1WdExCdmt1REdEaUh1NkNOc255aTNYNG16cVlON3lNU2FuN1hFZllNTm5temxUSnVZWXhCK3Y0Zi9ZT0JPZm5Cd2F5N09Vd2VDbm5NSXpPL3dna1JCMWZVWnlvVkZkTEhKTFBQOXo1cHIiLCJtYWMiOiIwY2I5NTk0NDFlYzY4YzQ1OWU2MjA0YmJmZDdhOWNjZTNlYTJhNjM2YzRjOTdlOWFhMmQyZjEwMTEwOTEwYmIwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74853878\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-294390087 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6XiKPjBgrXYbErZ1GkmqRu1XYoWcefeLRUsLgJWY</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-294390087\", {\"maxDepth\":0})</script>\n"}}