{"__meta": {"id": "Xb989cab1f341789990c800f517ae0c7d", "datetime": "2025-07-31 05:37:22", "utime": **********.1178, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940240.379154, "end": **********.117865, "duration": 1.7387111186981201, "duration_str": "1.74s", "measures": [{"label": "Booting", "start": 1753940240.379154, "relative_start": 0, "end": 1753940241.967568, "relative_end": 1753940241.967568, "duration": 1.588413953781128, "duration_str": "1.59s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753940241.967622, "relative_start": 1.***************, "end": **********.117882, "relative_end": 1.6927719116210938e-05, "duration": 0.*****************, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pAVM3JoiXd2cLNFSlEB3OzrKAtfyquI3oYRVBz5G", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-977617841 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-977617841\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-993474790 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-993474790\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-756417024 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-756417024\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1096464802 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1096464802\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-45486722 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-45486722\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:37:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InM0RDQvYy9waDFPQnNqNk5MRk9EZXc9PSIsInZhbHVlIjoiaVNOYWxJUlh0Y0pUUEtPd3JuNUdsV1hlN3JjWUJRNjJwdjVicm1EUEJGWXlJS2kzNDU3Ym5RdlNjZkphcVF0SzRyQkREaWdIbWJnY3pGVC9UVWF0SEFmRUxsRkROUmM4cDExeGJlVUExb3B6d1pZTVlHd0FncGxiQlNmSUJ2MjJnVFJBdFhnellTRlhEeHR6OWEyVjU3TGdFc2hmT1dBcXlZRGtlcDQvK2RRSXY4RWdlcWZIekJ6Mm81dHRvM3hRNU9SbzUrS1R4UHJYTXhEQ2FYOTdzT3ZCeW52MERjRVExeUcvcWh5RkRXdk1IN3c4RkRvTVgzTUI5VlhZTW5vM0NMNnVhNTVTY0ZHbWI3Umwvd3ZqT1o3WjU3L3BwYUxXanpkck9GSlRKQzQxWlZEaTVLeTNwdUZ5aEJSWHVQay91ejRtTWNCSUl4aUxoYktMQzFTQkFNSnp2dGRsVSs0MmIvV05ieXVqOEYxWFhRa09JN1I4a051eGt5YlRML0YwcmJmWFJNanJnWFdxeDFBOEF0QlgrcTRHbUVxbTRRU3NTUTU3VFpnczhyREZRYy9kZm5WMmlRa3Q1TG4yYzlxTkVOcmkxOE9kdnB2SHF5VGltT00ySWJxZjhXUUptczRnQWtOaGdPbEUyakVBaEZzWTQ1WGllZk0zdUd2U1dTWDQiLCJtYWMiOiJmM2NmN2Y3NmRiNmEzNDM0NjZlZDlhMDdiYmM2YTFlMjcwZWEzMzdhZTVmODhlN2Q4Nzg2ZWMyMTE3NTQzODlmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:37:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlVYY2RBVHJVLzZXcm1SN3gzVlNPeFE9PSIsInZhbHVlIjoiSmwzbFlVYW5kdTdwdHprcFFGQXdOQncxbVFNRXk0bUJjSXhMRzVVa2NnMVRqSnhveGxSaDhWZnZheXFxRTRtNkIyajFHTFNCWFJzVWZPanlRZHJXWklraVhnaWJmSXFvMXY0OXZHaGp2QytQYVRZMW9JcHg5aThOWFVRQVpXY1pNMkdwZ2xxUytLZ1I0VmZNaDRIZ1dQWFJlOVdUcEIwU1JhZDFibDhLWENaTEM0MWRTRkZjQlRBbHdzaEMyZUN3Ky90MFFjaUdGN3F6ai81K0JYL29ZTzJqZDVaWFdESzhLK3JBTUlVbVQrYnh1NlVOSFVtdmlyS3UrT1dVbnpGZ0ZPWHF6NHhocE1rQXd3MHFSUjZxNW52bSs2WDJ3MWdrdzJMVmd6ZkdIcW9tNkNjK1ljR3ppazloc3BvQzVSTUxkTXVUcXo4dG9VQVB4anFpYVBta3JOR2lKTWQ4TExTQ2drWE9Odm94NitqVG9za1A0ZDVUQ0xleXZpL3R3M1pKQ3ZEZ3pmZm5pL21MMTdEUXFNdlNwT1RqdUcva1BEbzNHNis4bFhyRGlpRVdxTncwK2FkeWdEd0lVWU1XZzZpU0xIaXJSQytndURTNVNuZDZ4UldFaG9GU0Z5dU1LUFI2MUpLT1hkNDhrRGdtZ1dpd2QrNGZCbVlvZXZzOU9aZzMiLCJtYWMiOiIxOGU1ZmM0M2I2NjgxNjI3Y2ZmOWM4OGU0YWFmODE2NTkyNmU4NzFjOWE2NzMwY2Q1OGQ4YjAzNjMzNjE1MGViIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:37:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InM0RDQvYy9waDFPQnNqNk5MRk9EZXc9PSIsInZhbHVlIjoiaVNOYWxJUlh0Y0pUUEtPd3JuNUdsV1hlN3JjWUJRNjJwdjVicm1EUEJGWXlJS2kzNDU3Ym5RdlNjZkphcVF0SzRyQkREaWdIbWJnY3pGVC9UVWF0SEFmRUxsRkROUmM4cDExeGJlVUExb3B6d1pZTVlHd0FncGxiQlNmSUJ2MjJnVFJBdFhnellTRlhEeHR6OWEyVjU3TGdFc2hmT1dBcXlZRGtlcDQvK2RRSXY4RWdlcWZIekJ6Mm81dHRvM3hRNU9SbzUrS1R4UHJYTXhEQ2FYOTdzT3ZCeW52MERjRVExeUcvcWh5RkRXdk1IN3c4RkRvTVgzTUI5VlhZTW5vM0NMNnVhNTVTY0ZHbWI3Umwvd3ZqT1o3WjU3L3BwYUxXanpkck9GSlRKQzQxWlZEaTVLeTNwdUZ5aEJSWHVQay91ejRtTWNCSUl4aUxoYktMQzFTQkFNSnp2dGRsVSs0MmIvV05ieXVqOEYxWFhRa09JN1I4a051eGt5YlRML0YwcmJmWFJNanJnWFdxeDFBOEF0QlgrcTRHbUVxbTRRU3NTUTU3VFpnczhyREZRYy9kZm5WMmlRa3Q1TG4yYzlxTkVOcmkxOE9kdnB2SHF5VGltT00ySWJxZjhXUUptczRnQWtOaGdPbEUyakVBaEZzWTQ1WGllZk0zdUd2U1dTWDQiLCJtYWMiOiJmM2NmN2Y3NmRiNmEzNDM0NjZlZDlhMDdiYmM2YTFlMjcwZWEzMzdhZTVmODhlN2Q4Nzg2ZWMyMTE3NTQzODlmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:37:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlVYY2RBVHJVLzZXcm1SN3gzVlNPeFE9PSIsInZhbHVlIjoiSmwzbFlVYW5kdTdwdHprcFFGQXdOQncxbVFNRXk0bUJjSXhMRzVVa2NnMVRqSnhveGxSaDhWZnZheXFxRTRtNkIyajFHTFNCWFJzVWZPanlRZHJXWklraVhnaWJmSXFvMXY0OXZHaGp2QytQYVRZMW9JcHg5aThOWFVRQVpXY1pNMkdwZ2xxUytLZ1I0VmZNaDRIZ1dQWFJlOVdUcEIwU1JhZDFibDhLWENaTEM0MWRTRkZjQlRBbHdzaEMyZUN3Ky90MFFjaUdGN3F6ai81K0JYL29ZTzJqZDVaWFdESzhLK3JBTUlVbVQrYnh1NlVOSFVtdmlyS3UrT1dVbnpGZ0ZPWHF6NHhocE1rQXd3MHFSUjZxNW52bSs2WDJ3MWdrdzJMVmd6ZkdIcW9tNkNjK1ljR3ppazloc3BvQzVSTUxkTXVUcXo4dG9VQVB4anFpYVBta3JOR2lKTWQ4TExTQ2drWE9Odm94NitqVG9za1A0ZDVUQ0xleXZpL3R3M1pKQ3ZEZ3pmZm5pL21MMTdEUXFNdlNwT1RqdUcva1BEbzNHNis4bFhyRGlpRVdxTncwK2FkeWdEd0lVWU1XZzZpU0xIaXJSQytndURTNVNuZDZ4UldFaG9GU0Z5dU1LUFI2MUpLT1hkNDhrRGdtZ1dpd2QrNGZCbVlvZXZzOU9aZzMiLCJtYWMiOiIxOGU1ZmM0M2I2NjgxNjI3Y2ZmOWM4OGU0YWFmODE2NTkyNmU4NzFjOWE2NzMwY2Q1OGQ4YjAzNjMzNjE1MGViIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:37:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pAVM3JoiXd2cLNFSlEB3OzrKAtfyquI3oYRVBz5G</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}