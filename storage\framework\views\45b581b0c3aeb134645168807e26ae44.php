<!-- Payment Gateways Tab Content -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="mb-0"><?php echo e(__('Payment Gateway Settings')); ?></h4>
    </div>
</div>

<!-- Payment Gateway Cards -->
<div class="row">
    <!-- Stripe -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <img src="<?php echo e(asset('assets/images/payments/stripe.png')); ?>" alt="Stripe" class="img-fluid" style="max-height: 40px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="theme-avtar bg-primary mx-auto" style="width: 60px; height: 60px; display: none;">
                        <i class="ti ti-credit-card" style="font-size: 1.5rem;"></i>
                    </div>
                </div>
                <h5 class="mb-2"><?php echo e(__('Stripe')); ?></h5>
                <p class="text-muted small mb-3"><?php echo e(__('Accept credit cards and digital payments')); ?></p>
                <div class="form-check form-switch d-flex justify-content-center mb-3">
                    <input class="form-check-input" type="checkbox" id="stripeToggle" <?php echo e((env('STRIPE_KEY') && env('STRIPE_SECRET')) ? 'checked' : ''); ?>>
                    <label class="form-check-label ms-2" for="stripeToggle">
                        <?php echo e(__('Enable Stripe')); ?>

                    </label>
                </div>
                <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#stripeModal">
                    <i class="ti ti-settings me-1"></i><?php echo e(__('Configure')); ?>

                </button>
            </div>
        </div>
    </div>

    <!-- PayPal -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <img src="<?php echo e(asset('assets/images/payments/paypal.png')); ?>" alt="PayPal" class="img-fluid" style="max-height: 40px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="theme-avtar bg-info mx-auto" style="width: 60px; height: 60px; display: none;">
                        <i class="ti ti-brand-paypal" style="font-size: 1.5rem;"></i>
                    </div>
                </div>
                <h5 class="mb-2"><?php echo e(__('PayPal')); ?></h5>
                <p class="text-muted small mb-3"><?php echo e(__('Accept PayPal payments worldwide')); ?></p>
                <div class="form-check form-switch d-flex justify-content-center mb-3">
                    <input class="form-check-input" type="checkbox" id="paypalToggle" <?php echo e((env('PAYPAL_CLIENT_ID') && env('PAYPAL_CLIENT_SECRET')) ? 'checked' : ''); ?>>
                    <label class="form-check-label ms-2" for="paypalToggle">
                        <?php echo e(__('Enable PayPal')); ?>

                    </label>
                </div>
                <button class="btn btn-outline-info btn-sm" data-bs-toggle="modal" data-bs-target="#paypalModal">
                    <i class="ti ti-settings me-1"></i><?php echo e(__('Configure')); ?>

                </button>
            </div>
        </div>
    </div>

    <!-- Razorpay -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <img src="<?php echo e(asset('assets/images/payments/razorpay.png')); ?>" alt="Razorpay" class="img-fluid" style="max-height: 40px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="theme-avtar bg-success mx-auto" style="width: 60px; height: 60px; display: none;">
                        <i class="ti ti-currency-rupee" style="font-size: 1.5rem;"></i>
                    </div>
                </div>
                <h5 class="mb-2"><?php echo e(__('Razorpay')); ?></h5>
                <p class="text-muted small mb-3"><?php echo e(__('Indian payment gateway solution')); ?></p>
                <div class="form-check form-switch d-flex justify-content-center mb-3">
                    <input class="form-check-input" type="checkbox" id="razorpayToggle" <?php echo e((env('RAZORPAY_KEY') && env('RAZORPAY_SECRET')) ? 'checked' : ''); ?>>
                    <label class="form-check-label ms-2" for="razorpayToggle">
                        <?php echo e(__('Enable Razorpay')); ?>

                    </label>
                </div>
                <button class="btn btn-outline-success btn-sm" data-bs-toggle="modal" data-bs-target="#razorpayModal">
                    <i class="ti ti-settings me-1"></i><?php echo e(__('Configure')); ?>

                </button>
            </div>
        </div>
    </div>

    <!-- Flutterwave -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <img src="<?php echo e(asset('assets/images/payments/flutterwave.png')); ?>" alt="Flutterwave" class="img-fluid" style="max-height: 40px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="theme-avtar bg-warning mx-auto" style="width: 60px; height: 60px; display: none;">
                        <i class="ti ti-credit-card" style="font-size: 1.5rem;"></i>
                    </div>
                </div>
                <h5 class="mb-2"><?php echo e(__('Flutterwave')); ?></h5>
                <p class="text-muted small mb-3"><?php echo e(__('African payment gateway')); ?></p>
                <div class="form-check form-switch d-flex justify-content-center mb-3">
                    <input class="form-check-input" type="checkbox" id="flutterwaveToggle" <?php echo e((env('FLW_PUBLIC_KEY') && env('FLW_SECRET_KEY')) ? 'checked' : ''); ?>>
                    <label class="form-check-label ms-2" for="flutterwaveToggle">
                        <?php echo e(__('Enable Flutterwave')); ?>

                    </label>
                </div>
                <button class="btn btn-outline-warning btn-sm" data-bs-toggle="modal" data-bs-target="#flutterwaveModal">
                    <i class="ti ti-settings me-1"></i><?php echo e(__('Configure')); ?>

                </button>
            </div>
        </div>
    </div>

    <!-- Paytm -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <img src="<?php echo e(asset('assets/images/payments/paytm.png')); ?>" alt="Paytm" class="img-fluid" style="max-height: 40px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="theme-avtar bg-danger mx-auto" style="width: 60px; height: 60px; display: none;">
                        <i class="ti ti-wallet" style="font-size: 1.5rem;"></i>
                    </div>
                </div>
                <h5 class="mb-2"><?php echo e(__('Paytm')); ?></h5>
                <p class="text-muted small mb-3"><?php echo e(__('Indian digital wallet and payments')); ?></p>
                <div class="form-check form-switch d-flex justify-content-center mb-3">
                    <input class="form-check-input" type="checkbox" id="paytmToggle" <?php echo e((env('PAYTM_MERCHANT_ID') && env('PAYTM_MERCHANT_KEY')) ? 'checked' : ''); ?>>
                    <label class="form-check-label ms-2" for="paytmToggle">
                        <?php echo e(__('Enable Paytm')); ?>

                    </label>
                </div>
                <button class="btn btn-outline-danger btn-sm" data-bs-toggle="modal" data-bs-target="#paytmModal">
                    <i class="ti ti-settings me-1"></i><?php echo e(__('Configure')); ?>

                </button>
            </div>
        </div>
    </div>

    <!-- Bank Transfer -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <div class="theme-avtar bg-secondary mx-auto" style="width: 60px; height: 60px;">
                        <i class="ti ti-building-bank" style="font-size: 1.5rem;"></i>
                    </div>
                </div>
                <h5 class="mb-2"><?php echo e(__('Bank Transfer')); ?></h5>
                <p class="text-muted small mb-3"><?php echo e(__('Direct bank transfer payments')); ?></p>
                <div class="form-check form-switch d-flex justify-content-center mb-3">
                    <input class="form-check-input" type="checkbox" id="bankToggle" checked>
                    <label class="form-check-label ms-2" for="bankToggle">
                        <?php echo e(__('Enable Bank Transfer')); ?>

                    </label>
                </div>
                <button class="btn btn-outline-secondary btn-sm" data-bs-toggle="modal" data-bs-target="#bankModal">
                    <i class="ti ti-settings me-1"></i><?php echo e(__('Configure')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<!-- Configuration Modals -->
<?php echo $__env->make('finance.payment-gateways.modals', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Payment gateway toggles
    const toggles = document.querySelectorAll('.form-check-input[type="checkbox"]');
    
    toggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const gatewayName = this.id.replace('Toggle', '');
            const isEnabled = this.checked;
            
            // Here you would typically make an AJAX call to update the setting
            console.log(`${gatewayName} ${isEnabled ? 'enabled' : 'disabled'}`);
            
            // Show notification
            const message = isEnabled ? 
                `${gatewayName} has been enabled` : 
                `${gatewayName} has been disabled`;
            
            // You can add a toast notification here
            // showToast(message);
        });
    });
});
</script>
<?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/finance/tabs/payment-gateways.blade.php ENDPATH**/ ?>