{"__meta": {"id": "Xddb13b1cfe17386b2a2745cdf4267027", "datetime": "2025-07-31 06:13:46", "utime": **********.277161, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:13:46] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.268525, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753942424.140746, "end": **********.277201, "duration": 2.1364548206329346, "duration_str": "2.14s", "measures": [{"label": "Booting", "start": 1753942424.140746, "relative_start": 0, "end": **********.058218, "relative_end": **********.058218, "duration": 1.9174718856811523, "duration_str": "1.92s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.058255, "relative_start": 1.917508840560913, "end": **********.277205, "relative_end": 4.0531158447265625e-06, "duration": 0.2189500331878662, "duration_str": "219ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45942512, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00937, "accumulated_duration_str": "9.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.170809, "duration": 0.00608, "duration_str": "6.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 64.888}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.2166631, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 64.888, "width_percent": 12.807}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.228002, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 77.695, "width_percent": 12.7}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2378259, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 90.395, "width_percent": 9.605}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-678409371 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-678409371\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-768304275 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-768304275\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-421650906 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-421650906\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1000708780 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im9xT1BBOElDL0pZcTJvZ2ZBOWhoaFE9PSIsInZhbHVlIjoiS0JtS25XNFF3UlQyVFNoVUVrd2xjbDd1cmU5ajVpaVY1THM4ekJ5UVU2eGlnQkp3OGdvSVNhaGhjbnFGZG9qVFlSVklGTTB1b1BnelVZYkRtV2ZsTm1sRDFGOU45QTh2OUtIcWRmeUNYb0Y5aSsxS3ROT3diMkpITHByT3N0UUFBbUtZZGlTTURKMTFaWktrRU9zN2pBUFM1UVJFeS9DRDYxUUpFaENZMGg3TFZFK3pQbFNwUk9MTnpZNlFjMm1Yc0ppZnIremZpR3Q1Vkk3VWJUMTgrQkltOVh1SGhUZGx3cVRlWHVQbkJoNGRlVmpKd1U4Q0N6SFNrR0NrRmtqOTdCaG9ybW9jQXBhN1lPSkg5VXJMY2ErcXE3S283RktZdFJPTUluZ3ZCTW85Y0hvQ3ZaZFVqZ0ducDJYQ1hDc2crVE9ZZzhvRjdpWXNuS2xmck45UUNDOFBaTzM1ZjlTTU92eHNmbzZjM2hLOU8zTTIvRURmVktpMkVjeVBKSnZEQ0VLdnZSRVNnRmJXQmRubzRJL3A3RTY0dDU4ZHRYajNwV0MrSW9sYWhUcHR1UnBrM1dXNTFnL3F5dVNCTThCNzBZeGhDeFRkV3R2Rm14WWxialNkMWU2YllSR1o3dWJDVi8wUHpHamdqL1ZoMm05TlNJV3RqMW5Tajc2R1RWSGgiLCJtYWMiOiIzMDRlNTVhMDZhMWEzZGQ2Y2M2MGJkYzRhMjQwMzFkNmM2ODY4ZDk1NjBiNGM3MjQ4YzEwMmM2ZDMyNWRmODNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik90aENYYjdKK1FSUldhdTM0MnJHeXc9PSIsInZhbHVlIjoiSGZYVTg2NkFzY0NPWDl4TWNhZjRraTRxc2J3Q0E2NjAxN3FEajJrWFhMcGQvZkVWcHVQUWI0T0dwUkI3OHJubVp4RU5KbEtQL2RxV1B4T1JpTlRHL1BQaXdMZ1RNbmZpUFNncVFoMlNFVzA1dDhhdE9ENCtoUE4yeHVmTFJUWnhSbU1ORkNSVlpzOWFLOEV0RFp0bWtINlk1VFZhNEFIRzZmWEpNUGJ3RHFGMVJKTGJ6RDJzLzcrUnAyMEhLdWgrcGdnejcrWEd3YTFxc2lvWG5maFRnMGZiWGNGZ1FaMmdTUEx1Wm9tK1Nqb0gyczFXNWNKNmN2WFlQakhjaGZQd01wNndGWUtSdDNvc2FaaG5ZUXdZbC9pMVl1WHFRcERjV1RJVCtjbXg5QVdxUFJONTdNT1hGWnBpZmdrTDZtTGtUeTN5NlRFWnhJYngzY3lNMW4xR2lUZWVKdlphK210a0VzRmFWZm5GTzhYTUNtRXZBNk9wSVpUQUN0K0tkc1E0U0k4OTZtcmtndm5LUzQ0UDlveU1zNkhmU3Zuc0lUZEd1cU5pWEkvTXpRTUp5UEZHSDdsY25HcGdkUzRVVE1hbk1OU0dIemtteVpidTlQcUwyQWtvdzdidldjYWhCdlBUTzhmN1U0bHd5V0g1bEcwa2tYSW9NdVEzYUN4OHhuSnUiLCJtYWMiOiI0Yjc3OTc0MzNmZTljMTRiZjJjZDJjMmEwY2ExMzE3ZDY1NDNiNGU2MGE3ZDIyMTllODkzOGZjYmJhMzJmZjJkIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6ImVaRzBiMzNwMXppaHl5ZHhVaTdGTkE9PSIsInZhbHVlIjoicVN5TDB2SDMwUGRHQnRzUk43Z01KM3pXNVZpbkQxM0hXZU5CdWlYQUhMbk9qSFZHWUpVMzhZSlF1T00vbm5iV2puaG5teXliL0JhUTAzdmpsZ2VpV0c5Z3JsaGFma3RaU05HWGJXak9xU2JZS3daeFc4dWFNMW51ZXZpZHhxV1I1NlZvL0JsRVI4YklIekxvZG9sSDNsaU1qYldPVDhzTTNyU3lxaGVna0l0Mk9HZEZ6V25Qd0lia2JZdXp0cEw4VG9Qc2JDaXdYajJ4ZHp1WHhRTEd0TWhxN1MzV2ZkcURBWkVQOURFT0g2aE9aenp5amVocGlKWUdOZGV2VGJiZTNCUjlRa2pTSnBnSzZ4YjBTT2JjajFFRGpqYVZYZHRRc3h6ZXJyc29nN05FeGFoV1pudFk4endVK3FVdll5VUlKS2JSNFNTQWJ2UWtHY3pDTEI5RmlyTUxQSHRnK0FhelA4czIvbkN4SHBsYkQ4SElDci9ZVE9lcmFQbTFCL1lTZzlUTWxpam5wd2dXaHZKOTRTa1dZRWdSdTBYNGRWcWdyQ1FhU3JyTlBHR2hqRTN1clc3YmNRaTAwQjNOMFlmVFZhUHhCTGhXc3V1dk40eUR0ckFVR0pzZWxzZ3c5bXBGVUxFeXZuZGh6MnVGT3phNXZvZDB5RXRrWndmd0RrNHQiLCJtYWMiOiI4NDA3N2FiYWFlN2QzZjgzZGU0N2Q3N2M3MDI0ZjM1OWRiNDk2YzhhZDYwNTVkMmI5MTMzZjYwMDk2YWFmMDU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1000708780\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-629745744 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5LJND5bUclZnLzzClCMEaxKzZ5Yt8AAmGjprz0I</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sUbnPperdA1I3PaRxBpMGV4YsR5AVGq0ERh2LBdJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-629745744\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1793507134 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:13:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImwrK3E1NXZIMDd5bW1rNG1ucVVlUEE9PSIsInZhbHVlIjoiMElQYXVpQXpnZmRDaFZ6TUdIaXBtR21nK3JHTm1EUDNOYm0zcExSb2JxZm9xajBWZ0hSYTFZYWRWOXVNMi9yT0psMmFQdWVIRUZORmF0cVptQ1dUQk93ZXROU2c2Tk5LR2NySzJnenVuUzhYb1NLWFpGdXJaaEVIaWkycysweWxrWmFhcHR1UU5HSnplMXZ6cHVuMk9idEdZN2xjR3RPK0dRZS9CSlZMMW9lak9saDIweHA5WHNtQkdOWE1oZm4xRnJuQ21CMC80cFpxYmZId1pacDdYd1FUL0psOGZkeG4yZ3IrZTZLMDl5NDlnN2JkNkdrMUdlcC9sSHB3YnEwTFZ4d1hFWGFaM3ZxNlhCTkIrS3RRaTN6QzNHM3FJQlFFaEZWMEdkUzRlS0plczYvS0ZxbUZDeWwvdFNyazNPbkJlcWhMdXJFdmVpaTRhb2gxdzlVdklDcW9TWFJobEl4TmV2VllUMDlWK2JXS2RnU3dMbUhYYU1idGNUcXhzWU1GSFdYRFFmZWlOUTYySTZreUlmLzV2cjAycHZ1dFJCd0FIRjRPdTIzZkF6MmpTbEVQWGxSUklaZFlSZVFvc2VuTU1RVVIreGhHbk9KalFibG9DQlM0YkdUZXNPTjRFSWxPaENPcnlIaEQydUlJVFR0SXdDeXBFWkFEb2xPTXRZYU0iLCJtYWMiOiIzZjlmMDc3ZDE5ZjFkODQyM2E1Y2M4Mjc3NzQzOTg2NDBmOTM1MDg0YmE2YTk3ZTA3NWU4ZTYyOGI3OGE5MzUzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:13:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IjlnekxQR2NiaC9JYlRrQzUwcWhIWnc9PSIsInZhbHVlIjoibW9samRrQ1paQ2JlUlhtcHZDbXM4S01FTWprdVBNaGRlb3cxV0lDZS9YRHlmYXdPS3JaQmlYK2F3L2lvcTdBRlhxeHVaeVpmdFJNa1EyMkFwSGFnTkh4RXBVeWdUU0VQYjR3amM0RnRMNWN4cGhmQ3ZjNVVGODFaaUpTNHRNbDFDWGVjV2QrTE5lNFFUekpCbzl1WXBkUTVmL0c5bkRaVWhEY0N5S3NSNi8rTWRkVVNvU3ZsY2VYd3A3cUdoeXJRUnM2VjIwdHpJTDZJY1NFSUZtdVlhVUNpNW5MWjJVamxEdEQxTXFaZExjRUhSUkRJSEhjMTQwZGYyYjhadjlrdEtvSnJjVTBFdTgxcVhQa3lKN20zRFowVFoyajhVejNzWnRnMHlCejRGeEFNV0hScVl1SnNzNDZNZWJJcE9SWHlIdjVSU2E4RXhGVnhnRC9teldPeVJSMzZCK1Z2dEZuczhSM1owYW5McDYyTG8rNlhhVWRMclB1OVN3OFJyK2g3TVcxTTM1OVZBc3E5dmQ5V2kwcHhiQVhhZEIvUVlCMFJTMFRNM3EwVnJBWjBYQ1JuZnJsNFBic2JYb1NnQ2dDZlBXaDB5U1J5L2NPb2JYcER2bytNT0l2VDBHeWxCbCtwTFRSdkhXbHl5bVVSOStxdFQvdmV3NlAzVWU2VXNlRjciLCJtYWMiOiI2MjEwZGNjNTNjYmYwZTUyYjA3NDNiMmI4N2U2ZjI3YmFlODBjZmZlOWJlMzU2YWQzMmNjODQwMDA5YzM5NmMzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:13:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImwrK3E1NXZIMDd5bW1rNG1ucVVlUEE9PSIsInZhbHVlIjoiMElQYXVpQXpnZmRDaFZ6TUdIaXBtR21nK3JHTm1EUDNOYm0zcExSb2JxZm9xajBWZ0hSYTFZYWRWOXVNMi9yT0psMmFQdWVIRUZORmF0cVptQ1dUQk93ZXROU2c2Tk5LR2NySzJnenVuUzhYb1NLWFpGdXJaaEVIaWkycysweWxrWmFhcHR1UU5HSnplMXZ6cHVuMk9idEdZN2xjR3RPK0dRZS9CSlZMMW9lak9saDIweHA5WHNtQkdOWE1oZm4xRnJuQ21CMC80cFpxYmZId1pacDdYd1FUL0psOGZkeG4yZ3IrZTZLMDl5NDlnN2JkNkdrMUdlcC9sSHB3YnEwTFZ4d1hFWGFaM3ZxNlhCTkIrS3RRaTN6QzNHM3FJQlFFaEZWMEdkUzRlS0plczYvS0ZxbUZDeWwvdFNyazNPbkJlcWhMdXJFdmVpaTRhb2gxdzlVdklDcW9TWFJobEl4TmV2VllUMDlWK2JXS2RnU3dMbUhYYU1idGNUcXhzWU1GSFdYRFFmZWlOUTYySTZreUlmLzV2cjAycHZ1dFJCd0FIRjRPdTIzZkF6MmpTbEVQWGxSUklaZFlSZVFvc2VuTU1RVVIreGhHbk9KalFibG9DQlM0YkdUZXNPTjRFSWxPaENPcnlIaEQydUlJVFR0SXdDeXBFWkFEb2xPTXRZYU0iLCJtYWMiOiIzZjlmMDc3ZDE5ZjFkODQyM2E1Y2M4Mjc3NzQzOTg2NDBmOTM1MDg0YmE2YTk3ZTA3NWU4ZTYyOGI3OGE5MzUzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:13:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IjlnekxQR2NiaC9JYlRrQzUwcWhIWnc9PSIsInZhbHVlIjoibW9samRrQ1paQ2JlUlhtcHZDbXM4S01FTWprdVBNaGRlb3cxV0lDZS9YRHlmYXdPS3JaQmlYK2F3L2lvcTdBRlhxeHVaeVpmdFJNa1EyMkFwSGFnTkh4RXBVeWdUU0VQYjR3amM0RnRMNWN4cGhmQ3ZjNVVGODFaaUpTNHRNbDFDWGVjV2QrTE5lNFFUekpCbzl1WXBkUTVmL0c5bkRaVWhEY0N5S3NSNi8rTWRkVVNvU3ZsY2VYd3A3cUdoeXJRUnM2VjIwdHpJTDZJY1NFSUZtdVlhVUNpNW5MWjJVamxEdEQxTXFaZExjRUhSUkRJSEhjMTQwZGYyYjhadjlrdEtvSnJjVTBFdTgxcVhQa3lKN20zRFowVFoyajhVejNzWnRnMHlCejRGeEFNV0hScVl1SnNzNDZNZWJJcE9SWHlIdjVSU2E4RXhGVnhnRC9teldPeVJSMzZCK1Z2dEZuczhSM1owYW5McDYyTG8rNlhhVWRMclB1OVN3OFJyK2g3TVcxTTM1OVZBc3E5dmQ5V2kwcHhiQVhhZEIvUVlCMFJTMFRNM3EwVnJBWjBYQ1JuZnJsNFBic2JYb1NnQ2dDZlBXaDB5U1J5L2NPb2JYcER2bytNT0l2VDBHeWxCbCtwTFRSdkhXbHl5bVVSOStxdFQvdmV3NlAzVWU2VXNlRjciLCJtYWMiOiI2MjEwZGNjNTNjYmYwZTUyYjA3NDNiMmI4N2U2ZjI3YmFlODBjZmZlOWJlMzU2YWQzMmNjODQwMDA5YzM5NmMzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:13:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793507134\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}