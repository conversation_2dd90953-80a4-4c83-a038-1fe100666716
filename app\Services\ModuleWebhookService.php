<?php

namespace App\Services;

use App\Models\ModuleIntegration;
use App\Constants\CrmWebhookActions;
use App\Services\WebhookLogger;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Module Webhook Service
 * 
 * This service handles webhook calls to integrated modules.
 * It fetches base URLs from the module_integrations table and
 * calls the /saas-webhook endpoint with CRM action data.
 */
class ModuleWebhookService
{
    /**
     * The webhook endpoint path for all modules
     */
    const WEBHOOK_ENDPOINT = '/external-crm/webhook';

    /**
     * Default timeout for webhook calls (in seconds)
     */
    const DEFAULT_TIMEOUT = 30;

    /**
     * @var WebhookLogger
     */
    private $logger;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->logger = new WebhookLogger();
    }
    
    /**
     * Send webhook to all enabled module integrations
     * 
     * @param string $action The CRM action type (from CrmWebhookActions)
     * @param array $data The data to send with the webhook
     * @param int|null $userId The user ID (for filtering integrations)
     * @return array Results of webhook calls
     */
    public function sendToAllModules($action, $data, $userId = null)
    {
        // Validate action
        if (!CrmWebhookActions::isValidAction($action)) {
            Log::warning("Invalid webhook action attempted: {$action}");
            return ['success' => false, 'error' => 'Invalid action type'];
        }
        
        // Get enabled module integrations
        $integrations = ModuleIntegration::enabled()->get();

        if ($integrations->isEmpty()) {
            Log::info("No enabled module integrations found for webhook action: {$action}");
            return ['success' => true, 'message' => 'No integrations to notify'];
        }
        
        $results = [];
        
        foreach ($integrations as $integration) {
            $result = $this->sendToModule($integration, $action, $data, $userId);
            $results[$integration->name] = $result;
        }
        
        return $results;
    }
    
    /**
     * Send webhook to a specific module integration
     * 
     * @param ModuleIntegration $integration
     * @param string $action
     * @param array $data
     * @param int|null $userId
     * @return array
     */
    public function sendToModule(ModuleIntegration $integration, $action, $data, $userId = null)
    {
        $startTime = microtime(true);
        $webhookUrl = $this->buildWebhookUrl($integration);
        $payload = $this->buildPayload($action, $data, $userId);
        $entityId = isset($data['id']) ? $data['id'] : null;
        $entityType = $this->extractEntityType($data);

        try {
            $response = Http::timeout(self::DEFAULT_TIMEOUT)
                ->withoutVerifying() // Disable SSL verification
                ->withHeaders([
                    'Authorization' => 'Bearer ' . $integration->api_token,
                    'Content-Type' => 'application/json',
                    'X-CRM-Source' => config('app.name', 'CRM'),
                    'X-Webhook-Action' => $action,
                ])
                ->post($webhookUrl, $payload);

            $responseTime = round((microtime(true) - $startTime) * 1000);

            if ($response->successful()) {
                $this->logger->logSuccess(
                    $action,
                    $integration->name,
                    $webhookUrl,
                    $payload,
                    [
                        'status_code' => $response->status(),
                        'response' => $response->json()
                    ],
                    $responseTime,
                    $userId,
                    $entityId,
                    $entityType
                );

                return [
                    'success' => true,
                    'status_code' => $response->status(),
                    'response' => $response->json(),
                    'integration' => $integration->name
                ];
            } else {
                $responseTime = round((microtime(true) - $startTime) * 1000);

                $this->logger->logFailure(
                    $action,
                    $integration->name,
                    $webhookUrl,
                    $payload,
                    'HTTP Error: ' . $response->status(),
                    $response->status(),
                    $response->body(),
                    $responseTime,
                    $userId,
                    $entityId,
                    $entityType
                );

                return [
                    'success' => false,
                    'status_code' => $response->status(),
                    'error' => $response->body(),
                    'integration' => $integration->name
                ];
            }

        } catch (Exception $e) {
            $responseTime = round((microtime(true) - $startTime) * 1000);

            $this->logger->logFailure(
                $action,
                $integration->name,
                $webhookUrl,
                $payload,
                $e->getMessage(),
                null,
                null,
                $responseTime,
                $userId,
                $entityId,
                $entityType
            );

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'integration' => $integration->name
            ];
        }
    }
    
    /**
     * Build the complete webhook URL for a module
     * 
     * @param ModuleIntegration $integration
     * @return string
     */
    private function buildWebhookUrl(ModuleIntegration $integration)
    {
        $baseUrl = rtrim($integration->base_url, '/');
        return $baseUrl . self::WEBHOOK_ENDPOINT;
    }
    
    /**
     * Build the webhook payload
     * 
     * @param string $action
     * @param array $data
     * @param int|null $userId
     * @return array
     */
    private function buildPayload($action, $data, $userId = null)
    {
        return [
            'action' => $action,
            'timestamp' => now()->toISOString(),
            'data' => $data,
            'user_id' => $userId,
            'source' => [
                'system' => config('app.name', 'CRM'),
                'version' => config('app.version', '1.0'),
                'url' => config('app.url')
            ]
        ];
    }
    
    /**
     * Send webhook for a specific module by name
     * 
     * @param string $moduleName
     * @param string $action
     * @param array $data
     * @param int|null $userId
     * @return array|null
     */
    public function sendToModuleByName($moduleName, $action, $data, $userId = null)
    {
        $integration = ModuleIntegration::where('name', $moduleName)
            ->where('enabled', true)
            ->first();
            
        if (!$integration) {
            Log::warning("Module integration not found or disabled: {$moduleName}");
            return null;
        }
        
        return $this->sendToModule($integration, $action, $data, $userId);
    }
    
    /**
     * Test webhook connectivity for a module
     * 
     * @param ModuleIntegration $integration
     * @return array
     */
    public function testWebhook(ModuleIntegration $integration)
    {
        $testData = [
            'test' => true,
            'message' => 'This is a test webhook from ' . config('app.name', 'CRM')
        ];

        $result = $this->sendToModule($integration, 'crm.test_webhook', $testData);
        $this->logger->logTest($integration->name, $result);

        return $result;
    }

    /**
     * Extract entity type from data array
     *
     * @param array $data
     * @return string|null
     */
    private function extractEntityType($data)
    {
        // Try to determine entity type from data structure
        if (isset($data['stage_id']) && isset($data['pipeline_id'])) {
            if (isset($data['email'])) {
                return 'Lead';
            } elseif (isset($data['price'])) {
                return 'Deal';
            }
        }

        if (isset($data['lead_id'])) {
            return 'LeadTask';
        }

        if (isset($data['deal_id'])) {
            return 'DealTask';
        }

        return null;
    }
}
