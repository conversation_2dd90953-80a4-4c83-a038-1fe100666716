<!-- Expenses Tab Content -->
<style>
.upload-area {
    cursor: pointer;
    transition: all 0.3s ease;
}
.upload-area:hover {
    border-color: #007bff !important;
    background-color: #f8f9fa;
}
.upload-area.dragover {
    border-color: #28a745 !important;
    background-color: #d4edda;
}

/* Receipt viewer styles */
#receipt-content {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#receipt-content img {
    max-width: 100%;
    height: auto;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

#receipt-content embed {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.receipt-actions {
    margin-top: 15px;
}

.btn-group .btn {
    margin-right: 5px;
}
</style>
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">{{ __('Expense Management') }}</h4>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-warning active" data-expense-view="manage">
                    <i class="ti ti-list me-1"></i>{{ __('Manage') }}
                </button>
                <button type="button" class="btn btn-outline-warning" data-expense-view="categories">
                    <i class="ti ti-category me-1"></i>{{ __('Categories') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Manage View -->
<div id="manage-view" class="expense-view active">
    <div class="row mb-4">
        <!-- Expense Stats -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-danger text-white">
                <div class="card-body text-center">
                    <i class="ti ti-receipt" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ \Auth::user()->priceFormat(0) }}</h4>
                    <small>{{ __('Total Expenses') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-warning text-white">
                <div class="card-body text-center">
                    <i class="ti ti-clock" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ \Auth::user()->priceFormat(0) }}</h4>
                    <small>{{ __('This Month') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-info text-white">
                <div class="card-body text-center">
                    <i class="ti ti-clock" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0</h4>
                    <small>{{ __('Pending Bills') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-success text-white">
                <div class="card-body text-center">
                    <i class="ti ti-trending-down" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0%</h4>
                    <small>{{ __('vs Last Month') }}</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Recent Expenses') }}</h5>
                    <div class="d-flex gap-2">
                        @can('create expense')
                            <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#expenseModal">
                                <i class="ti ti-plus me-1"></i>{{ __('Create') }}
                            </button>
                        @endcan
                        <a href="{{ route('expense.index') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-eye me-1"></i>{{ __('View All') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="expensesTable">
                            <thead>
                                <tr>
                                    <th>{{ __('ID') }}</th>
                                    <th>{{ __('Expense Category') }}</th>
                                    <th>{{ __('Expense') }}</th>
                                    <th>{{ __('Vendor') }}</th>
                                    <th>{{ __('Date') }}</th>
                                    <th>{{ __('Expense Receipt') }}</th>
                                    <th>{{ __('Action') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $expenses = \App\Models\Expense::where('created_by', \Auth::user()->creatorId())
                                        ->with(['category'])
                                        ->latest()
                                        ->limit(5)
                                        ->get();
                                @endphp
                                @forelse($expenses as $expense)
                                <tr data-expense-id="{{ $expense->id }}">
                                    <td>{{ $expense->id }}</td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            {{ !empty($expense->category) ? $expense->category->name : '-' }}
                                        </span>
                                    </td>
                                    <td>{{ \Auth::user()->priceFormat($expense->amount) }}</td>
                                    <td>{{ $expense->vendor_name ?? '-' }}</td>
                                    <td>{{ \Auth::user()->dateFormat($expense->date) }}</td>
                                    <td>
                                        @if($expense->attachment)
                                            <div class="d-flex gap-1">
                                                <button class="btn btn-sm btn-outline-info view-receipt"
                                                        data-expense-id="{{ $expense->id }}"
                                                        data-attachment="{{ $expense->attachment }}"
                                                        title="{{ __('View Receipt') }}">
                                                    <i class="ti ti-eye"></i>
                                                </button>
                                                <a href="{{ asset('storage/' . $expense->attachment) }}"
                                                   download
                                                   class="btn btn-sm btn-outline-success"
                                                   title="{{ __('Download Receipt') }}">
                                                    <i class="ti ti-download"></i>
                                                </a>
                                            </div>
                                        @else
                                            <span class="text-muted">{{ __('No Receipt') }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            @can('edit expense')
                                                <button class="btn btn-sm btn-outline-secondary edit-expense"
                                                        data-expense-id="{{ $expense->id }}"
                                                        title="{{ __('Edit') }}">
                                                    <i class="ti ti-edit"></i>
                                                </button>
                                            @endcan
                                            @can('delete expense')
                                                <button class="btn btn-sm btn-outline-danger delete-expense"
                                                        data-expense-id="{{ $expense->id }}"
                                                        title="{{ __('Delete') }}">
                                                    <i class="ti ti-trash"></i>
                                                </button>
                                            @endcan
                                            <button class="btn btn-sm btn-outline-info view-description"
                                                    data-expense-id="{{ $expense->id }}"
                                                    title="{{ __('Description') }}">
                                                <i class="ti ti-file-text"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="ti ti-receipt" style="font-size: 3rem;"></i>
                                            <h5 class="mt-3">{{ __('No Expenses Found') }}</h5>
                                            <p>{{ __('Start tracking your business expenses') }}</p>
                                            @can('create expense')
                                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#expenseModal">
                                                    <i class="ti ti-plus me-1"></i>{{ __('Add Expense') }}
                                                </button>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Categories View -->
<div id="categories-view" class="expense-view" style="display: none;">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Expense Categories') }}</h5>
                    @can('create constant category')
                        <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                            <i class="ti ti-plus me-1"></i>{{ __('Add Category') }}
                        </button>
                    @endcan
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="categoriesTable">
                            <thead>
                                <tr>
                                    <th>{{ __('ID') }}</th>
                                    <th>{{ __('Created At') }}</th>
                                    <th>{{ __('Name') }}</th>
                                    <th>{{ __('Action') }}</th>
                                </tr>
                            </thead>
                            <tbody id="categoriesTableBody">
                                <!-- Categories will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                    <div id="no-categories" class="text-center py-5" style="display: none;">
                        <i class="ti ti-category text-muted" style="font-size: 4rem;"></i>
                        <h5 class="mt-3">{{ __('No Categories Found') }}</h5>
                        <p class="text-muted">{{ __('Create categories to organize your expenses') }}</p>
                        @can('create constant category')
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                <i class="ti ti-plus me-1"></i>{{ __('Add Category') }}
                            </button>
                        @endcan
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ __('Quick Actions') }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('expense.create') }}" class="btn btn-outline-danger w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-plus mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Add Expense') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('bill.create') }}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-file-invoice mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Create Bill') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('product-category.index') }}" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-category mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Categories') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('vender.index') }}" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-users mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Vendors') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('report.expense.summary') }}" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-chart-line mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Reports') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-download mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Export') }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Expense view switching
    const expenseViewButtons = document.querySelectorAll('[data-expense-view]');
    const expenseViews = document.querySelectorAll('.expense-view');

    expenseViewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetView = this.getAttribute('data-expense-view');

            // Remove active class from all buttons
            expenseViewButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            // Hide all views
            expenseViews.forEach(view => view.style.display = 'none');
            // Show target view
            const targetElement = document.getElementById(targetView + '-view');
            if (targetElement) {
                targetElement.style.display = 'block';
            }
        });
    });

    // File upload handling
    function setupFileUpload(uploadArea, fileInput) {
        uploadArea.addEventListener('click', function() {
            fileInput.click();
        });

        fileInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const uploadContent = uploadArea.querySelector('.upload-content');
                uploadContent.innerHTML = `
                    <i class="ti ti-file" style="font-size: 2rem; color: #28a745;"></i>
                    <p class="mb-0 text-success">${file.name}</p>
                    <small class="text-muted">File selected</small>
                `;
            }
        });
    }

    // Setup file uploads
    const uploadArea = document.querySelector('#expenseModal .upload-area');
    const fileInput = document.querySelector('#expense_receipt');
    if (uploadArea && fileInput) {
        setupFileUpload(uploadArea, fileInput);
    }

    const editUploadArea = document.querySelector('#editExpenseModal .upload-area');
    const editFileInput = document.querySelector('#edit_expense_receipt');
    if (editUploadArea && editFileInput) {
        setupFileUpload(editUploadArea, editFileInput);
    }

    // Set default date to today
    const today = new Date().toISOString().split('T')[0];
    const expenseDateField = document.getElementById('expense_date');
    if (expenseDateField) {
        expenseDateField.value = today;
    }

    // Debug: Check if form elements exist
    console.log('Form elements check:');
    console.log('expenseForm:', document.getElementById('expenseForm'));
    console.log('expense_category:', document.getElementById('expense_category'));
    console.log('expense_amount:', document.getElementById('expense_amount'));
    console.log('vendor_name:', document.getElementById('vendor_name'));
    console.log('expense_date:', document.getElementById('expense_date'));

    // Expense form submission
    const expenseForm = document.getElementById('expenseForm');
    if (expenseForm) {
        expenseForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Basic form validation
            const categoryId = document.getElementById('expense_category').value;
            const amount = document.getElementById('expense_amount').value;
            const vendorName = document.getElementById('vendor_name').value;
            const billDate = document.getElementById('expense_date').value;

            if (!categoryId) {
                showAlert('error', '{{ __("Please select a category") }}');
                return;
            }
            if (!amount || amount <= 0) {
                showAlert('error', '{{ __("Please enter a valid amount") }}');
                return;
            }
            if (!vendorName.trim()) {
                showAlert('error', '{{ __("Please enter vendor name") }}');
                return;
            }
            if (!billDate) {
                showAlert('error', '{{ __("Please select a date") }}');
                return;
            }

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            // Debug: Log form data
            console.log('Form data being submitted:');
            for (let [key, value] of formData.entries()) {
                console.log(key, value);
            }

        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="ti ti-loader"></i> {{ __("Saving...") }}';

        fetch('{{ route("expense.store.ajax") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('expenseModal'));
                modal.hide();

                // Reset form
                this.reset();
                document.getElementById('expense_date').value = today;

                // Reset file upload display
                const uploadContent = document.querySelector('#expenseModal .upload-content');
                uploadContent.innerHTML = `
                    <i class="ti ti-upload" style="font-size: 2rem; color: #6c757d;"></i>
                    <p class="mb-0 text-muted">{{ __('Click to upload receipt') }}</p>
                    <small class="text-muted">{{ __('Supported: JPG, PNG, PDF, DOC') }}</small>
                `;

                // Show success message first
                showAlert('success', data.message || '{{ __("Expense created successfully") }}');

                // Refresh table after a short delay to show the message
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                console.error('Server returned error:', data.message);
                showAlert('error', data.message || '{{ __("Error creating expense") }}');
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            showAlert('error', '{{ __("An error occurred while saving the expense") }}');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    });

    // Edit expense functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.edit-expense')) {
            const expenseId = e.target.closest('.edit-expense').getAttribute('data-expense-id');

            fetch(`{{ url('expense') }}/${expenseId}/edit-data`, {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const expense = data.expense;

                    // Populate edit form
                    document.getElementById('edit_expense_id').value = expense.id;
                    document.getElementById('edit_expense_category').value = expense.category_id || '';
                    document.getElementById('edit_expense_amount').value = expense.total || '';
                    document.getElementById('edit_vendor_name').value = expense.vendor_name || '';
                    document.getElementById('edit_expense_date').value = expense.bill_date || '';
                    document.getElementById('edit_expense_description').value = expense.description || '';

                    // Show current receipt if exists
                    const currentReceiptDiv = document.getElementById('current-receipt');
                    if (expense.attachment) {
                        currentReceiptDiv.innerHTML = `
                            <small class="text-muted">Current receipt:</small>
                            <a href="{{ asset('storage/') }}/${expense.attachment}" target="_blank" class="btn btn-sm btn-outline-info">
                                <i class="ti ti-file"></i> View Current
                            </a>
                        `;
                    } else {
                        currentReceiptDiv.innerHTML = '<small class="text-muted">No current receipt</small>';
                    }

                    // Show modal
                    const modal = new bootstrap.Modal(document.getElementById('editExpenseModal'));
                    modal.show();
                } else {
                    showAlert('error', data.message || '{{ __("Error loading expense data") }}');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', '{{ __("An error occurred while loading expense data") }}');
            });
        }

        // Delete expense functionality
        if (e.target.closest('.delete-expense')) {
            const expenseId = e.target.closest('.delete-expense').getAttribute('data-expense-id');

            if (confirm('{{ __("Are you sure you want to delete this expense?") }}')) {
                fetch(`{{ url('expense') }}/${expenseId}/delete-ajax`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove row from table
                        const row = document.querySelector(`tr[data-expense-id="${expenseId}"]`);
                        if (row) {
                            row.remove();
                        }
                        showAlert('success', data.message || '{{ __("Expense deleted successfully") }}');
                    } else {
                        showAlert('error', data.message || '{{ __("Error deleting expense") }}');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('error', '{{ __("An error occurred while deleting the expense") }}');
                });
            }
        }

        // View description functionality
        if (e.target.closest('.view-description')) {
            const expenseId = e.target.closest('.view-description').getAttribute('data-expense-id');

            fetch(`{{ url('expense') }}/${expenseId}/description`, {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const descriptionContent = document.getElementById('expense-description-content');
                    descriptionContent.innerHTML = data.description || '<em class="text-muted">No description available</em>';

                    const modal = new bootstrap.Modal(document.getElementById('descriptionModal'));
                    modal.show();
                } else {
                    showAlert('error', data.message || '{{ __("Error loading description") }}');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', '{{ __("An error occurred while loading description") }}');
            });
        }

        // View receipt functionality
        if (e.target.closest('.view-receipt')) {
            const button = e.target.closest('.view-receipt');
            const expenseId = button.getAttribute('data-expense-id');

            // Show loading state
            const receiptContent = document.getElementById('receipt-content');
            receiptContent.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">{{ __('Loading...') }}</span>
                    </div>
                    <p class="mt-2 text-muted">{{ __('Loading receipt...') }}</p>
                </div>
            `;

            // Show modal immediately with loading state
            const modal = new bootstrap.Modal(document.getElementById('receiptModal'));
            modal.show();

            // Fetch receipt information from server
            fetch(`{{ url('expense') }}/${expenseId}/receipt-info`, {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.receipt) {
                    const receipt = data.receipt;
                    const fileUrl = receipt.url;
                    const fileName = receipt.filename;
                    const fileExtension = receipt.extension;

                    // Set download and open links
                    document.getElementById('download-receipt').href = fileUrl;
                    document.getElementById('download-receipt').download = fileName;
                    document.getElementById('open-receipt').href = fileUrl;

                    // Update modal title with file info
                    document.getElementById('receiptModalLabel').textContent = `{{ __('View Receipt') }} - ${fileName} (${receipt.size_formatted})`;

                    // Display based on file type
                    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExtension)) {
                        // Image file
                        receiptContent.innerHTML = `
                            <img src="${fileUrl}"
                                 alt="Receipt"
                                 class="img-fluid"
                                 style="max-height: 500px; border: 1px solid #ddd; border-radius: 4px;"
                                 onload="this.style.opacity=1"
                                 style="opacity: 0; transition: opacity 0.3s;">
                        `;
                    } else if (fileExtension === 'pdf') {
                        // PDF file
                        receiptContent.innerHTML = `
                            <embed src="${fileUrl}"
                                   type="application/pdf"
                                   width="100%"
                                   height="500px"
                                   style="border: 1px solid #ddd; border-radius: 4px;">
                            <p class="mt-2 text-muted">
                                <i class="ti ti-info-circle"></i>
                                {{ __('If PDF is not displaying properly, click "Open in New Tab" to view it.') }}
                            </p>
                        `;
                    } else {
                        // Other file types
                        receiptContent.innerHTML = `
                            <div class="text-center py-4">
                                <i class="ti ti-file" style="font-size: 4rem; color: #6c757d;"></i>
                                <h5 class="mt-3">${fileName}</h5>
                                <p class="text-muted">{{ __('File Type:') }} ${fileExtension.toUpperCase()}</p>
                                <p class="text-muted">{{ __('Size:') }} ${receipt.size_formatted}</p>
                                <p class="text-muted">{{ __('Preview not available for this file type') }}</p>
                                <p class="text-muted">{{ __('Use the download or open buttons to view the file') }}</p>
                            </div>
                        `;
                    }
                } else {
                    receiptContent.innerHTML = `
                        <div class="text-center py-4">
                            <i class="ti ti-alert-circle text-danger" style="font-size: 4rem;"></i>
                            <h5 class="mt-3 text-danger">{{ __('Error') }}</h5>
                            <p class="text-muted">${data.message || '{{ __("Unable to load receipt") }}'}</p>
                        </div>
                    `;

                    // Hide action buttons if there's an error
                    document.getElementById('download-receipt').style.display = 'none';
                    document.getElementById('open-receipt').style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error loading receipt:', error);
                receiptContent.innerHTML = `
                    <div class="text-center py-4">
                        <i class="ti ti-alert-circle text-danger" style="font-size: 4rem;"></i>
                        <h5 class="mt-3 text-danger">{{ __('Error') }}</h5>
                        <p class="text-muted">{{ __('An error occurred while loading the receipt') }}</p>
                    </div>
                `;

                // Hide action buttons if there's an error
                document.getElementById('download-receipt').style.display = 'none';
                document.getElementById('open-receipt').style.display = 'none';
            });
        }
    });

    // Edit expense form submission
    document.getElementById('editExpenseForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const expenseId = document.getElementById('edit_expense_id').value;
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="ti ti-loader"></i> {{ __("Updating...") }}';

        fetch(`{{ url('expense') }}/${expenseId}/update-ajax`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('editExpenseModal'));
                modal.hide();

                // Refresh table
                location.reload();

                // Show success message
                showAlert('success', data.message || '{{ __("Expense updated successfully") }}');
            } else {
                showAlert('error', data.message || '{{ __("Error updating expense") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', '{{ __("An error occurred while updating the expense") }}');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    });

    // Alert function
    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // Insert alert at the top of the page
        const container = document.querySelector('.container-fluid') || document.body;
        container.insertAdjacentHTML('afterbegin', alertHtml);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    // Categories management functions
    function loadCategories() {
        fetch('{{ route("expense-categories.list.ajax") }}', {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const tbody = document.getElementById('categoriesTableBody');
                const noCategories = document.getElementById('no-categories');
                const categoriesTable = document.getElementById('categoriesTable');

                if (data.categories.length === 0) {
                    categoriesTable.style.display = 'none';
                    noCategories.style.display = 'block';
                } else {
                    categoriesTable.style.display = 'table';
                    noCategories.style.display = 'none';

                    tbody.innerHTML = '';
                    data.categories.forEach(category => {
                        const row = `
                            <tr data-category-id="${category.id}">
                                <td>${category.id}</td>
                                <td>${category.created_at}</td>
                                <td>${category.name}</td>
                                <td>
                                    <div class="d-flex gap-1">
                                        @can('edit constant category')
                                            <button class="btn btn-sm btn-outline-primary edit-category"
                                                    data-category-id="${category.id}"
                                                    title="{{ __('Edit') }}">
                                                <i class="ti ti-edit"></i>
                                            </button>
                                        @endcan
                                        @can('delete constant category')
                                            <button class="btn btn-sm btn-outline-danger delete-category"
                                                    data-category-id="${category.id}"
                                                    title="{{ __('Delete') }}">
                                                <i class="ti ti-trash"></i>
                                            </button>
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                        `;
                        tbody.insertAdjacentHTML('beforeend', row);
                    });
                }
            } else {
                showAlert('error', data.message || '{{ __("Error loading categories") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', '{{ __("An error occurred while loading categories") }}');
        });
    }

    // Load categories when categories view is shown
    const categoriesViewButton = document.querySelector('[data-expense-view="categories"]');
    if (categoriesViewButton) {
        categoriesViewButton.addEventListener('click', function() {
            setTimeout(() => {
                loadCategories();
            }, 100);
        });
    }

    // Add category form submission
    document.getElementById('addCategoryForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="ti ti-loader"></i> {{ __("Saving...") }}';

        fetch('{{ route("expense-categories.store.ajax") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('addCategoryModal'));
                modal.hide();

                // Reset form
                this.reset();

                // Reload categories
                loadCategories();

                // Show success message
                showAlert('success', data.message || '{{ __("Category created successfully") }}');
            } else {
                showAlert('error', data.message || '{{ __("Error creating category") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', '{{ __("An error occurred while saving the category") }}');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    });

    // Edit and delete category functionality
    document.addEventListener('click', function(e) {
        // Edit category functionality
        if (e.target.closest('.edit-category')) {
            const categoryId = e.target.closest('.edit-category').getAttribute('data-category-id');

            fetch(`{{ url('expense-categories') }}/${categoryId}/edit-ajax`, {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const category = data.category;

                    // Populate edit form
                    document.getElementById('edit_category_id').value = category.id;
                    document.getElementById('edit_category_name').value = category.name;

                    // Show modal
                    const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
                    modal.show();
                } else {
                    showAlert('error', data.message || '{{ __("Error loading category data") }}');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', '{{ __("An error occurred while loading category data") }}');
            });
        }

        // Delete category functionality
        if (e.target.closest('.delete-category')) {
            const categoryId = e.target.closest('.delete-category').getAttribute('data-category-id');

            if (confirm('{{ __("Are you sure you want to delete this category?") }}')) {
                fetch(`{{ url('expense-categories') }}/${categoryId}/delete-ajax`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove row from table
                        const row = document.querySelector(`tr[data-category-id="${categoryId}"]`);
                        if (row) {
                            row.remove();
                        }

                        // Check if table is empty and show no categories message
                        const tbody = document.getElementById('categoriesTableBody');
                        if (tbody.children.length === 0) {
                            document.getElementById('categoriesTable').style.display = 'none';
                            document.getElementById('no-categories').style.display = 'block';
                        }

                        showAlert('success', data.message || '{{ __("Category deleted successfully") }}');
                    } else {
                        showAlert('error', data.message || '{{ __("Error deleting category") }}');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('error', '{{ __("An error occurred while deleting the category") }}');
                });
            }
        }
    });

    // Edit category form submission
    document.getElementById('editCategoryForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const categoryId = document.getElementById('edit_category_id').value;
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="ti ti-loader"></i> {{ __("Updating...") }}';

        fetch(`{{ url('expense-categories') }}/${categoryId}/update-ajax`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('editCategoryModal'));
                modal.hide();

                // Reload categories
                loadCategories();

                // Show success message
                showAlert('success', data.message || '{{ __("Category updated successfully") }}');
            } else {
                showAlert('error', data.message || '{{ __("Error updating category") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', '{{ __("An error occurred while updating the category") }}');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    });
    } else {
        console.error('expenseForm element not found');
    }

    // Reset receipt modal when closed
    const receiptModal = document.getElementById('receiptModal');
    if (receiptModal) {
        receiptModal.addEventListener('hidden.bs.modal', function () {
            // Reset modal content
            document.getElementById('receiptModalLabel').textContent = '{{ __("View Receipt") }}';
            document.getElementById('receipt-content').innerHTML = '';

            // Show action buttons (in case they were hidden due to error)
            document.getElementById('download-receipt').style.display = 'inline-block';
            document.getElementById('open-receipt').style.display = 'inline-block';

            // Reset links
            document.getElementById('download-receipt').href = '#';
            document.getElementById('open-receipt').href = '#';
        });
    }
});
</script>

<!-- Expense Modal -->
<div class="modal fade" id="expenseModal" tabindex="-1" aria-labelledby="expenseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="expenseModalLabel">{{ __('Add Expense') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="expenseForm" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="expense_category" class="form-label">{{ __('Expense Category') }}</label>
                            <select class="form-select" id="expense_category" name="category_id" required>
                                <option value="">{{ __('Select Category') }}</option>
                                @php
                                    $categories = \App\Models\ProductServiceCategory::where('created_by', \Auth::user()->creatorId())
                                        ->where('type', 'expense')
                                        ->get();
                                @endphp
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="expense_amount" class="form-label">{{ __('Expense') }}</label>
                            <input type="number" class="form-control" id="expense_amount" name="amount" step="0.01" min="0" required placeholder="{{ __('Enter amount') }}">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="vendor_name" class="form-label">{{ __('Vendor') }}</label>
                            <input type="text" class="form-control" id="vendor_name" name="vendor_name" required placeholder="{{ __('Vendor Name') }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="expense_date" class="form-label">{{ __('Date') }} <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="expense_date" name="bill_date" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="expense_receipt" class="form-label">{{ __('Receipt') }}</label>
                            <div class="upload-area border-2 border-dashed rounded p-3 text-center">
                                <input type="file" class="form-control d-none" id="expense_receipt" name="attachment" accept="image/*,.pdf,.doc,.docx">
                                <div class="upload-content">
                                    <i class="ti ti-upload" style="font-size: 2rem; color: #6c757d;"></i>
                                    <p class="mb-0 text-muted">{{ __('Click to upload receipt') }}</p>
                                    <small class="text-muted">{{ __('Supported: JPG, PNG, PDF, DOC') }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="expense_description" class="form-label">{{ __('Description') }}</label>
                            <textarea class="form-control" id="expense_description" name="description" rows="4" placeholder="{{ __('Enter description') }}"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-success">{{ __('Submit') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Expense Modal -->
<div class="modal fade" id="editExpenseModal" tabindex="-1" aria-labelledby="editExpenseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editExpenseModalLabel">{{ __('Edit Expense') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editExpenseForm" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <input type="hidden" id="edit_expense_id" name="expense_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_expense_category" class="form-label">{{ __('Expense Category') }}</label>
                            <select class="form-select" id="edit_expense_category" name="category_id" required>
                                <option value="">{{ __('Select Category') }}</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_expense_amount" class="form-label">{{ __('Expense') }}</label>
                            <input type="number" class="form-control" id="edit_expense_amount" name="amount" step="0.01" min="0" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_vendor_name" class="form-label">{{ __('Vendor') }}</label>
                            <input type="text" class="form-control" id="edit_vendor_name" name="vendor_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_expense_date" class="form-label">{{ __('Date') }} <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="edit_expense_date" name="bill_date" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_expense_receipt" class="form-label">{{ __('Receipt') }}</label>
                            <div class="upload-area border-2 border-dashed rounded p-3 text-center">
                                <input type="file" class="form-control d-none" id="edit_expense_receipt" name="attachment" accept="image/*,.pdf,.doc,.docx">
                                <div class="upload-content">
                                    <i class="ti ti-upload" style="font-size: 2rem; color: #6c757d;"></i>
                                    <p class="mb-0 text-muted">{{ __('Click to upload new receipt') }}</p>
                                    <small class="text-muted">{{ __('Leave empty to keep current receipt') }}</small>
                                </div>
                            </div>
                            <div id="current-receipt" class="mt-2"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_expense_description" class="form-label">{{ __('Description') }}</label>
                            <textarea class="form-control" id="edit_expense_description" name="description" rows="4"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-success">{{ __('Update') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Description Modal -->
<div class="modal fade" id="descriptionModal" tabindex="-1" aria-labelledby="descriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="descriptionModalLabel">{{ __('Expense Description') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="expense-description-content"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCategoryModalLabel">{{ __('Add Category') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addCategoryForm">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="category_name" class="form-label">{{ __('Category Name') }} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="category_name" name="name" required placeholder="{{ __('Expense Category name') }}">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-success">{{ __('Submit') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCategoryModalLabel">{{ __('Edit Category') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editCategoryForm">
                @csrf
                <input type="hidden" id="edit_category_id" name="category_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_category_name" class="form-label">{{ __('Category Name') }} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_category_name" name="name" required placeholder="{{ __('Expense Category name') }}">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-success">{{ __('Update') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Receipt Viewer Modal -->
<div class="modal fade" id="receiptModal" tabindex="-1" aria-labelledby="receiptModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="receiptModalLabel">{{ __('View Receipt') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <div id="receipt-content">
                    <!-- Receipt content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <a id="download-receipt" href="#" download class="btn btn-success">
                    <i class="ti ti-download me-1"></i>{{ __('Download') }}
                </a>
                <a id="open-receipt" href="#" target="_blank" class="btn btn-primary">
                    <i class="ti ti-external-link me-1"></i>{{ __('Open in New Tab') }}
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
            </div>
        </div>
    </div>
</div>
