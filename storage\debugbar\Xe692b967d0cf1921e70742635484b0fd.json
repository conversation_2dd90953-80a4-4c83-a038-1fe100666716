{"__meta": {"id": "Xe692b967d0cf1921e70742635484b0fd", "datetime": "2025-07-31 05:49:42", "utime": **********.887641, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940981.748292, "end": **********.887678, "duration": 1.1393859386444092, "duration_str": "1.14s", "measures": [{"label": "Booting", "start": 1753940981.748292, "relative_start": 0, "end": **********.758642, "relative_end": **********.758642, "duration": 1.010349988937378, "duration_str": "1.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.75866, "relative_start": 1.****************, "end": **********.887682, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "129ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YIbslisMcg6HLeaQKjN1FtSeS4Cwdy2QQK3ldxsy", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1917154946 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1917154946\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-679721968 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-679721968\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-103068984 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-103068984\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-646804284 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-646804284\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1171551497 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1171551497\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:49:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRvaXNDYnV2aE9hKzVQN3UyQnNVUVE9PSIsInZhbHVlIjoiT2lTYXlVUUw1SW1mSjJsYVRtQUtRY1ErUE9jN1JSeFU4ejRBT1FTQ2YrNlNDMEVXSnB6VXprcmFseUFUSnVCNmh1T2FnU08zaHVSaElEcHR2c1dSenZOaXpnTmdQeTZOYWlPZldmRklTdlBJU1JPZ2hzNG8rUE1aZXk2WDNLT05hNTl1ZWlnOTVkUjhBTmJKL1RocHNaaG96WHlmMmFkSkNwbENUdFk4R1N0WCtoV291YkE2ZWozQWtFU0VNZE8valF5dDVreWRFSWxLbG81TmpISjhjZVRSUWxQUjBVbzJCWkI0VXl2TTVXMWZMOUMzM1dRdjRIUlBSMkFSUmtaTHFGVTlvTStWaHZCSVJrVXVMcEgvTWhNZ2x5eWtvUGlDRHJTREdvVndFTndLSU5wZlVDSkFacmJEUEtlSVJqbVhreDZTSENnNjlOanpuL2ZzaTFpYmQ1T1lLN0hNU3A3NkljUjVEb21zZmhkUFdTMVpXNlVkWmpvTmtHbTBHdU5sMFAzZVRFczlVRGk1ekdSVldPclZqT2oreXNZWWlDUFdBM1VXRmZhbzBjWVhRR1AxV3gwNkZoWjNzYU8wN2lpUXRTNmdyRnBHT3crUUFDdjBiaEdSMTlDU0xXaG5UZ0dTU2phZHUvRU1JeTdaM0dOZndwR2h6dkNsV0RGWXpYVXgiLCJtYWMiOiIxYzQ2MzM0MjE4YzRkMGNhMzY2Njc4Y2QwNzUzNjdjNjcxNTA4OTUzZTFjMWI2OTE5NTZhNWY1NmMyM2I4Y2VmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:49:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InM2TW5taVRoalFaVlpxUEcxZmVIUVE9PSIsInZhbHVlIjoiRVpMYTFEcGNhaEY4Z3RzLzgrRmJ6NWVSamVvaWlnT3RCdUhuZzR6ZkNSRkpMa2ZwUi9GMkJRbHRWRTNFeS9MbVBpcWh3aWVqM25CM1R5SVFlWGhQcVh6UzRzajhrWHZyVWhSdHJJL1JieVNVS0Y0MHJxQUwrUjMzQ1NmbzJKRkJ0NExCQkNoaEVmSWFUaVdTSVBKek9Zcy9GeDQ3WTJEQkFBbC9oRHBWbmdzNk41eTN1d05hVTZBQThQZXpYWWp3ZFBEN0E0TGFFNEE2bmNBSnlhbW82UDFTSVJpQlgvTUhIVFJRUWhLQmEvSTd6UktwNVZsUytOZlZzUnpOUElPT0o3d05oMS96MHdkNzFBaXliaWRTZ0ZBYjBrLy9Gblo4RDU1UFBaRG5zRUFhUjlGTkc5NlIrTVpmYUkzTE1ZemhkTkZ6NXVZQWxCeFVHaVFzOWhBQm8wcG1kcGN1OWpnMm9lblVnalAxNWJYbnhCTnVyN01hSGYvSzVncWdzUmlkbjYxWk43MUxnTS9SaXczdjMxOXFiTWJiUW5jRFhWKzJ4Wm9vaXZpbzQ5ek1pNkFjUmVJaGtaS2toNzRaVlFkNE5jQWlwUXVSeEx4SjZVaDBLNlBiNFdIVm9EbGRPOEdZVGs5M3pGZURMMVBLdUN2ZmtGVnYwVWE0Q1lyRlU1TDUiLCJtYWMiOiI1Njc0NjVlZGQ2YTdlYjc3YzhmZTdiNzFjNmZlNTk3YzVmMjJjOGI5YmM1YWZlZTJkM2NlZGJkNmMzZjFiZjg1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:49:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRvaXNDYnV2aE9hKzVQN3UyQnNVUVE9PSIsInZhbHVlIjoiT2lTYXlVUUw1SW1mSjJsYVRtQUtRY1ErUE9jN1JSeFU4ejRBT1FTQ2YrNlNDMEVXSnB6VXprcmFseUFUSnVCNmh1T2FnU08zaHVSaElEcHR2c1dSenZOaXpnTmdQeTZOYWlPZldmRklTdlBJU1JPZ2hzNG8rUE1aZXk2WDNLT05hNTl1ZWlnOTVkUjhBTmJKL1RocHNaaG96WHlmMmFkSkNwbENUdFk4R1N0WCtoV291YkE2ZWozQWtFU0VNZE8valF5dDVreWRFSWxLbG81TmpISjhjZVRSUWxQUjBVbzJCWkI0VXl2TTVXMWZMOUMzM1dRdjRIUlBSMkFSUmtaTHFGVTlvTStWaHZCSVJrVXVMcEgvTWhNZ2x5eWtvUGlDRHJTREdvVndFTndLSU5wZlVDSkFacmJEUEtlSVJqbVhreDZTSENnNjlOanpuL2ZzaTFpYmQ1T1lLN0hNU3A3NkljUjVEb21zZmhkUFdTMVpXNlVkWmpvTmtHbTBHdU5sMFAzZVRFczlVRGk1ekdSVldPclZqT2oreXNZWWlDUFdBM1VXRmZhbzBjWVhRR1AxV3gwNkZoWjNzYU8wN2lpUXRTNmdyRnBHT3crUUFDdjBiaEdSMTlDU0xXaG5UZ0dTU2phZHUvRU1JeTdaM0dOZndwR2h6dkNsV0RGWXpYVXgiLCJtYWMiOiIxYzQ2MzM0MjE4YzRkMGNhMzY2Njc4Y2QwNzUzNjdjNjcxNTA4OTUzZTFjMWI2OTE5NTZhNWY1NmMyM2I4Y2VmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:49:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InM2TW5taVRoalFaVlpxUEcxZmVIUVE9PSIsInZhbHVlIjoiRVpMYTFEcGNhaEY4Z3RzLzgrRmJ6NWVSamVvaWlnT3RCdUhuZzR6ZkNSRkpMa2ZwUi9GMkJRbHRWRTNFeS9MbVBpcWh3aWVqM25CM1R5SVFlWGhQcVh6UzRzajhrWHZyVWhSdHJJL1JieVNVS0Y0MHJxQUwrUjMzQ1NmbzJKRkJ0NExCQkNoaEVmSWFUaVdTSVBKek9Zcy9GeDQ3WTJEQkFBbC9oRHBWbmdzNk41eTN1d05hVTZBQThQZXpYWWp3ZFBEN0E0TGFFNEE2bmNBSnlhbW82UDFTSVJpQlgvTUhIVFJRUWhLQmEvSTd6UktwNVZsUytOZlZzUnpOUElPT0o3d05oMS96MHdkNzFBaXliaWRTZ0ZBYjBrLy9Gblo4RDU1UFBaRG5zRUFhUjlGTkc5NlIrTVpmYUkzTE1ZemhkTkZ6NXVZQWxCeFVHaVFzOWhBQm8wcG1kcGN1OWpnMm9lblVnalAxNWJYbnhCTnVyN01hSGYvSzVncWdzUmlkbjYxWk43MUxnTS9SaXczdjMxOXFiTWJiUW5jRFhWKzJ4Wm9vaXZpbzQ5ek1pNkFjUmVJaGtaS2toNzRaVlFkNE5jQWlwUXVSeEx4SjZVaDBLNlBiNFdIVm9EbGRPOEdZVGs5M3pGZURMMVBLdUN2ZmtGVnYwVWE0Q1lyRlU1TDUiLCJtYWMiOiI1Njc0NjVlZGQ2YTdlYjc3YzhmZTdiNzFjNmZlNTk3YzVmMjJjOGI5YmM1YWZlZTJkM2NlZGJkNmMzZjFiZjg1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:49:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-518527063 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YIbslisMcg6HLeaQKjN1FtSeS4Cwdy2QQK3ldxsy</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518527063\", {\"maxDepth\":0})</script>\n"}}