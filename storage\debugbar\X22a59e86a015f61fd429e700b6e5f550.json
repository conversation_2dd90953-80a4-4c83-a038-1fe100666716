{"__meta": {"id": "X22a59e86a015f61fd429e700b6e5f550", "datetime": "2025-07-31 05:19:53", "utime": **********.325727, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939192.14236, "end": **********.32576, "duration": 1.1833999156951904, "duration_str": "1.18s", "measures": [{"label": "Booting", "start": 1753939192.14236, "relative_start": 0, "end": **********.212777, "relative_end": **********.212777, "duration": 1.0704169273376465, "duration_str": "1.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.21281, "relative_start": 1.****************, "end": **********.325763, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "tUhna9mK6R981LBBP7si6bnbQrPGB9XIj7d5imlj", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1429311617 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1429311617\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-748830271 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-748830271\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1391314726 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1391314726\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-590316525 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-590316525\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1261102698 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1261102698\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1905787951 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:19:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNHMTlaUjh1QkdHVHZCQ1J4WFA4NWc9PSIsInZhbHVlIjoiWU5LekZDRG5FZHNZQkxUZ1RYRkpBRWpjNkFWOThHR3lIb2dqZnlWV3VwQStQT1FSRXFYTjE2RXdUVHVsbUlYZ3ViSml4QXJRaGk3QWZwajZpQXp4QTI2TG45Yk1RL0VJcWFzaVd2b1RZS3RvTkNEVDZQM3JFOFR4ZkkyOVJMbEN1Yy8rUHJYSTgvYXhPOFVoeGtsdmpuTGZRcitGaGkvSVRkT1hNQUxla2ZnMlBhenYxd05hM1BwSTY5enlIQTVySjl1aWxSSEZ6ZTdqbU9nSzZ4dGV2d2NFbVoxcFd4OEl1d1dDVExCN0Q0K0MxbDZ1MWZ3dTN3ckZYZS9teW11a1h6dWQ4THlWZHFKV01mUnhLbzI0K1FaUmFSYjJqZUl6b1JJMk01Q0cvOGI0aGx5RU13UWJ1Nm1vRlduckhOWjV5UVI2MHhOZUVyalNiOHM2TE1jS0hoRVdMQ2owdXRIZWx2dGFoQjY4VjRIUDlrbzZxR2w2N1J3TkpZcTZBalBONE9PVGVhcnpnaXpnMm9WQitrZXNWTnVmY2Y1VHR6YjFzMHIyTzBiSHh4N2VGOFlUY0ovdnR4ejdVNlVRTk9sT1BSSGtJU2QxeGFDdCszU0EvTENLRFAwNTI3RER0eFlCbUo2QnQvYjVXWWI5SUtRc1VLbmRINldvYk9hMk4vVk0iLCJtYWMiOiI3ZWFiZDE2ZTk3YzcwMGE3OWFlYTVkY2EzOWQzOTA2MTRkNjA5NjUwNWEzZDE0MGIyMmQxYzIzZWQzM2ExN2M1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:19:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InBQcG9DbXNjV1EyY0FNdlJYSFVlclE9PSIsInZhbHVlIjoidzJrVDNsbldhSHZ3a05HT3lhWWk3R1Z3eEtIdXM0ZERCVm9FVUw1TEc1WTk5eElPU1JTWTRLMGZNTWVZT2Vja09zb3I2ZzFIcWpvckREZ3Q1RXovenczbmpwV0JFQjVWVER6eHV0b3pzQUlvRlJPYmZDWHZ2bWM2Y0tkN2lsT0lQTEVKb0tuZkY4Tk1KWDVLek1lNkgyQm1WeXlsR05MaHJPMVkxU0pEcFhUQXVxUldaenpnaUdPNzVFODIwT1B5SG95bG93QUd1YStzYlo1WUdIT2ZVU084VXNwenRCeXd1Nlp0dzdLa3FHNjV0S2d0U1N6NjNZR3cxaExMdjVoYzBIeGtyUDl2N2VzRHhnK2tyRDk3L21lQmVpTE1mQ0ZrODdBSlc2NUoybmtRREFFOG9PRzZRNGFzeUExQkV5d2JseXcwcGhPSWxWTWxjRGhJeFNDUDFCTVF2V3FhWDJabEk3cDFoSFR4WnNuR2JOWE1BTVRqQVVxLzNEeThhZmR6TFEzWlUxZEN0bTl4VDMzV3hJa0NJNmJCZ252VDBSWUVZbmtTL1RjS2x1QzBqRVhYellVbnJ0RTBUTXpvNXhhVDdZRVJ3TENLQ1NpTk1pRGlwV0MvWWJYWCtXWkVoMHhOVW1FU29PMnZGVEtoTm1uQ0F4TzA1VCtRei9TYjI5Yk0iLCJtYWMiOiJmMjUxZmEwYThmOTY0NTg5NWVjMWYzNzc3MmVjOThlNjEyNjY2ZWE2ZDdiYmZmMGZjZmFhZmM3M2FjZTgyMjVmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:19:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNHMTlaUjh1QkdHVHZCQ1J4WFA4NWc9PSIsInZhbHVlIjoiWU5LekZDRG5FZHNZQkxUZ1RYRkpBRWpjNkFWOThHR3lIb2dqZnlWV3VwQStQT1FSRXFYTjE2RXdUVHVsbUlYZ3ViSml4QXJRaGk3QWZwajZpQXp4QTI2TG45Yk1RL0VJcWFzaVd2b1RZS3RvTkNEVDZQM3JFOFR4ZkkyOVJMbEN1Yy8rUHJYSTgvYXhPOFVoeGtsdmpuTGZRcitGaGkvSVRkT1hNQUxla2ZnMlBhenYxd05hM1BwSTY5enlIQTVySjl1aWxSSEZ6ZTdqbU9nSzZ4dGV2d2NFbVoxcFd4OEl1d1dDVExCN0Q0K0MxbDZ1MWZ3dTN3ckZYZS9teW11a1h6dWQ4THlWZHFKV01mUnhLbzI0K1FaUmFSYjJqZUl6b1JJMk01Q0cvOGI0aGx5RU13UWJ1Nm1vRlduckhOWjV5UVI2MHhOZUVyalNiOHM2TE1jS0hoRVdMQ2owdXRIZWx2dGFoQjY4VjRIUDlrbzZxR2w2N1J3TkpZcTZBalBONE9PVGVhcnpnaXpnMm9WQitrZXNWTnVmY2Y1VHR6YjFzMHIyTzBiSHh4N2VGOFlUY0ovdnR4ejdVNlVRTk9sT1BSSGtJU2QxeGFDdCszU0EvTENLRFAwNTI3RER0eFlCbUo2QnQvYjVXWWI5SUtRc1VLbmRINldvYk9hMk4vVk0iLCJtYWMiOiI3ZWFiZDE2ZTk3YzcwMGE3OWFlYTVkY2EzOWQzOTA2MTRkNjA5NjUwNWEzZDE0MGIyMmQxYzIzZWQzM2ExN2M1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:19:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InBQcG9DbXNjV1EyY0FNdlJYSFVlclE9PSIsInZhbHVlIjoidzJrVDNsbldhSHZ3a05HT3lhWWk3R1Z3eEtIdXM0ZERCVm9FVUw1TEc1WTk5eElPU1JTWTRLMGZNTWVZT2Vja09zb3I2ZzFIcWpvckREZ3Q1RXovenczbmpwV0JFQjVWVER6eHV0b3pzQUlvRlJPYmZDWHZ2bWM2Y0tkN2lsT0lQTEVKb0tuZkY4Tk1KWDVLek1lNkgyQm1WeXlsR05MaHJPMVkxU0pEcFhUQXVxUldaenpnaUdPNzVFODIwT1B5SG95bG93QUd1YStzYlo1WUdIT2ZVU084VXNwenRCeXd1Nlp0dzdLa3FHNjV0S2d0U1N6NjNZR3cxaExMdjVoYzBIeGtyUDl2N2VzRHhnK2tyRDk3L21lQmVpTE1mQ0ZrODdBSlc2NUoybmtRREFFOG9PRzZRNGFzeUExQkV5d2JseXcwcGhPSWxWTWxjRGhJeFNDUDFCTVF2V3FhWDJabEk3cDFoSFR4WnNuR2JOWE1BTVRqQVVxLzNEeThhZmR6TFEzWlUxZEN0bTl4VDMzV3hJa0NJNmJCZ252VDBSWUVZbmtTL1RjS2x1QzBqRVhYellVbnJ0RTBUTXpvNXhhVDdZRVJ3TENLQ1NpTk1pRGlwV0MvWWJYWCtXWkVoMHhOVW1FU29PMnZGVEtoTm1uQ0F4TzA1VCtRei9TYjI5Yk0iLCJtYWMiOiJmMjUxZmEwYThmOTY0NTg5NWVjMWYzNzc3MmVjOThlNjEyNjY2ZWE2ZDdiYmZmMGZjZmFhZmM3M2FjZTgyMjVmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:19:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1905787951\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1464264133 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tUhna9mK6R981LBBP7si6bnbQrPGB9XIj7d5imlj</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464264133\", {\"maxDepth\":0})</script>\n"}}