{"__meta": {"id": "X688920cd31f1e6a5d81c2ab1a4dd2493", "datetime": "2025-07-31 07:51:26", "utime": 1753948286.005952, "method": "GET", "uri": "/expense/3/description", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.141921, "end": 1753948286.005972, "duration": 0.8640508651733398, "duration_str": "864ms", "measures": [{"label": "Booting", "start": **********.141921, "relative_start": 0, "end": **********.863101, "relative_end": **********.863101, "duration": 0.7211799621582031, "duration_str": "721ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.863114, "relative_start": 0.7211930751800537, "end": 1753948286.005973, "relative_end": 1.1920928955078125e-06, "duration": 0.14285898208618164, "duration_str": "143ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46938296, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET expense/{id}/description", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ExpenseController@getDescription", "namespace": null, "prefix": "", "where": [], "as": "expense.description", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=1179\" onclick=\"\">app/Http/Controllers/ExpenseController.php:1179-1204</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020550000000000002, "accumulated_duration_str": "20.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.936893, "duration": 0.019010000000000003, "duration_str": "19.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 92.506}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.984854, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 92.506, "width_percent": 4.866}, {"sql": "select * from `expenses` where `id` = '3' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["3", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 1184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9913669, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ExpenseController.php:1184", "source": "app/Http/Controllers/ExpenseController.php:1184", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=1184", "ajax": false, "filename": "ExpenseController.php", "line": "1184"}, "connection": "radhe_same", "start_percent": 97.372, "width_percent": 2.628}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Expense": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FExpense.php&line=1", "ajax": false, "filename": "Expense.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/expense/3/description\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/expense/3/description", "status_code": "<pre class=sf-dump id=sf-dump-888106260 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-888106260\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1883896115 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1883896115\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-361844666 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-361844666\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1631974300 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlRLVXZySTZDYUFLcnBiZkFJaUp2Y2c9PSIsInZhbHVlIjoiUzEyMHZ4YSt2bFlIKzZ4MXBnT2FQUmVGc0RsbEtTazRaYjFaQWxSTHRQenl5TzVyTW5wMlRibmlYYXRvaVlWOVFXNzY0dmM1dWVJZEo1L29DVzJ3WTZ3YUZRSE5NOC95TXlXN3BVc1hZZmRBNzdpRGtwRUpDdjArcmtWSjYxMnMweVhLTVNyZnRsS3RZWWxnMXU4bVMvZmovdFRXRW1nVkVyemxSMWNkUUdVem1Ea0RBUEJ0cTFpY2x4UHZyUkxsSEVFVjEvUjdLdldTSVlwcENwa2l4YUVnRUhoK3paTnQ2MHhodEYwS3oxTlFPV0Y4S1NQeU1ITmNwcG5PQnlpRWdBT3hydkcvZUZMa3dzMmJQN0RYQUpwRDBjU2p4SXBrY2plc0pscWYzSVNoT3kvVzl3TlNkaTN4a3BHS0Eydmo2aTFwa3RiWTNlN1Z2YjFTLzFkaXJLd2szTVR6UVVtODRYODNRUC9NV0dQMGhWdWxYTjUwWnRxWWkvanF6MHZtb2JzcSsxMW9NOEVZU1RKYjNQNkcrYi8vc3pRSHFGem9Gb25wUzl0aUNzOXlmRWpaU2RPcm9BWlZJUzZPSTRsVWdCTnVZSmF1TytXeWNXYjFFa0VFUHQxNkJsNHR3bVNvZ3UxUlA5c2ZkbjlZc3U3QWdlYnhiOGIwNHptSGJsTHgiLCJtYWMiOiI3MTBkNDQ1ODcyMTM3NTVjOWFkOGJlNjJiMmFhNzg0MDZiYjAxY2ZiYzNhODliZGRiYjgxZTcxYWVmZDQ1ZWNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ikg3WjNPdmJ6RnBQVjdBL1ZxbjVkRUE9PSIsInZhbHVlIjoiL0tpRTRGbE5MUzdTR3lOWllpQVl5akZFUkhNOXdCY0RHUXh0M2NMaDFVd1d3bjRTQnZBbThJL2ZiRktJcEdYOTQwbGRrMDJSRzk4d3BlTWg1R1hTMmQvYzBtWDNPL3B3Qzc2NTVKS1lCeEowVjZ2ZE5iUTRwOWd0V2xiWThZdFBhaHpKcnlZb1NvMTJKYlJjaFRBSzBNeEs0R2dmdjZ6OW5MRENUbjJ6b0JYRFdERXdyN3NkUUVuRzNLNWZBaDQ1TjFEL0MxQllndW9iRkJPOUZVSkN1ZitaZ2l4cEU4WHlrYTcrNHNBQjdIbWhXTkxzRm5lem9ybVBSdU1CdlkyeCtjKzQ2YXpIaS80N1RjazAyMmR6RVV5NGZXYXhZcG5yS09QYTlzUC8zdE0xS0xrOEhNM1krRWVzaGlFbCtuR2Z6TVg3cnoyV0NnTytGSThCYS9VOFlILzdMaHJmdEJlKzdBU3lLbEZTN250RWk2SVR0WWFTUW9HMzJPZWM3MnMzV1cyUnRUTy9adGxqYmtuM01oaUdWbkQ3dHB6K214aGNYRUczMUh6bDVXSE1hMFFtUDBDemtUamViQ2NQY05sckt1bzFSbEZqSHYxUnBtNks5Y2xUb1drUXVHYU5KRjBkN3NGRnRybXREV3dBNUdlR09lYnRqbkZlbG1HWjRjVlQiLCJtYWMiOiJlNGU2ZWE4N2Y5OWUzN2FlYTViMWU4NDlhOGM3YjdjMzdiYWRiZTBjOGQ4NWJiNjY0M2Y0ZjYzYmY4MmVkNDVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Imc0L1Vhc0hPTHhpNFFjM0Q0SmFiR1E9PSIsInZhbHVlIjoiTG82ZFd2UUQ0YkNoeUk5cVBnV0FWR2M0NlNiVVIraEovamx6K0FYV2RMTzhLb2UwSWJYRngyOThEK2w2M2RuQVlHNVNZQm5iK3ZMM3hhRjR5QUR1MDd5clI5OXcrOGJoU1JZdGFPQ1YveFJsTlQxRHBhTTh6ZUlWM3pDZVdON1dvL3JIYWpHaElndnJKS0VNdGRDK0cwaHBYeXhNS0UzOU1zVTlKeFBUdmYyVURXeTc3V1BrcFdrTHRxM2NZUnIzcnlZMnhqb0xQbE9HTTdCNFF1dkYzVGZUbS9HNHVCS1JNZHBocnpsaW9JRmpQNDBUK0tJOEFqK1I3MGdLbVVZQ2tpeTczVm5GV3VjaGJzbmdRTXpMdFQxU1RzajhwUlN0c2pmMUNTeC9HK0ZSWkM1TEoyVkpyODA2TTFCSDVVeUY4dzhRdjBCRlF6Nno2b1BZUk0vQk1uZXpMR01RTGJCclR3bXN6OTNiOW5veDhnRCtPTmhPRzBKN2UyWVpTOXdhSFFLR21RKzRldFZtR1IyNzM5dWVDRnFBc213ZXFIN3RnNkQyZlB3QmFETGJaS0p1LzhzQ1NNaXI2RWRXY0kycVlwNTB4dDRNUFhSd2lJd3BMbHBkWXlRVCtGd3ZESGlxVk9VbVk2b3NPcFV5eXpzRVFPbTdEZS93VDA3YVdRVksiLCJtYWMiOiJlOGRiNzZlMDdkY2ZiZjk3ZTg5ZmI4MTQ5YmRjOTdhN2Q1ZmNkYWM2MTZjNGQwODNmMzg2ZTYyN2QxMWU3ODE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1631974300\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1233238850 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21K3ZmFzZyr4s2pH9aKWRqwEKhgdUCnDfhl6jx78</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1233238850\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1037244726 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:51:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVTM3pCM2VMZW9ZQVR6cWxFdGVtekE9PSIsInZhbHVlIjoiMWpMNExjV1pUY2tuNGNrNWZaR1BsWFdhRGRHMi9GRVNmZGI4aHhBNHFxQlRvUVpCVGsvcFdMS2xNWDc3UEtHVVpwUkVqZlNJSXdmaXBFQmd5eTRyZmdWbEdDeG1hMFFwMDVrVGZVaHJGWFVhNXVaUTRvcFJObjZLSUU5aTdhanc4YUlLSGJCMy9jVmIzMSt4U3FlaTNaRGp6UVQ5VVJyekpNSnBwa2xadXpXV2JLcjNUZ0tnUXFSTzIvRG1zKyswVFFKMFlrZDZ5SGIrNmFiaHRPcnBlejBvbkt3d0ttbVpubkVyQzJSNHl3bDVRUWpzRHN1REFCdzFBN1BSRVJieGM3dFU1Sm5BQVlGTTZuS25KdzdzbjNaUGNpenU5SzArbDllWnFBR1VoNy9ibVE0VEpsaWd4cXlKNG9vdENiYkpzZzJXbTJJQ1RRR016QXgzSkY0VThyMUhVV2hyVXowSUhYS3FYd09MLy93N2pqUDFCa1ZsQ0I0V1plRnkxRFJ0Q2d1MkZUak9BNWd0OTJkRzgwNDBEWmZuTDRBekNXeWVZS2ZWMi80MTdBOGY2NVU5OWJBRGthWDR5bVNrVHNua2FIQVJDSFY4UHZVQlkrZmpkN2lQT1puRWRkUlY2M2FGZGtsWHRpcUlydFVsbnBLTEZISjNGT0JpbjVuSTVTb0oiLCJtYWMiOiJmNjhlZGFlOTlhMzg3MzhlZDI3ZTE0MTNmOTViOGY4Y2NkMDhlYzNkM2U1Mzg1M2VjMTY4ZDgyMGQ5Y2I5NzYzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:51:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6Ikg0VDlyZXhUQ1RwMmwxTm52cm9YRkE9PSIsInZhbHVlIjoicy85MndtODFZUTF0L3VFbndlbGNrRzdTWGk4M1UzYi9HYjdTNlIvQXV4VDN5dGZrV0RpUDNKZmMwdUdCVkplNGlQdi9NZ1Z2U1VnVjU1bnNGSnBwWk9JcGNINGdRUHZTYmJ6alhMbDR2ZTlMd2taVzAvekpNeExUdStzNHdJbVZWenpRWnVXKzh2RHFZQ1RKSnlxMGpZOGtnOEM5Q3JHK1RNOWhOa2NpTjRkTTBZK0JQUnI1c1JVTzAydCtQTVV3OHlTTGIxYVdFTkdSTnpWNEx6WGFnK1Zmb2I0b01oOGhMSUI2NDhWVEdHWTE5dml0U2ZGSTJzRERZTHBBRFdXb3JKZG5GNXRWcDN1VDFGUGRmNzM4b3BNYWhWYUl3Mkp5NWgxVFFnVWk2NllGL1RKMTNFSDY0bGIzblp6RytiVXYxT1VWNEI3OHkxc01mZnlOVjZ3cm51bHMvb3lrR2poaUNRSlhLa1BwNFFVaGNtNVRTKzBqeW9qNjM1VTNJQmhaam8rRnk0VjAzWFdiVElFOW5FUWszTjhhYVUvS1VpNmRqMGM3MTVuc3psY2ZaeWZXVllOcTZUaXVBL0tKYUVMWTdHNnU2b1U1dzduaGhON0oveDljWjByenNrQzZQVTBodExEOE9PeHpGNHZBdnJLRW4zWVJhR0JsV3pvZ09TVloiLCJtYWMiOiJlNjc4ZTE1NmFkZmVlN2M3MGVjMjNhNzc1OTA5ZTEwZmRjMjBhMDlmY2VjYTljMTI3MmZkMzRiZTBkNDc3ZDY2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:51:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVTM3pCM2VMZW9ZQVR6cWxFdGVtekE9PSIsInZhbHVlIjoiMWpMNExjV1pUY2tuNGNrNWZaR1BsWFdhRGRHMi9GRVNmZGI4aHhBNHFxQlRvUVpCVGsvcFdMS2xNWDc3UEtHVVpwUkVqZlNJSXdmaXBFQmd5eTRyZmdWbEdDeG1hMFFwMDVrVGZVaHJGWFVhNXVaUTRvcFJObjZLSUU5aTdhanc4YUlLSGJCMy9jVmIzMSt4U3FlaTNaRGp6UVQ5VVJyekpNSnBwa2xadXpXV2JLcjNUZ0tnUXFSTzIvRG1zKyswVFFKMFlrZDZ5SGIrNmFiaHRPcnBlejBvbkt3d0ttbVpubkVyQzJSNHl3bDVRUWpzRHN1REFCdzFBN1BSRVJieGM3dFU1Sm5BQVlGTTZuS25KdzdzbjNaUGNpenU5SzArbDllWnFBR1VoNy9ibVE0VEpsaWd4cXlKNG9vdENiYkpzZzJXbTJJQ1RRR016QXgzSkY0VThyMUhVV2hyVXowSUhYS3FYd09MLy93N2pqUDFCa1ZsQ0I0V1plRnkxRFJ0Q2d1MkZUak9BNWd0OTJkRzgwNDBEWmZuTDRBekNXeWVZS2ZWMi80MTdBOGY2NVU5OWJBRGthWDR5bVNrVHNua2FIQVJDSFY4UHZVQlkrZmpkN2lQT1puRWRkUlY2M2FGZGtsWHRpcUlydFVsbnBLTEZISjNGT0JpbjVuSTVTb0oiLCJtYWMiOiJmNjhlZGFlOTlhMzg3MzhlZDI3ZTE0MTNmOTViOGY4Y2NkMDhlYzNkM2U1Mzg1M2VjMTY4ZDgyMGQ5Y2I5NzYzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:51:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6Ikg0VDlyZXhUQ1RwMmwxTm52cm9YRkE9PSIsInZhbHVlIjoicy85MndtODFZUTF0L3VFbndlbGNrRzdTWGk4M1UzYi9HYjdTNlIvQXV4VDN5dGZrV0RpUDNKZmMwdUdCVkplNGlQdi9NZ1Z2U1VnVjU1bnNGSnBwWk9JcGNINGdRUHZTYmJ6alhMbDR2ZTlMd2taVzAvekpNeExUdStzNHdJbVZWenpRWnVXKzh2RHFZQ1RKSnlxMGpZOGtnOEM5Q3JHK1RNOWhOa2NpTjRkTTBZK0JQUnI1c1JVTzAydCtQTVV3OHlTTGIxYVdFTkdSTnpWNEx6WGFnK1Zmb2I0b01oOGhMSUI2NDhWVEdHWTE5dml0U2ZGSTJzRERZTHBBRFdXb3JKZG5GNXRWcDN1VDFGUGRmNzM4b3BNYWhWYUl3Mkp5NWgxVFFnVWk2NllGL1RKMTNFSDY0bGIzblp6RytiVXYxT1VWNEI3OHkxc01mZnlOVjZ3cm51bHMvb3lrR2poaUNRSlhLa1BwNFFVaGNtNVRTKzBqeW9qNjM1VTNJQmhaam8rRnk0VjAzWFdiVElFOW5FUWszTjhhYVUvS1VpNmRqMGM3MTVuc3psY2ZaeWZXVllOcTZUaXVBL0tKYUVMWTdHNnU2b1U1dzduaGhON0oveDljWjByenNrQzZQVTBodExEOE9PeHpGNHZBdnJLRW4zWVJhR0JsV3pvZ09TVloiLCJtYWMiOiJlNjc4ZTE1NmFkZmVlN2M3MGVjMjNhNzc1OTA5ZTEwZmRjMjBhMDlmY2VjYTljMTI3MmZkMzRiZTBkNDc3ZDY2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:51:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1037244726\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-475379887 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/expense/3/description</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475379887\", {\"maxDepth\":0})</script>\n"}}