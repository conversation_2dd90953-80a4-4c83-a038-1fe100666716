{"__meta": {"id": "Xcb54af3d908070cbd9d57c1b513704d5", "datetime": "2025-07-31 05:17:12", "utime": **********.501253, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939031.541148, "end": **********.501298, "duration": 0.9601500034332275, "duration_str": "960ms", "measures": [{"label": "Booting", "start": 1753939031.541148, "relative_start": 0, "end": **********.428038, "relative_end": **********.428038, "duration": 0.8868899345397949, "duration_str": "887ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.428055, "relative_start": 0.****************, "end": **********.5013, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "73.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NvErVib1IlHXNOgTEaYgNGfQRzBWPTpU16O8b8CL", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-823149720 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-823149720\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-849610879 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-849610879\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1661629119 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1661629119\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1900943715 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1900943715\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-818342127 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-818342127\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-372358379 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:17:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikt3WFdPeC9yV0NWalkxYmpMT1ZERHc9PSIsInZhbHVlIjoiM3BFaTIvRG1jKzRpVGFGYVlUbXVhRUc2OE9XSE1xTVc2RGFuVzVKU1ljWit4eUU0SGNjejJ5RzArR3E5U0xVbERWQmdWUlprcCtSQnAxNVFXMnl5RGNBejZLZlpWcDBaMjJLWkhDS2VEWlUwZ0dVMVE2NmtJRm03MVNLUUdIa1B6bGR5bFpIdEZtYjlBdmpMYU80ZVJGQnU0anFPY1lqRnpWT1lkekRJVE45V0NEQmpqUjFYNGgwRGY1RTJaZFgrdzhkcjdIYW41UmMrSllaQTFqK2R1dVRQaTRwb284aDEweHdXd0V2V0kwZTRFa1FEekt2aWNldkVNUlAyM0E2MVJrMEVNRzl5YUNYTE9vUk4vOWhkUk9McFVyMnM1YlpqNGc2Uks1QWNzODN2STN2LzRQZ0JuSTk5UmFZY2kwUXkwSHVJRVJFMmplQXJFc2xUVFgvVEd2eUgzOS9RSllFbDBUOHRMLzVTb2lJSHZ5M2prWUJ3OEdILzd6TGdtR1h5YWFxVkNqWStvYlJhVEpRdGpnZEV2aCtEMTVPUDVzZzJhaHQwblFES2prNkRONmRQVWhlSGZKMWMvZm1iaWN1Nk1hTklaQWdOcnc1bVhyZFc1YUNRVTVZdk55TUIzbmNUYUpzTXcyQm5ZSC9DQm1hWGZJRG1MSVJvcWpTNlFkeFgiLCJtYWMiOiJhNWYwNTgxYmFiNDY0MGI2NDk2ZDQwMjUxZDEyODIwMzQwYTg2YzNkM2IyMTQ0ZjllZDNhMDhjZWZhZDdmNWEwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:17:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImM0d3FmY0diR2xrazVnQ3lXdFQ3S3c9PSIsInZhbHVlIjoiMlk2K1hPUU1hMjQrNy92OVBCeXRONDhiZy9VS1NIdzd4Z05jeFROVVgzN1B4dzBkaHBib0xCbkJteWlRL1phVzYxcWszaFVYK0tOMVlSM1NuMEVuKzdJTHB4eW0vTkhsMkpjTk9TNnZRQ0Q4ZDFna1FtZ2ttZWxKU2ZKK1pCeFNUM0JHZjh1am9jYlhwaHRvbDA1b0JiaWQ5b3JvK1kxcGd0eXZXNFBWdS9LUEt3cXdZUEZqWWsydkRndUErWWlmWUlpWWZUaFU2Q3A2Q1N4RHhQUFF3akdOS3hrL2lFWjdRTlc5YnY5T0IzM09QOGF0YWhPOGh2NllVS2xIelRaVnNoaCt5U2ZPSmNiZ1ZKYTd0K0dGZ1Nrc3FFS0c2ZnZzTUtOOElhdjJIdkg0anE4L2N6TzFKdlVjaG9QK0RiYU1MZkpwYmhvczl3ZU03emJ4bFNVZ1hxWlFFTS8zN1U3RDFUc2FpRmtGZ0tvSVVsaTdRN2JlSE0zTENuY1dIelh5Y1FSeG5RSUtjK3RINm9GQmRqc3lmQ3ZrNExOcWFueUQ4WmxoSWUxY0ZMb1VoU3AyT1o4aCtVRzRTcWdpaE14RnVxeGZhczBPb0d6QWpvNy9yaXY1bG9Ta05Md2NMUmE1YlVSZGlUQitJS2NRVG5zdXBHT0JTdXgyVkV3NlVQSnUiLCJtYWMiOiJkOGIxZWJhMmFhZWJhMWYxNmY5MjQ3OWUxODhmZGU1YmJjN2M3ZjhmOGJmMzZkOTQzYTk2NzZhM2JlN2ViZmVkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:17:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikt3WFdPeC9yV0NWalkxYmpMT1ZERHc9PSIsInZhbHVlIjoiM3BFaTIvRG1jKzRpVGFGYVlUbXVhRUc2OE9XSE1xTVc2RGFuVzVKU1ljWit4eUU0SGNjejJ5RzArR3E5U0xVbERWQmdWUlprcCtSQnAxNVFXMnl5RGNBejZLZlpWcDBaMjJLWkhDS2VEWlUwZ0dVMVE2NmtJRm03MVNLUUdIa1B6bGR5bFpIdEZtYjlBdmpMYU80ZVJGQnU0anFPY1lqRnpWT1lkekRJVE45V0NEQmpqUjFYNGgwRGY1RTJaZFgrdzhkcjdIYW41UmMrSllaQTFqK2R1dVRQaTRwb284aDEweHdXd0V2V0kwZTRFa1FEekt2aWNldkVNUlAyM0E2MVJrMEVNRzl5YUNYTE9vUk4vOWhkUk9McFVyMnM1YlpqNGc2Uks1QWNzODN2STN2LzRQZ0JuSTk5UmFZY2kwUXkwSHVJRVJFMmplQXJFc2xUVFgvVEd2eUgzOS9RSllFbDBUOHRMLzVTb2lJSHZ5M2prWUJ3OEdILzd6TGdtR1h5YWFxVkNqWStvYlJhVEpRdGpnZEV2aCtEMTVPUDVzZzJhaHQwblFES2prNkRONmRQVWhlSGZKMWMvZm1iaWN1Nk1hTklaQWdOcnc1bVhyZFc1YUNRVTVZdk55TUIzbmNUYUpzTXcyQm5ZSC9DQm1hWGZJRG1MSVJvcWpTNlFkeFgiLCJtYWMiOiJhNWYwNTgxYmFiNDY0MGI2NDk2ZDQwMjUxZDEyODIwMzQwYTg2YzNkM2IyMTQ0ZjllZDNhMDhjZWZhZDdmNWEwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:17:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImM0d3FmY0diR2xrazVnQ3lXdFQ3S3c9PSIsInZhbHVlIjoiMlk2K1hPUU1hMjQrNy92OVBCeXRONDhiZy9VS1NIdzd4Z05jeFROVVgzN1B4dzBkaHBib0xCbkJteWlRL1phVzYxcWszaFVYK0tOMVlSM1NuMEVuKzdJTHB4eW0vTkhsMkpjTk9TNnZRQ0Q4ZDFna1FtZ2ttZWxKU2ZKK1pCeFNUM0JHZjh1am9jYlhwaHRvbDA1b0JiaWQ5b3JvK1kxcGd0eXZXNFBWdS9LUEt3cXdZUEZqWWsydkRndUErWWlmWUlpWWZUaFU2Q3A2Q1N4RHhQUFF3akdOS3hrL2lFWjdRTlc5YnY5T0IzM09QOGF0YWhPOGh2NllVS2xIelRaVnNoaCt5U2ZPSmNiZ1ZKYTd0K0dGZ1Nrc3FFS0c2ZnZzTUtOOElhdjJIdkg0anE4L2N6TzFKdlVjaG9QK0RiYU1MZkpwYmhvczl3ZU03emJ4bFNVZ1hxWlFFTS8zN1U3RDFUc2FpRmtGZ0tvSVVsaTdRN2JlSE0zTENuY1dIelh5Y1FSeG5RSUtjK3RINm9GQmRqc3lmQ3ZrNExOcWFueUQ4WmxoSWUxY0ZMb1VoU3AyT1o4aCtVRzRTcWdpaE14RnVxeGZhczBPb0d6QWpvNy9yaXY1bG9Ta05Md2NMUmE1YlVSZGlUQitJS2NRVG5zdXBHT0JTdXgyVkV3NlVQSnUiLCJtYWMiOiJkOGIxZWJhMmFhZWJhMWYxNmY5MjQ3OWUxODhmZGU1YmJjN2M3ZjhmOGJmMzZkOTQzYTk2NzZhM2JlN2ViZmVkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:17:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-372358379\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-703387001 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NvErVib1IlHXNOgTEaYgNGfQRzBWPTpU16O8b8CL</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-703387001\", {\"maxDepth\":0})</script>\n"}}