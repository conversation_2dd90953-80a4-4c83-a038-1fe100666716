{"__meta": {"id": "X39b625c39d8608909c7236361d846bb8", "datetime": "2025-07-31 06:32:44", "utime": **********.485361, "method": "GET", "uri": "/users/79/login-with-company", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943562.460096, "end": **********.485395, "duration": 2.025299072265625, "duration_str": "2.03s", "measures": [{"label": "Booting", "start": 1753943562.460096, "relative_start": 0, "end": **********.305303, "relative_end": **********.305303, "duration": 1.8452072143554688, "duration_str": "1.85s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.305334, "relative_start": 1.845238208770752, "end": **********.485399, "relative_end": 4.0531158447265625e-06, "duration": 0.18006491661071777, "duration_str": "180ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44698040, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1450\" onclick=\"\">app/Http/Controllers/UserController.php:1450-1474</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00699, "accumulated_duration_str": "6.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.417659, "duration": 0.0056, "duration_str": "5.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 80.114}, {"sql": "select * from `users` where `users`.`id` = '79' limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\UserController.php", "line": 1452}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.439358, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "UserController.php:1452", "source": "app/Http/Controllers/UserController.php:1452", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1452", "ajax": false, "filename": "UserController.php", "line": "1452"}, "connection": "radhe_same", "start_percent": 80.114, "width_percent": 19.886}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/users/79/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/79/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1718807557 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1718807557\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjhwUTQ2Z3BndW9xZnJhVnU2R2NhNWc9PSIsInZhbHVlIjoiR3kwamhNTFZMMGpsMlBUY0ZlcjNVekRHQU9nZEFIVTB4TTVXWTFkclRmb2lNYWI1Ni9HUktUWUZNUlh5ZFVCc29BU0dBem1ON0xjaEpudmx5ejFMYUxIdjhYMWFZUjVLMHVoSmRBcGphTnBYbjJEaXdwallaZldla1pSTXNDZWdnN2E2dFp3ajEzZk52cnVTVTNHS3lyVDYwYi9ydE5JZTdScFkrdVlMMVJrelIrekorajZmSW1OZzhFbUZveUdNOFVpa2FjTjFKMUlheFhwUjNvZ3BDVjNDK1pxcjd5WUdRMUNjcTZDZzA1b1RNd0Qzd0xVaWEzcmdwcVdtblk5OThWZWJDMUxDdzNhZ1ZjN0V3VjZOYTZRSC9HcGlTTzFrUnprWndCRldkNFgxVDVHaXRBUnkrUUtmZFFudDRCMWtCUFhNUC8wa2JGaHdhdFduOGNQdVhFc09CY2FPV3B1ZXVsQ3JmaVJqWjFiTVVPZWl4cXU3amgxZ1luNHh4clo3RnpSVlN1NGxDZlJhVGlCOXJ6cTQ3eTJjaHFwcXRvaGIwdTM3ckF5NWNsS3dFZkpMQVBCcENJcDNjWVIrOVVha0c0MDJGN2s1eGlzS0ZzdnJmbDFKOUVNdm9xQzVTL3Rrd3hpY2RrYTFUNFVFdlB2TnlMRlk3NjJndFhOUlpZelMiLCJtYWMiOiIzM2MyYWI3NTJiNTdjYWY1ZGExMDAwODk4NzIxMjIzZDk4MjlmYWQ3N2Q1MmY3ZWFkYjZkYjA2YTE5YjI0NmQ0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlNXaTJ6bGVCYkxSLzNHZHE5N0hTYXc9PSIsInZhbHVlIjoiTS9qTUQ5MFFKWFNvRnZ5dzhKa1h4THo3QTlHQzZvY3RIMEJ6S2N3bitqQjI3Y3EyMHg3a3R4czdpNkdzdWl2MlpDM1RWbEdrSEsxcXFscXY1ZDdoanI0TU5NZTVkdnhGSWErTXN4QmZpQ1BEZHJ5U1M4b1lXdVhMZHhhRWw4cFhhRHlXbDRzemtKdGo2U1EzYWp2dlh5RmI2ZnRoc0ZKdnFvcjBsVzBDNDJFZElwNjczU3pHcDIzSEdHVnAvQjg3MW0yRytkb0doZmk0RFVocEVrN3lhcGtGZkp0WE95bDRTWE05cjJuSHpjSmlGZHczWHlVclBzWVhVNVZaUkNVWjhOdXd6S2xXZG4zTUFnOHFUd3RBWGpuU0tZWlFLNmNIV2paSjZPU01SWWg0YmNQeGMvd0t0dVQ4ck5qNmhGd3Foc2R3NnFuZ3AyaGU0YjJhY0JnY0Ixd3BKYUlrcE15d2hPWWZHK2Q4U0NEYzEzbzlmVFh6bGJaUmk3ZXVrSnR4OVpwY29jNUg2OTNiUk5sRzJTWGNia2tGTDhuZkwralJsaGNNb2xnK1hKNytlUTRLNjdNbHNyZXZoWjFRejRlajN6ODNiU00reHlqSGkwTFNmY1lYZCtWb25tUWZhZXZiaTgxcGdFUzViRExsbGRURTFzenJkNjBoemNJaVkzdFgiLCJtYWMiOiIyOTM1NmQ2Y2NmNjMxNGU2MzFlOTUwMTE4ZTU0NGU1ZGFkNjk2M2I2YjA5ZDE5ODhjN2M2MDE1Y2QzYWEyY2Q2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2061512215 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:32:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdGeks3NFRMZUR6cDlTZlZQSDlseFE9PSIsInZhbHVlIjoiT0s5WGJlN1lLMElSS1FWdldEVHRzcENiSjh0dHpSUFJMdnlZWDg3VTVsd0RkbTR3UngrbUtvaFlLVStjbk5HM04yb1pUZVNOMzFvOXpldTJSTCt6MmxpTXg3RkY2VGh0TTJXK254aTlZdGE3anNhcW9TSnM2ZzNjM1d4UXUwbE9RTC9yMVd2czZteDE3TVlLbUFySWs5c1JvTDJ0NUxPZDBoT21FcEpzdEplME5yN1J4amRXNkdHT3pvdHFXc2dZMVdYbzBLbDZwVWtKeEk5bkZSVEZvdGQ1YkhQdFVyc1VaVzVPVjhMdXJwYjduRllIcEtYR0Erc2VLbkduLy9qbXVvemZodTZhZnh4eWVQK2gyMVJZRi8zS0swVXhGZ1dVam1YTzNTMDhJNEkzeHZsUWdWWElOMk0waXJMY3phdCtzc3E1RWY0amd6NmxYVFFCSENNTHFmbUREU1JSS1ZKTGxiYmw5N0tuKzBUS0Z6S3Zpa1loTDhpd1JmZDRrNzZZcExGVi95SHRqTDhxSXV3ODlSZnhLdU9lQ2pxN21RcHBLRDVKYzhCUWV4TXBWNTRyTUdVYmIrclF0ZFAzTXlsNkg3ei96U1VwUFRWREsvK3RFbU1nM2lRN3R6dy9vZXB3UmNtZlVkOU9oSXBHb2dKMUozRWpuMWNRdGQ3bWhTS0EiLCJtYWMiOiI1MDg4ZmVmZDc0YTg2NmE1Yzg4OTg3MjgxOWMwZjQwNGVmYWIxODg1OWM1M2IwYTNkZDkzOGI3OTAwNTk0MDViIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Iis1OFJiY1pQVTlabzdXSmpxdXBnVUE9PSIsInZhbHVlIjoiQlp4REdyTU8yOUhGbWtVZGdDOUVHYk9YdG1JUFRyZlEyU01INTAvZCtwclZqN0Q5eEVpZktXZVZ5cHZWM1RhR3VOejMzWkNuUktOS0oyaVc3eXh6R1psSDgwRHFPVG9FWUdGNmFJRXdLV1kweUIyYkphYU1tMkpoUXBLMldRYlBJM1lITmN4VTBHNDhmamtlczA3WjhEUSszcnpxZVhVZHhEM3A0bDhrRnFGa0tqaWxRZW9OMlZPSGc5VTg3Z3d3enR0WkVjSlhQSG5sUkhKRlJNNU1FbUQ5SnVvSFFOOEl5d1F1VjNhaTVjTmZaSzVIQ0Z5RTYyQ0M5cVQ3ek12aG8vNzJKVFB4MitlTHdBR1d5YnZWVXo0dHlWQ2pId2d0L09Na3FlSHZiSXFJa3NqN2s3NDdFVWx2RGY3ZEJSMWU0N0xTODl3QnJwYzlxb0VEc05QeUxGL1ZlejNudHI2SGJLUVBNcXFmZEJla3VDSGVHdFBlSk5WSE92ck5OVnFieStrYUJTdWV5LzNjMVlIQ1NzTE04dThnNnJHSkx3Vi9RYWxXOGJVRlZJVy9jMUNBUWx2SjVtS1ZlRk9DcnNLMXBEcTh0NmpQcUJqY2p4cFozWHpra1YwZ3RrZnM2dWMzVHcvaHdOWDJxdzFpUmFtVk1MdWcwTnVsekxEeHRjd1giLCJtYWMiOiJkNWM3NzIwMzYzNGM2YTA1Y2ZlYmJjMWVjZDQ2ZDZmM2U3ZWFkNzc4Nzk4YTZiMGMwODQwMzQ3OTE1YTYzMDM3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdGeks3NFRMZUR6cDlTZlZQSDlseFE9PSIsInZhbHVlIjoiT0s5WGJlN1lLMElSS1FWdldEVHRzcENiSjh0dHpSUFJMdnlZWDg3VTVsd0RkbTR3UngrbUtvaFlLVStjbk5HM04yb1pUZVNOMzFvOXpldTJSTCt6MmxpTXg3RkY2VGh0TTJXK254aTlZdGE3anNhcW9TSnM2ZzNjM1d4UXUwbE9RTC9yMVd2czZteDE3TVlLbUFySWs5c1JvTDJ0NUxPZDBoT21FcEpzdEplME5yN1J4amRXNkdHT3pvdHFXc2dZMVdYbzBLbDZwVWtKeEk5bkZSVEZvdGQ1YkhQdFVyc1VaVzVPVjhMdXJwYjduRllIcEtYR0Erc2VLbkduLy9qbXVvemZodTZhZnh4eWVQK2gyMVJZRi8zS0swVXhGZ1dVam1YTzNTMDhJNEkzeHZsUWdWWElOMk0waXJMY3phdCtzc3E1RWY0amd6NmxYVFFCSENNTHFmbUREU1JSS1ZKTGxiYmw5N0tuKzBUS0Z6S3Zpa1loTDhpd1JmZDRrNzZZcExGVi95SHRqTDhxSXV3ODlSZnhLdU9lQ2pxN21RcHBLRDVKYzhCUWV4TXBWNTRyTUdVYmIrclF0ZFAzTXlsNkg3ei96U1VwUFRWREsvK3RFbU1nM2lRN3R6dy9vZXB3UmNtZlVkOU9oSXBHb2dKMUozRWpuMWNRdGQ3bWhTS0EiLCJtYWMiOiI1MDg4ZmVmZDc0YTg2NmE1Yzg4OTg3MjgxOWMwZjQwNGVmYWIxODg1OWM1M2IwYTNkZDkzOGI3OTAwNTk0MDViIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Iis1OFJiY1pQVTlabzdXSmpxdXBnVUE9PSIsInZhbHVlIjoiQlp4REdyTU8yOUhGbWtVZGdDOUVHYk9YdG1JUFRyZlEyU01INTAvZCtwclZqN0Q5eEVpZktXZVZ5cHZWM1RhR3VOejMzWkNuUktOS0oyaVc3eXh6R1psSDgwRHFPVG9FWUdGNmFJRXdLV1kweUIyYkphYU1tMkpoUXBLMldRYlBJM1lITmN4VTBHNDhmamtlczA3WjhEUSszcnpxZVhVZHhEM3A0bDhrRnFGa0tqaWxRZW9OMlZPSGc5VTg3Z3d3enR0WkVjSlhQSG5sUkhKRlJNNU1FbUQ5SnVvSFFOOEl5d1F1VjNhaTVjTmZaSzVIQ0Z5RTYyQ0M5cVQ3ek12aG8vNzJKVFB4MitlTHdBR1d5YnZWVXo0dHlWQ2pId2d0L09Na3FlSHZiSXFJa3NqN2s3NDdFVWx2RGY3ZEJSMWU0N0xTODl3QnJwYzlxb0VEc05QeUxGL1ZlejNudHI2SGJLUVBNcXFmZEJla3VDSGVHdFBlSk5WSE92ck5OVnFieStrYUJTdWV5LzNjMVlIQ1NzTE04dThnNnJHSkx3Vi9RYWxXOGJVRlZJVy9jMUNBUWx2SjVtS1ZlRk9DcnNLMXBEcTh0NmpQcUJqY2p4cFozWHpra1YwZ3RrZnM2dWMzVHcvaHdOWDJxdzFpUmFtVk1MdWcwTnVsekxEeHRjd1giLCJtYWMiOiJkNWM3NzIwMzYzNGM2YTA1Y2ZlYmJjMWVjZDQ2ZDZmM2U3ZWFkNzc4Nzk4YTZiMGMwODQwMzQ3OTE1YTYzMDM3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2061512215\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://127.0.0.1:8000/users/79/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}