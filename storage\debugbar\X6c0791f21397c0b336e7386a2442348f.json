{"__meta": {"id": "X6c0791f21397c0b336e7386a2442348f", "datetime": "2025-07-31 06:32:22", "utime": **********.680047, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943540.153504, "end": **********.680101, "duration": 2.526597023010254, "duration_str": "2.53s", "measures": [{"label": "Booting", "start": 1753943540.153504, "relative_start": 0, "end": **********.533, "relative_end": **********.533, "duration": 2.3794960975646973, "duration_str": "2.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.533029, "relative_start": 2.****************, "end": **********.680105, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "147ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "mObpBrzEV0jwdZINvBGjqCROGGG4Xwal5TlnDHC6", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1030609955 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1030609955\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2069185306 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2069185306\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-269017538 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-269017538\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-901498039 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-901498039\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1056631619 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1056631619\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-719871538 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:32:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ildpc1V6UFFjcGduL2Vyckt2cnhvdHc9PSIsInZhbHVlIjoiWlZ4RkVyQlErVS9Ncm5vN0d4NVBpM1ViREpLNXB5ZU1zY1FBNHFjWS80YnF6SVIyTTJtdjlVQ1pSM1NYem5rYTg4NXRHdjdYc3lQTmtXQWxXL1FZNUtBUVYxNjA5TFNLOGt5S28wTzZFYTJVeU5aYmhYU2l1RjRITWQ2WW9NODAyRUdTZFVOZmRhVXVrUW9zenpJMHFVakxUWVZzTGxySUNJQk1XZ2NaUDYrb2pwK3hiSUIvem05NmNxVmhxRkVlY0piN3pKcWZTNVNVaThpd1ZCeDVWZ0w5NnlETDgyTEJyektITGl6LzVESjM1U1NUK1hLdGpOSW9RT0xpQzhwYmFPUzZBUEZxZ2hnQVFwWVBucklnWlJRcW1FTlJjOG9kMS9WT1dpdnZ6Z1l3V2lJUEdRSkxkN0dXU3ZlRTc0aWdDZitWMHVnOGpNb3dEeXRrMWFNcmxOY0x5aXJuNWYwR2E1cnNmUE5yQ0JBQnZ2bmh5WEt5bFdxdW1NUVE0RzFad3pta1BKS2NsUXJOUUQzdWZFaS9EOG5rUFQrb202dnlxaUFkWkEvbmRtN2lIN0tZQ2tRUUxqMWtndmVPRHpUcDlscWZqeTRmVW1JZEhnK1IyNTA4SmZHVkdsZlJKNU9IUXRtdHpXYTJGaG1TZjkralFQTmpsaHo2U1U0MkwwaVkiLCJtYWMiOiJlYjBiNjY2ZTRhZWJjMGFmODYzY2FhNWNmNjIwZWVhYTEzNWNjMWZlMjRlMzUyMzM4OGE5MTNhMjI5NmVjZDJjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjV5cWp2WDlUekZhL3pJQjRNMkhpdXc9PSIsInZhbHVlIjoiZU4vZm1mUXFIMmJVa0lZRi9LUEFObDVXa1VRUFFqQVJ1L1hjSHdaQ2hTZ3owTHNmR3RyVG9haWNkTnlOM0NuNk1DcGdwWXJySGY1Mjc1RWh6UUFKT1N2VEZ6R2dLcDBKd3M4L1pZdmVSMWtZRXlnQnZ6YmlZQlRGMGU1dTkxQ3U1N2IybCtWQ3dUQ3ozY2M5RGlnR21hVWZ4T1RidWtGajNicmFzczdCQXFkMmg0K1h6Sm93ckI3bllQVUIxaUxiQm1rRVFrdmtrSVhwemZxaG8waGdPWkw2NGJsa0lVa1hLZDU5SzlFeGR0eWVUcklWb1kvcnBVRUMvN3lzclVRVEc2alhHZHUvZzNyT0N4VDhtVmh3c2ZXZWRuTUxqd0VweTNWeExzeE13WHE1dTBsNE45cU1OZGpaeGQrUElxRXliQmVwZ281eFAwdHRHZHZUZ1hzVVlZWENxNnpzQUcreGdPK2hna0l6ZmxIYkNhQ1kyMVZpU0RtdUtYaVNZVVBHeElzd3kwUGdCcDZBR0w5MlpmbWR6QkN0a1VrVDNqbkZWOUxYc2k1ZHpCS3BhWFJ0TVhSemlmY3FMMDNXUVNDL0Q2U09OYjhPNDY1V0VLWEZ5NXNKNFV2c3RsT2dYYlpmRVZNQ29uOUVINlVYR3Z6SHVTQWsrekVjMzVRV0htYksiLCJtYWMiOiI1ZDUyMjdiYTk3MDVhNGRiNWE1NDUwZDczMDFhZTQ3MDQzOGE4ODcwM2QzMGEzYzExNjAzMmY5MTU3YjMwNzQ5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ildpc1V6UFFjcGduL2Vyckt2cnhvdHc9PSIsInZhbHVlIjoiWlZ4RkVyQlErVS9Ncm5vN0d4NVBpM1ViREpLNXB5ZU1zY1FBNHFjWS80YnF6SVIyTTJtdjlVQ1pSM1NYem5rYTg4NXRHdjdYc3lQTmtXQWxXL1FZNUtBUVYxNjA5TFNLOGt5S28wTzZFYTJVeU5aYmhYU2l1RjRITWQ2WW9NODAyRUdTZFVOZmRhVXVrUW9zenpJMHFVakxUWVZzTGxySUNJQk1XZ2NaUDYrb2pwK3hiSUIvem05NmNxVmhxRkVlY0piN3pKcWZTNVNVaThpd1ZCeDVWZ0w5NnlETDgyTEJyektITGl6LzVESjM1U1NUK1hLdGpOSW9RT0xpQzhwYmFPUzZBUEZxZ2hnQVFwWVBucklnWlJRcW1FTlJjOG9kMS9WT1dpdnZ6Z1l3V2lJUEdRSkxkN0dXU3ZlRTc0aWdDZitWMHVnOGpNb3dEeXRrMWFNcmxOY0x5aXJuNWYwR2E1cnNmUE5yQ0JBQnZ2bmh5WEt5bFdxdW1NUVE0RzFad3pta1BKS2NsUXJOUUQzdWZFaS9EOG5rUFQrb202dnlxaUFkWkEvbmRtN2lIN0tZQ2tRUUxqMWtndmVPRHpUcDlscWZqeTRmVW1JZEhnK1IyNTA4SmZHVkdsZlJKNU9IUXRtdHpXYTJGaG1TZjkralFQTmpsaHo2U1U0MkwwaVkiLCJtYWMiOiJlYjBiNjY2ZTRhZWJjMGFmODYzY2FhNWNmNjIwZWVhYTEzNWNjMWZlMjRlMzUyMzM4OGE5MTNhMjI5NmVjZDJjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjV5cWp2WDlUekZhL3pJQjRNMkhpdXc9PSIsInZhbHVlIjoiZU4vZm1mUXFIMmJVa0lZRi9LUEFObDVXa1VRUFFqQVJ1L1hjSHdaQ2hTZ3owTHNmR3RyVG9haWNkTnlOM0NuNk1DcGdwWXJySGY1Mjc1RWh6UUFKT1N2VEZ6R2dLcDBKd3M4L1pZdmVSMWtZRXlnQnZ6YmlZQlRGMGU1dTkxQ3U1N2IybCtWQ3dUQ3ozY2M5RGlnR21hVWZ4T1RidWtGajNicmFzczdCQXFkMmg0K1h6Sm93ckI3bllQVUIxaUxiQm1rRVFrdmtrSVhwemZxaG8waGdPWkw2NGJsa0lVa1hLZDU5SzlFeGR0eWVUcklWb1kvcnBVRUMvN3lzclVRVEc2alhHZHUvZzNyT0N4VDhtVmh3c2ZXZWRuTUxqd0VweTNWeExzeE13WHE1dTBsNE45cU1OZGpaeGQrUElxRXliQmVwZ281eFAwdHRHZHZUZ1hzVVlZWENxNnpzQUcreGdPK2hna0l6ZmxIYkNhQ1kyMVZpU0RtdUtYaVNZVVBHeElzd3kwUGdCcDZBR0w5MlpmbWR6QkN0a1VrVDNqbkZWOUxYc2k1ZHpCS3BhWFJ0TVhSemlmY3FMMDNXUVNDL0Q2U09OYjhPNDY1V0VLWEZ5NXNKNFV2c3RsT2dYYlpmRVZNQ29uOUVINlVYR3Z6SHVTQWsrekVjMzVRV0htYksiLCJtYWMiOiI1ZDUyMjdiYTk3MDVhNGRiNWE1NDUwZDczMDFhZTQ3MDQzOGE4ODcwM2QzMGEzYzExNjAzMmY5MTU3YjMwNzQ5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-719871538\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1268395785 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mObpBrzEV0jwdZINvBGjqCROGGG4Xwal5TlnDHC6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268395785\", {\"maxDepth\":0})</script>\n"}}