{"__meta": {"id": "X07dcd678880dd0415f4feab2ecc5c0fa", "datetime": "2025-07-31 05:10:58", "utime": **********.356006, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753938657.422177, "end": **********.356036, "duration": 0.9338588714599609, "duration_str": "934ms", "measures": [{"label": "Booting", "start": 1753938657.422177, "relative_start": 0, "end": **********.268035, "relative_end": **********.268035, "duration": 0.8458578586578369, "duration_str": "846ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.26807, "relative_start": 0.****************, "end": **********.356039, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "87.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QiE3M2TrWMAlInEC6UdmLUD0s4aEZOtssl10J5AI", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-629331487 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-629331487\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1622955636 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1622955636\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-390763188 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-390763188\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-482569355 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-482569355\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2131486747 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2131486747\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2034797004 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:10:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjM0MG5XUnoxUXVTUzl0amk4M3pzRkE9PSIsInZhbHVlIjoiN1RSaUwvSUhvdEF6MjJIZThMbS9iVHlzU0d6V2wxenBjUGQvRC8zRWN4RVV6RFpQVlVzdldwTERrbXR3UGQ4ZXlQMDVYbTU1QU8vMVRPMmE5cGdnOFpsazdzR2xvNUVnL1psdXZLU1N5Y3B0TFU1cGJ4dUZDTmxodHhDRFgxUzZuWmowRDV5QldzTGk0RzlwQXJwOHFYajhFV0FYcElyU2NMSkJ6NmJDWG13MmZLY0RTa1NFQU5IaXBxVkFIazFSdWhBaDlsNmgrUk40RzNKU3FnRk5PeGNISjFBaDh1UkFlbDJaWTVWSDZ2dWNUWU1qb1V3VGZKc2poYzI4RmhjMXRsS1JsUmR0bmNrNU9sbUhSOWpRTzNlWGhnaHQ0bWFGRXRXWEVwclRkcmt2T0dGRnRaQmNoK2ZlWHZ4blM0UXdjdEJ3MG04NXdPRDlQVjhyK2ZvdkUwVlFLWEVlaWkxQXcwODlkb3pPeVkxNThZTkZhelB5cm1saWZ6Vk1uckhQenhPaEowZDc3RFM2SCs5NiswbnBOZWo4M2VEL29ZbFNGLytsbFlDN3Ftbm5oSnJtWkZWdm5yK1JNZHo4bnZUc09YRURnVGxGOEVXQWtWQmdLSUs1WDdJK0JvUXZwblFTeUZGQmpUU0RJMTZTM1kvb0ZIWWZJM09COGRCeHU0QzAiLCJtYWMiOiI1ZDIzMTBiODJiYTVlMzM5YjQ3OTI0Mjc0ZWJmODQ0ZGQ2YWNhMDQ2NjZmMjRmMjIwMjRhNTljOTkwNTRlN2NiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:10:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InFrdnJaZEYzYkx2WkFDdXR0VkF3a3c9PSIsInZhbHVlIjoib3N0T1JqejNicGRkckxDYjdmbTlJMVhFdnl4OEFGM1M4Y0tsNXljNWdUNE1ENUJWeEFDd3pKbzE1UVo1VlBJb3Z1a24rOHFOMUtvZFArRW0xL294VDdrY01lTFFvaUx0OHMzQWhCMDlwMzVZeUZxSjVMMHpEakhwWGRnTFNMd0RrWDJFM3htQllHUGMzTGV2Rkcxd1NpNmYzZTNsMWpLTzJNOHVPSmxCWEZpK0d4QjVzNnl1RUMyOTBQQmRFVFpFNlVMVmdYM2NTVFp3V0ozYkZiQlZMZXNMME1vR0dGdUFaelFWdnRYQTNjcFh5YjUwRWxMSzlzM0FqUDgxbUpZYXgyL1FObVNWRXRDcmh5T0pucHFlU3FVVDcyUFZCZzFRZVRLZlRZSWFBVXZ3UmNGTEY5UExkaDQyQlB2N1JTNkVNamJMZWdpQWVoZWZEZyt6aEZyTkVib2h1RFNIVGZjSVkraEF4SEdqSEdMOHZYb0FNWTlrUDZNdFhzYW5zVUlpVWdkeldUS0hTeG9hSStQbVh2YmZPYmxkd0wwYlVrRFhTSSt5cVhXMGV2NjhES0orUEdiT2RIR211K2RydzJLNm03QkF0NDR5UmFuU3hwOVE0YU8vbjI3RG02eENRUUtHdVUvcGdmcGduRFpjR1VjZlRYTmxnOWx1QmFCb2E5NGgiLCJtYWMiOiIzODE3MWE1NzY2NTFmYWM0MzhlMDk3Yjk2NWM0ZTFmYWRkZGVlZTE1ZTU3MDViMGVmNmY5MWU3NDdhMTVhODBhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:10:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjM0MG5XUnoxUXVTUzl0amk4M3pzRkE9PSIsInZhbHVlIjoiN1RSaUwvSUhvdEF6MjJIZThMbS9iVHlzU0d6V2wxenBjUGQvRC8zRWN4RVV6RFpQVlVzdldwTERrbXR3UGQ4ZXlQMDVYbTU1QU8vMVRPMmE5cGdnOFpsazdzR2xvNUVnL1psdXZLU1N5Y3B0TFU1cGJ4dUZDTmxodHhDRFgxUzZuWmowRDV5QldzTGk0RzlwQXJwOHFYajhFV0FYcElyU2NMSkJ6NmJDWG13MmZLY0RTa1NFQU5IaXBxVkFIazFSdWhBaDlsNmgrUk40RzNKU3FnRk5PeGNISjFBaDh1UkFlbDJaWTVWSDZ2dWNUWU1qb1V3VGZKc2poYzI4RmhjMXRsS1JsUmR0bmNrNU9sbUhSOWpRTzNlWGhnaHQ0bWFGRXRXWEVwclRkcmt2T0dGRnRaQmNoK2ZlWHZ4blM0UXdjdEJ3MG04NXdPRDlQVjhyK2ZvdkUwVlFLWEVlaWkxQXcwODlkb3pPeVkxNThZTkZhelB5cm1saWZ6Vk1uckhQenhPaEowZDc3RFM2SCs5NiswbnBOZWo4M2VEL29ZbFNGLytsbFlDN3Ftbm5oSnJtWkZWdm5yK1JNZHo4bnZUc09YRURnVGxGOEVXQWtWQmdLSUs1WDdJK0JvUXZwblFTeUZGQmpUU0RJMTZTM1kvb0ZIWWZJM09COGRCeHU0QzAiLCJtYWMiOiI1ZDIzMTBiODJiYTVlMzM5YjQ3OTI0Mjc0ZWJmODQ0ZGQ2YWNhMDQ2NjZmMjRmMjIwMjRhNTljOTkwNTRlN2NiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:10:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InFrdnJaZEYzYkx2WkFDdXR0VkF3a3c9PSIsInZhbHVlIjoib3N0T1JqejNicGRkckxDYjdmbTlJMVhFdnl4OEFGM1M4Y0tsNXljNWdUNE1ENUJWeEFDd3pKbzE1UVo1VlBJb3Z1a24rOHFOMUtvZFArRW0xL294VDdrY01lTFFvaUx0OHMzQWhCMDlwMzVZeUZxSjVMMHpEakhwWGRnTFNMd0RrWDJFM3htQllHUGMzTGV2Rkcxd1NpNmYzZTNsMWpLTzJNOHVPSmxCWEZpK0d4QjVzNnl1RUMyOTBQQmRFVFpFNlVMVmdYM2NTVFp3V0ozYkZiQlZMZXNMME1vR0dGdUFaelFWdnRYQTNjcFh5YjUwRWxMSzlzM0FqUDgxbUpZYXgyL1FObVNWRXRDcmh5T0pucHFlU3FVVDcyUFZCZzFRZVRLZlRZSWFBVXZ3UmNGTEY5UExkaDQyQlB2N1JTNkVNamJMZWdpQWVoZWZEZyt6aEZyTkVib2h1RFNIVGZjSVkraEF4SEdqSEdMOHZYb0FNWTlrUDZNdFhzYW5zVUlpVWdkeldUS0hTeG9hSStQbVh2YmZPYmxkd0wwYlVrRFhTSSt5cVhXMGV2NjhES0orUEdiT2RIR211K2RydzJLNm03QkF0NDR5UmFuU3hwOVE0YU8vbjI3RG02eENRUUtHdVUvcGdmcGduRFpjR1VjZlRYTmxnOWx1QmFCb2E5NGgiLCJtYWMiOiIzODE3MWE1NzY2NTFmYWM0MzhlMDk3Yjk2NWM0ZTFmYWRkZGVlZTE1ZTU3MDViMGVmNmY5MWU3NDdhMTVhODBhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:10:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2034797004\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1234420951 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QiE3M2TrWMAlInEC6UdmLUD0s4aEZOtssl10J5AI</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1234420951\", {\"maxDepth\":0})</script>\n"}}