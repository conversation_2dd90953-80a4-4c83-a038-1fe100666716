{"__meta": {"id": "X329c4e67506e5ad1948cf98add909066", "datetime": "2025-07-31 05:55:12", "utime": **********.624915, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753941311.504241, "end": **********.624946, "duration": 1.1207051277160645, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": 1753941311.504241, "relative_start": 0, "end": **********.530084, "relative_end": **********.530084, "duration": 1.0258429050445557, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.53011, "relative_start": 1.****************, "end": **********.624949, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "94.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6JbzEU07GZDquF2eoJWOTUKynD5JysKXCU76RpB", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1146773005 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1146773005\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-146845606 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-146845606\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1592538327 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1592538327\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1993036867 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1993036867\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1156358766 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1156358766\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-71208966 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:55:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ink5aWlaUnY2d1YwUyt3KzBMVVRHRlE9PSIsInZhbHVlIjoiT1lHVmp6Zk9tVGtzZzlnNWgyRFlRSVJOZkM0RTJrc2Rsd1BRVUJ4S3N5ZTVIZUtBZzFMd0Z1ajFyUVRDeTNBN3l5VUNURVlvR01EZGxvL045elZsRmx0VG9CSENNR1N0WTVCTXlCWERUWTBaKzEreTVBTlh2KzdqRm1LcWdtc1FhWlBUaXBtdzgrWEJGa1RlanhWNzhwZDFtWlAzcnovUG1aZExzcitnZG9Cd1BKWXBRS2hZejdpTXk5cjdiWDM1azlGbE1qbG13UnRyWFdvZ3R0bTZvUFBDWEZPcFhWZDd6Njd5U1FOdzhSMEl5RVJpNnRKdHVhVmVTaTk0b3p2bGlkdVYrUGRnVkwrWVdKMkNvbXZ5NkJXWjhteUJZRFlOT0dxTEV1VXdPYytLL0YzTUt5eTc3N1BsK1pxQmVmUHlwRXRIQlVGSkVUSENGWHJPWTJiRC9BN2hNSkdmYkFWcFhTUHZtQS9PeWtkQ1JsaWtGVjAwcXBXTHJUTGRTNk5VeDFDRDM1cXF2Q05aS1lxVU8vOUVXb2RiNnNOdER2RUtRMGNDK1FMOUlMMkZvNXIrQlREVVNNU1Y5bGNNamsyYzMxMlpydm1FcnN6Vk1ZdnU2cUVhZXEyL0cvUEs5YU1IZGhha1Z1Q3NqamxLbkxSTnRUTVZVZ1N4TUFlRVQzQ2oiLCJtYWMiOiI1NDMyYzY0MmY2ZTJjMzZjN2YwNzU4YzVmOGE4NzYzNGE4NTQ4MjJiM2FlMzMyMTFlZmJmYzk5OWQ2ZTcxYTQwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:55:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im9NVkpHVG0zMDdIT1FZVVVJRmMwZ3c9PSIsInZhbHVlIjoiVUtNbm95UjZXSnRsejV1dkhkY09rNkpFcThKSWNlVnVHNk1oaS8yTUwzN3p3azRGRGdjRkdlWWhGNlYvUEc4Ty9WU2MxcmNxNEQrSkxkcU0zTHZYR3VFVUJCUEZwR3ZWT0VWaHp6ZWtVVUxWMmQrNnc5UWx0eXlweHJtR3F5Z3ZwV3BTKzhzQkNmMHJDaWN4RDU3V2NSOFNLTmV0RllHdHJmK2RpN2RNc2taMHZCSTNiZ2o0cWdveEpPZmtHSUpodUhyRm4rc0dDMG44MGE4SmlEa3F6UFpDTUN5U2pRaE0vKzZxWUpSQVNjeU0xSWVvdmdDdHZlaXV6ZW1mSUVVU1MvbG13TDZDRE11TTlrKzRXRVBxNWtrbHR3dnJWRFlVK05HeUV6RGI1WHIyUTBzV3hFZWVxeWl6U1k2dEZ1aWhGMTE0K0V6MUExWkZmbWVHRkRWSTZpYjFlTmhJNWRDZUUyWERxKzFUZ2k3N3hVeS9iMEVrandLV3FkSHovb3pyTHVVSDN6ajl6aE5XVGFoM1RRR0Z2NTRBaGZQZzdWNm5qZndPL0RLalZrbWx0RjdGSjZHbC9aRkZoWVdid1pBT1JJQ1AvM3VpbGg3MktBTXRUWTFTa3lIall2dDJIM25QQzVjQytwRit3VmhWdlBTbmRDWFEycW4zcWFZWTA5K2siLCJtYWMiOiJmYmQ2MWEyZWY4ZjZiZjZhNDZhOGNmOGI0YjU1NmE4YjQ0MDI1Y2QwM2RiMmRkMjUzMGI2OTc1Njk2OGMzYjEwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:55:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ink5aWlaUnY2d1YwUyt3KzBMVVRHRlE9PSIsInZhbHVlIjoiT1lHVmp6Zk9tVGtzZzlnNWgyRFlRSVJOZkM0RTJrc2Rsd1BRVUJ4S3N5ZTVIZUtBZzFMd0Z1ajFyUVRDeTNBN3l5VUNURVlvR01EZGxvL045elZsRmx0VG9CSENNR1N0WTVCTXlCWERUWTBaKzEreTVBTlh2KzdqRm1LcWdtc1FhWlBUaXBtdzgrWEJGa1RlanhWNzhwZDFtWlAzcnovUG1aZExzcitnZG9Cd1BKWXBRS2hZejdpTXk5cjdiWDM1azlGbE1qbG13UnRyWFdvZ3R0bTZvUFBDWEZPcFhWZDd6Njd5U1FOdzhSMEl5RVJpNnRKdHVhVmVTaTk0b3p2bGlkdVYrUGRnVkwrWVdKMkNvbXZ5NkJXWjhteUJZRFlOT0dxTEV1VXdPYytLL0YzTUt5eTc3N1BsK1pxQmVmUHlwRXRIQlVGSkVUSENGWHJPWTJiRC9BN2hNSkdmYkFWcFhTUHZtQS9PeWtkQ1JsaWtGVjAwcXBXTHJUTGRTNk5VeDFDRDM1cXF2Q05aS1lxVU8vOUVXb2RiNnNOdER2RUtRMGNDK1FMOUlMMkZvNXIrQlREVVNNU1Y5bGNNamsyYzMxMlpydm1FcnN6Vk1ZdnU2cUVhZXEyL0cvUEs5YU1IZGhha1Z1Q3NqamxLbkxSTnRUTVZVZ1N4TUFlRVQzQ2oiLCJtYWMiOiI1NDMyYzY0MmY2ZTJjMzZjN2YwNzU4YzVmOGE4NzYzNGE4NTQ4MjJiM2FlMzMyMTFlZmJmYzk5OWQ2ZTcxYTQwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:55:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im9NVkpHVG0zMDdIT1FZVVVJRmMwZ3c9PSIsInZhbHVlIjoiVUtNbm95UjZXSnRsejV1dkhkY09rNkpFcThKSWNlVnVHNk1oaS8yTUwzN3p3azRGRGdjRkdlWWhGNlYvUEc4Ty9WU2MxcmNxNEQrSkxkcU0zTHZYR3VFVUJCUEZwR3ZWT0VWaHp6ZWtVVUxWMmQrNnc5UWx0eXlweHJtR3F5Z3ZwV3BTKzhzQkNmMHJDaWN4RDU3V2NSOFNLTmV0RllHdHJmK2RpN2RNc2taMHZCSTNiZ2o0cWdveEpPZmtHSUpodUhyRm4rc0dDMG44MGE4SmlEa3F6UFpDTUN5U2pRaE0vKzZxWUpSQVNjeU0xSWVvdmdDdHZlaXV6ZW1mSUVVU1MvbG13TDZDRE11TTlrKzRXRVBxNWtrbHR3dnJWRFlVK05HeUV6RGI1WHIyUTBzV3hFZWVxeWl6U1k2dEZ1aWhGMTE0K0V6MUExWkZmbWVHRkRWSTZpYjFlTmhJNWRDZUUyWERxKzFUZ2k3N3hVeS9iMEVrandLV3FkSHovb3pyTHVVSDN6ajl6aE5XVGFoM1RRR0Z2NTRBaGZQZzdWNm5qZndPL0RLalZrbWx0RjdGSjZHbC9aRkZoWVdid1pBT1JJQ1AvM3VpbGg3MktBTXRUWTFTa3lIall2dDJIM25QQzVjQytwRit3VmhWdlBTbmRDWFEycW4zcWFZWTA5K2siLCJtYWMiOiJmYmQ2MWEyZWY4ZjZiZjZhNDZhOGNmOGI0YjU1NmE4YjQ0MDI1Y2QwM2RiMmRkMjUzMGI2OTc1Njk2OGMzYjEwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:55:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-71208966\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-877627754 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6JbzEU07GZDquF2eoJWOTUKynD5JysKXCU76RpB</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-877627754\", {\"maxDepth\":0})</script>\n"}}