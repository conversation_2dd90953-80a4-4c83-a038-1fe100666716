{"__meta": {"id": "X3f0cda22e45f707388886d1278d10a06", "datetime": "2025-07-31 06:28:48", "utime": **********.822631, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:28:48] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.816317, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943326.430809, "end": **********.822671, "duration": 2.391861915588379, "duration_str": "2.39s", "measures": [{"label": "Booting", "start": 1753943326.430809, "relative_start": 0, "end": **********.532463, "relative_end": **********.532463, "duration": 2.101654052734375, "duration_str": "2.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.532518, "relative_start": 2.1017088890075684, "end": **********.822675, "relative_end": 4.0531158447265625e-06, "duration": 0.2901570796966553, "duration_str": "290ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45918336, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02756, "accumulated_duration_str": "27.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.695049, "duration": 0.0238, "duration_str": "23.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 86.357}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.758831, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 86.357, "width_percent": 5.116}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.773688, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 91.473, "width_percent": 3.81}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.78277, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 95.283, "width_percent": 4.717}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-835635628 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-835635628\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-190755102 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-190755102\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-404011340 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-404011340\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2085837995 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Inc4VWxqbERFRzlmalV5RzFvNy9oN2c9PSIsInZhbHVlIjoiL3E1TThQMGY1RkZrMGNINnQ0cndZTWdOWTR3OTFQK0lkNTl4dmU4Sk5wR2Y1VnJiMTVYSHkrSE1HVXpqRzJ3L0ROdTk2R0NIakx0cXZMMEdMWURKaStubGVlQW9tZW42YVlrVGlRMUV4TjhaUHB3am8wVTBmQW5IS3pzU2orMFZITEQ2c1VLbUpXUGFsb1N6bkdEa2h4R1VkZXRaVllrRTQyamNvY3d1Nndid0s5cDRDY3NDUGt4cmdobWkyTGpFQ0Z1WEVMWThhOUtvT2dCby9QYlY4K0FUVDE5eFdScStoRGhXZW9rV1VTTEgvcjRGVW00V0NpVWJDenhibXkrT0xzRGdZckdVL3pxTjY3T2NjbFV1dWplQ0wzTnRPNTE4M2o1NzdmVXpoRCtyVnljZVpaVit2SStaTDAyWWluNFJRTjRwWHQrRnNPaGRTYWJGZTVsbXJtT052aHg2NFJWWlVKMWo2U3RvNjAycjB2ZFVlN2hpSk9HTnVTb3U3QURSL0VGUmNYbGwyRGJoSkpyM2hmSDJTODFqTHVIVVVuaC9ZVGlhaXpIZ1N5UDczNDV1aGVSNHRHdHVXb1JGOFVtajY3SWVxNGl5QXFhZmRPaGJKOWZKVUUvOXZTOXJHbmZzUlh3alZFOUdZekxNUnBlL0g5L3J0alByTnhhcVJmMjYiLCJtYWMiOiIzZWJhZGI2OTc4ZmY0OGEwYWEwODdjNjg0MDg1NDg3MmJmZjg3MzE1ZjRkMmNkNjAyNmMyY2YyZTc1MjA1MTE1IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImtMT1Y5UnV5N2t1WU1hS2lYS1NWcXc9PSIsInZhbHVlIjoiNk12MXk4Rkd1VkorVjJuQ1NYZWFhT0dWQ051Q0Q5QmJEbmc3T2liQ2Rvc05KKzNvbUNQNDFPR3FtNWlMQUJ6aWxJNHNpWnlxbHB6ejkydWFLRTRtOXJnNGUvaGtxbFFpMDZNTUdrSHUzajExV2lTUGttM0p5emhqVzQ5Rk5JaVNQR2dGa3o3MFpwOHA0d0ZITWx6Rm8vRjNub3phVWVIQ1pUa2RGZytuVFdFL0kycnNocWtudG91K3Z6MVBSNW5zampaOGJpa1NUdXlXV2hnWXYvZVlPLzNuYkErc3M3Vm0vVVFUVnhKODVNeFN4b0hBYmpBNU5aRExFdnJiOEVtM2tzcWo0ODRXU05NYm13MzJmb3VZSVd6c09mV00rTXFzQS9VeE5DemhBbVBNYlJ0b2VCTGFKc3JwNlREQTRVT3JsekFrV0xtTWkzSGd1QVpvUW1UOEhxVjR5Y001ekZPMTNqYVpQRVVYbitrUEVETnZXbWExdENrRUlKc2FKS0g0cXJGMFVkUVZOSGxPYkNaSzQ5M2hpSEMwT0FodEZEUFRHRFFxdzVkRUJFWWxjeUpvb3pKU3FseWZLN2lsdE9qRzhNTTNkNUdvZnZNTTIwQXlOT1o4RTc5SlNNYVh5bm44R3hLWDRvRitIdkU0Q0R1V3JXQmlCeUhMY2FGUE1JRWMiLCJtYWMiOiI3NGU1ZTgwZmNlM2U3NmNlZjdiOTI1OWJjYWM0NWI0NjQ3MGQ1NGZkMjcyZDliYWQ3OWNiYmE1MGVlZWZhNzkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2085837995\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1479812188 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1479812188\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-87594042 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:28:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFCZkdHY2RBWWtYS2JTam5RK2RKTHc9PSIsInZhbHVlIjoieFZWVFlFNURKVE9yeGY4YUtLaGR2OWpSN3VhMitINDdsOFFIOGpyZXpTVHppeitpU2tyeFIrRDdJWGNvK0gxaktid1lGREFESlErbjJ3SUU5SklHc2plZXZ3cGtra0J5Q1R1TFpUM2c3b2pXRDV5YzhEelY4SWU5RjM5UHd5MDVQSDFURndGdGVkd3lhM2VhbXRZOTZNUStoZ1Z0UDJFYTdneHg4cEhIeXpOMGpsSmhkdVJ6RXFmd2pMTzNQanZKM1kzRlNzMU01Y2h5UUdSenlnY1FHQmVrRVpJWmc4ZTJLV3gyV0tyZm9Pa0VGV3FYUVFxM1VKN0R6bkY4U0Z6ckk0MTZVTWRubkZYSURKb2F4RWRqOWRUaTVtYitzYVhmQXZNMHRCWm5TNG9IR2s1N2VpeDkxM3JQVlVKM1c0Mk02T0lWbkxsbEFoN0Q4VkhxSHBRejhvakt3NEtpQkhCcWdudDA5cStOb1NReE5RbGI5bmMvcGdFdU80Ri9ZUGZKQXhXZ3FUeG1BL2gxOU5BT0dmbWVLdC9IVDExWWhmejNpbzMzcjcxUzJFdUo2YWVPclBZR0VwTk5zL1JSWEFkM1AyWUt6QUhiZ2N4RzF5WUorbnpLLzNvZzJrK0hDMzhjZ3BZMWlyMWJDaVdsczlFeitqaXorRFl3OGtNRnJtaDgiLCJtYWMiOiJhMzk2NWMyN2RiMTgxN2FjZDM1OTE0MDkwYmM5ZTRjMmQzMjcwZmQyZTg4YzFiNjM5N2Y2YjFhZmY3NDUyNTkwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:28:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InN5cEpkMndHZDkxMVgyaVlkWHByU2c9PSIsInZhbHVlIjoic3lsdjZSK2lLdDJlRU1qb0xoM2JwU3U3eG9IRElnekZESGh4UFVkcUY3U0w3TVBSazViMUEybnhyY2twNjR0ZWxBdDU0em80OHN6NC9FMzltdVVRQjQ4YjRNN2grTkFQY2JhU0lESy9Mem1yM2RzVElkMEtJVkJoV25QRVFuMzB6UGF6YlNoQ0FOR09lZTVVRGUxMjZnV0pIVXgwUFJjeGdJVmdDK3FvODFoTGVldTg1YnBybUNscjkreWRMOFRmSEhPRm1yVEZCaEd1VEhITjc3RFlvN2gxQnhZMHpWNUJQR2lzbzVBTEVxTkkxaUZyOThJOG9sU2d2dHNLS3lmVS84ZVYyQWVpcGJuMGNUdldXdU51UkdQRktKaGx3ZGhTUVUrazRlU29Ec09hNEc2UDRkKzBzKytDWWhtcXZEa3M2TU5pZnhzMzdOeU9nNzRmU1hLMDFHN1M1L29ETWZWZE4vdWdDaDlOQ3VqMnJVUG1udm9iWk9pQXFsWWthRXZndG9SdVlQaHQ4WlhhdlYrYnMzbHZ5b2U2ZDU2Y2cvOXZnb3dMUGN1ejJlY3BhOGZRSFYwY2dLN1UrZ2lQaFlOR0tHaFVPQmxWYXJqNkp3TFRzMTE3UnY2R2Q0cDN1eFpoSkNpK0cwOVBCQjdFUERqdnZFTkswNWZoQTNmQTRuZTQiLCJtYWMiOiI1MWRlN2Q1NTIxMDNkMTk4ZTNhMmQ4ZWZlNGE2MDU4Mzk1ZjVjYzU0M2IwZGFkODY3ZDVmZmFjZDdlZGYwNjI2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:28:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFCZkdHY2RBWWtYS2JTam5RK2RKTHc9PSIsInZhbHVlIjoieFZWVFlFNURKVE9yeGY4YUtLaGR2OWpSN3VhMitINDdsOFFIOGpyZXpTVHppeitpU2tyeFIrRDdJWGNvK0gxaktid1lGREFESlErbjJ3SUU5SklHc2plZXZ3cGtra0J5Q1R1TFpUM2c3b2pXRDV5YzhEelY4SWU5RjM5UHd5MDVQSDFURndGdGVkd3lhM2VhbXRZOTZNUStoZ1Z0UDJFYTdneHg4cEhIeXpOMGpsSmhkdVJ6RXFmd2pMTzNQanZKM1kzRlNzMU01Y2h5UUdSenlnY1FHQmVrRVpJWmc4ZTJLV3gyV0tyZm9Pa0VGV3FYUVFxM1VKN0R6bkY4U0Z6ckk0MTZVTWRubkZYSURKb2F4RWRqOWRUaTVtYitzYVhmQXZNMHRCWm5TNG9IR2s1N2VpeDkxM3JQVlVKM1c0Mk02T0lWbkxsbEFoN0Q4VkhxSHBRejhvakt3NEtpQkhCcWdudDA5cStOb1NReE5RbGI5bmMvcGdFdU80Ri9ZUGZKQXhXZ3FUeG1BL2gxOU5BT0dmbWVLdC9IVDExWWhmejNpbzMzcjcxUzJFdUo2YWVPclBZR0VwTk5zL1JSWEFkM1AyWUt6QUhiZ2N4RzF5WUorbnpLLzNvZzJrK0hDMzhjZ3BZMWlyMWJDaVdsczlFeitqaXorRFl3OGtNRnJtaDgiLCJtYWMiOiJhMzk2NWMyN2RiMTgxN2FjZDM1OTE0MDkwYmM5ZTRjMmQzMjcwZmQyZTg4YzFiNjM5N2Y2YjFhZmY3NDUyNTkwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:28:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InN5cEpkMndHZDkxMVgyaVlkWHByU2c9PSIsInZhbHVlIjoic3lsdjZSK2lLdDJlRU1qb0xoM2JwU3U3eG9IRElnekZESGh4UFVkcUY3U0w3TVBSazViMUEybnhyY2twNjR0ZWxBdDU0em80OHN6NC9FMzltdVVRQjQ4YjRNN2grTkFQY2JhU0lESy9Mem1yM2RzVElkMEtJVkJoV25QRVFuMzB6UGF6YlNoQ0FOR09lZTVVRGUxMjZnV0pIVXgwUFJjeGdJVmdDK3FvODFoTGVldTg1YnBybUNscjkreWRMOFRmSEhPRm1yVEZCaEd1VEhITjc3RFlvN2gxQnhZMHpWNUJQR2lzbzVBTEVxTkkxaUZyOThJOG9sU2d2dHNLS3lmVS84ZVYyQWVpcGJuMGNUdldXdU51UkdQRktKaGx3ZGhTUVUrazRlU29Ec09hNEc2UDRkKzBzKytDWWhtcXZEa3M2TU5pZnhzMzdOeU9nNzRmU1hLMDFHN1M1L29ETWZWZE4vdWdDaDlOQ3VqMnJVUG1udm9iWk9pQXFsWWthRXZndG9SdVlQaHQ4WlhhdlYrYnMzbHZ5b2U2ZDU2Y2cvOXZnb3dMUGN1ejJlY3BhOGZRSFYwY2dLN1UrZ2lQaFlOR0tHaFVPQmxWYXJqNkp3TFRzMTE3UnY2R2Q0cDN1eFpoSkNpK0cwOVBCQjdFUERqdnZFTkswNWZoQTNmQTRuZTQiLCJtYWMiOiI1MWRlN2Q1NTIxMDNkMTk4ZTNhMmQ4ZWZlNGE2MDU4Mzk1ZjVjYzU0M2IwZGFkODY3ZDVmZmFjZDdlZGYwNjI2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:28:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-87594042\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-215120928 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-215120928\", {\"maxDepth\":0})</script>\n"}}