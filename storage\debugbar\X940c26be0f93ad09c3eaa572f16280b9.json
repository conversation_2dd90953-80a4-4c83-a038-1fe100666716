{"__meta": {"id": "X940c26be0f93ad09c3eaa572f16280b9", "datetime": "2025-07-31 06:31:10", "utime": **********.376494, "method": "GET", "uri": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943468.017818, "end": **********.376554, "duration": 2.358736038208008, "duration_str": "2.36s", "measures": [{"label": "Booting", "start": 1753943468.017818, "relative_start": 0, "end": **********.194921, "relative_end": **********.194921, "duration": 2.177103042602539, "duration_str": "2.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.194959, "relative_start": 2.***************, "end": **********.376561, "relative_end": 6.9141387939453125e-06, "duration": 0.*****************, "duration_str": "182ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dBMe0I370ZttYE69FvERfAckS2vCKXM6iIyjYx48", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "status_code": "<pre class=sf-dump id=sf-dump-1947674167 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1947674167\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-110921359 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-110921359\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1115875598 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1115875598\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-120953766 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-120953766\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-249980243 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-249980243\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-21781219 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:31:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVzVXptaThqWFF4S2E5b1BlNmhFdlE9PSIsInZhbHVlIjoiWWRsWk5yRHJJQnlsajVHakc5c2NrdVVjQXRNd0FSSHhXZlp6MXZDQndwWnA4blJxTWNzUlZhcE1aaXRvUytiaFpBaTY4bE9yUzQ0UCtIcFNqeFRmeFZGcFFCbmlLSUI2eEFvUWtaUk5VSksrajVVZnIvME1JY1hEWUJ0b0lsQ3RlM3Y4Sm1KaGFHTk1XOGViRy9Fa0NCY01Lay9sSWVMNlF4MVd2ZHIzd1BKWmphcllocHpZMjVVYXNrY1R3SjBHb01ZYVhMNnV6T0hTcG5sV2Q1Y2k3QkRWc05xdlZVMEpicE80bStqWTd0V243NURzTWdwOVFobUNpeXVndHhYN251UGRVbGRtUVJpcHczelV4SVhoSVpLamFZOUJ5Y1A5NWJxeVhCTlRQbi8vMmc4NUhZbjlKT0t3SGc4QW9ORi92S01CRmYySG5UQXpOSnhVZlN6K3BMdGRsZmpJYkJ4aGdNeXVreDNtd3BMMVAyWFh2TUMxSmsrTWsvQ3B2VWozVE8zZzRJL2o3c0FGdTd2b0xBR0c0UW1XTTQ3RW9LV2tDalNsWXJXMFd5T0FETkhGNWU0eDFEUnFDdWxzaHZqMEJaaVpWbE5WcWJUcUlEdzNrVjZicHQwNjBqcWErRjFSTzhpSjBzSHRnRUxuNGVCK3J0MGNtK1ZZM2hTVDRNZW8iLCJtYWMiOiIwZjg2OGQwOGMyNmQ3N2FiYWNjODM4MzdkN2UwZjYyNTQ5MWQ4MDM0OWI5MWRlYjRhNjg0MTYyNTY1NDZmOWRhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImQ5WmUra3FLR0hCcXdCZ1NFYTR2WkE9PSIsInZhbHVlIjoiUlFzWDdVM3k1akVlQlFxRDJ2ZEhFSGYzRXRXcGtJa2RvQmlmcUd4M2FzeUJ5YjBTbGJqbzhiNjdpL3JxcnRDbzY3NC91VG8yZ25va3ZEbjlOTVZMbXoraU1CakpPM0NXWUltVDFHNElHMko1bSs1bjNld1pidU5vcW9pdSsrQU9qQnkwaXlxS21nUVhHbk5NSnhJNHlWcHM5Z0V2aVZLY0hyNzQ0UTA5SVFwYUNKL0xjbEh3dk9pdWUvMVgyeTh0UmM1ZDBhV2RFMDhWU0dKbnRQd1B3QWduQWpmUW40Q1p1ZGRXNTR0UnRTeERvSWNMUlpIWTBlcldVaFc4Rythc21EcDUvZFRLSUV1bng0Tk12SU5PbnkvVXg0M21PS0ZBTkhQQTlTZzNLT3MxaTBaWnA4ZUVrUE1mVmhpSkN2TU4reHEvZjN0bjJ0clk1SCs3Z3RWbkpRV1FoSlA5V0VLR2FEK1d3S2ZEZHZCYmlSTVltLzAzTWtxRlp5bStxVkZibmJzSE5NT3R0M2FuMlp3RVVCem1kUnpyTzhhZFNWU09jYTBCMFdqdFdxOTBML29BdHJLa0M2ZkhIWS9UZHN1SGNGRGJrWGRrYWlyY0Y1bkZkeElaVFdhR2dMV24xS2VIWkZNdmdKZlNTMkhTYk5uRWZUTElXT2JyYU81Y05ubFEiLCJtYWMiOiJlMzMyZGM3Y2ViNmY1ZDJlZjM3MTIyYTljYmZkMGY2ZDAyZjJhNjhlMTgxZTI5ZTVkNmIyNjgyOWFkM2U2MTdkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVzVXptaThqWFF4S2E5b1BlNmhFdlE9PSIsInZhbHVlIjoiWWRsWk5yRHJJQnlsajVHakc5c2NrdVVjQXRNd0FSSHhXZlp6MXZDQndwWnA4blJxTWNzUlZhcE1aaXRvUytiaFpBaTY4bE9yUzQ0UCtIcFNqeFRmeFZGcFFCbmlLSUI2eEFvUWtaUk5VSksrajVVZnIvME1JY1hEWUJ0b0lsQ3RlM3Y4Sm1KaGFHTk1XOGViRy9Fa0NCY01Lay9sSWVMNlF4MVd2ZHIzd1BKWmphcllocHpZMjVVYXNrY1R3SjBHb01ZYVhMNnV6T0hTcG5sV2Q1Y2k3QkRWc05xdlZVMEpicE80bStqWTd0V243NURzTWdwOVFobUNpeXVndHhYN251UGRVbGRtUVJpcHczelV4SVhoSVpLamFZOUJ5Y1A5NWJxeVhCTlRQbi8vMmc4NUhZbjlKT0t3SGc4QW9ORi92S01CRmYySG5UQXpOSnhVZlN6K3BMdGRsZmpJYkJ4aGdNeXVreDNtd3BMMVAyWFh2TUMxSmsrTWsvQ3B2VWozVE8zZzRJL2o3c0FGdTd2b0xBR0c0UW1XTTQ3RW9LV2tDalNsWXJXMFd5T0FETkhGNWU0eDFEUnFDdWxzaHZqMEJaaVpWbE5WcWJUcUlEdzNrVjZicHQwNjBqcWErRjFSTzhpSjBzSHRnRUxuNGVCK3J0MGNtK1ZZM2hTVDRNZW8iLCJtYWMiOiIwZjg2OGQwOGMyNmQ3N2FiYWNjODM4MzdkN2UwZjYyNTQ5MWQ4MDM0OWI5MWRlYjRhNjg0MTYyNTY1NDZmOWRhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImQ5WmUra3FLR0hCcXdCZ1NFYTR2WkE9PSIsInZhbHVlIjoiUlFzWDdVM3k1akVlQlFxRDJ2ZEhFSGYzRXRXcGtJa2RvQmlmcUd4M2FzeUJ5YjBTbGJqbzhiNjdpL3JxcnRDbzY3NC91VG8yZ25va3ZEbjlOTVZMbXoraU1CakpPM0NXWUltVDFHNElHMko1bSs1bjNld1pidU5vcW9pdSsrQU9qQnkwaXlxS21nUVhHbk5NSnhJNHlWcHM5Z0V2aVZLY0hyNzQ0UTA5SVFwYUNKL0xjbEh3dk9pdWUvMVgyeTh0UmM1ZDBhV2RFMDhWU0dKbnRQd1B3QWduQWpmUW40Q1p1ZGRXNTR0UnRTeERvSWNMUlpIWTBlcldVaFc4Rythc21EcDUvZFRLSUV1bng0Tk12SU5PbnkvVXg0M21PS0ZBTkhQQTlTZzNLT3MxaTBaWnA4ZUVrUE1mVmhpSkN2TU4reHEvZjN0bjJ0clk1SCs3Z3RWbkpRV1FoSlA5V0VLR2FEK1d3S2ZEZHZCYmlSTVltLzAzTWtxRlp5bStxVkZibmJzSE5NT3R0M2FuMlp3RVVCem1kUnpyTzhhZFNWU09jYTBCMFdqdFdxOTBML29BdHJLa0M2ZkhIWS9UZHN1SGNGRGJrWGRrYWlyY0Y1bkZkeElaVFdhR2dMV24xS2VIWkZNdmdKZlNTMkhTYk5uRWZUTElXT2JyYU81Y05ubFEiLCJtYWMiOiJlMzMyZGM3Y2ViNmY1ZDJlZjM3MTIyYTljYmZkMGY2ZDAyZjJhNjhlMTgxZTI5ZTVkNmIyNjgyOWFkM2U2MTdkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21781219\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1171387265 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dBMe0I370ZttYE69FvERfAckS2vCKXM6iIyjYx48</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1171387265\", {\"maxDepth\":0})</script>\n"}}