{"__meta": {"id": "X5061bd7a6fe65126d80c7a3411f20b64", "datetime": "2025-07-31 05:36:53", "utime": **********.600921, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:36:53] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.568054, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753940211.140623, "end": **********.601006, "duration": 2.4603829383850098, "duration_str": "2.46s", "measures": [{"label": "Booting", "start": 1753940211.140623, "relative_start": 0, "end": **********.153676, "relative_end": **********.153676, "duration": 2.0130529403686523, "duration_str": "2.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.153719, "relative_start": 2.0130958557128906, "end": **********.601012, "relative_end": 5.9604644775390625e-06, "duration": 0.4472930431365967, "duration_str": "447ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50614624, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.516823, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.08210999999999999, "accumulated_duration_str": "82.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.312766, "duration": 0.0317, "duration_str": "31.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 38.607}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.389088, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 38.607, "width_percent": 2.825}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4089, "duration": 0.04521, "duration_str": "45.21ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 41.432, "width_percent": 55.06}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.471171, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 96.493, "width_percent": 3.507}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1650865326 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1650865326\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-523021908 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-523021908\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-801590251 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-801590251\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1491940167 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkoySDdVaENHUXpxRnNqRVpET0lncWc9PSIsInZhbHVlIjoiR1JKWVJkNlZDV2VLcG90SXlTbW5NRUt0MW9RM2NyQjJkcFg3TUtPUzBvL3ZxOUwwSkhHbHlXeVF1aE02SndSZk1SaWl0QS9LaStMbURNQVpadFpJa29MTWdOM3haanljWU5DcWtDc01ZWUVyMStpNU55U2dQTnEyN1lyd0l2eUpJT1NodUljNmVTUFZEOUtsbENBczhucGhtdEg1enNMNUdFeFIwc3JDSkZNUUlwSVRHdmNDSTJFbDA5R2xTNGV5UEprbjJOTUttK3c4amQyVDBLL3pmdythUldic052Rzhka3dVdHhqZHVGeEVFQ2ZlNm9PTFZ5eWZaT01DMzA1VlduVzZONFh3eXRycW1WMFhrL2E3ZFlaUDg2REExODBlVTB4ZmJpSlAvOGE0c0hxR2pQZzBpazVya015Q3V6M1JRWmZMczN2RHNTYzFBNStXVlYwOEw3Nm5nQVlwR042RUppdStaN21uL043ZlRJTEI3a2x1aWh4MGpFaUFxUmI5dzdoWThSeTZRenhBdzR0dGtOdFdHd3VKQkRWZWxLUGNjV1c3bzkzZlZCWFVSNy9yRDRqcVF6QzJiTkVDbC9EMUZRRVBhK0VkQ3NraG82Rm95Tm9ad2RBWTBnK1FVK2o4UjlIektnV2FjbTRDenpEMlg2UXIzeUlnYW44YmRiTWMiLCJtYWMiOiI0M2UyYTcxOTExNTYyNzRiOWI3YzU2ZDMzYzZmOTcyMjdlMTc2MzQ5MTg2OWIyYjJlMmIxZWU3ZDVhM2M0MThiIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjVVaWNiRVcyNHJCSjlvM3c5dWVSTXc9PSIsInZhbHVlIjoiZk9HOHpQTG5SVHlQNDh4UmpuSzBIUEU5OU1JbXlJQklveGdrMjZXdHB0ZGpFOHBDb2Q4cEFzb3hlRHFIT3BmMmlWbm5oTFJBWHNLbjZGcjd0S0dLamUvTXRMZVVLZldMVllTWEdMRXd1M20rVVl5NEcwbjNUaGpzMEJkSEE1R09KZ1BNSEx2T2c3bzN6RHpQUEJNRVRMbER5OS9iR0srVkwrb29VZHYvTnV3dkF2eXQ4OEVqVDVHU2lNczZCRWVUNzNScTNMNG1mb2tMWUM4N01BclEwQ0d3eEZFR1RGWURTdUN4a2Z3QWp2TjY0NmI2WnYrams0cVM2UGRScWE5NkFNY1dQenpkb3BKenFyNzRtODRZTThDeFYyd21rbmJscUM1NVp1dU8yQlc5RW81cmFCSmVIUnV4dHdzUVhVUlhzbVBMOVc2aVorNFNJNGc3ZEtMVVNvQWZsdHJ4RXZLWHRMRHBPMG41eXZ6UjdMMWlnaDdwamp3RDZEZ2pFUWpPdXFJajVVaTZBTFBrcHY0bWZJWnVITU5nS1VCS05ScnhWV3c4b1czN0lmenJPZndtcWZRdFJURjh5K3pGMU5PdmRKQzF2WmVMOStVZHZCalN0dFo1YXdLNmtMb05nazlPeSsvdUFaOEVHNnJvN2FkQ3prN25yRjBUbkY4QnExRVQiLCJtYWMiOiJhNmMyZmY0NmQxYTI4MDVhM2RmNTA1ZGU1Y2U1YzI0MzE4NWQ3ZGJlMDRiMGJmNjVmYjcyYzZiYjQ3MTY5MTNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1491940167\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1946335861 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lOWuOQi5K5zalKq6lxC9bmXS2G5Yh3URND5pGg9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1946335861\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1966341130 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:36:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVDbzEzd0Ixa1ZRWkR0Qk5ZQ3IvV3c9PSIsInZhbHVlIjoiRzZCUEZGOUk5aWZIY1o2SzFjOURaQllETTlvYnc4Qkgra0QyZjFVS3U4QXc2WUFNUjB2OXh2WVlBNTVwNkl3R3dkcU5YUDQyVTl1ZFByUWYvN2NlVUk1eVRjelpkQzROdjYyS3hOSStnNFVkeE5rMFBuMTlyNmJoT3JmZ25Na1pwS3ZHcHJ2bEF0WkVXemptNXFSQk5WNmpOeElMR1lrdXI4Ym54ak03NXRSNWhCZ3JkcVY0amVQckxybDFlUFlFZmZTVjBhUkN3R2YrcHhpOVVEclFUWUI1ZkpYeEZmejBIaXlsWG1NVHRvQ01RNFEvQnErRWdoRVNxaVRpUFVDeFBOcFBUWitOeUhaRDdrTHFXV1E2dFg2eXdrazQzRG85NXI1L1VpVEoxQnZhcDJ1N2ViaUN3U0duMjVEOVkwa2VNQUN5TE5LclZXcFhVeXdqamJENlBjQmN4MjRDejZ0U2JEcGpSeHRQV1QxOGdYOGdXUWp1TkpwWFlNRlYzVnd0TGJ1cnJYZ0lFNktsdDA4WE5aWHg1U3RtTWhYK3VCZ0FvbGdqQXZ1WllSZ1M4Wkl3akFLSFpoa2tlWUkzUDlDTjYwb08rS2ZTRVdaeWtvVGVRdkRwenVJNmpWVlFLUDRmSlU4OUtUWFJmVGVIZ1hwKzJtWE44TFB1b21RdjljZC8iLCJtYWMiOiI4NmM0OGUxNDNiYzgwY2IwODM4ZTIwMmU2ODczODg2NTNlMTRjM2M2NjFhNWQwNTk3OTM3YmYzZjBhY2UwYTZkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:36:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IllFU21RMXA5aXVQeTd0NWUwcnpZbVE9PSIsInZhbHVlIjoiOThKRENqRUtBQjZCeGJmYW82NndlbWFaR0lhb1ErelpHWS9rdzZ4QmlCSk1DSTUvS0JpOGphanR2ekdSakxvcnhiaGo0NW05VTExVXRNQnlEdENjSVhQTElCNU4rZFNLZkVZVEZRZkw3UWIxQ3pjYUJCMm1qNG5zcWdkRmV1UjZMSTA3bFB6YjllYVJ3SlluUXd0dUVNTDV1UlVZMy9LTS9wdDZIYzNxREhsTkVQdEZ6OHB4WDI5a21jL04rUlhuLzg4MFhONzVXOFlLRjNsV0tmRzNFNW12MjlCTVA2aCt4R3hLajliTm9iQTNvQ1J6aGczVzcvT1V6VStFbEEwTG9DSjRPd2VwVlMrSVpsS09uTTlmN3NoV1d3cnQwVlVSaVZPc3QrQWVXelRvM3JsWjNFM3hFQzRFa3gwTy9hY3ZxSDRjMG9RelduaFpvOCtpMEdvV2t5d0FlMFgxUWlZbkMvMmdWcklPbDYrL2J0T2dmUlgxMXVkeXRjMzF2VHpMdFZvL1BVTnFCZU5ZTTVjSjhqaWRpY2lXZ2p1VzFIbGNUUXVnRENJYVZERlU0V2JTTWVVSGUraEtSV0lIYkhxbDJhQ2VsM3IwQXFKRTUzN3I0SC9jNnoxMFZweWlMR0l6a0s0WlNMdDJWMXdqMVlkU3VqaHZMTGNLL3F2QnJoazIiLCJtYWMiOiI4MWVkZGNiOTNjMjRiMTdiNzgwNThjNDgzNTYyMWMxNGYxM2M0MmEzZDRkOWMyMGU3MWZmYWE4ZjE1N2Y0ODVjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:36:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVDbzEzd0Ixa1ZRWkR0Qk5ZQ3IvV3c9PSIsInZhbHVlIjoiRzZCUEZGOUk5aWZIY1o2SzFjOURaQllETTlvYnc4Qkgra0QyZjFVS3U4QXc2WUFNUjB2OXh2WVlBNTVwNkl3R3dkcU5YUDQyVTl1ZFByUWYvN2NlVUk1eVRjelpkQzROdjYyS3hOSStnNFVkeE5rMFBuMTlyNmJoT3JmZ25Na1pwS3ZHcHJ2bEF0WkVXemptNXFSQk5WNmpOeElMR1lrdXI4Ym54ak03NXRSNWhCZ3JkcVY0amVQckxybDFlUFlFZmZTVjBhUkN3R2YrcHhpOVVEclFUWUI1ZkpYeEZmejBIaXlsWG1NVHRvQ01RNFEvQnErRWdoRVNxaVRpUFVDeFBOcFBUWitOeUhaRDdrTHFXV1E2dFg2eXdrazQzRG85NXI1L1VpVEoxQnZhcDJ1N2ViaUN3U0duMjVEOVkwa2VNQUN5TE5LclZXcFhVeXdqamJENlBjQmN4MjRDejZ0U2JEcGpSeHRQV1QxOGdYOGdXUWp1TkpwWFlNRlYzVnd0TGJ1cnJYZ0lFNktsdDA4WE5aWHg1U3RtTWhYK3VCZ0FvbGdqQXZ1WllSZ1M4Wkl3akFLSFpoa2tlWUkzUDlDTjYwb08rS2ZTRVdaeWtvVGVRdkRwenVJNmpWVlFLUDRmSlU4OUtUWFJmVGVIZ1hwKzJtWE44TFB1b21RdjljZC8iLCJtYWMiOiI4NmM0OGUxNDNiYzgwY2IwODM4ZTIwMmU2ODczODg2NTNlMTRjM2M2NjFhNWQwNTk3OTM3YmYzZjBhY2UwYTZkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:36:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IllFU21RMXA5aXVQeTd0NWUwcnpZbVE9PSIsInZhbHVlIjoiOThKRENqRUtBQjZCeGJmYW82NndlbWFaR0lhb1ErelpHWS9rdzZ4QmlCSk1DSTUvS0JpOGphanR2ekdSakxvcnhiaGo0NW05VTExVXRNQnlEdENjSVhQTElCNU4rZFNLZkVZVEZRZkw3UWIxQ3pjYUJCMm1qNG5zcWdkRmV1UjZMSTA3bFB6YjllYVJ3SlluUXd0dUVNTDV1UlVZMy9LTS9wdDZIYzNxREhsTkVQdEZ6OHB4WDI5a21jL04rUlhuLzg4MFhONzVXOFlLRjNsV0tmRzNFNW12MjlCTVA2aCt4R3hLajliTm9iQTNvQ1J6aGczVzcvT1V6VStFbEEwTG9DSjRPd2VwVlMrSVpsS09uTTlmN3NoV1d3cnQwVlVSaVZPc3QrQWVXelRvM3JsWjNFM3hFQzRFa3gwTy9hY3ZxSDRjMG9RelduaFpvOCtpMEdvV2t5d0FlMFgxUWlZbkMvMmdWcklPbDYrL2J0T2dmUlgxMXVkeXRjMzF2VHpMdFZvL1BVTnFCZU5ZTTVjSjhqaWRpY2lXZ2p1VzFIbGNUUXVnRENJYVZERlU0V2JTTWVVSGUraEtSV0lIYkhxbDJhQ2VsM3IwQXFKRTUzN3I0SC9jNnoxMFZweWlMR0l6a0s0WlNMdDJWMXdqMVlkU3VqaHZMTGNLL3F2QnJoazIiLCJtYWMiOiI4MWVkZGNiOTNjMjRiMTdiNzgwNThjNDgzNTYyMWMxNGYxM2M0MmEzZDRkOWMyMGU3MWZmYWE4ZjE1N2Y0ODVjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:36:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1966341130\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1367218522 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1367218522\", {\"maxDepth\":0})</script>\n"}}