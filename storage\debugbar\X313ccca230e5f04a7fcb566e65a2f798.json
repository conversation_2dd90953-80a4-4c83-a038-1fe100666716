{"__meta": {"id": "X313ccca230e5f04a7fcb566e65a2f798", "datetime": "2025-07-31 05:10:56", "utime": **********.571945, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753938655.512908, "end": **********.571996, "duration": 1.0590879917144775, "duration_str": "1.06s", "measures": [{"label": "Booting", "start": 1753938655.512908, "relative_start": 0, "end": **********.487378, "relative_end": **********.487378, "duration": 0.9744699001312256, "duration_str": "974ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.487409, "relative_start": 0.****************, "end": **********.571999, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "84.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9jnw7W5QxdNjFEBK7Eh3dQJB5JQ3awZbTqtwVcCp", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-633951592 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-633951592\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-747730163 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-747730163\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-566813454 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-566813454\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2020022886 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020022886\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-629343755 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-629343755\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-480856211 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:10:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImwrNURTbVA2WVhGZzF4bzZrWlNBV1E9PSIsInZhbHVlIjoiQzFub08rNWx5cEdrMzlUb1NMZWhrdUhSbU9sWFpWNStTdlo1NUt6M3ZNWld4Z0Y5RFovbVJLMm1wVzMvcTQzMW5PNUliUUI0Yk5CMGZoS1NhNmRFNU5ITzFtZTZlYWV4ZEhyYklEMmRqNm5JREhCZWFHcHN4dFFYUkNlZklHTncwWUNXSUJhY093cXUwbTFHblpQVWZlS0pRWUs4TlVwU1R2MmU4Y25IYUN6Y0dYS2NSaDd1blU1Ty85WGpnb1JyOS9zY1RrOTZVN2t5RUZmZlYxUE9ScFRJVUJuMnhyMkRaR2l0aXFYVVJEUnpmMERMaHk1RHFyb3lLaTVVK2NkdVZJUVhTbVo0b3RKRk1lcm42SHNWcnhETENUekxNMkV5cEl6SnByVHB5RklmSTFRN0FWM0J4L2NDOEpndURlWiszd3Z1SUFpSmlWRk9NMXJVKzhtbEZpeC9GU2JLNzFLdkNvaUxGdGRpeVNmcDJERWdralhvOXIwbTdlUFJaYVh3dnlCUlcyanhsQlZSSmw3WTV5U0VkdVhyMnBoVzBjN01SYmlidDVpM1g3ZEgyWTdhVE4xOURIZW1tckp4Zk9BczZUVk8yd2grcFdqZFk0aTZYUmRaNHExWEpSNTEwenJCRXk3MXIycjQ4dnlwVWdjUFpRYm5rWGlyQUhqdEI0aHMiLCJtYWMiOiJhZTNkODlhMWY4ZGRlZThiOGJkMWI0ZDE3MjdiMzliYjJiODMwNjg1ZmQ1Y2EyMmZiMDg4NDM4Yzk4ZjM5ODgyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:10:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkZESG9NSW5BSS9CTXNXYWhIRUNDZEE9PSIsInZhbHVlIjoiaVpFOGdFZG5MTDR4OFY1SEdjNHlTbWlaS0FVdGtjK2t3WEFNY1VnbEFORGtzc3dvTUFWQU5aSnU3NTQvTWsrUmdvSVE5cUkvRFhJWEtNelNTU3N0d1hGQWlveGJUdzZSS3VUQTJnMkF4QTNHcC9rcnYxbVBMVks2MkliT3RvZlpOWjlUUUJkbk9zVUpkT1VjVElzQWhIVk9CV3RvQTJtUUZ2TVAzR1VMYmZDZHhTWUUrWGRtU2pLZWRPeFg0akZvbmk2b2dvMWQ5NEZOdFN3VHpVeGJVaVRGVzRLZDhPZmFzYlZEdW92L054MkxValZLZFhDdFhGSE9NQ1JSTm1FSVlxUW8reDlwbitpZ1duYTNHOXUwVDlUbXdhNWtaL3MxRjZadDcyeDlHS0NDYk1PUE1acG1RNEFidzhqVlZCUE95L2JNYUU0aDFaSGxGWmtuc2ZrcExISTVxWmFBQ2FXdUJ2QitDMFEzSExuTkpYZk1GUmg3Ty9jOWpSOVJaK25OMUdxMTAwdFJVK2dnZm1HWXRDSUQvcTVZU0lrN3I4VkVZM24yaVc0SFpTKzQ2VDhQbnMycFV2a0ZmQ0FETHZiVTdZRUVzSzVWRW5nU0tOdGlyUmVIWXM4bFV3S3AxYnhrR2pnR1BzelNpang5K3IvTjEyMDFROGU4c3lhb2s4WTUiLCJtYWMiOiIxYTliNTBkZGNmMGMyODM2MWRmMzFmYTNiOGY2YTUzNmQ1MWRlYTI1OWRjNThmMzhkOGFhMTQ3YjE4OTUxMGU0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:10:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImwrNURTbVA2WVhGZzF4bzZrWlNBV1E9PSIsInZhbHVlIjoiQzFub08rNWx5cEdrMzlUb1NMZWhrdUhSbU9sWFpWNStTdlo1NUt6M3ZNWld4Z0Y5RFovbVJLMm1wVzMvcTQzMW5PNUliUUI0Yk5CMGZoS1NhNmRFNU5ITzFtZTZlYWV4ZEhyYklEMmRqNm5JREhCZWFHcHN4dFFYUkNlZklHTncwWUNXSUJhY093cXUwbTFHblpQVWZlS0pRWUs4TlVwU1R2MmU4Y25IYUN6Y0dYS2NSaDd1blU1Ty85WGpnb1JyOS9zY1RrOTZVN2t5RUZmZlYxUE9ScFRJVUJuMnhyMkRaR2l0aXFYVVJEUnpmMERMaHk1RHFyb3lLaTVVK2NkdVZJUVhTbVo0b3RKRk1lcm42SHNWcnhETENUekxNMkV5cEl6SnByVHB5RklmSTFRN0FWM0J4L2NDOEpndURlWiszd3Z1SUFpSmlWRk9NMXJVKzhtbEZpeC9GU2JLNzFLdkNvaUxGdGRpeVNmcDJERWdralhvOXIwbTdlUFJaYVh3dnlCUlcyanhsQlZSSmw3WTV5U0VkdVhyMnBoVzBjN01SYmlidDVpM1g3ZEgyWTdhVE4xOURIZW1tckp4Zk9BczZUVk8yd2grcFdqZFk0aTZYUmRaNHExWEpSNTEwenJCRXk3MXIycjQ4dnlwVWdjUFpRYm5rWGlyQUhqdEI0aHMiLCJtYWMiOiJhZTNkODlhMWY4ZGRlZThiOGJkMWI0ZDE3MjdiMzliYjJiODMwNjg1ZmQ1Y2EyMmZiMDg4NDM4Yzk4ZjM5ODgyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:10:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkZESG9NSW5BSS9CTXNXYWhIRUNDZEE9PSIsInZhbHVlIjoiaVpFOGdFZG5MTDR4OFY1SEdjNHlTbWlaS0FVdGtjK2t3WEFNY1VnbEFORGtzc3dvTUFWQU5aSnU3NTQvTWsrUmdvSVE5cUkvRFhJWEtNelNTU3N0d1hGQWlveGJUdzZSS3VUQTJnMkF4QTNHcC9rcnYxbVBMVks2MkliT3RvZlpOWjlUUUJkbk9zVUpkT1VjVElzQWhIVk9CV3RvQTJtUUZ2TVAzR1VMYmZDZHhTWUUrWGRtU2pLZWRPeFg0akZvbmk2b2dvMWQ5NEZOdFN3VHpVeGJVaVRGVzRLZDhPZmFzYlZEdW92L054MkxValZLZFhDdFhGSE9NQ1JSTm1FSVlxUW8reDlwbitpZ1duYTNHOXUwVDlUbXdhNWtaL3MxRjZadDcyeDlHS0NDYk1PUE1acG1RNEFidzhqVlZCUE95L2JNYUU0aDFaSGxGWmtuc2ZrcExISTVxWmFBQ2FXdUJ2QitDMFEzSExuTkpYZk1GUmg3Ty9jOWpSOVJaK25OMUdxMTAwdFJVK2dnZm1HWXRDSUQvcTVZU0lrN3I4VkVZM24yaVc0SFpTKzQ2VDhQbnMycFV2a0ZmQ0FETHZiVTdZRUVzSzVWRW5nU0tOdGlyUmVIWXM4bFV3S3AxYnhrR2pnR1BzelNpang5K3IvTjEyMDFROGU4c3lhb2s4WTUiLCJtYWMiOiIxYTliNTBkZGNmMGMyODM2MWRmMzFmYTNiOGY2YTUzNmQ1MWRlYTI1OWRjNThmMzhkOGFhMTQ3YjE4OTUxMGU0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:10:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-480856211\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1770157729 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9jnw7W5QxdNjFEBK7Eh3dQJB5JQ3awZbTqtwVcCp</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1770157729\", {\"maxDepth\":0})</script>\n"}}