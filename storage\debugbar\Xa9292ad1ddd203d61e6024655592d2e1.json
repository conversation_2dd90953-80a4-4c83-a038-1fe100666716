{"__meta": {"id": "Xa9292ad1ddd203d61e6024655592d2e1", "datetime": "2025-07-31 07:31:45", "utime": 1753947105.023079, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[07:31:45] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753947105.018323, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753947103.685399, "end": 1753947105.023101, "duration": 1.3377020359039307, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1753947103.685399, "relative_start": 0, "end": **********.796542, "relative_end": **********.796542, "duration": 1.111142873764038, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.796557, "relative_start": 1.1111578941345215, "end": 1753947105.023103, "relative_end": 1.9073486328125e-06, "duration": 0.226546049118042, "duration_str": "227ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51844736, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.940263, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.037090000000000005, "accumulated_duration_str": "37.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8602152, "duration": 0.01573, "duration_str": "15.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 42.41}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.89302, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 42.41, "width_percent": 2.022}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 79 or `ch_messages`.`to_id` = 79 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["79", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9009311, "duration": 0.01417, "duration_str": "14.17ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 44.432, "width_percent": 38.204}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 79", "type": "query", "params": [], "bindings": ["client", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 364}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.920465, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:364", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=364", "ajax": false, "filename": "MessagesController.php", "line": "364"}, "connection": "radhe_same", "start_percent": 82.637, "width_percent": 2.588}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.95834, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 85.225, "width_percent": 2.022}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.972382, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 87.247, "width_percent": 1.833}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.977042, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 89.081, "width_percent": 1.941}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.983009, "duration": 0.00333, "duration_str": "3.33ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 91.022, "width_percent": 8.978}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 551, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1576088112 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1576088112\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-802736070 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-802736070\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-446674018 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-446674018\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlRLVXZySTZDYUFLcnBiZkFJaUp2Y2c9PSIsInZhbHVlIjoiUzEyMHZ4YSt2bFlIKzZ4MXBnT2FQUmVGc0RsbEtTazRaYjFaQWxSTHRQenl5TzVyTW5wMlRibmlYYXRvaVlWOVFXNzY0dmM1dWVJZEo1L29DVzJ3WTZ3YUZRSE5NOC95TXlXN3BVc1hZZmRBNzdpRGtwRUpDdjArcmtWSjYxMnMweVhLTVNyZnRsS3RZWWxnMXU4bVMvZmovdFRXRW1nVkVyemxSMWNkUUdVem1Ea0RBUEJ0cTFpY2x4UHZyUkxsSEVFVjEvUjdLdldTSVlwcENwa2l4YUVnRUhoK3paTnQ2MHhodEYwS3oxTlFPV0Y4S1NQeU1ITmNwcG5PQnlpRWdBT3hydkcvZUZMa3dzMmJQN0RYQUpwRDBjU2p4SXBrY2plc0pscWYzSVNoT3kvVzl3TlNkaTN4a3BHS0Eydmo2aTFwa3RiWTNlN1Z2YjFTLzFkaXJLd2szTVR6UVVtODRYODNRUC9NV0dQMGhWdWxYTjUwWnRxWWkvanF6MHZtb2JzcSsxMW9NOEVZU1RKYjNQNkcrYi8vc3pRSHFGem9Gb25wUzl0aUNzOXlmRWpaU2RPcm9BWlZJUzZPSTRsVWdCTnVZSmF1TytXeWNXYjFFa0VFUHQxNkJsNHR3bVNvZ3UxUlA5c2ZkbjlZc3U3QWdlYnhiOGIwNHptSGJsTHgiLCJtYWMiOiI3MTBkNDQ1ODcyMTM3NTVjOWFkOGJlNjJiMmFhNzg0MDZiYjAxY2ZiYzNhODliZGRiYjgxZTcxYWVmZDQ1ZWNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InJUeW0wYnQ2QkxSZVlaVHgrSElIM1E9PSIsInZhbHVlIjoiMFIwR0piSEQxQlo2Um84WmVRaUIyUmpDWGJHUjRSTkNvYjg2a0ZjUnVzNDI3SjJ5UFMrUU5ablR2c0lsY25KMEtCVHI0NlB0V1dLQXo5dFdWWCtYSVVHSERmZkZLaXFDNys0SUhGcW0raXhWTXFLQVo2ZU1ZUElhUVlDN2o5ZkxJdDdVejFrSXIveDdUNmtjWEtyRWZTMTVRYTczdGpDb2gyemNYWlVBcXNwZDk1QnlMZXVDcjBROTVUcFdsVXR0S2tQTzJiUFRiaXhRWGtzVzZFMDlYTXdoNUFPUTFsc3Q1Mmdjb2VHZHdaQW4yc1hWSlNoUjhQK0ZGUE5qN3JzZkNENEY4ZUg3ejRGcDdEdzR4cXZNZEFzRFJBeHFNQWxzSUZPVHpSM2pUTEdPZWpzSm1WR0NYNFNuaEpUcXhHb1kySS9YdnV2SFFRS000UmxBZnl3ZXFVQkVrQzV0bEVacnhvbnc5TVZpN2t1VkJzQTFrclAxZzVIUENDZ3JMbFhnOGgwU1NVQzd5VUhDNlFIN2orRmkzcExOc2ZCWWNSbENCUURRcFU0aWV2Vk1nODVxdlI5VU12L0tMN3pVYmc2Wkp0ZGhCSFVKSmU0SVBvMWMxOEFXeGlJZHY0RG13Wmpyb1habW1BQmZYeVlqaTlYT3ljNmxnb1k3RjJXS3o2VWgiLCJtYWMiOiIxYjk1ZDZmMzBjOTAwMTlkZTM2ZGNiMjQ4NmYxNjFmM2Q3YjIzMDRhZDYyNjMyOWVlZWQyODk2MmJhNTFiYmM5IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlBBb0VzUmJlb3kvbDVOUHdJU2VrNHc9PSIsInZhbHVlIjoiS1pWRTErOWc3anBLUlBvRHA5TlcxY1JGcE1GUG5ZRXAvS0pSeklLQUkrL2g4WW5XSXJaeVRKTkNHSFFwVFh4eWYrQUx6U1htWEdUZ2tldTcxTWludldqYmpGRVJVRG9zSjU0R01WMGtwRGt0eDhIVGI0bHFBUldhWXdEd3dGSHhtUXlWaVJsZnVNdHRKY3pja0hIMHFRRUMxdlg2ZjFlYStaN2FUZHdReFRZeVVPYjVLZC9YeXhlZHdPVkhjZkRmYnY0KzZvK2lZTjBsRUZNWjF2Q2thbjNyMDBjem1oSkk5bithSGUyc0JUTHdHOVRTWDFnTGJ1STFFN0o0ZEljallobjR6M21XTjFHZGlHUlltSE8yWUFTK2ZFdCtSMkpGNjJGbGZiSkNDMzRtS3lINk9iWUszcGwxa2wrckN3MjgyeThjV3UraXhsL3l2VFdreEYyd2phY1hxNGVXTUZwa1JoRjNocVFFR3B6M1orUVJCcWRCM011OW9XQy9ZUHdLZUhjZWZEZXZYRU1McDAwbVV1cHV4OVUwNFdKWWVSRnZtYjdqN0thZDBvNDBaZXJqOWRGVFRxL01lVElEQm9FSlc3VkdsNmpnby9mOVNKTDBUQy9PWGF1cENvZ3UyRFFBZDhSVTViUUxZSjloVEsvam9DMTkvQ3FhejRxcW50ODUiLCJtYWMiOiI0ZjM5YTFmNjc1OWQ4ZWMyNjc5ZTFmMTViY2QxNDRkNmZhYTFmMWM2ZmU1MTVlNzBjNGRkMjIyNjI0Yjc3OWRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-141383555 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21K3ZmFzZyr4s2pH9aKWRqwEKhgdUCnDfhl6jx78</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-141383555\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1514718208 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:31:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjIrT1lITkIvaE84dGU4Nmo5ZnBFeHc9PSIsInZhbHVlIjoiRGlMM0xxcEw2dWV6U1drbnRXNEJwa0ZzNHdxUGJJb2xHeFYwZnphL0paTVZGVGxpS3Z0bVBxckJSZW1kc2YzTHZlbStYdHE3YzdaL0dtRUtZZUFKUjFIVUNWMEo1ZXI5QmVyM1BLSU9WS0F4ZGJ2OVo3VnBEbWFZTUFhaVFic0xLRW5FRFhVcEpNWG9sRkxHeGNuRksrY3pScis5ajVCUzZvT2FaTzJnd21XUXlqdlhCeFh1eWhSQkhuVTgxV1BtS3hhVUY3Q3RxSXh2R2dTSUhNVWErVy9CdjhVaU9PYjF3eFNjc1hYQm1PY0JtWlNpZk41OVlMTHBXU2dkV0tFZTZFbk1NU1VFdFNoclh5RCtRSUoyNjFYelk1MTJVeEJHS2hUY29lUmlqTnMvaFlqUUIrTTlMcXpuWFBZdk1FVWlOREIwVkR1L2JNS0Y5S0h5NzBaYUNMM09ERXE1a1ZQRjR3ek9RNmtzNU9VN2Y3YjUrUHh4TEk3bXFoMFlHTHQ3dTQrZlhPYmtLajdWck56cmc2U2x3MlkyQm1xMGhiaHJSQjVTV0lsQ3IrTTZ0SWZwOGJ0UENsM29xbTRiT3hGUFh2YkhxUG0zcWliVHdrbXBUKy9sR1MrM0RidkF2WFhBUWwyK2t1L2JxSjR2cU1jZjRXRnRSV21ieUNtazlzSHMiLCJtYWMiOiI5M2Y5Mzg4Y2FiY2I1NmU5MWMwYzNlNzEzMGYyZWE3ZTA3Mzg1NzljZTUxN2Q5MzZjNTBiMTQ1ZjU3MDRhZWU1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:31:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6Imtqa1JNcW9BcFFwd01kckVKc1NrVUE9PSIsInZhbHVlIjoiRDc4UUU4Wmxzd21LOXBjNlBBT2JSaFBJY2R6Q0ZsSU9hdjd5V1NwSVV0NW9MUnQybllQNWEwVzYwZlJTOHFkdkhVVmc4V3Rqam5uZjNGcjJRclhHc2ZnSFdkanlNUFprTWxUMzYyUTh3RU5zb3ZISStteDJWVXBYQVR0M2FDUy9sT1hXNGxuKzNaNmpyRGJJOU9wWS93d1NTL3lwcjJaQld2emI2R2NWZk1IdVUzV3dnMlY3N2s0TjhoN3BqaS85VDFPc1h0cUVaOXpJTXdDbGZGMFQyeWFJeXlaTlJjd1Joekd2S0xrWDdQbDg1dnlSeTZneUVJMVFuL2NmQTRtcjg5SzN3UGxlMVRoTGc4ZjhjakFGejhIbkJheXFQTkF6V0RDQTZUNFNWV0IyVFVsWlR0K0krSkdYQy9ld24yaFdBb05qamxrMmRTeHp1UDAwZ0NKMlBJWHkrR04vTTV1TkZIa0V3Qk1jUkpEMzNkSS9CZ2ZFRVJwQ21zNW1FbDhHUWRZZEVmUHBUdmpPVnJLRDIvK1hJa3hRVndUd2dUNUNBWkhoQXJ5Y3F3ZXBFR3I2L3pNZkoxODFRS1p1bGxuSDhaN1ZFaGhMVEpzMUtUMlVZS3dkOVRReEhpdHJJN0o5c1FSVTZxZHdJcjVIMkVhZ0NYUkRsYitqNnBhb3dDY3UiLCJtYWMiOiIzMzhkZWMwMDMwNzJiMDg0ZjIwYWQyMDNkZTdjZTU1NTU4MGI3ZjY5NTM2MGQ3MzY2NDkyM2Y1ODQxZTk1MTIyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:31:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjIrT1lITkIvaE84dGU4Nmo5ZnBFeHc9PSIsInZhbHVlIjoiRGlMM0xxcEw2dWV6U1drbnRXNEJwa0ZzNHdxUGJJb2xHeFYwZnphL0paTVZGVGxpS3Z0bVBxckJSZW1kc2YzTHZlbStYdHE3YzdaL0dtRUtZZUFKUjFIVUNWMEo1ZXI5QmVyM1BLSU9WS0F4ZGJ2OVo3VnBEbWFZTUFhaVFic0xLRW5FRFhVcEpNWG9sRkxHeGNuRksrY3pScis5ajVCUzZvT2FaTzJnd21XUXlqdlhCeFh1eWhSQkhuVTgxV1BtS3hhVUY3Q3RxSXh2R2dTSUhNVWErVy9CdjhVaU9PYjF3eFNjc1hYQm1PY0JtWlNpZk41OVlMTHBXU2dkV0tFZTZFbk1NU1VFdFNoclh5RCtRSUoyNjFYelk1MTJVeEJHS2hUY29lUmlqTnMvaFlqUUIrTTlMcXpuWFBZdk1FVWlOREIwVkR1L2JNS0Y5S0h5NzBaYUNMM09ERXE1a1ZQRjR3ek9RNmtzNU9VN2Y3YjUrUHh4TEk3bXFoMFlHTHQ3dTQrZlhPYmtLajdWck56cmc2U2x3MlkyQm1xMGhiaHJSQjVTV0lsQ3IrTTZ0SWZwOGJ0UENsM29xbTRiT3hGUFh2YkhxUG0zcWliVHdrbXBUKy9sR1MrM0RidkF2WFhBUWwyK2t1L2JxSjR2cU1jZjRXRnRSV21ieUNtazlzSHMiLCJtYWMiOiI5M2Y5Mzg4Y2FiY2I1NmU5MWMwYzNlNzEzMGYyZWE3ZTA3Mzg1NzljZTUxN2Q5MzZjNTBiMTQ1ZjU3MDRhZWU1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:31:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6Imtqa1JNcW9BcFFwd01kckVKc1NrVUE9PSIsInZhbHVlIjoiRDc4UUU4Wmxzd21LOXBjNlBBT2JSaFBJY2R6Q0ZsSU9hdjd5V1NwSVV0NW9MUnQybllQNWEwVzYwZlJTOHFkdkhVVmc4V3Rqam5uZjNGcjJRclhHc2ZnSFdkanlNUFprTWxUMzYyUTh3RU5zb3ZISStteDJWVXBYQVR0M2FDUy9sT1hXNGxuKzNaNmpyRGJJOU9wWS93d1NTL3lwcjJaQld2emI2R2NWZk1IdVUzV3dnMlY3N2s0TjhoN3BqaS85VDFPc1h0cUVaOXpJTXdDbGZGMFQyeWFJeXlaTlJjd1Joekd2S0xrWDdQbDg1dnlSeTZneUVJMVFuL2NmQTRtcjg5SzN3UGxlMVRoTGc4ZjhjakFGejhIbkJheXFQTkF6V0RDQTZUNFNWV0IyVFVsWlR0K0krSkdYQy9ld24yaFdBb05qamxrMmRTeHp1UDAwZ0NKMlBJWHkrR04vTTV1TkZIa0V3Qk1jUkpEMzNkSS9CZ2ZFRVJwQ21zNW1FbDhHUWRZZEVmUHBUdmpPVnJLRDIvK1hJa3hRVndUd2dUNUNBWkhoQXJ5Y3F3ZXBFR3I2L3pNZkoxODFRS1p1bGxuSDhaN1ZFaGhMVEpzMUtUMlVZS3dkOVRReEhpdHJJN0o5c1FSVTZxZHdJcjVIMkVhZ0NYUkRsYitqNnBhb3dDY3UiLCJtYWMiOiIzMzhkZWMwMDMwNzJiMDg0ZjIwYWQyMDNkZTdjZTU1NTU4MGI3ZjY5NTM2MGQ3MzY2NDkyM2Y1ODQxZTk1MTIyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:31:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1514718208\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2023428782 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2023428782\", {\"maxDepth\":0})</script>\n"}}