<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Services\CrmWebhookDispatcher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class BookingController extends Controller
{
    public function index(Request $request)
    {
        // Check permission based on pricing plan
        if (!$this->hasBookingPermission('view booking')) {
            abort(403, 'Unauthorized - Booking module not available in your plan');
        }

        // Get all bookings with their related events and assigned staff
        $query = Booking::with(['event', 'event.assignedStaff'])
            ->whereHas('event', function($query) {
                $query->where('created_by', Auth::id());
            });

        // Apply date filters if provided
        if ($request->has('start_date') && $request->start_date) {
            $query->where('date', '>=', $request->start_date);
        }

        if ($request->has('end_date') && $request->end_date) {
            $query->where('date', '<=', $request->end_date);
        }

        $bookings = $query->orderBy('created_at', 'desc')->get();

        return view('bookings.index', compact('bookings'));
    }

    public function store(Request $request)
    {
        // Check permission based on pricing plan
        if (!$this->hasBookingPermission('create booking')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Booking creation not available in your plan'
            ], 403);
        }

        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'nullable|string|max:20',
                'event_id' => 'required|exists:calendar_events,id',
                'date' => 'required|date',
                'time' => 'required|string',
                'timezone' => 'nullable|string|max:100',
                'custom_fields' => 'nullable|array',
                'custom_fields_value' => 'nullable|array',
            ]);

            // Verify the event exists and get it
            $event = \App\Models\CalendarEvent::findOrFail($validated['event_id']);

            // Prepare custom fields value with timezone
            $customFieldsValue = $validated['custom_fields_value'] ?? [];
            if (!empty($validated['timezone'])) {
                $customFieldsValue['timezone'] = $validated['timezone'];
            }

            // Create the booking
            $booking = Booking::create([
                'event_id' => $validated['event_id'],
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
                'date' => $validated['date'],
                'time' => $validated['time'],
                'selected_location' => $request->selected_location,
                'custom_fields' => $validated['custom_fields'] ?? null,
                'custom_fields_value' => $customFieldsValue,
                'payment_amount' => $event->payment_required ? ($validated['payment_amount'] ?? $event->payment_amount) : 0.00,
                'payment_status' => $event->payment_required ? 'pending' : 'not_required',
                'status' => 'scheduled'
            ]);

            // Dispatch booking form submitted webhook
            try {
                $webhookDispatcher = new CrmWebhookDispatcher();
                $formData = [
                    'name' => $validated['name'],
                    'email' => $validated['email'],
                    'phone' => $validated['phone'],
                    'date' => $validated['date'],
                    'time' => $validated['time'],
                    'custom_fields' => $validated['custom_fields'] ?? [],
                    'custom_fields_value' => $validated['custom_fields_value'] ?? []
                ];
                $webhookDispatcher->dispatchBookingFormSubmitted($booking, $formData);
            } catch (\Exception $e) {
                Log::error('Booking webhook dispatch failed: ' . $e->getMessage());
                // Don't fail the main operation due to webhook issues
            }

            return response()->json([
                'success' => true,
                'message' => 'Booking created successfully',
                'data' => $booking
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create booking: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Public store method for bookings (no authentication required)
     */
    public function publicStore(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'nullable|string|max:20',
                'event_id' => 'required|exists:calendar_events,id',
                'date' => 'required|date',
                'time' => 'required|string',
                'selected_location' => 'nullable|array',
                'custom_fields' => 'nullable|array',
                'custom_fields_value' => 'nullable|array',
                'payment_amount' => 'nullable|numeric|min:0'
            ]);

            // Verify the event exists and get it
            $event = \App\Models\CalendarEvent::findOrFail($validated['event_id']);

            // Create the booking
            $booking = Booking::create([
                'event_id' => $validated['event_id'],
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
                'date' => $validated['date'],
                'time' => $validated['time'],
                'selected_location' => $validated['selected_location'] ?? null,
                'custom_fields' => $validated['custom_fields'] ?? null,
                'custom_fields_value' => $validated['custom_fields_value'] ?? null,
                'payment_amount' => $event->payment_required ? ($validated['payment_amount'] ?? $event->payment_amount) : 0.00,
                'payment_status' => $event->payment_required ? 'pending' : 'not_required',
                'status' => 'scheduled'
            ]);

            // Dispatch booking form submitted webhook (public booking)
            try {
                $webhookDispatcher = new CrmWebhookDispatcher();
                $formData = [
                    'name' => $validated['name'],
                    'email' => $validated['email'],
                    'phone' => $validated['phone'],
                    'date' => $validated['date'],
                    'time' => $validated['time'],
                    'selected_location' => $validated['selected_location'] ?? null,
                    'custom_fields' => $validated['custom_fields'] ?? [],
                    'custom_fields_value' => $validated['custom_fields_value'] ?? [],
                    'booking_type' => 'public'
                ];
                $webhookDispatcher->dispatchBookingFormSubmitted($booking, $formData);
            } catch (\Exception $e) {
                Log::error('Public booking webhook dispatch failed: ' . $e->getMessage());
                // Don't fail the main operation due to webhook issues
            }

            // Determine redirect URL - use custom redirect URL if provided, otherwise default confirmation page
            $redirectUrl = !empty($event->custom_redirect_url)
                ? $event->custom_redirect_url
                : route('booking.confirmation', ['booking' => $booking->id]);

            // Add success message to the redirect URL for non-payment bookings
            if (!$event->payment_required) {
                $redirectUrl .= (strpos($redirectUrl, '?') !== false ? '&' : '?') . 'success=1';
            }

            return response()->json([
                'success' => true,
                'message' => 'Booking created successfully',
                'data' => $booking,
                'redirect_url' => $redirectUrl
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create booking: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show booking confirmation page
     */
    public function confirmation(Booking $booking)
    {
        // Load the event relationship
        $booking->load('event');

        // Get timezone from booking's custom fields, request parameter, or default
        $timezone = null;

        // First, try to get timezone from booking's custom_fields_value
        if ($booking->custom_fields_value && is_array($booking->custom_fields_value)) {
            $timezone = $booking->custom_fields_value['timezone'] ?? null;
        }

        // If not found, try request parameter
        if (!$timezone) {
            $timezone = request('timezone');
        }

        // Finally, use default
        if (!$timezone) {
            $timezone = 'Asia/Calcutta';
        }

        // Add success message if coming from non-payment booking
        if (request('success') == '1') {
            session()->flash('success', 'Booking confirmed successfully! Your meeting has been scheduled.');
        }

        return view('bookings.confirmation', compact('booking', 'timezone'));
    }

    public function show(Booking $booking)
    {
        // Check permission based on pricing plan
        if (!$this->hasBookingPermission('show booking')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Booking view not available in your plan'
            ], 403);
        }

        // Load the booking with its event and assigned staff
        $booking->load(['event', 'event.assignedStaff']);

        // Get custom field details if custom fields exist
        $customFieldDetails = [];
        if ($booking->custom_fields && is_array($booking->custom_fields)) {
            $customFieldIds = $booking->custom_fields;
            $customFields = \App\Models\CustomField::whereIn('id', $customFieldIds)->get();
            
            foreach ($customFields as $field) {
                $customFieldDetails[$field->id] = [
                    'id' => $field->id,
                    'name' => $field->name,
                    'type' => $field->type,
                    'options' => $field->options
                ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => $booking,
            'custom_field_details' => $customFieldDetails
        ]);
    }

    public function update(Request $request, Booking $booking)
    {
        // Check permission based on pricing plan
        if (!$this->hasBookingPermission('edit booking')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Booking editing not available in your plan'
            ], 403);
        }

        try {
            // Check if user is admin or owns the event associated with this booking
            if (!$this->isAdmin() && $booking->event->created_by !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'nullable|string|max:20',
                'date' => 'required|date',
                'time' => 'nullable|string',
                'custom_fields_value' => 'nullable|array',
            ]);

            // Update the booking
            $booking->update([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
                'date' => $validated['date'],
                'time' => $validated['time'],
                'custom_fields_value' => $validated['custom_fields_value'] ?? $booking->custom_fields_value,
            ]);

            // Reload the booking with its event
            $booking->load('event');

            return response()->json([
                'success' => true,
                'message' => 'Booking updated successfully',
                'data' => $booking
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update booking: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Booking $booking)
    {
        // Check permission based on pricing plan
        if (!$this->hasBookingPermission('delete booking')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Booking deletion not available in your plan'
            ], 403);
        }

        // Check if user is admin or owns the event associated with this booking
        if (!$this->isAdmin() && $booking->event->created_by !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $booking->delete();

        return response()->json([
            'success' => true,
            'message' => 'Booking deleted successfully'
        ]);
    }

    /**
     * Check if current user is admin
     */
    private function isAdmin()
    {
        return Auth::user() && (Auth::user()->type === 'super admin' || Auth::user()->type === 'system admin' || Auth::user()->type === 'company');
    }

    /**
     * Check if user has booking permission based on pricing plan
     */
    private function hasBookingPermission($permission)
    {
        $user = Auth::user();
        
        // Super admin and system admin always have access
        if ($user->type === 'super admin' || $user->type === 'system admin') {
            return true;
        }

        // For company users, check pricing plan permissions
        if ($user->type === 'company') {
            return $user->hasModulePermission('booking', $permission);
        }

        // For other user types, check traditional permissions
        return $user->can($permission);
    }

    /**
     * Update booking status
     */
    public function updateStatus(Request $request, $id)
    {
        try {
            $booking = Booking::with('event')
                ->whereHas('event', function($query) {
                    $query->where('created_by', Auth::id());
                })
                ->where('id', $id)
                ->first();

            if (!$booking) {
                return response()->json([
                    'success' => false,
                    'message' => 'Booking not found'
                ], 404);
            }

            $validated = $request->validate([
                'status' => 'required|in:scheduled,reschedule,show_up,no_show,cancel'
            ]);

            $booking->update(['status' => $validated['status']]);

            return response()->json([
                'success' => true,
                'message' => 'Booking status updated successfully',
                'data' => $booking->load('event')
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the booking status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk cancel bookings
     */
    public function bulkCancel(Request $request)
    {
        // Check permission based on pricing plan
        if (!$this->hasBookingPermission('delete booking')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Booking cancellation not available in your plan'
            ], 403);
        }

        try {
            $validated = $request->validate([
                'booking_ids' => 'required|array|min:1',
                'booking_ids.*' => 'integer|exists:bookings,id'
            ]);

            $bookingIds = $validated['booking_ids'];

            // Get bookings that belong to the current user's events
            $bookings = Booking::with('event')
                ->whereHas('event', function($query) {
                    if (!$this->isAdmin()) {
                        $query->where('created_by', Auth::id());
                    }
                })
                ->whereIn('id', $bookingIds)
                ->get();

            if ($bookings->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No bookings found or you do not have permission to cancel these bookings'
                ], 404);
            }

            // Update status to cancelled for all selected bookings
            $cancelledCount = 0;
            foreach ($bookings as $booking) {
                $booking->update(['status' => 'cancel']);
                $cancelledCount++;
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully cancelled {$cancelledCount} appointment(s)",
                'cancelled_count' => $cancelledCount
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel bookings: ' . $e->getMessage()
            ], 500);
        }
    }
}

