{"__meta": {"id": "X297ea04a862166cefd5399f6753b14ce", "datetime": "2025-07-31 06:29:08", "utime": **********.368726, "method": "GET", "uri": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943346.145631, "end": **********.368775, "duration": 2.2231438159942627, "duration_str": "2.22s", "measures": [{"label": "Booting", "start": 1753943346.145631, "relative_start": 0, "end": **********.150711, "relative_end": **********.150711, "duration": 2.005079984664917, "duration_str": "2.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.150758, "relative_start": 2.************, "end": **********.36878, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0b3Z0nXIaf0UXUTRmrVmUem4ePYo83hBS8MsRz4F", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "status_code": "<pre class=sf-dump id=sf-dump-786219625 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-786219625\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1443380078 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1443380078\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-229292803 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-229292803\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-859803614 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-859803614\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1220704258 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1220704258\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1274305727 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:29:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijg0UjVaRW0vMHArbDAvOFdnTjJTVUE9PSIsInZhbHVlIjoiTU44YVJUVjZTMDNqenJtR3g1a0JSNS9IbldobWhJNWJxSWdmQWt1aVFmd2JUekhWdkZDVlpBNG9Dc3BGNUplODZoVnV4WjdQK1RLV1VDTE01NUNoV0Q3WFltMnAvMVkxbGVuYkRJTWFJWVdseE8vbEVGd2htVFg5dzNxaXZuNlE5TXhqM2R3NEFVaGN3YkMySWdLYWppY3pwZ01SZmlOQXYzdUNTK3NOTWNhSm9GTVJ4V21nVytRdU5TTU9vTkZwNjRVRm1yVms1dExsVndla1VacXBmNHNxTWVBb0pOWDJ3dkpZMHNHd2FxQkpTNVFXTTh6ZVB0allSOWtIOG9ueWgvUFM0ZGhjeTEvVWdON2FiVVBTTG1YeEtIZWFiT0htdDc4ZVR6S3ZSSDhjdzhPTXRLcGFnUHNJeVVVQXJaRzFHbUVkR1BYNlJZMDlZWW01WmJOQ2ZJOWZzenBtMDJrbU5ubkFKK2ZXMVhEYlZ3Tmk2QlY2dmRZMjJEblFsaTZRZ0p4anFoT1laMnNDWGVQd0ZRU2kvK3ZLSXhkY3VORnpzZDh4d0E3YTdvQTFsQWorRDJhSXhKT1FaOFZYaFM4ZzhoRXZ4eHYvdmNhdVFreGh6OEovVWRsSWVUV3EzN3FQS3hKZDFPK3VBNGhYb0xNbjFIVGc1S2ZueVdWZDRMZXAiLCJtYWMiOiI3MWQzYzQwNDY3MjliMWVhMDFkMThhZmE2YzJiNzZlNjczMWZlMGNhZDVjMjc4NmEyOWYxMjUxODUzMWVhODM4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkMzNVpsNEJ2aE1oL3dtU2V6cHY1K2c9PSIsInZhbHVlIjoiVnAvVFB2R0RYeG95WXlZeWpmdk1Jb3ZVVzFMbGdzSUQrRHZ3TEdsVDcydGx4aEJVUXJpZHNQTHoxL2VRbjQzUTNrTzhXZERheStNNDZReFNSVWVPaWNaakFwbXJnTnRWcFhJOUdrOWE1YzBaWjc3UWtZaDByVm5xbXNqekRjNjJodlloU0d1ZlFPM1NUQUk0cmFpem5sczhLU0ZZcVZXSHlWNnkxWGdzZ0ZqOGpZRjFvVitpV3lRV0JrTWYwclRyelg4SmZJTU9wOXRVTWd5WHhWa2Jvc1VEdHR6emFzcjFWb0lRQ004VHdtMkNpbjV3OHdYWTZiT3JPQ1V6QzV5YnZVSDhMM0VPR251TExPalVwUVluTm1NZXRxWC96U29tNXk1VWc5cUllVzIydjdlME0zeVAxcEViTWhFd3E0ZDNxeitmS1FvQ09EZkhRSFR4MUtrRnIwUFdvSlo2T29BWWdkUEZKeEJDZDRkRlVVM2s0K3BhT01hNmRnOVRxdHhUN2w4NlJ6VVZ4MktOZjNYY3FsYTBGZmdOeVRRcEo1OVZtU2xJMkVzZnczZjN2dExRQUlYcklkR0J2eGtaUjNnMHRjc0FLWFVsVSthSFFXdVJlaitJT3RVKytEdjJZR0lPM1FMQ1h0aDFVRVI2aUx3bkRabUJLa21OTWpwSmUybE0iLCJtYWMiOiI0YjY4MGI5YTRlN2RiN2NmNTAxNzA3MGRkOTRiYzljMGExN2U4MTY1MTJhMzU2MzM4MjhjYzg4ZDYxMDA0MDg2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijg0UjVaRW0vMHArbDAvOFdnTjJTVUE9PSIsInZhbHVlIjoiTU44YVJUVjZTMDNqenJtR3g1a0JSNS9IbldobWhJNWJxSWdmQWt1aVFmd2JUekhWdkZDVlpBNG9Dc3BGNUplODZoVnV4WjdQK1RLV1VDTE01NUNoV0Q3WFltMnAvMVkxbGVuYkRJTWFJWVdseE8vbEVGd2htVFg5dzNxaXZuNlE5TXhqM2R3NEFVaGN3YkMySWdLYWppY3pwZ01SZmlOQXYzdUNTK3NOTWNhSm9GTVJ4V21nVytRdU5TTU9vTkZwNjRVRm1yVms1dExsVndla1VacXBmNHNxTWVBb0pOWDJ3dkpZMHNHd2FxQkpTNVFXTTh6ZVB0allSOWtIOG9ueWgvUFM0ZGhjeTEvVWdON2FiVVBTTG1YeEtIZWFiT0htdDc4ZVR6S3ZSSDhjdzhPTXRLcGFnUHNJeVVVQXJaRzFHbUVkR1BYNlJZMDlZWW01WmJOQ2ZJOWZzenBtMDJrbU5ubkFKK2ZXMVhEYlZ3Tmk2QlY2dmRZMjJEblFsaTZRZ0p4anFoT1laMnNDWGVQd0ZRU2kvK3ZLSXhkY3VORnpzZDh4d0E3YTdvQTFsQWorRDJhSXhKT1FaOFZYaFM4ZzhoRXZ4eHYvdmNhdVFreGh6OEovVWRsSWVUV3EzN3FQS3hKZDFPK3VBNGhYb0xNbjFIVGc1S2ZueVdWZDRMZXAiLCJtYWMiOiI3MWQzYzQwNDY3MjliMWVhMDFkMThhZmE2YzJiNzZlNjczMWZlMGNhZDVjMjc4NmEyOWYxMjUxODUzMWVhODM4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkMzNVpsNEJ2aE1oL3dtU2V6cHY1K2c9PSIsInZhbHVlIjoiVnAvVFB2R0RYeG95WXlZeWpmdk1Jb3ZVVzFMbGdzSUQrRHZ3TEdsVDcydGx4aEJVUXJpZHNQTHoxL2VRbjQzUTNrTzhXZERheStNNDZReFNSVWVPaWNaakFwbXJnTnRWcFhJOUdrOWE1YzBaWjc3UWtZaDByVm5xbXNqekRjNjJodlloU0d1ZlFPM1NUQUk0cmFpem5sczhLU0ZZcVZXSHlWNnkxWGdzZ0ZqOGpZRjFvVitpV3lRV0JrTWYwclRyelg4SmZJTU9wOXRVTWd5WHhWa2Jvc1VEdHR6emFzcjFWb0lRQ004VHdtMkNpbjV3OHdYWTZiT3JPQ1V6QzV5YnZVSDhMM0VPR251TExPalVwUVluTm1NZXRxWC96U29tNXk1VWc5cUllVzIydjdlME0zeVAxcEViTWhFd3E0ZDNxeitmS1FvQ09EZkhRSFR4MUtrRnIwUFdvSlo2T29BWWdkUEZKeEJDZDRkRlVVM2s0K3BhT01hNmRnOVRxdHhUN2w4NlJ6VVZ4MktOZjNYY3FsYTBGZmdOeVRRcEo1OVZtU2xJMkVzZnczZjN2dExRQUlYcklkR0J2eGtaUjNnMHRjc0FLWFVsVSthSFFXdVJlaitJT3RVKytEdjJZR0lPM1FMQ1h0aDFVRVI2aUx3bkRabUJLa21OTWpwSmUybE0iLCJtYWMiOiI0YjY4MGI5YTRlN2RiN2NmNTAxNzA3MGRkOTRiYzljMGExN2U4MTY1MTJhMzU2MzM4MjhjYzg4ZDYxMDA0MDg2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1274305727\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0b3Z0nXIaf0UXUTRmrVmUem4ePYo83hBS8MsRz4F</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}