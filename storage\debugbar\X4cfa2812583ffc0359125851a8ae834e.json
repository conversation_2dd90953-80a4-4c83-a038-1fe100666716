{"__meta": {"id": "X4cfa2812583ffc0359125851a8ae834e", "datetime": "2025-07-31 06:24:40", "utime": **********.498066, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:24:40] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.490228, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943077.315192, "end": **********.498115, "duration": 3.1829230785369873, "duration_str": "3.18s", "measures": [{"label": "Booting", "start": 1753943077.315192, "relative_start": 0, "end": **********.241549, "relative_end": **********.241549, "duration": 2.9263570308685303, "duration_str": "2.93s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.24159, "relative_start": 2.9263980388641357, "end": **********.49812, "relative_end": 5.0067901611328125e-06, "duration": 0.2565300464630127, "duration_str": "257ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45918312, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02328, "accumulated_duration_str": "23.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.368639, "duration": 0.01979, "duration_str": "19.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 85.009}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.429735, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 85.009, "width_percent": 6.314}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4436, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 91.323, "width_percent": 3.952}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4533498, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 95.275, "width_percent": 4.725}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-47981801 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-47981801\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-939207754 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-939207754\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1554667897 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554667897\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1584200408 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InNPU2lBVEg1ZzVNOFYyTGtRc2tzbmc9PSIsInZhbHVlIjoiRE5VTGJiNzI1Qk92RWdaYjR6ZmZQSFhrV1E0OTR1SXh5dUFYcXdZUXh3TkJrWFpxNFRUQ2lNVTFnZ3l2S0ltS1VXOEE2ZVhQQjNEWmVUU3VIMldWbDY0eExVK2ZPajZwcm5oZWxyYTlsY0ZmQk9HSjl1aTRrOG9rNTZzcGJlNXZMdmFPYUFTNEF0U2EzRHhtaEZvc2U5eTNvL291UzRyK0ZGMjdxZk4xYTFJNEdiSFdhWXVqRTdWVlhmOU52bDBYZDY1d1hJc0lLeHQrRXRacUgwY0FaR01kVWJVRGhZTDgvSjd0bE9WVnQrTkZFZUR5Z3pLRmtuOG5hVFhsMG9RNmR5Y0p5TW5KU1BpaWJYNGZjNlh6RDhxcm1NLzgzYjZjQTE5WTEvTzg4RDl5VVFiSE0wT1ZURURPYW4rei83SExGa3NNZ0p5K2kvOWJDMk5lWXVndU9EZUhjUFpRVXhla093UURRRjNQWkJJbGdXcGNOcDJkZFFIMTdVNXg0WUNiMTRRYXc2aEFmaWUrcC9uTG4wMklOZkNPZCtaQmQzeHgrVitlQTJwbDAyVmxjQjR4TlNYeW1tWE1oOUpmNWlraWZ0ODJKQmZMQmpzdEwxcUxzSFFGTjg5b1o0QU5HRHFGZVZJT3BvVXFxNUVaVEYwaWQxTlAxZVJDVzlCUU5Bc24iLCJtYWMiOiJiMWNkZDIwZWQyYmE5MTZkZTc3YjRlNTExYjdiMGZhNTdmYjY0OGJkYzMwOTEwYTM3YmU5MzQ4ODM1NzA0YjE5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ing2VCtqYmMvVUE4RCtOem9tKzNXU3c9PSIsInZhbHVlIjoiQVV1TTl0NW1VeUd0bG9IUG1NTlR0S0hXaURQODB6emYwT3ZOb1pZNWtzaGQ4TEFpbkN6c09OYXhvTE9mUSsxbmFjQnNqSitGR2RkT0x3RUI3b0xTV2pTSUJVSnNzdWRlUVZZR0VFTUVkcU1BZW9Vb2kyeWFIb1F1MlBxaWpXTFhJMTFHQ2RKV0ZobWJ2TGFSdmtKZWhhaHM1S0h2M3lpMDlDa3lLcGxzWkphcnJMZHdoWjh2NWdxSC9hN21QeXFHTUI0czJ3TndSTXkzMlg1RDJXbXdmZm5HYXhwOHVHaEl0NmEybVRCQXo0UGp1UWl3c0VxQ1RmTE16Q0dpQXBuRW1WUXpVVWZXUlVkL09UbGMwQUx2dWo5S0s1Z2psN012YmhjMS9nM2VDOC9ZZ2xjTnZYTnJRdzNHcDA1Y0tqZGs0S1gyY3BldzRYZ1JjSW95RllmVW1RRzJtNE5nd21UZ1pjdzQydWUvdE81RStPZnJHY2lpN1d2Z3p1c3cyTUtYR2U0WGFjRkFVdnhnOTFqQlp1Vlc4bVZwei8zc2ZPSUNicVpxNmZYTEdpWWVkc2pSM1RIZzk5akF3Q3h6aUxzRmFkaUQwWFpLWG1KMHdPV3RxWkQ2ZEIxUFZ6c21YNzFramVDZkRUSCtoTERYNFZRTW5hR3h2VFRqT01UcVJPSG0iLCJtYWMiOiJmOGY5YTIyYTk3MmM3ZjQwMzE1MjZkMWVmM2RkOTMzYTgyNDBhYjM5ZWEyNTMwOTVjYzhlNDk1NGMwZDkyOTI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1584200408\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-191066356 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-191066356\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-612028079 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:24:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldsUGxXcXMyUG80N0JXa2g1OHJNSEE9PSIsInZhbHVlIjoiUDB5NG00RHhQd0tMd01uU01ETExlcFgrMGhTc3loeWxUWkUxaGVib0x2QlBlV2p4d0JvRWd1OUgzUGZnYncybmJxZC9Ubng0OEEyb2RtZjVXV256b3ZjWE9JVkk2amMxOUp0UEpNT2tybVJGQ3YrUkxzcUg5anFiQ2pHNDJCUlBra1pRN0FZeEN6OHBjdFpnYkdTZ1p0UHVsUXQ0ODZrUUFLWmh1OFpoOFVpWDVONXc3Tmh5MTlzTzVjN2QrK3VoUy9mMlh6MDdMNU1mamJVYUtqK2NEZEJSK0R2R3A0U0czR3ZKWUhMTVF0MTlVczYraDVpYm4xaGdaWE51ZnI3RHdDaFdYNjE4OTJrS2NYRWl6WlllYTB5d0YyUG41a21ubUlsTTRPZ1NIRXR4MElrYnNaend4U3NiekdlbEhqdGl1dWRnZUU0cE9LZUk1SWhOVzZseWRqWWtkcmFQajdkQzUvWlRubGQ0cnk1MnRwK1BuOGM1T3cxZURkREV3alNyaDQrVFVyV3czdk91RTZlc2JISCt0cFlOKzRBdFk5REdldUxXd0V0d2FoVU8zUVRRVWdJRHhaN1Iwa3U4SFVpNmtzZk9kM0VNYXVjSC9BYUdLc2hndElWalNDbmZNdTVqb0E4cTJPUUllUDdZUTJnN0RnWTJ4ekwzdWVuM1VSdW4iLCJtYWMiOiJhM2FjMTQ1YzZiNjUwMjU5MjY0OTM0ZDRlYmJhODgxNGFmN2IxYzVhZWY0ZTRiZTA3YWM0Mzg0YzQyYmIwZDc1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:24:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlRKSHRhVCt0VXlrS3BCVHVCMGdoY0E9PSIsInZhbHVlIjoiN0pBMUV6K2FVY0E1UXFpRWJMeU5hWVNQNHRGUSt4N0xnQ3NHbUlpeHBtd2ZEMU9WRkMwd3RGaCt0THZXVEJhZ29Qb3IrREVKT085TUJsTE5rUWh1UlQ2YnN0dFhOSnROTk1POVhnd09rVWM1VVBXQzl2bCtrTzlQc3cwNTZEZC9GZk5PUlljWEhXcDVWaUJhcm1veXVZa1FZWG9PVk5xUGdPaXhybk50TkZqcEI0bTRKa1FiVlFnOWV3Qzl6WGVoUHVDL0FiT2FkcVo0YWR0NTBjV2hzVmd0Z1AxcnNXaDUrdTdPejJheDVPdHYzSTZsODJ6QzF3d1Rjak9jU0RCZVJDc0dmbnJzWTE1NmZVOEFiMU8zcEJFcUNFMmFONUVmL2VFMUZBcVJDVmVxSDI3SXhhVnlkTmpVMlYrcnFudm5YRThiYncvc20zWkNvYlpnY1E2SU5IcUxTcHQyaTdqM1Y5dEFXYjhmQVFiOThoK2F0VHdQY3lNODl3OVphWmkyWHNneTdPUVcxRWRMYXlnSTZuYnpwOHBDbzJWUy84WW1jZUJGbURWNmpVUWFqR3ZxMUYvR1NDeGNETURubWhwNTR4Zmt0aXo4UHZBRU9mSDN2OGp3cmRkU3drK3AvKzZsWWl4WlFIQit3RitNQXVoQ1dEOU1jSmsrdTBkYmR4bHgiLCJtYWMiOiI2Njc3NTc1Y2UzNGRlN2RhNTBhMzVjOGU3ODg3ZTg2ODg1MzQxYTQyODI0OGZiODM5ODI0NDMyMjI3NzQyMGM0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:24:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldsUGxXcXMyUG80N0JXa2g1OHJNSEE9PSIsInZhbHVlIjoiUDB5NG00RHhQd0tMd01uU01ETExlcFgrMGhTc3loeWxUWkUxaGVib0x2QlBlV2p4d0JvRWd1OUgzUGZnYncybmJxZC9Ubng0OEEyb2RtZjVXV256b3ZjWE9JVkk2amMxOUp0UEpNT2tybVJGQ3YrUkxzcUg5anFiQ2pHNDJCUlBra1pRN0FZeEN6OHBjdFpnYkdTZ1p0UHVsUXQ0ODZrUUFLWmh1OFpoOFVpWDVONXc3Tmh5MTlzTzVjN2QrK3VoUy9mMlh6MDdMNU1mamJVYUtqK2NEZEJSK0R2R3A0U0czR3ZKWUhMTVF0MTlVczYraDVpYm4xaGdaWE51ZnI3RHdDaFdYNjE4OTJrS2NYRWl6WlllYTB5d0YyUG41a21ubUlsTTRPZ1NIRXR4MElrYnNaend4U3NiekdlbEhqdGl1dWRnZUU0cE9LZUk1SWhOVzZseWRqWWtkcmFQajdkQzUvWlRubGQ0cnk1MnRwK1BuOGM1T3cxZURkREV3alNyaDQrVFVyV3czdk91RTZlc2JISCt0cFlOKzRBdFk5REdldUxXd0V0d2FoVU8zUVRRVWdJRHhaN1Iwa3U4SFVpNmtzZk9kM0VNYXVjSC9BYUdLc2hndElWalNDbmZNdTVqb0E4cTJPUUllUDdZUTJnN0RnWTJ4ekwzdWVuM1VSdW4iLCJtYWMiOiJhM2FjMTQ1YzZiNjUwMjU5MjY0OTM0ZDRlYmJhODgxNGFmN2IxYzVhZWY0ZTRiZTA3YWM0Mzg0YzQyYmIwZDc1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:24:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlRKSHRhVCt0VXlrS3BCVHVCMGdoY0E9PSIsInZhbHVlIjoiN0pBMUV6K2FVY0E1UXFpRWJMeU5hWVNQNHRGUSt4N0xnQ3NHbUlpeHBtd2ZEMU9WRkMwd3RGaCt0THZXVEJhZ29Qb3IrREVKT085TUJsTE5rUWh1UlQ2YnN0dFhOSnROTk1POVhnd09rVWM1VVBXQzl2bCtrTzlQc3cwNTZEZC9GZk5PUlljWEhXcDVWaUJhcm1veXVZa1FZWG9PVk5xUGdPaXhybk50TkZqcEI0bTRKa1FiVlFnOWV3Qzl6WGVoUHVDL0FiT2FkcVo0YWR0NTBjV2hzVmd0Z1AxcnNXaDUrdTdPejJheDVPdHYzSTZsODJ6QzF3d1Rjak9jU0RCZVJDc0dmbnJzWTE1NmZVOEFiMU8zcEJFcUNFMmFONUVmL2VFMUZBcVJDVmVxSDI3SXhhVnlkTmpVMlYrcnFudm5YRThiYncvc20zWkNvYlpnY1E2SU5IcUxTcHQyaTdqM1Y5dEFXYjhmQVFiOThoK2F0VHdQY3lNODl3OVphWmkyWHNneTdPUVcxRWRMYXlnSTZuYnpwOHBDbzJWUy84WW1jZUJGbURWNmpVUWFqR3ZxMUYvR1NDeGNETURubWhwNTR4Zmt0aXo4UHZBRU9mSDN2OGp3cmRkU3drK3AvKzZsWWl4WlFIQit3RitNQXVoQ1dEOU1jSmsrdTBkYmR4bHgiLCJtYWMiOiI2Njc3NTc1Y2UzNGRlN2RhNTBhMzVjOGU3ODg3ZTg2ODg1MzQxYTQyODI0OGZiODM5ODI0NDMyMjI3NzQyMGM0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:24:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-612028079\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1439110323 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439110323\", {\"maxDepth\":0})</script>\n"}}