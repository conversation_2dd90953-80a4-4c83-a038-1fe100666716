<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('coupons', function (Blueprint $table) {
            // Check if columns don't already exist before adding them
            if (!Schema::hasColumn('coupons', 'product_id')) {
                $table->unsignedBigInteger('product_id')->nullable()->after('code');
            }
            if (!Schema::hasColumn('coupons', 'product_name')) {
                $table->string('product_name')->nullable()->after('product_id');
            }
            if (!Schema::hasColumn('coupons', 'discount_type')) {
                $table->enum('discount_type', ['percentage', 'fixed'])->default('percentage')->after('discount');
            }
            if (!Schema::hasColumn('coupons', 'start_date')) {
                $table->date('start_date')->nullable()->after('limit');
            }
            if (!Schema::hasColumn('coupons', 'end_date')) {
                $table->date('end_date')->nullable()->after('start_date');
            }
            if (!Schema::hasColumn('coupons', 'created_by')) {
                $table->unsignedBigInteger('created_by')->nullable()->after('is_active');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('coupons', function (Blueprint $table) {
            $table->dropColumn([
                'product_id',
                'product_name', 
                'discount_type',
                'start_date',
                'end_date',
                'created_by'
            ]);
        });
    }
};
