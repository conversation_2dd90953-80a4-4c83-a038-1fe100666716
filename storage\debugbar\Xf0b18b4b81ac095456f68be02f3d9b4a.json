{"__meta": {"id": "Xf0b18b4b81ac095456f68be02f3d9b4a", "datetime": "2025-07-31 07:47:41", "utime": **********.87552, "method": "POST", "uri": "/expense/store-ajax", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[07:47:41] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/expense\\/store-ajax\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.867154, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753948060.480752, "end": **********.875546, "duration": 1.39479398727417, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": 1753948060.480752, "relative_start": 0, "end": **********.37425, "relative_end": **********.37425, "duration": 0.8934979438781738, "duration_str": "893ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.37431, "relative_start": 0.8935580253601074, "end": **********.875549, "relative_end": 3.0994415283203125e-06, "duration": 0.5012390613555908, "duration_str": "501ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 57133416, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST expense/store-ajax", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ExpenseController@storeAjax", "namespace": null, "prefix": "", "where": [], "as": "expense.store.ajax", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=952\" onclick=\"\">app/Http/Controllers/ExpenseController.php:952-1015</a>"}, "queries": {"nb_statements": 17, "nb_failed_statements": 0, "accumulated_duration": 0.04657, "accumulated_duration_str": "46.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4352112, "duration": 0.02102, "duration_str": "21.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 45.136}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.47239, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 45.136, "width_percent": 2.341}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 954}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4879131, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 47.477, "width_percent": 2.942}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 954}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4957259, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 50.419, "width_percent": 1.568}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 954}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.500533, "duration": 0.00471, "duration_str": "4.71ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 51.986, "width_percent": 10.114}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.540874, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 62.1, "width_percent": 5.025}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.578043, "duration": 0.00679, "duration_str": "6.79ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "radhe_same", "start_percent": 67.125, "width_percent": 14.58}, {"sql": "select exists(select * from `product_service_categories` where `name` = 'Office Supplies' and `type` = 'expense' and `created_by` = 79) as `exists`", "type": "query", "params": [], "bindings": ["Office Supplies", "expense", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 67}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 956}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.753023, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ExpenseController.php:67", "source": "app/Http/Controllers/ExpenseController.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=67", "ajax": false, "filename": "ExpenseController.php", "line": "67"}, "connection": "radhe_same", "start_percent": 81.705, "width_percent": 1.997}, {"sql": "select exists(select * from `product_service_categories` where `name` = 'Travel & Transportation' and `type` = 'expense' and `created_by` = 79) as `exists`", "type": "query", "params": [], "bindings": ["Travel &amp; Transportation", "expense", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 67}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 956}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.758337, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ExpenseController.php:67", "source": "app/Http/Controllers/ExpenseController.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=67", "ajax": false, "filename": "ExpenseController.php", "line": "67"}, "connection": "radhe_same", "start_percent": 83.702, "width_percent": 1.181}, {"sql": "select exists(select * from `product_service_categories` where `name` = 'Meals & Entertainment' and `type` = 'expense' and `created_by` = 79) as `exists`", "type": "query", "params": [], "bindings": ["Meals &amp; Entertainment", "expense", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 67}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 956}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.762103, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ExpenseController.php:67", "source": "app/Http/Controllers/ExpenseController.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=67", "ajax": false, "filename": "ExpenseController.php", "line": "67"}, "connection": "radhe_same", "start_percent": 84.883, "width_percent": 1.031}, {"sql": "select exists(select * from `product_service_categories` where `name` = 'Utilities' and `type` = 'expense' and `created_by` = 79) as `exists`", "type": "query", "params": [], "bindings": ["Utilities", "expense", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 67}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 956}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.765397, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ExpenseController.php:67", "source": "app/Http/Controllers/ExpenseController.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=67", "ajax": false, "filename": "ExpenseController.php", "line": "67"}, "connection": "radhe_same", "start_percent": 85.914, "width_percent": 1.095}, {"sql": "select exists(select * from `product_service_categories` where `name` = 'Marketing & Advertising' and `type` = 'expense' and `created_by` = 79) as `exists`", "type": "query", "params": [], "bindings": ["Marketing &amp; Advertising", "expense", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 67}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 956}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.768986, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ExpenseController.php:67", "source": "app/Http/Controllers/ExpenseController.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=67", "ajax": false, "filename": "ExpenseController.php", "line": "67"}, "connection": "radhe_same", "start_percent": 87.009, "width_percent": 1.868}, {"sql": "select exists(select * from `product_service_categories` where `name` = 'Professional Services' and `type` = 'expense' and `created_by` = 79) as `exists`", "type": "query", "params": [], "bindings": ["Professional Services", "expense", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 67}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 956}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7766562, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ExpenseController.php:67", "source": "app/Http/Controllers/ExpenseController.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=67", "ajax": false, "filename": "ExpenseController.php", "line": "67"}, "connection": "radhe_same", "start_percent": 88.877, "width_percent": 1.439}, {"sql": "select exists(select * from `product_service_categories` where `name` = 'Equipment & Software' and `type` = 'expense' and `created_by` = 79) as `exists`", "type": "query", "params": [], "bindings": ["Equipment &amp; Software", "expense", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 67}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 956}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.780544, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ExpenseController.php:67", "source": "app/Http/Controllers/ExpenseController.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=67", "ajax": false, "filename": "ExpenseController.php", "line": "67"}, "connection": "radhe_same", "start_percent": 90.316, "width_percent": 0.988}, {"sql": "select exists(select * from `product_service_categories` where `name` = 'Miscellaneous' and `type` = 'expense' and `created_by` = 79) as `exists`", "type": "query", "params": [], "bindings": ["Miscellaneous", "expense", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 67}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 956}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.784179, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ExpenseController.php:67", "source": "app/Http/Controllers/ExpenseController.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=67", "ajax": false, "filename": "ExpenseController.php", "line": "67"}, "connection": "radhe_same", "start_percent": 91.303, "width_percent": 1.31}, {"sql": "insert into `expenses` (`name`, `category_id`, `vendor_name`, `date`, `description`, `amount`, `project_id`, `task_id`, `created_by`, `attachment`, `updated_at`, `created_at`) values ('<PERSON><PERSON><PERSON> Expense', '13', '<PERSON><PERSON><PERSON>', '2025-07-31', 'this is the best.', '54000', 0, 0, 79, 'expense_receipts/**********_Class 5 math question.pdf', '2025-07-31 07:47:41', '2025-07-31 07:47:41')", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON> - Expense", "13", "<PERSON><PERSON><PERSON>", "2025-07-31", "this is the best.", "54000", "0", "0", "79", "expense_receipts/**********_Class 5 math question.pdf", "2025-07-31 07:47:41", "2025-07-31 07:47:41"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 994}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.840753, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "ExpenseController.php:994", "source": "app/Http/Controllers/ExpenseController.php:994", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=994", "ajax": false, "filename": "ExpenseController.php", "line": "994"}, "connection": "radhe_same", "start_percent": 92.613, "width_percent": 5.97}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.861562, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 98.583, "width_percent": 1.417}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 1598, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 1180, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}}, "count": 2780, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create expense, result => true, user => 79, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1712826364 data-indent-pad=\"  \"><span class=sf-dump-note>create expense</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create expense</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1712826364\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.750789, "xdebug_link": null}]}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/expense_receipts/1753947979_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/expense/store-ajax", "status_code": "<pre class=sf-dump id=sf-dump-1404520889 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1404520889\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1652012442 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1652012442\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-246872898 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">13</span>\"\n  \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54000</span>\"\n  \"<span class=sf-dump-key>vendor_name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Parichay Singha</span>\"\n  \"<span class=sf-dump-key>bill_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-31</span>\"\n  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"17 characters\">this is the best.</span>\"\n  \"<span class=sf-dump-key>attachment</span>\" => <span class=sf-dump-note title=\"Illuminate\\Http\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#167</a><samp data-depth=2 class=sf-dump-compact>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"25 characters\">Class 5 math question.pdf</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"15 characters\">application/pdf</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalPath</span>: \"<span class=sf-dump-str title=\"25 characters\">Class 5 math question.pdf</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">hashName</span>: <span class=sf-dump-const>null</span>\n    <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"12 characters\">C:\\xampp\\tmp</span>\"\n    <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"11 characters\">phpC5E1.tmp</span>\"\n    <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"11 characters\">phpC5E1.tmp</span>\"\n    <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\phpC5E1.tmp</span>\"\n    <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n    <span class=sf-dump-meta>realPath</span>: \"<span class=sf-dump-str title=\"C:\\xampp\\tmp\\phpC5E1.tmp\n24 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\xampp</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>tmp\\phpC5E1.tmp</span>\"\n    <span class=sf-dump-meta>aTime</span>: <span class=sf-dump-const title=\"**********\">2025-07-31 07:47:41</span>\n    <span class=sf-dump-meta>mTime</span>: <span class=sf-dump-const title=\"1753948060\">2025-07-31 07:47:40</span>\n    <span class=sf-dump-meta>cTime</span>: <span class=sf-dump-const title=\"1753948060\">2025-07-31 07:47:40</span>\n    <span class=sf-dump-meta>inode</span>: <span class=sf-dump-num>9007199255087085</span>\n    <span class=sf-dump-meta>size</span>: <span class=sf-dump-num>831289</span>\n    <span class=sf-dump-meta>perms</span>: <span class=sf-dump-const title=\"33206\">0100666</span>\n    <span class=sf-dump-meta>owner</span>: <span class=sf-dump-num>0</span>\n    <span class=sf-dump-meta>group</span>: <span class=sf-dump-num>0</span>\n    <span class=sf-dump-meta>type</span>: \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n    <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>true</span>\n    <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>true</span>\n    <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>true</span>\n    <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>linkTarget</span>: \"<span class=sf-dump-str title=\"24 characters\">C:\\xampp\\tmp\\phpC5E1.tmp</span>\"\n  </samp>}\n</samp>]\n</pre><script>Sfdump(\"sf-dump-246872898\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">832164</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryZdUrl9idS7rAtfre</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlRLVXZySTZDYUFLcnBiZkFJaUp2Y2c9PSIsInZhbHVlIjoiUzEyMHZ4YSt2bFlIKzZ4MXBnT2FQUmVGc0RsbEtTazRaYjFaQWxSTHRQenl5TzVyTW5wMlRibmlYYXRvaVlWOVFXNzY0dmM1dWVJZEo1L29DVzJ3WTZ3YUZRSE5NOC95TXlXN3BVc1hZZmRBNzdpRGtwRUpDdjArcmtWSjYxMnMweVhLTVNyZnRsS3RZWWxnMXU4bVMvZmovdFRXRW1nVkVyemxSMWNkUUdVem1Ea0RBUEJ0cTFpY2x4UHZyUkxsSEVFVjEvUjdLdldTSVlwcENwa2l4YUVnRUhoK3paTnQ2MHhodEYwS3oxTlFPV0Y4S1NQeU1ITmNwcG5PQnlpRWdBT3hydkcvZUZMa3dzMmJQN0RYQUpwRDBjU2p4SXBrY2plc0pscWYzSVNoT3kvVzl3TlNkaTN4a3BHS0Eydmo2aTFwa3RiWTNlN1Z2YjFTLzFkaXJLd2szTVR6UVVtODRYODNRUC9NV0dQMGhWdWxYTjUwWnRxWWkvanF6MHZtb2JzcSsxMW9NOEVZU1RKYjNQNkcrYi8vc3pRSHFGem9Gb25wUzl0aUNzOXlmRWpaU2RPcm9BWlZJUzZPSTRsVWdCTnVZSmF1TytXeWNXYjFFa0VFUHQxNkJsNHR3bVNvZ3UxUlA5c2ZkbjlZc3U3QWdlYnhiOGIwNHptSGJsTHgiLCJtYWMiOiI3MTBkNDQ1ODcyMTM3NTVjOWFkOGJlNjJiMmFhNzg0MDZiYjAxY2ZiYzNhODliZGRiYjgxZTcxYWVmZDQ1ZWNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkNvM2Vzemg2MEpEL0FjeUdpcml2bGc9PSIsInZhbHVlIjoiSkFPM0pQMTA0TjVZVnVQVCtobWVPUmoxWGoyOXR6Wnlma0xLY2Y2eHRjcGl1STdrYXEvb3p3TWFPWUxqUjFiVkFYbTFrb0JwRnRUOEFqNXlOdmJzd0tEUVRIUHRGYlpSeUdHTHZFMnJXTnl1VUJLZmVxR2s4WEZ4OUk0WVhXSkRVRGhSalA2bG15UUNHR3JBNmNNdU9sRmJobGtWTjc0R1pXNkhYRDM1bXhwS0ZRTm9YdjRFY2U1WXFHMzZZTHZPZlNCcEZVbUJiYjFuMTlTL3JXbUhRdjQ3VnpwQWN4N1NXbE5LeUozWDR1RXRmS0RMazUxK2szcC8vd056NU9GVDZJZFlUdVZUSmpicDZpQ3BsTWdGNjRFU1JsRVFlTzVDbWgwM0tjWEtOTUt4NHlPSWpiR0daQnA2VXhENXRDUFZPUWtjZGlqaE1FQ0J6MVNYRTk1aUpVR0NtWkIwS2hsa3d1Z1IvRUVEWHpCbnYxY1dIcWRhdGFWUEpaNHpKY2pBa1lZbVN0SEhGOW5QNXphVm92UlNzUHpqRjdMQXZpaUJFSUltd2FhNDVIS21zTU0waHFVTFR0UU50YU9QR1F2dHVRVElZU2NhbFJuMjYwdXBud2RvditJRFlYc2pKbGx5R09MRUxNSGRjSG1pbnVoK2gwZTJuOFgzQ3VNUG1jWkYiLCJtYWMiOiI4MmY5NGY5YjIzZDQ1MTMxZDliYzg4ZTQ0MjE5YjBkM2U2NjA1YzE1MTcxMmRhYWRlYzhhNzhjNmNjMDQ5NTA1IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlRBamxxOG9SREN4UGRLQzFFVStZWGc9PSIsInZhbHVlIjoiRWRDNDRLTFNuK2JUaTZOc24vTVluSkVRNUJRSGRVRGcvcjFJVmFacGMwM2l3dlJWM1pUOWtzVms3U1JsL1dqS2FWUUF0aU12TVVuNzI5bk5mMFFnVytsUm1ZdGYyS2ovK3YzeVpKVUZ0SGlaanFJbWM3OURjanc3ZThDbHkvUmdrWHRsVDFMZHhCL2tyZUFEa0dmdWhOeXVlMmFjMHdpUVB2d09vU2Z1SGxTVllhUDd3d2V5NVBMZlg0Zno4a085bG9reE13VnlwY1l1cDhxaE9GbjNzbGdZUWYwdEozQWJNaktDVlM5bmE0enBVUUxwcE5DUWs2azFuaU1UWHQwbkRwS1h1d1dSUVJ4THNhSndsekF1K0NrV3NqdE1OWDRud2NsSzZYdSt2dFhPd21weHVFVEN1WmpFdG9waDM3OVBGTDBIS1JuT0VMTjhDdmFlNzNjWjh6NmtlNEExakRsQ3YwWkVHSHhkTjdBbEFyNHBOWStKb0xzaFFFRXNmb3oydURkcHhWUUZPaDhWeU5tQ1NUaWt1RngzWFQ0UlNieGtUaittNFpMQ1RreUMwVi9YaytLTjZIUGJDOEdrTmk0UnNwZ01vbEZHRCt2QWhrZE1rYWZ2Um5LeE1ZUFF2U3ZYMDZkY0dwYTV1b2lvdVo2WXNzaW5qMXF1QS9FTnhvWEQiLCJtYWMiOiI2YTIzODEzZmY2Y2JhYTRiYjRlNjQxNjNjNzczODg0ODlkMTlkYzViZmIxNDA3Y2FlYjNkZmJmODM0MWRlMWY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21K3ZmFzZyr4s2pH9aKWRqwEKhgdUCnDfhl6jx78</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-362811533 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:47:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlA3ang0emltcjllMngrdk8rY1FlREE9PSIsInZhbHVlIjoiMmViUFU4RTdnN1JUV0Y3UUIyU0xGVzVzRVZ3MDlSNDVtQ2k1ZVV1L1IwOG9ySmV0OWw3eEhzb2xJeXB0R3E4Um9CV2p5b3EvZnJmWkJ6bE9TSGVXVjVFbDB1enhHZUs4aUlEcjZabWkrb3NzYnB1RDEvK1VYOVRDT2hKQ0wvUHVaSkVlcllsaXN3c0RIdzhrQWV6MUpibVRBVHUxeTQxUkE1MUsvNU44ZCtzQjBRWTdVVDViQ3Y1bFVxSFJrRWtPSHRhUFlQZlo0aHRPd1o5a3VnandLTFJkVG5RVEwvVDVUeUdjNEd1NkljNFAwTzdjMVhpb2RsRkZvWUZONHdhWXNMMUZpWU9jNkNGYU44dTIzdXhvOTUyNGp5a3pvZkdhSndSMnh5UndOeUczRzZmWXFadGFQMG5qU2svay9VSkxmalJQYXFjQm1oeWhuWkh1MEdFb3JsM2tlUjhDM3hGMVNsMEJFbEd1cUVuS2NISSswL00xNHNMVTVBNVJ6dmt6SG1KYm1vTWY5WlFqcDBXeEY2UGpNZGpvUWo3NlB0Z2phS29hcnIvUThWcTU1VmVzR1FTbzFUY3BSeHBPb2JnUGpaK1NCN0I3MDlablA1S3RETmVLd1h5ZTZ5ZHNsOHZldTc4OG1Bd3lwc1AzV3FLVVh3cTk5b0N5UWdvYU5pRHciLCJtYWMiOiI2Y2E4MTFmNzU1NzdmOGM1ZmM4ZWFjN2QyYWQyYjY0YzU2MzhmOTQzYWRlZGQ4OGU1M2NkZjRiN2Q5ZWQ1Y2E1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:47:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IlRsaVlsMzUwa09lSk5UNGRLMXhubWc9PSIsInZhbHVlIjoiaVBTZHZDTVhVeWhJcFZuSVJPZGMwQ0JSVVlvckhqQWhoazdDeUFDYmtyVVVLTENOYm1HOGZRMjhZdVVWMGUrZnJ2K0ZZclJUWEN5dnZLTU1LTzhwVjVkV3ZMcU1VQ1JORldEZUZoOHB4Wlp5N1QyUmxpVlZ1NGIxUStyY1BwNG9SbnR5M1dMWTdsZVNBVVZPRzRlZ0U1U0pqUUdZampNd2FKQVE2MkUwRE1sSGVjcXA1N2kvSWdTV3M3c2twNVMzWWxWS3JrUGFKNGV4SXZUZmJlM1AvbnNqSlAySVBOT1dLdnBrcm1LN2luZkNzelFEdjNTWVplZWwxOFBJUVRQU1duWDdoMUJGeWl4M2NMVk9zK1BEdk1pTjFib09mZkdnakVIVnVnU1BGeFdqTjZuNEk0aXJ6bVZIZlk0ZVAzZ1RYTlZOaW5MalhQY2s3c21iZDdadENwTktrRFpZZEFnazg2cU9pL2lsMXczdFJPcUVZKzBzaG9LL1VGNGVPZXRHMStVNkJNYlZleElxM1dpQVR0WkI1N2VwQjNRZVRqdXE2c3d1S1ZWUG5sRU0yQUQwbVB0Y3pxVXl5K2pHR1ZqZkZmU3F5N00rQW42L0FTNjBmUGk0aTV4cDBFMy96SXB5UzdYNmtpNEpabXFQRVNXRkFEZlFibjJvOGQvZFE3bEUiLCJtYWMiOiIwNjdkMmJiYTg3Mzg4YWNhZGI3MTg1ZDRiOGM5ODU3YmJmOWMyMmFiZGM4MDE3MGRiZWQwYTMxYzgwZGEwZDNhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:47:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlA3ang0emltcjllMngrdk8rY1FlREE9PSIsInZhbHVlIjoiMmViUFU4RTdnN1JUV0Y3UUIyU0xGVzVzRVZ3MDlSNDVtQ2k1ZVV1L1IwOG9ySmV0OWw3eEhzb2xJeXB0R3E4Um9CV2p5b3EvZnJmWkJ6bE9TSGVXVjVFbDB1enhHZUs4aUlEcjZabWkrb3NzYnB1RDEvK1VYOVRDT2hKQ0wvUHVaSkVlcllsaXN3c0RIdzhrQWV6MUpibVRBVHUxeTQxUkE1MUsvNU44ZCtzQjBRWTdVVDViQ3Y1bFVxSFJrRWtPSHRhUFlQZlo0aHRPd1o5a3VnandLTFJkVG5RVEwvVDVUeUdjNEd1NkljNFAwTzdjMVhpb2RsRkZvWUZONHdhWXNMMUZpWU9jNkNGYU44dTIzdXhvOTUyNGp5a3pvZkdhSndSMnh5UndOeUczRzZmWXFadGFQMG5qU2svay9VSkxmalJQYXFjQm1oeWhuWkh1MEdFb3JsM2tlUjhDM3hGMVNsMEJFbEd1cUVuS2NISSswL00xNHNMVTVBNVJ6dmt6SG1KYm1vTWY5WlFqcDBXeEY2UGpNZGpvUWo3NlB0Z2phS29hcnIvUThWcTU1VmVzR1FTbzFUY3BSeHBPb2JnUGpaK1NCN0I3MDlablA1S3RETmVLd1h5ZTZ5ZHNsOHZldTc4OG1Bd3lwc1AzV3FLVVh3cTk5b0N5UWdvYU5pRHciLCJtYWMiOiI2Y2E4MTFmNzU1NzdmOGM1ZmM4ZWFjN2QyYWQyYjY0YzU2MzhmOTQzYWRlZGQ4OGU1M2NkZjRiN2Q5ZWQ1Y2E1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:47:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IlRsaVlsMzUwa09lSk5UNGRLMXhubWc9PSIsInZhbHVlIjoiaVBTZHZDTVhVeWhJcFZuSVJPZGMwQ0JSVVlvckhqQWhoazdDeUFDYmtyVVVLTENOYm1HOGZRMjhZdVVWMGUrZnJ2K0ZZclJUWEN5dnZLTU1LTzhwVjVkV3ZMcU1VQ1JORldEZUZoOHB4Wlp5N1QyUmxpVlZ1NGIxUStyY1BwNG9SbnR5M1dMWTdsZVNBVVZPRzRlZ0U1U0pqUUdZampNd2FKQVE2MkUwRE1sSGVjcXA1N2kvSWdTV3M3c2twNVMzWWxWS3JrUGFKNGV4SXZUZmJlM1AvbnNqSlAySVBOT1dLdnBrcm1LN2luZkNzelFEdjNTWVplZWwxOFBJUVRQU1duWDdoMUJGeWl4M2NMVk9zK1BEdk1pTjFib09mZkdnakVIVnVnU1BGeFdqTjZuNEk0aXJ6bVZIZlk0ZVAzZ1RYTlZOaW5MalhQY2s3c21iZDdadENwTktrRFpZZEFnazg2cU9pL2lsMXczdFJPcUVZKzBzaG9LL1VGNGVPZXRHMStVNkJNYlZleElxM1dpQVR0WkI1N2VwQjNRZVRqdXE2c3d1S1ZWUG5sRU0yQUQwbVB0Y3pxVXl5K2pHR1ZqZkZmU3F5N00rQW42L0FTNjBmUGk0aTV4cDBFMy96SXB5UzdYNmtpNEpabXFQRVNXRkFEZlFibjJvOGQvZFE3bEUiLCJtYWMiOiIwNjdkMmJiYTg3Mzg4YWNhZGI3MTg1ZDRiOGM5ODU3YmJmOWMyMmFiZGM4MDE3MGRiZWQwYTMxYzgwZGEwZDNhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:47:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-362811533\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-594269339 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"71 characters\">http://127.0.0.1:8000/storage/expense_receipts/1753947979_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594269339\", {\"maxDepth\":0})</script>\n"}}