{"__meta": {"id": "Xceb3c8b3c4dbfcd7fd70c06e67ab8481", "datetime": "2025-07-31 06:31:14", "utime": **********.79215, "method": "GET", "uri": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943472.520764, "end": **********.792208, "duration": 2.2714438438415527, "duration_str": "2.27s", "measures": [{"label": "Booting", "start": 1753943472.520764, "relative_start": 0, "end": **********.445562, "relative_end": **********.445562, "duration": 1.924797773361206, "duration_str": "1.92s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.4456, "relative_start": 1.****************, "end": **********.792213, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2ExRwuySBGKsu2V3mGADAqyYOQW0QvViAibN4ViD", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "status_code": "<pre class=sf-dump id=sf-dump-1441732949 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1441732949\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1833386638 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1833386638\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1617541298 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1617541298\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1155550865 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155550865\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1704962997 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1704962997\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1200720540 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:31:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllpNTh5dzdEdS9DUGZXc1FnTTZaYmc9PSIsInZhbHVlIjoiSnQvOWs2TkpXTTV0ekVhZlVoUkp3czhpNkhMMFZrcCtoTFRsWGNqSVJPMk1iV290aUpQSU9peXR5WnBRQXF3TnhPaG9iSkQ0RUJ5dHZpYWkzZlhUOEducU9YUi9MNjJPeU1RQ2NEaGwvKzdaVDRIekR1QjJsb0c2aXNJeHZ0OWRZa0tRZ1ZoT21YQnZTN2lPamdSWVBvMVpVNUJXdUFYL0E4N0NTa3JCeWk1UEZxQitqa3FpN0RhRXhYd1gyL1RPWWxpWE9kNnM3M0lvd295OFpWNS9NbTJqT1lSeE1SVVd4UHcrd3Y4UVMremNoL3hWeW1nS2VsdUk4SmVjbldvUEpwTFhRMlQ2MzF3Zk9jaEZOT01la2U5UldUVDByVjV2VnAvZE1MMUd3TlBIU2ZvM2MzV0w1RCt5QlBvTGpyZTYzT01KR3pobm5OeWd5S0R4WG1CckhjZWNtTnFkcEg2aHpmQVZtaDJoS2RIRkRQOFRIV2d4RjBXMjQxYlJtUG4zcnFDRDduRmt5ZFJwR2J1clhpRmJ6KzhqVjQwbnlxUmxPM2RjRVVRQmNjOWJmRDg2YlhIb2dTZGtEdUdrS3plRExDMjdJVWlEUVpPV0tOdS83ZmR0MDdZbFRucGpBS1NLT09qTjljSm93djhXbG5UOGI0WHd4cEI2bG5hU0lFODEiLCJtYWMiOiJjZThhOGEwNzJmNjUzY2ZmYWMwZTE5MmZlYzI1MzUwNTI5NjdkZTViZTgwMTZmNjFmNDA2Njc2NDUwMTJjODBhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InBwMXFQWkQwNENYVDhCR0Rqb0FlMVE9PSIsInZhbHVlIjoic25pQ2xQWk1iV3J0aGJNUkxGczVxMm94Z2QxdW9Pdk1na1pmVEM1WW1Td2tnUkdCODgxN0FwelNjNmdhb3NxL3dLMDVGaXpIaUZZZjVVNnhWSEY4OWRFQUJIWUxmVUU2dUUyWVFrYTA2cGlMUGFJQys2bUl5ZnYxOEx4OHJmTVUzSGtBOTdtY1NCdkoxQTc4WEFQQnV6dHAyd2VOSk1FcEE4aUZHbWNBbmQvenpJeG8vdzI1dHdKZ0ozNCtYWUp3eCtHQkc3cWsxanBCMDU4cndBY25PNUlabDByQnJqc0FuejVoN1BOUmcxM0ZGL0szdTR4aEc1OVhLallMVXNCM3RwRS92OEo5a3crNGg3U3ZWaUE2L3VVRTZYTzJEMysrOHJlVFcvZ1JqeFBvcTdxT2x0NVhZbmM5U2FmdTFOU2JQMVlmWEF5OVY5WWtleTBxYlJzV2FRL0hzTUtrYlFwWEEvY2tUMDNFdGk2aTF5L1gyd1N5K21RYWJzMy9NVGZWWUxPb3hweWxsbGQxTENKY2V5cU5XSzRibER1MG4xUnlHYno2TVZzbm9SQnBSVm92U0E5L1lwaldNYmZOY0cvSUpjR1BwL0ZuWjRLMm1wZ1YxZVdYNllzOW0wNzBPYmpjZG9zSFpJL05EaW5VcEc0c01SQ2pNWWZ5Q1o2YjJscjQiLCJtYWMiOiI1N2YxYmNlYmE1NzY1MDdlYjc2YmI1ZjFlZDZiOWVlNTVjMGUyZTIxMmY5MWQ0YTIwZjc1ZWVhNTQwODdjODI4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllpNTh5dzdEdS9DUGZXc1FnTTZaYmc9PSIsInZhbHVlIjoiSnQvOWs2TkpXTTV0ekVhZlVoUkp3czhpNkhMMFZrcCtoTFRsWGNqSVJPMk1iV290aUpQSU9peXR5WnBRQXF3TnhPaG9iSkQ0RUJ5dHZpYWkzZlhUOEducU9YUi9MNjJPeU1RQ2NEaGwvKzdaVDRIekR1QjJsb0c2aXNJeHZ0OWRZa0tRZ1ZoT21YQnZTN2lPamdSWVBvMVpVNUJXdUFYL0E4N0NTa3JCeWk1UEZxQitqa3FpN0RhRXhYd1gyL1RPWWxpWE9kNnM3M0lvd295OFpWNS9NbTJqT1lSeE1SVVd4UHcrd3Y4UVMremNoL3hWeW1nS2VsdUk4SmVjbldvUEpwTFhRMlQ2MzF3Zk9jaEZOT01la2U5UldUVDByVjV2VnAvZE1MMUd3TlBIU2ZvM2MzV0w1RCt5QlBvTGpyZTYzT01KR3pobm5OeWd5S0R4WG1CckhjZWNtTnFkcEg2aHpmQVZtaDJoS2RIRkRQOFRIV2d4RjBXMjQxYlJtUG4zcnFDRDduRmt5ZFJwR2J1clhpRmJ6KzhqVjQwbnlxUmxPM2RjRVVRQmNjOWJmRDg2YlhIb2dTZGtEdUdrS3plRExDMjdJVWlEUVpPV0tOdS83ZmR0MDdZbFRucGpBS1NLT09qTjljSm93djhXbG5UOGI0WHd4cEI2bG5hU0lFODEiLCJtYWMiOiJjZThhOGEwNzJmNjUzY2ZmYWMwZTE5MmZlYzI1MzUwNTI5NjdkZTViZTgwMTZmNjFmNDA2Njc2NDUwMTJjODBhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InBwMXFQWkQwNENYVDhCR0Rqb0FlMVE9PSIsInZhbHVlIjoic25pQ2xQWk1iV3J0aGJNUkxGczVxMm94Z2QxdW9Pdk1na1pmVEM1WW1Td2tnUkdCODgxN0FwelNjNmdhb3NxL3dLMDVGaXpIaUZZZjVVNnhWSEY4OWRFQUJIWUxmVUU2dUUyWVFrYTA2cGlMUGFJQys2bUl5ZnYxOEx4OHJmTVUzSGtBOTdtY1NCdkoxQTc4WEFQQnV6dHAyd2VOSk1FcEE4aUZHbWNBbmQvenpJeG8vdzI1dHdKZ0ozNCtYWUp3eCtHQkc3cWsxanBCMDU4cndBY25PNUlabDByQnJqc0FuejVoN1BOUmcxM0ZGL0szdTR4aEc1OVhLallMVXNCM3RwRS92OEo5a3crNGg3U3ZWaUE2L3VVRTZYTzJEMysrOHJlVFcvZ1JqeFBvcTdxT2x0NVhZbmM5U2FmdTFOU2JQMVlmWEF5OVY5WWtleTBxYlJzV2FRL0hzTUtrYlFwWEEvY2tUMDNFdGk2aTF5L1gyd1N5K21RYWJzMy9NVGZWWUxPb3hweWxsbGQxTENKY2V5cU5XSzRibER1MG4xUnlHYno2TVZzbm9SQnBSVm92U0E5L1lwaldNYmZOY0cvSUpjR1BwL0ZuWjRLMm1wZ1YxZVdYNllzOW0wNzBPYmpjZG9zSFpJL05EaW5VcEc0c01SQ2pNWWZ5Q1o2YjJscjQiLCJtYWMiOiI1N2YxYmNlYmE1NzY1MDdlYjc2YmI1ZjFlZDZiOWVlNTVjMGUyZTIxMmY5MWQ0YTIwZjc1ZWVhNTQwODdjODI4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1200720540\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2ExRwuySBGKsu2V3mGADAqyYOQW0QvViAibN4ViD</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}