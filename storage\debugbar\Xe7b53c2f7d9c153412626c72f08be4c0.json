{"__meta": {"id": "Xe7b53c2f7d9c153412626c72f08be4c0", "datetime": "2025-07-31 06:25:32", "utime": **********.416163, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:25:32] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.40448, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943129.828417, "end": **********.416212, "duration": 2.5877950191497803, "duration_str": "2.59s", "measures": [{"label": "Booting", "start": 1753943129.828417, "relative_start": 0, "end": **********.124874, "relative_end": **********.124874, "duration": 2.296457052230835, "duration_str": "2.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.124908, "relative_start": 2.2964909076690674, "end": **********.416217, "relative_end": 5.0067901611328125e-06, "duration": 0.291309118270874, "duration_str": "291ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50618648, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.373753, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03882000000000001, "accumulated_duration_str": "38.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.255494, "duration": 0.00626, "duration_str": "6.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 16.126}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.298126, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 16.126, "width_percent": 4.225}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.310959, "duration": 0.0291, "duration_str": "29.1ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 20.35, "width_percent": 74.961}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.347069, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 95.312, "width_percent": 4.688}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1513978936 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1513978936\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1101671632 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1101671632\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2051576467 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2051576467\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1915983546 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/pricing-plans/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InpxMTh4M2pZalBTSVRBc0ZaVkFQZmc9PSIsInZhbHVlIjoiN01Yd1BYdDVUbG5RSFJ0S1RrNytXWnFTSzFzL0dMcDZ1T3RUc2tJVW1jZEhxd2FQa25YaitWODByNFROL1IvMEdFMWtOeFFFZHp0djN5ZFk5MG5ycmwyWEJ2RUE1ZjdFWUxaanpPRVRFS2l1R21xbm9Rb0pVcVNDYnVxK25MTUhTaklBT3RycHB3eW40djdJSmp3aWN3NGE0NmIwMUwzdVdod1NqZmV1bmlBSWJpMUwrQ3lVZjVxQ1U2OStiamNwVStrMkY1MWk0MU5PTnhYbjcrNytJRDQvdi9nRUc0M1M3NEd6dU9EUHlzRVc3cU1POG9PZitBWDg5SzlmWUNDakZ0WWd6WG1ocEFnNTZIa28yWXVLYWM2VkpTZDNLR25ZL2RtcDA4SDRNUGsra2NDMmtvcXdGemNLQnZMcmlsRnhSa2RpYklWNldwdU1wa0NZSjl0VmNSZTl2YjBIeVE2dGtwNW1ZejF6RkZRMlhBTXMzMHVzSHJiZU8vUU9BWXV3WWN1WjJkN2dmcDIrSkw5bjhmUVdOYS9OK2liSTdvL2FLOUdIL29pN1V6SC9lQU9sYXdIMVVvNDh0QVBRb3NZNW15c2I1dXlJZXhjeWhNYzNVR2lMYnpYRE9xR2FoSko4TjJuOUtFTy9ya0V6Y1R1VmVYdkx3Z090TDd4eXhoTlUiLCJtYWMiOiI5OTI5ODY1Yzg5ZjExNzA3ZTViZDc5NjRmNDM4MWIyMzFjMjVlY2ViOWZkOTYxYzY5NmY2MDMwYTczMTQ1NDM0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkxFbEpqS2NCK2NvNjNkTmQyN0h6QVE9PSIsInZhbHVlIjoiUXArbjU0WWt5RkhXcDJoY0tHM0cwYTg3aVBhK3l3ZGk2U0RNS2NKZ1RJSnNnbkZBdk4vU0EvR2drSVdLMTdEaDlDTGpqR1h3MEUrRHM2RE4yWmg1Nk1ud3lmT09yUml5cE8zMThDZlRtc3pBeXNtWmVOVEk4Y1d4OEIyZXpoYjUveUpRV2dUK2VQaHN2SWoxUU1DZlVmbzdKM3Yzem1Bd0g0QVVjUEhCbkU3SnFBT3NHcExUSmtsTUJVMk1HNElmdk9NcHJ1MkpjdDFaL0k0U3JGNUFzU0JWOURlNFRBVUpjRUwxMjNucXVuV0dRNlBBSm4rUmQ1WUIzTUZJTXpnYitoK1VqR1lYL0JxdG9ocExYbEtQMkNKZmlsQXdPaVUwOUNSOFZZMVZzV1pzL2dTQVBpOVJjRks0Q3F1YzBnNElBVWUvLzNrc0puOUIvUmhtSUhhSy8yT0NlSXAvdGFROW50YzBUeTRVdXhMR0NOZEZYQU1jc21QSlpESElmU3RDQU5KdUpnNnV1dE5wc1pRZVBzTFB5Z2IrM0hSUnNROGtHL0xjYm1ySWZyWTkrRmZnckQ0c1htTjlXd0d6b0tTUjVhR0tSRWFIaklrNlhVU1VMK05YTW43dnlyVUlUN1FpRWQ0VW9tcWpnS0dyZjhLcTMwK1pCSVN2R2FBVlJ1ZmkiLCJtYWMiOiJjNzY0ODE5NmVkZTUzZWQyNjJjM2NlYmYxMGMwNjdhYjlmMzA4NWFmNzA4MDNkM2RjOTgyYWVhN2FiYTJkNTExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1915983546\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1690665155 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1690665155\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-566502022 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:25:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVkMXo0cHlJM2N1QVJmcWhnYkkvWnc9PSIsInZhbHVlIjoiNTZmRitpRHBoRHpsNnVyTkJnSTdZUnpSWndGVnAraGtLeTltRkx3QzBoV0VaUE9YNzQ3RTA0UXFjSWl4TERZUlRXNktKZEZkcHdwMEI1bzNzaFBvU2NzbE14dUZKaVBjVmRGcmJkYkUxUmMrckwwOVhTYm9jQnV6UitvTkZobi9UNi9MQkQwa2oyRWFNanRBdzFIUGVQb0VRaVIrNkIzMFgraG00WmVZZzQ4WnhnYnowQzFCT0JpRDZ4aXpCVlFPNVEzdkMvb3NRckhMSVUrMjliaWJiMGU2YnROSGtXTXV4VEtwTnRaT3JiaDRteU9YTGNwTVBwb2orMlp0azhwN1hWS29QK2hNL1Z0Zm8wVngzbXVYcEIrdW1YNm9seG5Bak13YkFscVJyWHlmdzZTSjkxZEpzclhNUnFtMDIzMTF0MVBRa3k1b2czVUlSNlUwTlgvN3RSM0V0REs5SmpHSWV2dXBzNGxUUDVBcHc2S0xXTDZHQUxjNWpzbE92UHMzNEpwUlZvTHIxR1IzbXY0RXZRclp4a1BQZ0Z6Q2UwMTU1SGZBWE1aaTNtZHlMaE5BVlZ6bnIyWFNmTDlqNk0wYXBEY1krL2ErdXBmT2EzUDIveTFUWHQ2cm5Qc1hIeTdFVGUrTmdZSkxjQXJvUzhDeXR1Q3d1UUc2RzdPejVNWmQiLCJtYWMiOiI0ZDkyNzlhOTE1ZDI0ODVkNjRjMmYyODkxYTIzOTYzNTJiNTUwODJiZDY5NDlhZTA1NzRmYTNjMDIzNzI5NTdjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:25:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkdKUzEyNC84Z2xBWkhQYmNocGYzcmc9PSIsInZhbHVlIjoiQU1XUHZkTklSeXpFd1BwUGNGQjFQUDB4TE9NbG5BMHg0S3grQlFmZmtSNllETThMZzB3UTdMYStuVW1pczd6alQzQ3RNT3hGakJibXNvWTVqbGJ4dXpocDFOQkNVenR0SG1jRHpJRE9Pd2NCbk9MQ2QzZWlpRmVETnRiVno4ZXFxbE9FTkRzcTVpeEJWZ3QzUXYrV013R2xvUUxhQXRmaUpFVFU2WVNQZFcwRjVKb0lFaFJUQVRiKzgrVFpnaXVtZ1Nwa3lmY2dIME9xTG44WWZWTEJWb1g1OUgwZjlrZHZmTEdrWXkrcVhza2JmaVllZmhnSUxTQmxDalFmSGg1WTVjK21jOUJHMHhWSTRyVG1XVnlmSFQveUxMWU5pdHdpOVM5RWc3aXlJWGtXQzRXaHY3dWZQazVqdnBLZXB4eXEvcmdpTkdOSngra0pZTDdPQ0d3NDU0MWNpdUgybjhteDFEZ3k0WXNDanhCeFRHV1VzVk4zWUFkb284Yk5lS0dGWFl5UUpWRnFxU2VYcjdiNjBvV0hXT28reGFlTUk2OFVVVWt6bWI0eU5Kb0QzMHZya2xBODd2MldSd0NoangxUy82eThWYnhNY21HR01jRDNIZTNZdHFMaSsrdnh4R1J2LzBRdThYMlE1b3ZUYXUvMHpxaXlKM0RxS3NzclFjRFoiLCJtYWMiOiIyNDFlNDgwOTcwZTcxYWNmNDRhNmRmZGQ3MDU4ZjU5Yjk5ZmVhNzdmOTZjYTQxOWVkZDIzYTI2NmFmMzEwNzkyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:25:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVkMXo0cHlJM2N1QVJmcWhnYkkvWnc9PSIsInZhbHVlIjoiNTZmRitpRHBoRHpsNnVyTkJnSTdZUnpSWndGVnAraGtLeTltRkx3QzBoV0VaUE9YNzQ3RTA0UXFjSWl4TERZUlRXNktKZEZkcHdwMEI1bzNzaFBvU2NzbE14dUZKaVBjVmRGcmJkYkUxUmMrckwwOVhTYm9jQnV6UitvTkZobi9UNi9MQkQwa2oyRWFNanRBdzFIUGVQb0VRaVIrNkIzMFgraG00WmVZZzQ4WnhnYnowQzFCT0JpRDZ4aXpCVlFPNVEzdkMvb3NRckhMSVUrMjliaWJiMGU2YnROSGtXTXV4VEtwTnRaT3JiaDRteU9YTGNwTVBwb2orMlp0azhwN1hWS29QK2hNL1Z0Zm8wVngzbXVYcEIrdW1YNm9seG5Bak13YkFscVJyWHlmdzZTSjkxZEpzclhNUnFtMDIzMTF0MVBRa3k1b2czVUlSNlUwTlgvN3RSM0V0REs5SmpHSWV2dXBzNGxUUDVBcHc2S0xXTDZHQUxjNWpzbE92UHMzNEpwUlZvTHIxR1IzbXY0RXZRclp4a1BQZ0Z6Q2UwMTU1SGZBWE1aaTNtZHlMaE5BVlZ6bnIyWFNmTDlqNk0wYXBEY1krL2ErdXBmT2EzUDIveTFUWHQ2cm5Qc1hIeTdFVGUrTmdZSkxjQXJvUzhDeXR1Q3d1UUc2RzdPejVNWmQiLCJtYWMiOiI0ZDkyNzlhOTE1ZDI0ODVkNjRjMmYyODkxYTIzOTYzNTJiNTUwODJiZDY5NDlhZTA1NzRmYTNjMDIzNzI5NTdjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:25:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkdKUzEyNC84Z2xBWkhQYmNocGYzcmc9PSIsInZhbHVlIjoiQU1XUHZkTklSeXpFd1BwUGNGQjFQUDB4TE9NbG5BMHg0S3grQlFmZmtSNllETThMZzB3UTdMYStuVW1pczd6alQzQ3RNT3hGakJibXNvWTVqbGJ4dXpocDFOQkNVenR0SG1jRHpJRE9Pd2NCbk9MQ2QzZWlpRmVETnRiVno4ZXFxbE9FTkRzcTVpeEJWZ3QzUXYrV013R2xvUUxhQXRmaUpFVFU2WVNQZFcwRjVKb0lFaFJUQVRiKzgrVFpnaXVtZ1Nwa3lmY2dIME9xTG44WWZWTEJWb1g1OUgwZjlrZHZmTEdrWXkrcVhza2JmaVllZmhnSUxTQmxDalFmSGg1WTVjK21jOUJHMHhWSTRyVG1XVnlmSFQveUxMWU5pdHdpOVM5RWc3aXlJWGtXQzRXaHY3dWZQazVqdnBLZXB4eXEvcmdpTkdOSngra0pZTDdPQ0d3NDU0MWNpdUgybjhteDFEZ3k0WXNDanhCeFRHV1VzVk4zWUFkb284Yk5lS0dGWFl5UUpWRnFxU2VYcjdiNjBvV0hXT28reGFlTUk2OFVVVWt6bWI0eU5Kb0QzMHZya2xBODd2MldSd0NoangxUy82eThWYnhNY21HR01jRDNIZTNZdHFMaSsrdnh4R1J2LzBRdThYMlE1b3ZUYXUvMHpxaXlKM0RxS3NzclFjRFoiLCJtYWMiOiIyNDFlNDgwOTcwZTcxYWNmNDRhNmRmZGQ3MDU4ZjU5Yjk5ZmVhNzdmOTZjYTQxOWVkZDIzYTI2NmFmMzEwNzkyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:25:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-566502022\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-59315788 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/pricing-plans/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59315788\", {\"maxDepth\":0})</script>\n"}}