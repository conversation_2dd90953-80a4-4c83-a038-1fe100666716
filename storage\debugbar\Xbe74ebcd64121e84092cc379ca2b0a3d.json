{"__meta": {"id": "Xbe74ebcd64121e84092cc379ca2b0a3d", "datetime": "2025-07-31 06:14:10", "utime": **********.98856, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:14:10] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.981671, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753942449.153511, "end": **********.988616, "duration": 1.8351049423217773, "duration_str": "1.84s", "measures": [{"label": "Booting", "start": 1753942449.153511, "relative_start": 0, "end": **********.790206, "relative_end": **********.790206, "duration": 1.6366949081420898, "duration_str": "1.64s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.790239, "relative_start": 1.636728048324585, "end": **********.988621, "relative_end": 5.0067901611328125e-06, "duration": 0.19838190078735352, "duration_str": "198ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45942816, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.008220000000000002, "accumulated_duration_str": "8.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8898969, "duration": 0.00551, "duration_str": "5.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 67.032}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.92783, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 67.032, "width_percent": 12.53}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9399588, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 79.562, "width_percent": 11.557}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.948132, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 91.119, "width_percent": 8.881}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1865326999 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1865326999\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-868253685 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-868253685\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-66083255 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-66083255\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1311677653 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im9xT1BBOElDL0pZcTJvZ2ZBOWhoaFE9PSIsInZhbHVlIjoiS0JtS25XNFF3UlQyVFNoVUVrd2xjbDd1cmU5ajVpaVY1THM4ekJ5UVU2eGlnQkp3OGdvSVNhaGhjbnFGZG9qVFlSVklGTTB1b1BnelVZYkRtV2ZsTm1sRDFGOU45QTh2OUtIcWRmeUNYb0Y5aSsxS3ROT3diMkpITHByT3N0UUFBbUtZZGlTTURKMTFaWktrRU9zN2pBUFM1UVJFeS9DRDYxUUpFaENZMGg3TFZFK3pQbFNwUk9MTnpZNlFjMm1Yc0ppZnIremZpR3Q1Vkk3VWJUMTgrQkltOVh1SGhUZGx3cVRlWHVQbkJoNGRlVmpKd1U4Q0N6SFNrR0NrRmtqOTdCaG9ybW9jQXBhN1lPSkg5VXJMY2ErcXE3S283RktZdFJPTUluZ3ZCTW85Y0hvQ3ZaZFVqZ0ducDJYQ1hDc2crVE9ZZzhvRjdpWXNuS2xmck45UUNDOFBaTzM1ZjlTTU92eHNmbzZjM2hLOU8zTTIvRURmVktpMkVjeVBKSnZEQ0VLdnZSRVNnRmJXQmRubzRJL3A3RTY0dDU4ZHRYajNwV0MrSW9sYWhUcHR1UnBrM1dXNTFnL3F5dVNCTThCNzBZeGhDeFRkV3R2Rm14WWxialNkMWU2YllSR1o3dWJDVi8wUHpHamdqL1ZoMm05TlNJV3RqMW5Tajc2R1RWSGgiLCJtYWMiOiIzMDRlNTVhMDZhMWEzZGQ2Y2M2MGJkYzRhMjQwMzFkNmM2ODY4ZDk1NjBiNGM3MjQ4YzEwMmM2ZDMyNWRmODNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imw0dkVKc1VrOTFydnloUjNyakY3K0E9PSIsInZhbHVlIjoiZ2Z1RlBOOEN0d3RsU2pPNkJ4amR5L0dmQ3VmaEc0ZlNzUXQ0aUdnZkpRa2Y2ZGRqeG9McytuRTdSMzlJaE1qNk5LL3p1dHFielNNMmR2c0R6eTRGZjVqSWpMMXlXR3d2WVpsNk5mOVlkVEhybC9CanNNV3J4NnFjeEwyN00wNVczVktoQlZUVXlmRUhKYU1mTDErQ09jUFNNbE45TUFKRmx3bHZqeDRqdm5LcHc3M001M3BrZEsvMXYvaU9EQ2F6eWF6SU8zUHQxMXZHcnB1N3YzVm1Rek1qVTFXQlFES0czVS92TFNWYXZHQXo5RkZDdHVsa1FkUGNBNVBKanVBY1g5Qkx1Wjl0YU1qckIxRmtLYmgyYkxkRTB0c3VCQTBqTjNEOElmQ3pWL28yVUltVVJmWTlaMXF2YW8rRmdVSTg5TjhXMHlpYkRMUVhLby9EYUEzVVRZN2RtNXJab2RaRHRkdDVjVHhCQUxJcHpQakxWNTJSWmN0S3N0TjlEM0xyc01BQWp5MHF2dmVqTGZqK1NyS01kYVdja1F6RER1VmMxeGUyTmRUUTFCdkFzOHlETkZidlN0NmlKK0xjMlgvSWZOS1JsdzRxdlIxMkp6SHlZMldnK2ViU2cyMnkxeStFQXFkYkFrd010SytZdDluOUU0ZUIrQVdSZ2YrUzRTT04iLCJtYWMiOiJjMGExYTAzZjJjZGZhOGI1NmEyNDhlZWQzOTVhMmMxYjc4NzMxNzY1OGYwMGU1ZTQ5NmRhMmI3MTM5NjBlOGQxIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IjIwN0N5R21vMDJLeDBpd3NRZUJXemc9PSIsInZhbHVlIjoiUExVTk02THdMOE1VZ0ppdFQ3LzJPMkVYVnBxcHZDWVJqV3QxY0oyclNIOHFoL3llRHJlblpiaU5KeTlQNEphVGVpRFdCU1l5RDROQzhyc1p4RFRZUDloaVNtTGxLbGlhN28vL0hZL2FsVW05UGtrNEhxSnQvQ1NGMS9yRkNNOXpocnM1T0VYTVZmQlhhZFE5NXcwdUF3R0JtRi90dHBadDdDemNFUjJJNmVQSTU3WUFzeWFPUUlERlBVV3cvSy9PS3Y4NmtIMmh2NmJXUDBwRjA5YVIvYmcrZjA1Z1E5WExYbmMzaG9DWnVFQ0MwNGEybEZhSTFsNndTbWRKcStmdEVvOXdXTVJQSDJVcHFicjZWQTBtZ1A1S2VBaXQxTGxTaUg2Wi9VanJBdE1DSEdJTVFZNCtxbVoyRHphV1hiVzZYekNHM0MraDQvU0NrWkZqYVh0a21LSFVNQ1JZVTFIaUhtNVgxbTV4QzFKZGViTFMvMmxzZTRhT25JM3dJY2Erdk5MdmIwd3VjNTJ4eXRPb0FMMGR4anJNemEweTJya3Z3d09FazBHc0o5VW02Z2IzYWpGWllBS1pEVlVNYk44eFZoU1JzUkJodWVKcmh5TmwzQnV5RW9td2RXVnpQbEdQRGNDMVFIRWJSV0kvQ0gwb2sveUFVSTdKVG8ralQycjIiLCJtYWMiOiI3OTkzNDZkYjgzMjk1MWE1Yzg2ZTc2OGFlNWI2YWRiNDIxNTg0Njk4YWU5MTQyZWY0YzFjN2ExODQ5ZTRlYjViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1311677653\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1173116400 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5LJND5bUclZnLzzClCMEaxKzZ5Yt8AAmGjprz0I</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sUbnPperdA1I3PaRxBpMGV4YsR5AVGq0ERh2LBdJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1173116400\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-926290431 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:14:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNJRlhzWDIrT1dGL001RFhwV2hDaUE9PSIsInZhbHVlIjoiMk1qTTg5S2RpNnIzODNqODZxU1FQdDRUUkNFTUZuR2RlN2oyemtzZTk1amMyeWNyZ1ZFdkQ2K1plY2I5OTUxWGk2Ry80NXlLM05sVnk1eFFZNkFUU0hCUVRpOERQK2ZwMjRCUVhTb2o2bDlLb0dqcm9oUVk4aG9INEZkcStVdHlaSGw1cFBLZ1JhRytjMk9oWnBkQXRtNWx2eG9pTnZLSm83RHdPN1h4ZTRCN3k1ZER2NjdhcXppOEliNWJxSVdMR1p3SjhiSUh1RmtRVCsvK2o1cTlPdFBOa3FjRUd4SEQveFEvRjU5UnhaQjRIdXFGdVF5LzZuRThwQnN5RGZielE0RHFKaUQ5Wk84OVk4R3RkZDYyak4vOGdkS0YxRmN3eGVBcUxHMWdRa2xSVFVwbUMyOFNzRkZJRnR5NDlvYjlMZG9JU0dUTVFMbHBUSFN4SlVGdWhWZytlV3Q4U0tLWExQZmg5TldyMGFzVVFmZ3liQkFFaUtSUE1kalRDV2llUnZ3MXVPR2o2dHR5RGtnTE9UemlwaEZnRGw0ZyszWHErQWpyVlAvUXR3SFQ3aXZ0VDE3Z0Z0eGlYMnczNUVkU0JKOW95TDU1VTlKbzJaNWdpZTMwdm1ZRlIra2NkeEs0N3duT3ZqR3U1WVlEbFFCenhxcnNrLzc3MDhsdHdKdisiLCJtYWMiOiI1MDc3NzVmNTk0ZmVjYmZlNWM0MjFmNmQ4NDcwZWQxNzBmNzY4NDczZGJkYTI5MjIzZTRjZjM5OWViNGZjMTUwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:14:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6Imd6K2ZsellVZE9CSUJKOWpnQ3lvSEE9PSIsInZhbHVlIjoiOTJVQ0NPNzNFK3g0Y2x5RHlUam0rejRKN1NhMXA0bk1jblFOK2R2WEpTSUdOZEVSRU1WZWhxZ2psdEtyS3ltS2pxM0NDejlMMHQ3K1FDaWhaQjRjQUdLL1M4VVFqUjlxS3dsbjhVTWRxTlJLYUdSWDlScTdEVE1HZUFzTGpKODFncnRuNjkvdmZ4VXhjNGk3eWJSQkVmckRxcVpHL1d3amlnN0diNy9GYm9sOXlMSlRwR3FpQjJMV3U3RXRjMC9nRVFwQ0ZJK3lBeVJ6MnpGczdnbmlSdjVCblNTZDZobGs0VFRXWWo0MlFzN0xxdTBSa3pjZ3Z0bm5jYTJIb2VETmNrTVVYTnpZbnpLbFZGUUpqS1ByUnZNMVdJY01sSVVvWURuODlscWtETC83SUFSdmIyelpEZ1p5OUZOOEIramp4OG9Neldyd1cvNk13Nk1vdlJlSTlodDVWa2lrbVZ1SnY5b1ZTK09JT0dKdzV0M05ja29tM3hHeEhaN25VVFlCc3F1V2p3MXRCRVRaMTF4WlVUYWIrZUd6aWRYc2dLYmc3ZXdWZmlQeXN1a2xzckpSalFTenlqMnU4Tk1rTy9QQUhLMTE2RzZLRHZVQUN4cU1XSVd3WHdTQVFhcW91ZW1leTRZOVVOWmNZNUFNQ1BvUjhFYkJET2xKY0JqemdqY3AiLCJtYWMiOiI0MzUzNzc5ODI4OTZhOTU4NTc3NWU5YzZlMTU0Mzg4NWEwMjY4NmIyYjAwOTY0MDBmOTU4OTMzNGJmYTRlMjhlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:14:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNJRlhzWDIrT1dGL001RFhwV2hDaUE9PSIsInZhbHVlIjoiMk1qTTg5S2RpNnIzODNqODZxU1FQdDRUUkNFTUZuR2RlN2oyemtzZTk1amMyeWNyZ1ZFdkQ2K1plY2I5OTUxWGk2Ry80NXlLM05sVnk1eFFZNkFUU0hCUVRpOERQK2ZwMjRCUVhTb2o2bDlLb0dqcm9oUVk4aG9INEZkcStVdHlaSGw1cFBLZ1JhRytjMk9oWnBkQXRtNWx2eG9pTnZLSm83RHdPN1h4ZTRCN3k1ZER2NjdhcXppOEliNWJxSVdMR1p3SjhiSUh1RmtRVCsvK2o1cTlPdFBOa3FjRUd4SEQveFEvRjU5UnhaQjRIdXFGdVF5LzZuRThwQnN5RGZielE0RHFKaUQ5Wk84OVk4R3RkZDYyak4vOGdkS0YxRmN3eGVBcUxHMWdRa2xSVFVwbUMyOFNzRkZJRnR5NDlvYjlMZG9JU0dUTVFMbHBUSFN4SlVGdWhWZytlV3Q4U0tLWExQZmg5TldyMGFzVVFmZ3liQkFFaUtSUE1kalRDV2llUnZ3MXVPR2o2dHR5RGtnTE9UemlwaEZnRGw0ZyszWHErQWpyVlAvUXR3SFQ3aXZ0VDE3Z0Z0eGlYMnczNUVkU0JKOW95TDU1VTlKbzJaNWdpZTMwdm1ZRlIra2NkeEs0N3duT3ZqR3U1WVlEbFFCenhxcnNrLzc3MDhsdHdKdisiLCJtYWMiOiI1MDc3NzVmNTk0ZmVjYmZlNWM0MjFmNmQ4NDcwZWQxNzBmNzY4NDczZGJkYTI5MjIzZTRjZjM5OWViNGZjMTUwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:14:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6Imd6K2ZsellVZE9CSUJKOWpnQ3lvSEE9PSIsInZhbHVlIjoiOTJVQ0NPNzNFK3g0Y2x5RHlUam0rejRKN1NhMXA0bk1jblFOK2R2WEpTSUdOZEVSRU1WZWhxZ2psdEtyS3ltS2pxM0NDejlMMHQ3K1FDaWhaQjRjQUdLL1M4VVFqUjlxS3dsbjhVTWRxTlJLYUdSWDlScTdEVE1HZUFzTGpKODFncnRuNjkvdmZ4VXhjNGk3eWJSQkVmckRxcVpHL1d3amlnN0diNy9GYm9sOXlMSlRwR3FpQjJMV3U3RXRjMC9nRVFwQ0ZJK3lBeVJ6MnpGczdnbmlSdjVCblNTZDZobGs0VFRXWWo0MlFzN0xxdTBSa3pjZ3Z0bm5jYTJIb2VETmNrTVVYTnpZbnpLbFZGUUpqS1ByUnZNMVdJY01sSVVvWURuODlscWtETC83SUFSdmIyelpEZ1p5OUZOOEIramp4OG9Neldyd1cvNk13Nk1vdlJlSTlodDVWa2lrbVZ1SnY5b1ZTK09JT0dKdzV0M05ja29tM3hHeEhaN25VVFlCc3F1V2p3MXRCRVRaMTF4WlVUYWIrZUd6aWRYc2dLYmc3ZXdWZmlQeXN1a2xzckpSalFTenlqMnU4Tk1rTy9QQUhLMTE2RzZLRHZVQUN4cU1XSVd3WHdTQVFhcW91ZW1leTRZOVVOWmNZNUFNQ1BvUjhFYkJET2xKY0JqemdqY3AiLCJtYWMiOiI0MzUzNzc5ODI4OTZhOTU4NTc3NWU5YzZlMTU0Mzg4NWEwMjY4NmIyYjAwOTY0MDBmOTU4OTMzNGJmYTRlMjhlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:14:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-926290431\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-658894852 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-658894852\", {\"maxDepth\":0})</script>\n"}}