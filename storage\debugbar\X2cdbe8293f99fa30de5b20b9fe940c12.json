{"__meta": {"id": "X2cdbe8293f99fa30de5b20b9fe940c12", "datetime": "2025-07-31 05:11:02", "utime": **********.031501, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:11:02] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.025312, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.080291, "end": **********.031529, "duration": 0.9512379169464111, "duration_str": "951ms", "measures": [{"label": "Booting", "start": **********.080291, "relative_start": 0, "end": **********.899589, "relative_end": **********.899589, "duration": 0.8192980289459229, "duration_str": "819ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.899604, "relative_start": 0.8193130493164062, "end": **********.031532, "relative_end": 3.0994415283203125e-06, "duration": 0.1319279670715332, "duration_str": "132ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45913392, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02572, "accumulated_duration_str": "25.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.957355, "duration": 0.023739999999999997, "duration_str": "23.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 92.302}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.000266, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 92.302, "width_percent": 3.149}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.005589, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 95.451, "width_percent": 2.216}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.009266, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 97.667, "width_percent": 2.333}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-35276978 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-35276978\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-32546705 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-32546705\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-120976172 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-120976172\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1308054977 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjZuL3JHeTBmMC9xcmE2TWxoV0t6Zmc9PSIsInZhbHVlIjoiSGdIMW52djF2WVZKQm5naG1sUkora3RXb3VIU3FVT3hoZU5CSlllejdmazBpWEVYcnZnRWlxazN6WjJSNlZyUVZCZmI0ZGxERDFSTmtpWGhxUkI2d1NjcmlZMU8wSy9XaHg3R0d0QXZ3R2VzMFZDVW4xcFd1TUY4dzJkVGFXeVlReFR5L1pMQTdwVG5XMFF1MEpqRUN0NW5TRDVaOHRpZTZsdHIxTXF4VGhWREYwbDB2bFZwdFdXQ1VWQm1lRytEVVJBZFgvWVZnbHNkdmlrMkNranBJbVNiaVdTN2FRYWYySXE4MzFjN0dtR0lmK2c0Q0MvaGtGVVEveFZBV1VqeGtRQnZ3R3lZMUxBblZhR1VRY25EMmd3c1E2ZWFqclR0cGdtbjhJNCtwcmRKMmdMQjhrR3hwS3lhU2VKMi83Qi9FWElwUXZvSjh6dkNZN08vNVRHWDM0dzhDcDlCZHlvMnF4WGpoY05lQnpzU0xadXRCMThXeGdrZ2M1WWNwQ29PaWxPNThKOURObWRQWWl6c2NKeEs4UXpGRHFoanJSRGhCSW42UTRWVUZrc3czNzhKTVJDeTc5OHVuM28xK21Sc0Y5bDlBUjZtR3hDOWVncWs1Wm1PYmJwdDg2Q3QwcWVoTjhqVDFmMm5DSFplc1NTTFVnNjZwSHlRUVZzdDB5TnQiLCJtYWMiOiIzNTA1YWZlMjViNTUwZWFmMjM1ODU2NTdiZTM2ZjE5NjEwMDgyYWZjZWYzMDI1ZTJjY2Y2ODJlYzE4YzI4NzVlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkJIcEJVTXlwYSt0UmxjamVtcS9kMUE9PSIsInZhbHVlIjoiMVY2cXpSTjNNZm02ZDYvL2VFeVNTK3IzUEg3SC9rMGFHT1FCU2labCs2SzdrWjEwMHBwYTA2a042Q01MM2hjS3puNWhwdVJsSldWRy9CUFBCZFM1KytNQlQ2SXYvbUVuYmYrdmI2NUltUWlYYVc2RTNaSUM3NWZRNkN0TVBiMFVZVDNqRGp2c3VFcW5PT3g3V3VNMkdwZmlGMStGeithZ09ONGNsbGxES2hMeEhxS09Mb0trcXF4a0Nia1RDQ0Z2bnpoVUtNMGM5NGN0MUxTaVQ3M0dodjhiKzRleGpKUXprSmphWHFTOVczRzNzNE5NbUF0Qlp3bnRxRDI2K1ZHRytJYjhkSEFHYWVnQmdYQnhrVHVqbVQ4U3JPL2htVUFVV3hCMm5qa0VPYnp4d2dJZmZqMjY1UE1pVDNndWk0RDUrcW5CUHk0UWJSS0sxTk5hN3BtNXp2QTd0VnhmTXNsTElSS1F3cXFkVnB3MTZvWlFuUjJYa3I3TEc0MW5ZNVlWRnFIbzRkcVNFcnNDYjJOOEp6amhoWFZ0WWRyTjk1ZThoL21FbHNpenp6ZW1GVFpqQTkwdUxScmFNczY2OElVemtEQXE2Mlk2NmlCRTM5NmRkM3U0aUdZdlo3MXp1eEQ3Q3VqZGZVNXdMcWN4SVB5ZDRScW9ud21jL2FOalQ4d1MiLCJtYWMiOiIwZWVhZjc3OTU3NDhkNGNjMmYzY2JlZmJjZTQ1ZWUyNTUzNTA2YTMzNzNmZTc4ZTA1ZmY2NzkzOWM4MTU5MWJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1308054977\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1775597358 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXcIUJMcydAyqtnEKWEUzIzoSEq1x7CzphaduhZT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1775597358\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:11:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndZN284RlYxNXU2YTVWY3d6Z1pnaXc9PSIsInZhbHVlIjoicG9wNXd3M2d2MWlaZ1B0bExGV00wT0RvdDZvd2NCWEREMytHaTFqZVRjWnB0U2NqYUJnN3Vpd3pWdmw4eW83V1NhMTVObk9uT0FaeG9RZVdkOFdQby8rOGhxT2NUTnVHOG4wYzVhSmczbzdaWXgxQm16UEE2eTZ5ckZNTnFwR0FRZkMvamo3M3hndnc0RVR0aktXRmloSytKcW1MM2tSbzdMOHd6aFVvRWpkT2VmamF2SUlXSkhtRlphVWk1d2sxSWFBc0dzV2ovSGtIbWh0Rk9wZlVqaGJvUEVCTDFsd1BaQU5GaUdFcHpTaGs2YWVOSTN6cXNyQWRHRkVZQlR3cHBLcE9sek1RZlNZUSsveWFmeFJMMzExS2lJeUNrSnZtd2VmRVhOaFN5TzA3UkRFdGZ5Q1Izdno5MnZ6VWczVkZtVU85WTkrMjBWcUhicVB1ckd0L1ZnaitsZUd2RUpJWWlYTCtWSzZOSzRZdmJxREhBbXQzQ1cwVDZBOW1hWm1qOEtrWlk2bFFzZGVqS1pzZEw0OCtzTGlDRGN2RytwSUhxOHpoU3J5dUl1c2hWYS9mSk83cVJqV25WZ3lSMEpYTExNb0Jzamlja040d1Q5bzFZWUxoZ2ZyV2E4ODBReXdQbDFvUkZZN1pOZlNDcTdGb2hISHN4VGZVRm9iMFNoWlYiLCJtYWMiOiJmNjk2NGJlMmFjOGYyNTJhNjM3MjkwMmVjNGI2NzQ3NWZmZWRiOWZkNjVmZjNmZmZhNTEzMDZkMDcxODhiMWUxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:11:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkVBdjZrcE44WC92MUtaTmVPSU1EdWc9PSIsInZhbHVlIjoiUTVsa0JFL3lreGsvUnBpN3dYSHJiQ2E2WUdJSGI4bEZmUit5SkFhY04yVVU2cFV2K292bC9YbjlXazdyTXkxK3MxMThvd2o1MnRLRWVxTjZtMDgvZDZ0S2doTzZ2RzVyeEI1bzVWSWY0bnE4cHhWMTlPMi9UZEJ4b1ZYSnBwU29vbllhTG9pbjBxRDVMT3ZEMDFhYzlGZnFuc0EvRjVNV002UE1kTTEvczV6V2NOQTlEV3dJTHdzRTdLWkpjY3RyN0lKZzA3OG9FZUtzKzdDS3FOa3Y0d3JHSTQ1aWRvN05CZkVHRXpzRzNkRlU1L0VYcW5QM0JoSnc5U1Z0SVYwSzdJVUNFWGxIWnF2RmNadU9CT0ZvSFhsYXM0UUMvaVNrUTMzRUNoMVJyRkE3R1V0UWlJRWRLUFhQS2UyMUNldHU2WnVDK1p5R0t1eEx4eHlLd2VyWFRVV1lSVjBtMDF1U0VaSDd6aTI4cDBocUViekl2d2JLSVRzZ3cwejZFa0pKUURiRGpuVTRHekZiYlg1bGRrV043RUFSTFR3YXk2TXlmeW1NdjZKOUpKR3N1WXd1MGpKMG1rNFkyYWJVbVR1RXk0ZGhyckdCa3RVa29wNkZsRUZVOUEwUmpsNHBEK2VrNHBKUEYxQ00rWEtRbkNIeHFSMFRSYzc0d0R3dzJPUHciLCJtYWMiOiJhODU4MzYzM2Y0MWU3ZDY1NDgxMGFmOTAxMzhiNDNiODI3NWQ0NDg5YTRhYzRkZDAyNDAwNDdkZWE1MjI1NDI0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:11:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndZN284RlYxNXU2YTVWY3d6Z1pnaXc9PSIsInZhbHVlIjoicG9wNXd3M2d2MWlaZ1B0bExGV00wT0RvdDZvd2NCWEREMytHaTFqZVRjWnB0U2NqYUJnN3Vpd3pWdmw4eW83V1NhMTVObk9uT0FaeG9RZVdkOFdQby8rOGhxT2NUTnVHOG4wYzVhSmczbzdaWXgxQm16UEE2eTZ5ckZNTnFwR0FRZkMvamo3M3hndnc0RVR0aktXRmloSytKcW1MM2tSbzdMOHd6aFVvRWpkT2VmamF2SUlXSkhtRlphVWk1d2sxSWFBc0dzV2ovSGtIbWh0Rk9wZlVqaGJvUEVCTDFsd1BaQU5GaUdFcHpTaGs2YWVOSTN6cXNyQWRHRkVZQlR3cHBLcE9sek1RZlNZUSsveWFmeFJMMzExS2lJeUNrSnZtd2VmRVhOaFN5TzA3UkRFdGZ5Q1Izdno5MnZ6VWczVkZtVU85WTkrMjBWcUhicVB1ckd0L1ZnaitsZUd2RUpJWWlYTCtWSzZOSzRZdmJxREhBbXQzQ1cwVDZBOW1hWm1qOEtrWlk2bFFzZGVqS1pzZEw0OCtzTGlDRGN2RytwSUhxOHpoU3J5dUl1c2hWYS9mSk83cVJqV25WZ3lSMEpYTExNb0Jzamlja040d1Q5bzFZWUxoZ2ZyV2E4ODBReXdQbDFvUkZZN1pOZlNDcTdGb2hISHN4VGZVRm9iMFNoWlYiLCJtYWMiOiJmNjk2NGJlMmFjOGYyNTJhNjM3MjkwMmVjNGI2NzQ3NWZmZWRiOWZkNjVmZjNmZmZhNTEzMDZkMDcxODhiMWUxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:11:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkVBdjZrcE44WC92MUtaTmVPSU1EdWc9PSIsInZhbHVlIjoiUTVsa0JFL3lreGsvUnBpN3dYSHJiQ2E2WUdJSGI4bEZmUit5SkFhY04yVVU2cFV2K292bC9YbjlXazdyTXkxK3MxMThvd2o1MnRLRWVxTjZtMDgvZDZ0S2doTzZ2RzVyeEI1bzVWSWY0bnE4cHhWMTlPMi9UZEJ4b1ZYSnBwU29vbllhTG9pbjBxRDVMT3ZEMDFhYzlGZnFuc0EvRjVNV002UE1kTTEvczV6V2NOQTlEV3dJTHdzRTdLWkpjY3RyN0lKZzA3OG9FZUtzKzdDS3FOa3Y0d3JHSTQ1aWRvN05CZkVHRXpzRzNkRlU1L0VYcW5QM0JoSnc5U1Z0SVYwSzdJVUNFWGxIWnF2RmNadU9CT0ZvSFhsYXM0UUMvaVNrUTMzRUNoMVJyRkE3R1V0UWlJRWRLUFhQS2UyMUNldHU2WnVDK1p5R0t1eEx4eHlLd2VyWFRVV1lSVjBtMDF1U0VaSDd6aTI4cDBocUViekl2d2JLSVRzZ3cwejZFa0pKUURiRGpuVTRHekZiYlg1bGRrV043RUFSTFR3YXk2TXlmeW1NdjZKOUpKR3N1WXd1MGpKMG1rNFkyYWJVbVR1RXk0ZGhyckdCa3RVa29wNkZsRUZVOUEwUmpsNHBEK2VrNHBKUEYxQ00rWEtRbkNIeHFSMFRSYzc0d0R3dzJPUHciLCJtYWMiOiJhODU4MzYzM2Y0MWU3ZDY1NDgxMGFmOTAxMzhiNDNiODI3NWQ0NDg5YTRhYzRkZDAyNDAwNDdkZWE1MjI1NDI0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:11:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}