{"__meta": {"id": "X596b6239fef040274b86f11a784c449a", "datetime": "2025-07-31 05:35:18", "utime": **********.581024, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:35:18] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.57155, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753940117.16463, "end": **********.581097, "duration": 1.4164669513702393, "duration_str": "1.42s", "measures": [{"label": "Booting", "start": 1753940117.16463, "relative_start": 0, "end": **********.272453, "relative_end": **********.272453, "duration": 1.107823133468628, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.272496, "relative_start": 1.1078660488128662, "end": **********.581103, "relative_end": 6.198883056640625e-06, "duration": 0.3086071014404297, "duration_str": "309ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45913808, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.032010000000000004, "accumulated_duration_str": "32.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.42818, "duration": 0.02689, "duration_str": "26.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 84.005}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.500689, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 84.005, "width_percent": 6.217}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.517049, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 90.222, "width_percent": 4.936}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.528024, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 95.158, "width_percent": 4.842}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1563646348 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1563646348\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1455576466 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1455576466\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-741758696 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-741758696\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-889070927 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjUzWlI0cG1BZGdjZHRKNi9Nb1ovNUE9PSIsInZhbHVlIjoiSkRxRHZ4LzRRQytBQ3dCa0pvTDVkcDdzOGZNOWoxRWpZbzIxc1pJY3dEVzZpTVZmazBHeTJMdUVBdzVBTTRDRWRjdWNTY0doUEpuTlBNbjhvUzlKZzdBSjdwc21rVjFCbDFFWHYyVk1obnlRSFJXRUhLN3E2ODdGdWZ4MWJxbkNOemFETjJQbHNHWXlJWk5FV3VLWkE1V0duWjhiQ0w0WTlMbnBabFk1K3NzaWMwOUlRdTluOG1qcDYxeThRRjlYdm9Cb2d5NWhySjM2S2FieVFNS1lGbVp5MW04U3RBdVE3Qit6c3BBNVlrV0hwRTZRMFRVeWVEUS8rbGVJZDhSZGV2QUFQOU9Sc2hqaDA2aHFOMGF2YTk4L2VsVStYMDhNcWZnaVNKNkdxZHhHS2ZhK1lFODIwdmxzeWRuY2VEV0pLaTNzWW5yU2dBSmwxbVR0QmtMTWtBS0I4VCs5TjQ3T0NDRlZRdXNNWTh6bitOOWtvR0xKZ2syZUpSS3pBN2xOdnJMaGFBUzVwQXJoVnlSaEx5YndaLzBaRW5BeVB4eGlDTjdOaHlLeDdpaFJTWVZ4SlBGQndndWxNamVCTElRVTNoOStKK1FZVGtock5LZ0I1Q2ZZSXZFdnM1eWVYNnFyRklGcTlmd2JFSzRoVkRFdFZGK1RmaU0wT1V0TU80NjEiLCJtYWMiOiI0Nzc4MjRlMDQ3ODRhZTgwMjRkNmIyNjY5Yzg4ZGZkODk3NzU1ODEzMmI5ODkyMDJlOTk3YmEzMDBmZGU3OThiIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im1OL0J1emM0cHE0OHFjNEp3enM1WHc9PSIsInZhbHVlIjoiTXh6WjJKeDVVOHJycTJyWERyT3ZBSVVkSisyQW8vdWZ2Rzd2NlNxQ2NBM3ZINmNrUkplZTNBSEdzUDFmam9SUjR4SjRxZFNCODRQRkhETklZN2ViV0M4Z1RndTZCbzQyY1dKWkk1TEViQWh3N244Z1lJRHJ6TEhyT0tFV2F5MjVNT2hTaFFmQzBUbnRnYm1DUzN5K2Z1c1p3blQrdGhudi9IaEhBWVF5R1NrYzFyeS9Od3dFN0JWQkk4bzBFVWM4K2RSMHJ5VkpvWHM2QTlhUFYrTktnUTJmZDh6VkFKNUVZS3dERVpyU2t1Z2taZ1J3VHJLcCtueWF4YUM3dE9XcU90K2RkaUVyZHgzaVYyYzlyTUs4czd5bC9VR3dVd0s3djVPQkNZbURGZUYvL1RQU29WazRET3F3djdnV0tLWHZ1OW5CVnVhUTBZV2ZsZGpJYW9Ja1BKdVg2S3htS0s0WFlEbENnbU1ETlVLR2NsazAvN0JybnFmbk5yeCtIMXRxcWk3MkVsSE9XSHl3ekVwUnplUklIQVcybkl0WWlac0FhakFkeldCZEJ2UHdILzcwcTZINTdGVm9pR1IyYW5XTmUwcWRROU9HTlZGYnNMeTc5WmRLSGdhMzVxOVNjRlVaOVZtUW5pRm14NjN6TXVOOEZ2VWUxUE1qNTR0bERJUWkiLCJtYWMiOiIwZDM1MDVkNWI0YjRkMTkzNDliMDU4MDE4MDYyYzQ2ODgwMDAxOTg4YjBlYzM1NDhjNTA3YWU2ZTU3MTA0YmEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-889070927\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1899936891 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lOWuOQi5K5zalKq6lxC9bmXS2G5Yh3URND5pGg9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1899936891\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1953292245 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:35:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZDN0pTZ1p6b2Yzay9KOExncWFsZEE9PSIsInZhbHVlIjoiQ3IvRTlPcTZjY2lEckdzcXF4cEYwbityZjVUbjF5c3dvdGFqVlNheTM0SHQ4a2Z2Qng5R3JQc0Z1WkhJMEcweXhMZEIvT0gzNGFXOEcvV3NMT3pGKzJ4ZGd1ZkoxSEZtQUhDd1E0NHFmMHMzcWxWY3VHR0xuY1ZVS3RYWTFpdXlyRmJieHJnSjVhc1dyQUhYbFduRVM3T2tIdHQxVUtmazUzOWo5UVB0V3BiMEVaVmZLYzNHZ25zQklqU2R0Y284YWR0WVVubm9HNUIxaXZwVGlzR0hvbUhYTldZUkVULzFqQUk5SWIySUNSRlppYXRsd2Ruc3I2dmI4MkNPblZ0RGhoamNRVEpGTG5kbzExZXFUVWxLM2c2WFFSQU5VY0U0aWxhWEJuY1NYbWg3VHlFWCt4UUxFNHV0dDlnK1FsV2RlcFA1SzA4ZDJnRzhKampjZG9zZHJUUVV0NDdJL0FUTVV1TGRtYmEwK2FOZVlqN1BHbjNjNmhWSm5qODZ6NkNEZXhMS3JjVGVpN0VNclZYSFlVTkQxelFhWWNNQXA3UmxaY2krVVBuK3lRUER2T0NiSVBScUdKbjVxK3NwQm9lQ0kzMFZJTHpFVGpkcnZJbUlXclN4RzBiZWNGMlo5QjQ0U0ZzYlNBa05Kb2VwTnhWL0xXQ2FTZm1ueTRISkU4TVMiLCJtYWMiOiJjMzQ5OGZmNTA1ZmI5OGUwODFkNjFlNzE0MWM5ZWEyYTA1Y2I2ZmRjMDk3YTY1ZDQyZTkyN2VmNDM1MDZmMDVkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:35:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InJEcThJSkh5U3pxdU1OYm0xMGp5OHc9PSIsInZhbHVlIjoiWURqNkJ4YVZvRWNtSUhrdFpqeXJMSUdZbTFuZitOaUZPYzVvd3JlV25keHh6V3I1TDBtZFB4NDVRamZFc094bHFodnYrajR2c3RXTG1PYUI1UWdTZkEwZEozbnRoWitENCs0YkYybWI1ZnZURDZ2a2hiODRFbHFGcU53cmNIU0JsOVJlN0lNbVVoT3pEbXpMdWpFMWhxZmpjbExoS2pBQUMzL2pybXlPMGFMaGNPTkJKU2ZQa2RrK0FoN1lOZG5hcHNEdTdZa3dBSFY2dndMZnJBVUJpaGJsZVdsMG5lYVNKcFZrQnMvMEF1WkxaY093ZWlKY0FweEpEbTZ2ekxYQ01Zc2RFVWt2WFJacm1heVd5N2dxOC80eUVzNXR1Tjdwb2FxbmJrazVRWlZzckNPY0R0WlNYZDZvenprd1E4VXR1czNlNWVGOENzOHlhRHJNbzJNVmdlSjY0K0JLY2pyUUkyZkxpQzFCa3VNUnVWRkZ0YkE5a2FMcFFWN0ZmRkNjWmhqTVcrakxtVHVicjZFemFwMlJhMXowZVF4QU4vbDNrenV3YVE3eUNpUnh1SjFMR0REYmdNMlIrY3g5amhkQy9JREJhVmlWd0xjSnV3SnFqZERYdFdFWUJLRWxlZDFPOGdmKzdIUGNJZkJ2amlieTZJUStEQ21BZDBWdXUyVVYiLCJtYWMiOiI4ZTEyMDRhMDY4YTQ0YmY4ZjEzYmNmNDMwNTYyNjVjMTllOTY0N2U2NGM1ZDBkMDA3M2IwOTgyNmU0YjcxN2ZkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:35:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZDN0pTZ1p6b2Yzay9KOExncWFsZEE9PSIsInZhbHVlIjoiQ3IvRTlPcTZjY2lEckdzcXF4cEYwbityZjVUbjF5c3dvdGFqVlNheTM0SHQ4a2Z2Qng5R3JQc0Z1WkhJMEcweXhMZEIvT0gzNGFXOEcvV3NMT3pGKzJ4ZGd1ZkoxSEZtQUhDd1E0NHFmMHMzcWxWY3VHR0xuY1ZVS3RYWTFpdXlyRmJieHJnSjVhc1dyQUhYbFduRVM3T2tIdHQxVUtmazUzOWo5UVB0V3BiMEVaVmZLYzNHZ25zQklqU2R0Y284YWR0WVVubm9HNUIxaXZwVGlzR0hvbUhYTldZUkVULzFqQUk5SWIySUNSRlppYXRsd2Ruc3I2dmI4MkNPblZ0RGhoamNRVEpGTG5kbzExZXFUVWxLM2c2WFFSQU5VY0U0aWxhWEJuY1NYbWg3VHlFWCt4UUxFNHV0dDlnK1FsV2RlcFA1SzA4ZDJnRzhKampjZG9zZHJUUVV0NDdJL0FUTVV1TGRtYmEwK2FOZVlqN1BHbjNjNmhWSm5qODZ6NkNEZXhMS3JjVGVpN0VNclZYSFlVTkQxelFhWWNNQXA3UmxaY2krVVBuK3lRUER2T0NiSVBScUdKbjVxK3NwQm9lQ0kzMFZJTHpFVGpkcnZJbUlXclN4RzBiZWNGMlo5QjQ0U0ZzYlNBa05Kb2VwTnhWL0xXQ2FTZm1ueTRISkU4TVMiLCJtYWMiOiJjMzQ5OGZmNTA1ZmI5OGUwODFkNjFlNzE0MWM5ZWEyYTA1Y2I2ZmRjMDk3YTY1ZDQyZTkyN2VmNDM1MDZmMDVkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:35:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InJEcThJSkh5U3pxdU1OYm0xMGp5OHc9PSIsInZhbHVlIjoiWURqNkJ4YVZvRWNtSUhrdFpqeXJMSUdZbTFuZitOaUZPYzVvd3JlV25keHh6V3I1TDBtZFB4NDVRamZFc094bHFodnYrajR2c3RXTG1PYUI1UWdTZkEwZEozbnRoWitENCs0YkYybWI1ZnZURDZ2a2hiODRFbHFGcU53cmNIU0JsOVJlN0lNbVVoT3pEbXpMdWpFMWhxZmpjbExoS2pBQUMzL2pybXlPMGFMaGNPTkJKU2ZQa2RrK0FoN1lOZG5hcHNEdTdZa3dBSFY2dndMZnJBVUJpaGJsZVdsMG5lYVNKcFZrQnMvMEF1WkxaY093ZWlKY0FweEpEbTZ2ekxYQ01Zc2RFVWt2WFJacm1heVd5N2dxOC80eUVzNXR1Tjdwb2FxbmJrazVRWlZzckNPY0R0WlNYZDZvenprd1E4VXR1czNlNWVGOENzOHlhRHJNbzJNVmdlSjY0K0JLY2pyUUkyZkxpQzFCa3VNUnVWRkZ0YkE5a2FMcFFWN0ZmRkNjWmhqTVcrakxtVHVicjZFemFwMlJhMXowZVF4QU4vbDNrenV3YVE3eUNpUnh1SjFMR0REYmdNMlIrY3g5amhkQy9JREJhVmlWd0xjSnV3SnFqZERYdFdFWUJLRWxlZDFPOGdmKzdIUGNJZkJ2amlieTZJUStEQ21BZDBWdXUyVVYiLCJtYWMiOiI4ZTEyMDRhMDY4YTQ0YmY4ZjEzYmNmNDMwNTYyNjVjMTllOTY0N2U2NGM1ZDBkMDA3M2IwOTgyNmU0YjcxN2ZkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:35:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1953292245\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-894637341 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-894637341\", {\"maxDepth\":0})</script>\n"}}