<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the bookings table exists before trying to modify it
        if (Schema::hasTable('bookings')) {
            Schema::table('bookings', function (Blueprint $table) {
                // Check if columns don't already exist before adding them
                if (!Schema::hasColumn('bookings', 'payment_amount')) {
                    $table->decimal('payment_amount', 10, 2)->nullable()->after('time');
                }
                if (!Schema::hasColumn('bookings', 'payment_status')) {
                    $table->string('payment_status')->default('pending')->after('payment_amount'); // pending, paid, failed, refunded
                }
                if (!Schema::hasColumn('bookings', 'payment_method')) {
                    $table->string('payment_method')->nullable()->after('payment_status'); // razorpay, stripe, etc.
                }
                if (!Schema::hasColumn('bookings', 'payment_transaction_id')) {
                    $table->string('payment_transaction_id')->nullable()->after('payment_method');
                }
                if (!Schema::hasColumn('bookings', 'payment_date')) {
                    $table->timestamp('payment_date')->nullable()->after('payment_transaction_id');
                }
                if (!Schema::hasColumn('bookings', 'payment_details')) {
                    $table->text('payment_details')->nullable()->after('payment_date'); // JSON field for payment response
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if the bookings table exists before trying to modify it
        if (Schema::hasTable('bookings')) {
            Schema::table('bookings', function (Blueprint $table) {
                // Only drop columns that exist
                $columnsToDrop = [];
                $columnsToCheck = ['payment_amount', 'payment_status', 'payment_method', 'payment_transaction_id', 'payment_date', 'payment_details'];

                foreach ($columnsToCheck as $column) {
                    if (Schema::hasColumn('bookings', $column)) {
                        $columnsToDrop[] = $column;
                    }
                }

                if (!empty($columnsToDrop)) {
                    $table->dropColumn($columnsToDrop);
                }
            });
        }
    }
}; 