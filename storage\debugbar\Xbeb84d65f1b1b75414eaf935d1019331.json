{"__meta": {"id": "Xbeb84d65f1b1b75414eaf935d1019331", "datetime": "2025-07-31 05:52:43", "utime": **********.78307, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753941161.325008, "end": **********.783178, "duration": 2.458170175552368, "duration_str": "2.46s", "measures": [{"label": "Booting", "start": 1753941161.325008, "relative_start": 0, "end": **********.498896, "relative_end": **********.498896, "duration": 2.1738879680633545, "duration_str": "2.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.498941, "relative_start": 2.****************, "end": **********.783187, "relative_end": 8.821487426757812e-06, "duration": 0.*****************, "duration_str": "284ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "BH3y5tSqHYIF8QJZm3HylyzUmr2S2bBDtf1Nyqi0", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1312386398 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1312386398\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-843788189 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-843788189\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1730866044 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1730866044\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1676870316 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1676870316\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-455778656 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-455778656\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-591383566 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:52:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijd3bEQ3aUJTMk5XRm5uaDZwUlpMQ2c9PSIsInZhbHVlIjoib1NpRVRQcmY2WjB0bUtkVFpJdC84RmZsNEhqSjVORHZ3OTU3dEVZaUpDblk4ME5zaExoN0FzUURVMmswZGJFdVd5ckFBK3FrYysrWkZtditMZjhtVU90U1piMGZZblFucnNzRHBUWUlFb1I2WFozcVl5Rkd4T2JMSDlyZ2VLYkJwb3FGTHlOZlBuVUxyNVVkenBFclpSNWdwam5LcmVhOG5BdjFmUGp3SnpSSWc1cGpQWC9Cd2hxZ3B6V1N2TElEbGFpMnVJN2l0OVdVMEFOWUZzRHg3cUc5U3QvWkNQSlpSRWtiR2lYUGV6OG4rTnMraWhBdTJIWGp5TldUd0tmR3ZhVGRCdHAwdGpTZEkya055Wm8rTDJnZ1IyTnZ0T0Yyc3B4WmNNTFFIL0xqTTR5NEpNczdFUFNTdFFuTjVhYnZ3c0FBZnpzWjMxbitRMzUyMXBBd1E2NlR0UDVIeUpTdWptdGo0alVpbTV2VU9BL3k4dEI5c3JhUVI1cWord3NuQ3pPbm1RdG9OeDl4U09OQnJwS0lmM2NkVVRjZXJ4WFVnUWNxaFVtbzhmN1ppdERWblcrSmtQMGJ4c296dlA3eC9WMk95aysyUXM2dnB3b1RQRElHbERDbHd6bzVwUURxSnhscjhFakorNVNZU09tYklYVEVsbElmcldFVHJYSGgiLCJtYWMiOiIzNGUwODU5ODdkOGFkM2ZhMTMyMDI2NmRhMTg0NTU4NjNjYjIwMTNhNWZkN2VmYzBmZTgwMTY2NTQ3OWMyMWM1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:52:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjU4N25FTFhJRnhCSFZ1UkdxMERudlE9PSIsInZhbHVlIjoibDAvdGQ5UFRTdHc5eldER2FUaWdhWUdqMXd2b3VKZ1Y0ejFyS0M5V1FGdXZ3Wm1KVjl6bTh5ZEwwTHdJT3FuVk9pOWtPSFd3ZWVraFZsa3htWUF4aTRqOUJ1N0pHRml4am5oVDZkbU5FMUovZkJKaDlDdEs5djZSeDFMdGg0WDVtYlFVMnM1RGFpUkczSTIzY2tSQlUzbEpoYmsxNmVpbjdiUnJQYkNCYWxOL25SdDdVVkVjVkRUUHpkU2EyRGlyTjFQWlN3cXJjditQTUxRcENZeTFPcDU4S0c0R2ROa1VKN2RmTjVFejZxQklwVmYxMUN5UzhEWUZIWlZiRTl0TVd4MGs3VGowNVFsbEVDK2ZYY0tQdy8rUWdhT0w0MlVhOTMrbHlNNUJrVXpOMTlLQ1JMZy9qOCt3d2E3Uy9aUDdhY0pSNkVPVGcvUzV4T0F0VTZJVzNva3c2MGdjNWIxZnllZjMySjhPQmNvczFDWHNISThKdDAySUlSRGRtaTlGalFFL2IzcDdWVldUay9adElLd1dWUCt2Ny9HU3hySkpDaC9TcFZkRG1FdFB5QVVta1RLbU9uUnhxZXVKNlEyZWdndUtPV1hRTWFGQS9QNitJb1Y2VTJBZ2hRK1FkNGhpOVZUczF0TU16TEVPcWJEYXZOd0dSRlhmaHFpRmpwTlQiLCJtYWMiOiJhM2NhNzYyNmE2NzZmMDdjMGVlNmU5ZmM1MWYwMDk1ZGQyNzc5MDNmZWY4MmMwYWRkNTViNmNlZmRjZGJmZTc2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:52:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijd3bEQ3aUJTMk5XRm5uaDZwUlpMQ2c9PSIsInZhbHVlIjoib1NpRVRQcmY2WjB0bUtkVFpJdC84RmZsNEhqSjVORHZ3OTU3dEVZaUpDblk4ME5zaExoN0FzUURVMmswZGJFdVd5ckFBK3FrYysrWkZtditMZjhtVU90U1piMGZZblFucnNzRHBUWUlFb1I2WFozcVl5Rkd4T2JMSDlyZ2VLYkJwb3FGTHlOZlBuVUxyNVVkenBFclpSNWdwam5LcmVhOG5BdjFmUGp3SnpSSWc1cGpQWC9Cd2hxZ3B6V1N2TElEbGFpMnVJN2l0OVdVMEFOWUZzRHg3cUc5U3QvWkNQSlpSRWtiR2lYUGV6OG4rTnMraWhBdTJIWGp5TldUd0tmR3ZhVGRCdHAwdGpTZEkya055Wm8rTDJnZ1IyTnZ0T0Yyc3B4WmNNTFFIL0xqTTR5NEpNczdFUFNTdFFuTjVhYnZ3c0FBZnpzWjMxbitRMzUyMXBBd1E2NlR0UDVIeUpTdWptdGo0alVpbTV2VU9BL3k4dEI5c3JhUVI1cWord3NuQ3pPbm1RdG9OeDl4U09OQnJwS0lmM2NkVVRjZXJ4WFVnUWNxaFVtbzhmN1ppdERWblcrSmtQMGJ4c296dlA3eC9WMk95aysyUXM2dnB3b1RQRElHbERDbHd6bzVwUURxSnhscjhFakorNVNZU09tYklYVEVsbElmcldFVHJYSGgiLCJtYWMiOiIzNGUwODU5ODdkOGFkM2ZhMTMyMDI2NmRhMTg0NTU4NjNjYjIwMTNhNWZkN2VmYzBmZTgwMTY2NTQ3OWMyMWM1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:52:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjU4N25FTFhJRnhCSFZ1UkdxMERudlE9PSIsInZhbHVlIjoibDAvdGQ5UFRTdHc5eldER2FUaWdhWUdqMXd2b3VKZ1Y0ejFyS0M5V1FGdXZ3Wm1KVjl6bTh5ZEwwTHdJT3FuVk9pOWtPSFd3ZWVraFZsa3htWUF4aTRqOUJ1N0pHRml4am5oVDZkbU5FMUovZkJKaDlDdEs5djZSeDFMdGg0WDVtYlFVMnM1RGFpUkczSTIzY2tSQlUzbEpoYmsxNmVpbjdiUnJQYkNCYWxOL25SdDdVVkVjVkRUUHpkU2EyRGlyTjFQWlN3cXJjditQTUxRcENZeTFPcDU4S0c0R2ROa1VKN2RmTjVFejZxQklwVmYxMUN5UzhEWUZIWlZiRTl0TVd4MGs3VGowNVFsbEVDK2ZYY0tQdy8rUWdhT0w0MlVhOTMrbHlNNUJrVXpOMTlLQ1JMZy9qOCt3d2E3Uy9aUDdhY0pSNkVPVGcvUzV4T0F0VTZJVzNva3c2MGdjNWIxZnllZjMySjhPQmNvczFDWHNISThKdDAySUlSRGRtaTlGalFFL2IzcDdWVldUay9adElLd1dWUCt2Ny9HU3hySkpDaC9TcFZkRG1FdFB5QVVta1RLbU9uUnhxZXVKNlEyZWdndUtPV1hRTWFGQS9QNitJb1Y2VTJBZ2hRK1FkNGhpOVZUczF0TU16TEVPcWJEYXZOd0dSRlhmaHFpRmpwTlQiLCJtYWMiOiJhM2NhNzYyNmE2NzZmMDdjMGVlNmU5ZmM1MWYwMDk1ZGQyNzc5MDNmZWY4MmMwYWRkNTViNmNlZmRjZGJmZTc2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:52:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-591383566\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-695185280 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BH3y5tSqHYIF8QJZm3HylyzUmr2S2bBDtf1Nyqi0</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-695185280\", {\"maxDepth\":0})</script>\n"}}