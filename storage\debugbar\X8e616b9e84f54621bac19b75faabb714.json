{"__meta": {"id": "X8e616b9e84f54621bac19b75faabb714", "datetime": "2025-07-31 06:32:17", "utime": **********.307908, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943534.841032, "end": **********.307957, "duration": 2.4669249057769775, "duration_str": "2.47s", "measures": [{"label": "Booting", "start": 1753943534.841032, "relative_start": 0, "end": **********.118032, "relative_end": **********.118032, "duration": 2.2769999504089355, "duration_str": "2.28s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.118081, "relative_start": 2.****************, "end": **********.307962, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "190ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "HBhslCCSQmxAD2JrMhf4CZJNMPa9NIY8tnB3UZIo", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-1664604760 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1664604760\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1621136998 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1621136998\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1854411425 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1854411425\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-551268938 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-551268938\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-756918830 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-756918830\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-906232373 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:32:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im01dGJUL0UrYmg1MVQ0czU2U1pmc2c9PSIsInZhbHVlIjoiVFpxOXRnOTEyTnZaOFFxeHRqNTRNQmlVRENmUkgvOGxrSVhKVnZKNEUzc0ZDYVBQbEYydW44cjk3OUcyOXlEcTZ5b3RndjduSnpFVWN4S0h1cVo4a3duV0dYMXQ4OWJBQjgzYmZXdjJTbkd5Ym5yNXkybFEvZ2VOZDZmY1U5SHhFWldnWXo3YXJ3WlFtUTJkVm5hWjUyd3VVVWJBNm5IcEVWVE9wTEpodnZJdmhMaDFmYWoxZ3I5eG16N1puTUxGUmE5bTdzclAxN1o3Rmh5U0pHN2QzWURRVFdTaXNLVFZxZXBBdHljOUxtNlFRenlra0QxT1pDWTg3R0JaeUwvQlZEWFIxRWY2TCtTWGFoYTlvL3FjNFRUa09HdTAweHdaQjFWZkg4SUFRbjhnVXdJakxiUWxvOVVzSm4xOHBIdlpLcVJDUXl4SEM1V2p4R0twOTVEYmxjU1pKWWI5NjZOQzNTbGtVRTg3QS9MMlBnbFdYeGE0QUNVc3JteXhyQ1VWUEE3RWR3NU9lYUxROC83dkNlSzJoM3ErZUM1eUw4NTdVMHdUdzVUcktybTlFYXVINXE3ME53c3Fua2VaL2d0UkFNNlFXbVVyQXplM2RiaEViaWNTMGs2dGVGQVBZYy82dGcyS3E4YjRUNk1iY0YxSDhJZVFqalpjN05ESk9HekUiLCJtYWMiOiIyYjllZWRlNDAwNWFkZWY0YjcxY2E1Mjc4YmEwN2Q0OTlkN2M1NGNmNDViMTBhZWM2YzgyNTk1MjM3ZDMwNWVjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InhNSjNFbHBMT1pMdlR6RS8vWVVFbFE9PSIsInZhbHVlIjoiYVJjRjNhN3kvcFpnNkYydkVTVVorcmdJd25reU1nMEdreVFDclpPMEk5S1BJRks2anVUUlZXYU4yc2prQXQ1Mno0ZnNUcndyM3NQaldvSnVRQWlQNVJXQkticnZkeGpHejZrWlBNUmh2WUJxUGZnQ05uZ1pQajdBZzdGbTBMaFIyYzV2S0hlWHVaU3pvRW9VVk1rT0VWRFNvRC9OQW5FWFkvVWtXazA5SGtlMTdHU3huQXZ2L1pBczduc0Jac3ZxSWhOZksvcUpxNkwxWFdMNno3dVVsWWUvdUd2cmhLc3NiT0IyWUZjS3hSZGRYT3AxWmhnZ0JCNkhOMmxicWZHN2h5UHVYNUlQeUNtazlnYlJodXg3aUVkZzNVaXhDRkMwa3VTLzRkREMwdUIzTThmQko3WlhzLzBsQnhtRDNPWmV0Ynl4LzRXbmZ1dzg3Qjk0Z3N5ZlNyN0hWaENka0xINVJRVzZ1OXFyTHFEZzNEWU1nNWJnTVQ2RzZnYjVQSmx0Y1VTbStvTy9wN1N3em9reXc4ZlZhM0RRZnJkUUQ2VWRUMFdoZ2djNndXRXBrbEd0THMzaU1KU0E5ajRqTmNReERlZ2hsVkxhanp1RlZZU0poaFd6MTRhZ3NCVkVGZVRkaWZXWTZTSHJybFJkeGgzU1FPNitxa05xald1dVlCVk4iLCJtYWMiOiI4NDQ2Nzg5ZGI0NmJkODYxOWU1MzYyNmVhM2Y1NTY4NzUxZTBlNjA2ZGNjYTMxMmMzMTRhNWM4MjA0ODQyOWYyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im01dGJUL0UrYmg1MVQ0czU2U1pmc2c9PSIsInZhbHVlIjoiVFpxOXRnOTEyTnZaOFFxeHRqNTRNQmlVRENmUkgvOGxrSVhKVnZKNEUzc0ZDYVBQbEYydW44cjk3OUcyOXlEcTZ5b3RndjduSnpFVWN4S0h1cVo4a3duV0dYMXQ4OWJBQjgzYmZXdjJTbkd5Ym5yNXkybFEvZ2VOZDZmY1U5SHhFWldnWXo3YXJ3WlFtUTJkVm5hWjUyd3VVVWJBNm5IcEVWVE9wTEpodnZJdmhMaDFmYWoxZ3I5eG16N1puTUxGUmE5bTdzclAxN1o3Rmh5U0pHN2QzWURRVFdTaXNLVFZxZXBBdHljOUxtNlFRenlra0QxT1pDWTg3R0JaeUwvQlZEWFIxRWY2TCtTWGFoYTlvL3FjNFRUa09HdTAweHdaQjFWZkg4SUFRbjhnVXdJakxiUWxvOVVzSm4xOHBIdlpLcVJDUXl4SEM1V2p4R0twOTVEYmxjU1pKWWI5NjZOQzNTbGtVRTg3QS9MMlBnbFdYeGE0QUNVc3JteXhyQ1VWUEE3RWR3NU9lYUxROC83dkNlSzJoM3ErZUM1eUw4NTdVMHdUdzVUcktybTlFYXVINXE3ME53c3Fua2VaL2d0UkFNNlFXbVVyQXplM2RiaEViaWNTMGs2dGVGQVBZYy82dGcyS3E4YjRUNk1iY0YxSDhJZVFqalpjN05ESk9HekUiLCJtYWMiOiIyYjllZWRlNDAwNWFkZWY0YjcxY2E1Mjc4YmEwN2Q0OTlkN2M1NGNmNDViMTBhZWM2YzgyNTk1MjM3ZDMwNWVjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InhNSjNFbHBMT1pMdlR6RS8vWVVFbFE9PSIsInZhbHVlIjoiYVJjRjNhN3kvcFpnNkYydkVTVVorcmdJd25reU1nMEdreVFDclpPMEk5S1BJRks2anVUUlZXYU4yc2prQXQ1Mno0ZnNUcndyM3NQaldvSnVRQWlQNVJXQkticnZkeGpHejZrWlBNUmh2WUJxUGZnQ05uZ1pQajdBZzdGbTBMaFIyYzV2S0hlWHVaU3pvRW9VVk1rT0VWRFNvRC9OQW5FWFkvVWtXazA5SGtlMTdHU3huQXZ2L1pBczduc0Jac3ZxSWhOZksvcUpxNkwxWFdMNno3dVVsWWUvdUd2cmhLc3NiT0IyWUZjS3hSZGRYT3AxWmhnZ0JCNkhOMmxicWZHN2h5UHVYNUlQeUNtazlnYlJodXg3aUVkZzNVaXhDRkMwa3VTLzRkREMwdUIzTThmQko3WlhzLzBsQnhtRDNPWmV0Ynl4LzRXbmZ1dzg3Qjk0Z3N5ZlNyN0hWaENka0xINVJRVzZ1OXFyTHFEZzNEWU1nNWJnTVQ2RzZnYjVQSmx0Y1VTbStvTy9wN1N3em9reXc4ZlZhM0RRZnJkUUQ2VWRUMFdoZ2djNndXRXBrbEd0THMzaU1KU0E5ajRqTmNReERlZ2hsVkxhanp1RlZZU0poaFd6MTRhZ3NCVkVGZVRkaWZXWTZTSHJybFJkeGgzU1FPNitxa05xald1dVlCVk4iLCJtYWMiOiI4NDQ2Nzg5ZGI0NmJkODYxOWU1MzYyNmVhM2Y1NTY4NzUxZTBlNjA2ZGNjYTMxMmMzMTRhNWM4MjA0ODQyOWYyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-906232373\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1596826782 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HBhslCCSQmxAD2JrMhf4CZJNMPa9NIY8tnB3UZIo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1596826782\", {\"maxDepth\":0})</script>\n"}}