{"__meta": {"id": "X962a56169603ba090635eaf88c750825", "datetime": "2025-07-31 06:13:44", "utime": **********.09775, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:13:44] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.087911, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753942421.052992, "end": **********.097793, "duration": 3.0448009967803955, "duration_str": "3.04s", "measures": [{"label": "Booting", "start": 1753942421.052992, "relative_start": 0, "end": **********.657936, "relative_end": **********.657936, "duration": 2.6049439907073975, "duration_str": "2.6s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.657992, "relative_start": 2.6049997806549072, "end": **********.097797, "relative_end": 3.814697265625e-06, "duration": 0.4398050308227539, "duration_str": "440ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50612352, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.05648, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.07493, "accumulated_duration_str": "74.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.852764, "duration": 0.0329, "duration_str": "32.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 43.908}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.9527178, "duration": 0.0030600000000000002, "duration_str": "3.06ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 43.908, "width_percent": 4.084}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.984291, "duration": 0.037020000000000004, "duration_str": "37.02ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 47.991, "width_percent": 49.406}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.02963, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 97.398, "width_percent": 2.602}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-420215021 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-420215021\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-613005711 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-613005711\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-979469953 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-979469953\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-300400832 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im9xT1BBOElDL0pZcTJvZ2ZBOWhoaFE9PSIsInZhbHVlIjoiS0JtS25XNFF3UlQyVFNoVUVrd2xjbDd1cmU5ajVpaVY1THM4ekJ5UVU2eGlnQkp3OGdvSVNhaGhjbnFGZG9qVFlSVklGTTB1b1BnelVZYkRtV2ZsTm1sRDFGOU45QTh2OUtIcWRmeUNYb0Y5aSsxS3ROT3diMkpITHByT3N0UUFBbUtZZGlTTURKMTFaWktrRU9zN2pBUFM1UVJFeS9DRDYxUUpFaENZMGg3TFZFK3pQbFNwUk9MTnpZNlFjMm1Yc0ppZnIremZpR3Q1Vkk3VWJUMTgrQkltOVh1SGhUZGx3cVRlWHVQbkJoNGRlVmpKd1U4Q0N6SFNrR0NrRmtqOTdCaG9ybW9jQXBhN1lPSkg5VXJMY2ErcXE3S283RktZdFJPTUluZ3ZCTW85Y0hvQ3ZaZFVqZ0ducDJYQ1hDc2crVE9ZZzhvRjdpWXNuS2xmck45UUNDOFBaTzM1ZjlTTU92eHNmbzZjM2hLOU8zTTIvRURmVktpMkVjeVBKSnZEQ0VLdnZSRVNnRmJXQmRubzRJL3A3RTY0dDU4ZHRYajNwV0MrSW9sYWhUcHR1UnBrM1dXNTFnL3F5dVNCTThCNzBZeGhDeFRkV3R2Rm14WWxialNkMWU2YllSR1o3dWJDVi8wUHpHamdqL1ZoMm05TlNJV3RqMW5Tajc2R1RWSGgiLCJtYWMiOiIzMDRlNTVhMDZhMWEzZGQ2Y2M2MGJkYzRhMjQwMzFkNmM2ODY4ZDk1NjBiNGM3MjQ4YzEwMmM2ZDMyNWRmODNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik90aENYYjdKK1FSUldhdTM0MnJHeXc9PSIsInZhbHVlIjoiSGZYVTg2NkFzY0NPWDl4TWNhZjRraTRxc2J3Q0E2NjAxN3FEajJrWFhMcGQvZkVWcHVQUWI0T0dwUkI3OHJubVp4RU5KbEtQL2RxV1B4T1JpTlRHL1BQaXdMZ1RNbmZpUFNncVFoMlNFVzA1dDhhdE9ENCtoUE4yeHVmTFJUWnhSbU1ORkNSVlpzOWFLOEV0RFp0bWtINlk1VFZhNEFIRzZmWEpNUGJ3RHFGMVJKTGJ6RDJzLzcrUnAyMEhLdWgrcGdnejcrWEd3YTFxc2lvWG5maFRnMGZiWGNGZ1FaMmdTUEx1Wm9tK1Nqb0gyczFXNWNKNmN2WFlQakhjaGZQd01wNndGWUtSdDNvc2FaaG5ZUXdZbC9pMVl1WHFRcERjV1RJVCtjbXg5QVdxUFJONTdNT1hGWnBpZmdrTDZtTGtUeTN5NlRFWnhJYngzY3lNMW4xR2lUZWVKdlphK210a0VzRmFWZm5GTzhYTUNtRXZBNk9wSVpUQUN0K0tkc1E0U0k4OTZtcmtndm5LUzQ0UDlveU1zNkhmU3Zuc0lUZEd1cU5pWEkvTXpRTUp5UEZHSDdsY25HcGdkUzRVVE1hbk1OU0dIemtteVpidTlQcUwyQWtvdzdidldjYWhCdlBUTzhmN1U0bHd5V0g1bEcwa2tYSW9NdVEzYUN4OHhuSnUiLCJtYWMiOiI0Yjc3OTc0MzNmZTljMTRiZjJjZDJjMmEwY2ExMzE3ZDY1NDNiNGU2MGE3ZDIyMTllODkzOGZjYmJhMzJmZjJkIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6ImVaRzBiMzNwMXppaHl5ZHhVaTdGTkE9PSIsInZhbHVlIjoicVN5TDB2SDMwUGRHQnRzUk43Z01KM3pXNVZpbkQxM0hXZU5CdWlYQUhMbk9qSFZHWUpVMzhZSlF1T00vbm5iV2puaG5teXliL0JhUTAzdmpsZ2VpV0c5Z3JsaGFma3RaU05HWGJXak9xU2JZS3daeFc4dWFNMW51ZXZpZHhxV1I1NlZvL0JsRVI4YklIekxvZG9sSDNsaU1qYldPVDhzTTNyU3lxaGVna0l0Mk9HZEZ6V25Qd0lia2JZdXp0cEw4VG9Qc2JDaXdYajJ4ZHp1WHhRTEd0TWhxN1MzV2ZkcURBWkVQOURFT0g2aE9aenp5amVocGlKWUdOZGV2VGJiZTNCUjlRa2pTSnBnSzZ4YjBTT2JjajFFRGpqYVZYZHRRc3h6ZXJyc29nN05FeGFoV1pudFk4endVK3FVdll5VUlKS2JSNFNTQWJ2UWtHY3pDTEI5RmlyTUxQSHRnK0FhelA4czIvbkN4SHBsYkQ4SElDci9ZVE9lcmFQbTFCL1lTZzlUTWxpam5wd2dXaHZKOTRTa1dZRWdSdTBYNGRWcWdyQ1FhU3JyTlBHR2hqRTN1clc3YmNRaTAwQjNOMFlmVFZhUHhCTGhXc3V1dk40eUR0ckFVR0pzZWxzZ3c5bXBGVUxFeXZuZGh6MnVGT3phNXZvZDB5RXRrWndmd0RrNHQiLCJtYWMiOiI4NDA3N2FiYWFlN2QzZjgzZGU0N2Q3N2M3MDI0ZjM1OWRiNDk2YzhhZDYwNTVkMmI5MTMzZjYwMDk2YWFmMDU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-300400832\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1130826817 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5LJND5bUclZnLzzClCMEaxKzZ5Yt8AAmGjprz0I</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sUbnPperdA1I3PaRxBpMGV4YsR5AVGq0ERh2LBdJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130826817\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-888014875 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:13:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im41VHlidW41OEd2eHdVc0JLSDkxb1E9PSIsInZhbHVlIjoiZ1NTMFgwVytUdVNmNTA5S1lsZXBKUVRrT0RMS1UvRzB0OThtbUhGblJWcGdMa1dWYlFBSjRibTBreXdUZmFTTGJjVXk0R3REcHlZTzVYWnBrbVBqeDd2dWx2eGx1SzZLVGkxblZBdGRzZVJuMHV4VHUxN1dLd0J5VlhxTURORGxsTEpacFpsdWh2ZnU0c3NCb2pwNERlWXM2eUp6QW90QU1PMnhKclUyeXdHWERTQVl4VlVWcVdYNmg4dmlHeTZrL2FNL0o3YTVORTZoS2RGc0dkYTA3bUUrbUhZS2xIdXkxZzlLdkdSaENCSXNUa0JUVVVYR05mVUs1NUFoa05zWDlIejZVYVJ5d05NUksraXRvOExwUjhkYWI5clpVUTVDa0Jld1lYa1FsQW5lRVRnN1JUM0JzbVlrZE1IN01vcysxMUNURXhtZ0lDVzdGQXRWdWFWdFMyZUwyZGVEczViczRIeVNjRlpyTkVrS1c5SW1IV01TcW4zc0pFNnBWcTVMcC9iTkhWMlR3djc0Uml6Nmp6TEx3K2RPQWY1UGhiL0xUZHZGL3E3ak1KQ1Uvd0drQTkrLy9yc0R0ZjRXVEZQc08zSGNqSDhJNkFyMUZjUEk4RlJTS1U2b2piQk9DdC9WVEszSFRMNWRidVRiR0N1MzNxUDNId3YyanRhMFFTV2QiLCJtYWMiOiJkMGU3Y2E0ZGE5M2Y4MmJkMTU3N2Y4MzFhMTcwYmIwMjFhYmI1MjFmNTc3NjViMjQxMjBiMmVmNzM4OGRkYTI5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:13:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IjlNSzM0MVpaRnZrMnNYSGFCeTR3cVE9PSIsInZhbHVlIjoiL1RNSnlnRzluN1pZd1pBVWxZK0duWCtEYlltOHZEWW5KUzB3M0JscnhvMlNZcUZTVWlMbUJWek13VDkzZFAvcnl2dlFwaGpSQVFMemFzdHcxTGJmT0NTSS9vejEwdnhiUXZ3MGlKem93QlRwdjZJWlo5ZElLUHg0R1YvR011R0F5eUtlSHZiV3pUNjBlWnF5UjFZMFYzK003NzAvWWNWQUViTERJRXluMk96MjV5bDcwY2ZOZ2ZHNW44d0pTbStxN29OTG5zL3VrMzBydkxWVFBOMjJVTVo2bUY2TThOQWxwaDZGdkFLUFdFTGt2cjd1aEUyT1RNZlVNT1NSNEUwMlE3UjhaT3VpRlZ3bzFCcFZIYTNuRmtSaDEvVGtRTmowWFRPTUNNYVBFcFNJM2k5eThqd3JuMXM0TG5mTkxWM3N1Q2NoVEw0d0RvbUpBY2c5cGh3UEcwTmgxdWFWZG0xa05ZL29FNk02TVd2Q2dRdmFvYnFVRXBHL1ZmSjYzT01pRzRnNDlkRjk3N3dqeTFkM3Zoc1Q3Qm9RbXFLeWVWbzJlOFd2b0NWRmwrNHVTTVgzTEorUExtV1hZTWhRcjYvU2JtV2ZTcjRQSU5GcnEyaGJYUnZveFkrbGM4VVJqUWNnVWtRRUtHSGJxMlZiRHc0d0tWSythNlNCNzROcklOQXYiLCJtYWMiOiJmNWM1MTc2MTVlYWE2MDhkNDk5MGZjMDYwMWY3NTA1NTIyNzdhYWY3MjE0N2U1MGI2MTlhODlhMDM3NTI2ZTJhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:13:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im41VHlidW41OEd2eHdVc0JLSDkxb1E9PSIsInZhbHVlIjoiZ1NTMFgwVytUdVNmNTA5S1lsZXBKUVRrT0RMS1UvRzB0OThtbUhGblJWcGdMa1dWYlFBSjRibTBreXdUZmFTTGJjVXk0R3REcHlZTzVYWnBrbVBqeDd2dWx2eGx1SzZLVGkxblZBdGRzZVJuMHV4VHUxN1dLd0J5VlhxTURORGxsTEpacFpsdWh2ZnU0c3NCb2pwNERlWXM2eUp6QW90QU1PMnhKclUyeXdHWERTQVl4VlVWcVdYNmg4dmlHeTZrL2FNL0o3YTVORTZoS2RGc0dkYTA3bUUrbUhZS2xIdXkxZzlLdkdSaENCSXNUa0JUVVVYR05mVUs1NUFoa05zWDlIejZVYVJ5d05NUksraXRvOExwUjhkYWI5clpVUTVDa0Jld1lYa1FsQW5lRVRnN1JUM0JzbVlrZE1IN01vcysxMUNURXhtZ0lDVzdGQXRWdWFWdFMyZUwyZGVEczViczRIeVNjRlpyTkVrS1c5SW1IV01TcW4zc0pFNnBWcTVMcC9iTkhWMlR3djc0Uml6Nmp6TEx3K2RPQWY1UGhiL0xUZHZGL3E3ak1KQ1Uvd0drQTkrLy9yc0R0ZjRXVEZQc08zSGNqSDhJNkFyMUZjUEk4RlJTS1U2b2piQk9DdC9WVEszSFRMNWRidVRiR0N1MzNxUDNId3YyanRhMFFTV2QiLCJtYWMiOiJkMGU3Y2E0ZGE5M2Y4MmJkMTU3N2Y4MzFhMTcwYmIwMjFhYmI1MjFmNTc3NjViMjQxMjBiMmVmNzM4OGRkYTI5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:13:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IjlNSzM0MVpaRnZrMnNYSGFCeTR3cVE9PSIsInZhbHVlIjoiL1RNSnlnRzluN1pZd1pBVWxZK0duWCtEYlltOHZEWW5KUzB3M0JscnhvMlNZcUZTVWlMbUJWek13VDkzZFAvcnl2dlFwaGpSQVFMemFzdHcxTGJmT0NTSS9vejEwdnhiUXZ3MGlKem93QlRwdjZJWlo5ZElLUHg0R1YvR011R0F5eUtlSHZiV3pUNjBlWnF5UjFZMFYzK003NzAvWWNWQUViTERJRXluMk96MjV5bDcwY2ZOZ2ZHNW44d0pTbStxN29OTG5zL3VrMzBydkxWVFBOMjJVTVo2bUY2TThOQWxwaDZGdkFLUFdFTGt2cjd1aEUyT1RNZlVNT1NSNEUwMlE3UjhaT3VpRlZ3bzFCcFZIYTNuRmtSaDEvVGtRTmowWFRPTUNNYVBFcFNJM2k5eThqd3JuMXM0TG5mTkxWM3N1Q2NoVEw0d0RvbUpBY2c5cGh3UEcwTmgxdWFWZG0xa05ZL29FNk02TVd2Q2dRdmFvYnFVRXBHL1ZmSjYzT01pRzRnNDlkRjk3N3dqeTFkM3Zoc1Q3Qm9RbXFLeWVWbzJlOFd2b0NWRmwrNHVTTVgzTEorUExtV1hZTWhRcjYvU2JtV2ZTcjRQSU5GcnEyaGJYUnZveFkrbGM4VVJqUWNnVWtRRUtHSGJxMlZiRHc0d0tWSythNlNCNzROcklOQXYiLCJtYWMiOiJmNWM1MTc2MTVlYWE2MDhkNDk5MGZjMDYwMWY3NTA1NTIyNzdhYWY3MjE0N2U1MGI2MTlhODlhMDM3NTI2ZTJhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:13:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-888014875\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1937724045 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1937724045\", {\"maxDepth\":0})</script>\n"}}