{{ Form::open(['route' => ['projects.tasks.store',$project_id,$stage_id],'id' => 'create_task', 'class'=>'needs-validation', 'novalidate']) }}
<div class="modal-body">
    {{-- start for ai module--}}
    @php
        $plan= \App\Models\Utility::getChatGPTSettings();
    @endphp
    @if($plan->chatgpt == 1)
    <div class="text-end">
        <a href="#" data-size="md" class="btn  btn-primary btn-icon btn-sm" data-ajax-popup-over="true" data-url="{{ route('generate',['project task']) }}"
           data-bs-placement="top" data-title="{{ __('Generate content with AI') }}">
            <i class="fas fa-robot"></i> <span>{{__('Generate with AI')}}</span>
        </a>
    </div>
    @endif
    {{-- end for ai module--}}
    <div class="row">
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('name', __('Task name'),['class' => 'form-label']) }}<x-required></x-required>
                {{ Form::text('name', null, ['class' => 'form-control','required'=>'required', 'placeholder'=>__('Enter Task Name')]) }}
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('milestone_id', __('Milestone'),['class' => 'form-label']) }}
                <select class="form-control select" name="milestone_id" id="milestone_id">
                    <option value="0" class="text-muted">{{__('Select Milestone')}}</option>
                    @foreach($project->milestones as $m_val)
                        <option value="{{ $m_val->id }}">{{ $m_val->title }}</option>
                    @endforeach
                </select>
                <div class="text-xs mt-1">
                    {{ __('Create milestone here.') }} <a href="{{ route('projects.show', $project_id) }}"><b>{{ __('Create milestone') }}</b></a>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="form-group">
                {{ Form::label('description', __('Description'),['class' => 'form-label']) }}
                <small class="form-text text-muted mb-2 mt-0">{{__('This textarea will autosize while you type')}}</small>
                {{ Form::textarea('description', null, ['class' => 'form-control','rows'=>'1','data-toggle' => 'autosize', 'placeholder'=>__('Enter Description')]) }}
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('estimated_hrs', __('Estimated Hours'),['class' => 'form-label']) }}<x-required></x-required>
                <small class="form-text text-muted mb-2 mt-0">{{__('allocated total ').$hrs['allocated'].__(' hrs in other tasks')}}</small>
                {{ Form::number('estimated_hrs', null, ['class' => 'form-control','required' => 'required','min'=>'0','maxlength' => '8', 'placeholder'=>__('Enter Estimated Hours')]) }}
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('priority', __('Priority'),['class' => 'form-label']) }}<x-required></x-required>
                <small class="form-text text-muted mb-2 mt-0">{{__('Set Priority of your task')}}</small>
                <select class="form-control select" name="priority" id="priority" required>
                    @foreach(\App\Models\ProjectTask::$priority as $key => $val)
                        <option value="{{ $key }}">{{ __($val) }}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('start_date', __('Start Date'),['class' => 'form-label']) }}
                {{ Form::date('start_date', null, ['class' => 'form-control']) }}
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('end_date', __('End Date'),['class' => 'form-label']) }}
                {{ Form::date('end_date', null, ['class' => 'form-control']) }}
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="form-label">{{__('Task members')}}</label>
        <small class="form-text text-muted mb-2 mt-0">{{__('Select users to assign this task to.')}}</small>
    </div>

    @if($project->users->count() > 0)
        <div class="user-select-grid">
            @foreach($project->users as $user)
                <div class="user-option">
                    <input type="checkbox" name="assign_to[]" value="{{ $user->id }}"
                           id="project_user_{{ $user->id }}"
                           data-user-name="{{ $user->name }}"
                           data-user-email="{{ $user->email }}">
                    <label for="project_user_{{ $user->id }}" class="user-label">
                        <div class="user-avatar">
                            {{ strtoupper(substr($user->name, 0, 1)) }}
                        </div>
                        <div class="user-info">
                            <div class="user-name">{{ $user->name }}</div>
                            <small class="text-muted">{{ $user->email }}</small>
                        </div>
                        <div class="user-check-indicator">
                            <i class="ti ti-check"></i>
                        </div>
                    </label>
                </div>
            @endforeach
        </div>
    @else
        <div class="alert alert-warning">
            <i class="ti ti-alert-triangle me-2"></i>
            {{ __('No users assigned to this project. Please assign users to the project first.') }}
        </div>
    @endif

    <div class="selected-users-summary mt-3">
        <small class="text-muted">
            <span id="selected-count">0</span> {{ __('user(s) selected') }}
        </small>
        <div id="selected-users-list" class="mt-2"></div>
    </div>
    @if(isset($settings['google_calendar_enable']) && $settings['google_calendar_enable'] == 'on')
        <div class="form-group col-md-6">
            {{Form::label('synchronize_type',__('Synchronize in Google Calendar ?'),array('class'=>'form-label')) }}
            <div class="form-switch">
                <input type="checkbox" class="form-check-input mt-2" name="synchronize_type" id="switch-shadow" value="google_calender">
                <label class="form-check-label" for="switch-shadow"></label>
            </div>
        </div>
    @endif
</div>
<div class="modal-footer">
    <input type="button" value="{{__('Cancel')}}" class="btn btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="{{__('Create')}}" class="btn btn-primary">
</div>
{{Form::close()}}

<style>
.user-select-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
    margin-top: 0.75rem;
}

.user-option {
    position: relative;
}

.user-option input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: pointer;
}

.user-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #ffffff;
}

.user-option input[type="checkbox"]:checked + .user-label {
    border-color: #3b82f6;
    background: #eff6ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.75rem;
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 0.875rem;
    color: #374151;
    line-height: 1.2;
}

.user-check-indicator {
    opacity: 0;
    transition: all 0.2s ease;
    color: #10b981;
    font-size: 1rem;
}

.user-option input[type="checkbox"]:checked + .user-label .user-check-indicator {
    opacity: 1;
    transform: scale(1.1);
}

.selected-users-summary {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 0.75rem;
}

.selected-user-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background: #eff6ff;
    color: #3b82f6;
    border: 1px solid #bfdbfe;
    border-radius: 0.375rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    margin: 0.125rem;
}
</style>

<script>
$(document).ready(function() {
    // User selection feedback and tracking
    function updateSelectedUsers() {
        const selectedUsers = [];
        $('.user-option input[type="checkbox"]:checked').each(function() {
            const userName = $(this).data('user-name');
            const userId = $(this).val();
            selectedUsers.push({id: userId, name: userName});
        });

        // Update count
        $('#selected-count').text(selectedUsers.length);

        // Update selected users list
        const usersList = $('#selected-users-list');
        usersList.empty();

        if (selectedUsers.length > 0) {
            selectedUsers.forEach(function(user) {
                const userTag = $(`
                    <span class="selected-user-tag">
                        <i class="ti ti-user"></i>
                        ${user.name}
                    </span>
                `);
                usersList.append(userTag);
            });
        } else {
            usersList.html('<small class="text-muted fst-italic">{{ __("No users selected") }}</small>');
        }
    }

    // Initialize selected users display
    updateSelectedUsers();

    $('.user-option input[type="checkbox"]').on('change', function() {
        const label = $(this).next('.user-label');
        if (this.checked) {
            label.addClass('selected');
        } else {
            label.removeClass('selected');
        }

        // Update selected users display
        updateSelectedUsers();
    });

    // Form validation enhancement
    $('#create_task').on('submit', function(e) {
        // Check if at least one user is selected
        const selectedUsers = $('.user-option input[type="checkbox"]:checked').length;
        if (selectedUsers === 0) {
            e.preventDefault();
            show_toastr('error', '{{ __("Please select at least one user to assign this task.") }}');
            return false;
        }
    });
});
</script>

