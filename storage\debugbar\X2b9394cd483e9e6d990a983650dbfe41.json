{"__meta": {"id": "X2b9394cd483e9e6d990a983650dbfe41", "datetime": "2025-07-31 06:33:47", "utime": **********.739741, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943625.70463, "end": **********.739819, "duration": 2.035189151763916, "duration_str": "2.04s", "measures": [{"label": "Booting", "start": 1753943625.70463, "relative_start": 0, "end": **********.556397, "relative_end": **********.556397, "duration": 1.8517670631408691, "duration_str": "1.85s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.556422, "relative_start": 1.****************, "end": **********.739825, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "UoDVwCHcsO1MAHe74Szd1n4IFzFdJdelnY0nrrIw", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-576848327 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-576848327\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-416269149 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-416269149\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1631082109 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1631082109\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-800398270 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-800398270\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1234998749 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1234998749\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-283891067 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:33:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhVVjFCM0N4N0YwTXQzS2xaTmFzSUE9PSIsInZhbHVlIjoidVNVeC9BNjNkVjdpQm4wVVdZS2Y3ZjBWYXVvbEwzbXpudGw3dmpQdHFVaTF2bXhUTHp2d1lkWU5vSlNESk5od21UVVVHaTB2Ym5HbDduaFlpczNCWnZYK1lpSDhjcmJDV1hZN2Zsdm1QN0VBaWRnNFZ2d2dPbXpmS3c5V0xjT1haMFNTNGR1UmVFWHJ6VnprUmhHSWpPTFFwNloxZFhlVTBWbEVtSHVscnoxQUtjdm1xRXBjcjFIRWNBR2JVUnNtQUlDbnd3YTFMVDdwTnl0ZGd5bWk5ZlBoMG50Mm5ydDFnUm5tbjlkNnRWdmNGYkFDM1ZDU3FnOTlabXFQeFEyOWEvTEw3N3haSjEvOWI4S1pESEFUb0psM2NhMzZMd3lXNE04QWMxeTlEOFBmZGtCMko1dmFqL0dXejFzQXZhMVZ1djR5VmdlL1NxYmxvb1pmMlhaVUdreVhEVm84VGN1aFB6Um8xKzIwdm9DSHRscmh2enpXNExGMUJTV29VYlRGb2htUzZMU1g3SEFFai9XcEQwYnhESEl5cGFUTkp1Z2MxMUFvU3NPRHo4NGJ5eHRKWUowRHNldDBLV25wbURkS0dpbDk1dytDbExEQ3JDMkF2a1BDTDF0SVd6cE52MXA2L2QyTHVNTUxPMHRKUUF5NmttZng3V1RVYmMxZFBKaWUiLCJtYWMiOiI3YWEwMDdmZDJmNzgwZDg0MTliNjVjZGM3ZDdhNTY2MTIwZTEzNjA4ZDMwMDI1NGVjMDA2YjdjZmJhMWVjOTYxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:33:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Inl0ZkVRZ0FvYzUwbklqdUFQRHhQdGc9PSIsInZhbHVlIjoiY3ozZnpGajZMTVlyZ1J3dnl6UnVObGRQa3VYMnFmaXdVRzNyVDdhUGV3ZjFZMTJ1UCtLSk5vOVJCY1ZqNDZCckJLWXNrOWlBUXhpVmNabUJXdE9KcVBscmVSVkp4bzhIbmtKTTRWRThqcHdRUkdtOEFiZzVpS1lQZHBqdmVVMEU2QkRHWGFPbjZwRkh3YXZVWjFncWt4ZVo4S0JTTUlIdTB0SXByK1g1b3dOS1JkblNmbjVMalZ6ZUNvY0UvamFHTGxZOTVEVW96eEdWOHpQc1ZZQWdRV3lWSmwza2hMK3dnZDVTQkVxRDA5UXREZjhmTlJlQUx5cXorV1BUM2UvTDhaM0dKbGsrdCtYRjVwRmpldHUyaWpma05QblRvem0xZm1UdzhQUERFekg3cysvdWthWkpoU2ZUWmRhM3BueWxYTU1aejlSbHA5OWw1TDVEeURTcTFoa0lUSWhVT3I2SEZ1eGQwYUl5UzF3WlhqOHRWaHA4aW1Oc2pGbXpZbjFzbE9KUGJxZjN4ZlA3eFB1QUhPeDZnNmxyeVYvMGJvWWdTUDZ5bmIzckh5U2hwT3lHRmZMRFluSGhpUzlpZlRLRWhadTBRZC9SUDVzczdaUmdJSi9vckVuVDR4b3dyOTRESU1qa0hVZ2FLOHNHT2xnMzAzYXZaOFZOZlpXWThiNDYiLCJtYWMiOiJmZTFjZmE1NmY2NDAxYzM4ODdhMTI4ZGU3Y2Y1M2NhZGJlYzI0NzQxN2E2YzM2NGQyMDYyMmU3ZTFjODMyZmVjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:33:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhVVjFCM0N4N0YwTXQzS2xaTmFzSUE9PSIsInZhbHVlIjoidVNVeC9BNjNkVjdpQm4wVVdZS2Y3ZjBWYXVvbEwzbXpudGw3dmpQdHFVaTF2bXhUTHp2d1lkWU5vSlNESk5od21UVVVHaTB2Ym5HbDduaFlpczNCWnZYK1lpSDhjcmJDV1hZN2Zsdm1QN0VBaWRnNFZ2d2dPbXpmS3c5V0xjT1haMFNTNGR1UmVFWHJ6VnprUmhHSWpPTFFwNloxZFhlVTBWbEVtSHVscnoxQUtjdm1xRXBjcjFIRWNBR2JVUnNtQUlDbnd3YTFMVDdwTnl0ZGd5bWk5ZlBoMG50Mm5ydDFnUm5tbjlkNnRWdmNGYkFDM1ZDU3FnOTlabXFQeFEyOWEvTEw3N3haSjEvOWI4S1pESEFUb0psM2NhMzZMd3lXNE04QWMxeTlEOFBmZGtCMko1dmFqL0dXejFzQXZhMVZ1djR5VmdlL1NxYmxvb1pmMlhaVUdreVhEVm84VGN1aFB6Um8xKzIwdm9DSHRscmh2enpXNExGMUJTV29VYlRGb2htUzZMU1g3SEFFai9XcEQwYnhESEl5cGFUTkp1Z2MxMUFvU3NPRHo4NGJ5eHRKWUowRHNldDBLV25wbURkS0dpbDk1dytDbExEQ3JDMkF2a1BDTDF0SVd6cE52MXA2L2QyTHVNTUxPMHRKUUF5NmttZng3V1RVYmMxZFBKaWUiLCJtYWMiOiI3YWEwMDdmZDJmNzgwZDg0MTliNjVjZGM3ZDdhNTY2MTIwZTEzNjA4ZDMwMDI1NGVjMDA2YjdjZmJhMWVjOTYxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:33:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Inl0ZkVRZ0FvYzUwbklqdUFQRHhQdGc9PSIsInZhbHVlIjoiY3ozZnpGajZMTVlyZ1J3dnl6UnVObGRQa3VYMnFmaXdVRzNyVDdhUGV3ZjFZMTJ1UCtLSk5vOVJCY1ZqNDZCckJLWXNrOWlBUXhpVmNabUJXdE9KcVBscmVSVkp4bzhIbmtKTTRWRThqcHdRUkdtOEFiZzVpS1lQZHBqdmVVMEU2QkRHWGFPbjZwRkh3YXZVWjFncWt4ZVo4S0JTTUlIdTB0SXByK1g1b3dOS1JkblNmbjVMalZ6ZUNvY0UvamFHTGxZOTVEVW96eEdWOHpQc1ZZQWdRV3lWSmwza2hMK3dnZDVTQkVxRDA5UXREZjhmTlJlQUx5cXorV1BUM2UvTDhaM0dKbGsrdCtYRjVwRmpldHUyaWpma05QblRvem0xZm1UdzhQUERFekg3cysvdWthWkpoU2ZUWmRhM3BueWxYTU1aejlSbHA5OWw1TDVEeURTcTFoa0lUSWhVT3I2SEZ1eGQwYUl5UzF3WlhqOHRWaHA4aW1Oc2pGbXpZbjFzbE9KUGJxZjN4ZlA3eFB1QUhPeDZnNmxyeVYvMGJvWWdTUDZ5bmIzckh5U2hwT3lHRmZMRFluSGhpUzlpZlRLRWhadTBRZC9SUDVzczdaUmdJSi9vckVuVDR4b3dyOTRESU1qa0hVZ2FLOHNHT2xnMzAzYXZaOFZOZlpXWThiNDYiLCJtYWMiOiJmZTFjZmE1NmY2NDAxYzM4ODdhMTI4ZGU3Y2Y1M2NhZGJlYzI0NzQxN2E2YzM2NGQyMDYyMmU3ZTFjODMyZmVjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:33:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-283891067\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-925610790 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UoDVwCHcsO1MAHe74Szd1n4IFzFdJdelnY0nrrIw</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-925610790\", {\"maxDepth\":0})</script>\n"}}