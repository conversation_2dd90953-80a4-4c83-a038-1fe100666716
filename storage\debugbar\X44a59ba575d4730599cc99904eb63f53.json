{"__meta": {"id": "X44a59ba575d4730599cc99904eb63f53", "datetime": "2025-07-31 07:34:21", "utime": **********.620889, "method": "GET", "uri": "/expense/1/description", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753947260.936279, "end": **********.620909, "duration": 0.6846299171447754, "duration_str": "685ms", "measures": [{"label": "Booting", "start": 1753947260.936279, "relative_start": 0, "end": **********.516709, "relative_end": **********.516709, "duration": 0.5804300308227539, "duration_str": "580ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.516722, "relative_start": 0.5804429054260254, "end": **********.620911, "relative_end": 1.9073486328125e-06, "duration": 0.10418891906738281, "duration_str": "104ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46916192, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET expense/{id}/description", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ExpenseController@getDescription", "namespace": null, "prefix": "", "where": [], "as": "expense.description", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=1179\" onclick=\"\">app/Http/Controllers/ExpenseController.php:1179-1204</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.014889999999999999, "accumulated_duration_str": "14.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.574187, "duration": 0.013529999999999999, "duration_str": "13.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 90.866}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.604804, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 90.866, "width_percent": 4.97}, {"sql": "select * from `expenses` where `id` = '1' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["1", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 1184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6096072, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ExpenseController.php:1184", "source": "app/Http/Controllers/ExpenseController.php:1184", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=1184", "ajax": false, "filename": "ExpenseController.php", "line": "1184"}, "connection": "radhe_same", "start_percent": 95.836, "width_percent": 4.164}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Expense": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FExpense.php&line=1", "ajax": false, "filename": "Expense.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/expense/1/description\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/expense/1/description", "status_code": "<pre class=sf-dump id=sf-dump-1397434639 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1397434639\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-773336332 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-773336332\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1618660990 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1618660990\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-928901420 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlRLVXZySTZDYUFLcnBiZkFJaUp2Y2c9PSIsInZhbHVlIjoiUzEyMHZ4YSt2bFlIKzZ4MXBnT2FQUmVGc0RsbEtTazRaYjFaQWxSTHRQenl5TzVyTW5wMlRibmlYYXRvaVlWOVFXNzY0dmM1dWVJZEo1L29DVzJ3WTZ3YUZRSE5NOC95TXlXN3BVc1hZZmRBNzdpRGtwRUpDdjArcmtWSjYxMnMweVhLTVNyZnRsS3RZWWxnMXU4bVMvZmovdFRXRW1nVkVyemxSMWNkUUdVem1Ea0RBUEJ0cTFpY2x4UHZyUkxsSEVFVjEvUjdLdldTSVlwcENwa2l4YUVnRUhoK3paTnQ2MHhodEYwS3oxTlFPV0Y4S1NQeU1ITmNwcG5PQnlpRWdBT3hydkcvZUZMa3dzMmJQN0RYQUpwRDBjU2p4SXBrY2plc0pscWYzSVNoT3kvVzl3TlNkaTN4a3BHS0Eydmo2aTFwa3RiWTNlN1Z2YjFTLzFkaXJLd2szTVR6UVVtODRYODNRUC9NV0dQMGhWdWxYTjUwWnRxWWkvanF6MHZtb2JzcSsxMW9NOEVZU1RKYjNQNkcrYi8vc3pRSHFGem9Gb25wUzl0aUNzOXlmRWpaU2RPcm9BWlZJUzZPSTRsVWdCTnVZSmF1TytXeWNXYjFFa0VFUHQxNkJsNHR3bVNvZ3UxUlA5c2ZkbjlZc3U3QWdlYnhiOGIwNHptSGJsTHgiLCJtYWMiOiI3MTBkNDQ1ODcyMTM3NTVjOWFkOGJlNjJiMmFhNzg0MDZiYjAxY2ZiYzNhODliZGRiYjgxZTcxYWVmZDQ1ZWNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImdqdU0yZ2tKY1dVa1lJcHZ6ZS94c1E9PSIsInZhbHVlIjoiYk5rMlRnSFI5SDVHelpaSjIwUU56UmFZeTMxMG0vSWk1Zm0wU3VzL0xIMzRMbEFDVEt4anY1ajd6SFpzeFhUNk41YTlPRm9UTDhoemtNQXlXcG1EczJZQ1dkZjVEcmQ3WUlHL2lIWUlicTRBOHI1Q3FxSlQ4djNCaWl1NVRzMFkrYnA2ZUxZT1Q5Tlk5cDNONGY2YWE4Vk9QMWJHdkF1TEloZ2xCc1lLUG11WHZkN291K2lzeWVYaG5TL2N5WUlqMENKWkUrd2dPMVd3S3JHOFg0ZFlFbkZnZ2I1WXpuWGIzNTJUZGtTa05pRlBiTDJNQi9RRk51TkhFUyt6OTh0YzhlTHc4SEVvSDdFaDFDUW9NakxCTWhjSlZyMCt5Qm1QS3ZVVmxoS05JRUhSREVaVGN5ZEFhUXJpaENpa2NXUDRUdW9EVG5zV1VKdUFhTnBZcmNtVDRjYllpajk4Y28wL1pWalNLVUtjNDVzdnBFVnNBcnFQRDBMdTRjdWtlMm5rSjIrZENzemJqV3pVUWlNdkNNN3czTUVxTkpZVS81SVhJZDhVOG1wREs1SVhXekI0N2lkU1pKa3p5S0djQy84cFp6V3ZKVi9tay9rdC9YS3VqOXozRUhnUVBTYmZ2ZFQyN0FsWWdzSk1MeXhnRENGcm5KUE4rOGJRenRMNmk1Z3MiLCJtYWMiOiI2OGM0ZGFhZTZjMDE4NWMwOTZmMzI2NzZmMDg0YjBkMDg4YzNjNDcwMWJhMmU3MTEwYzRkOGJhZDhiZTllMGMzIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6ImV3eE90SzVBZy84N1ZpQ0xiV252anc9PSIsInZhbHVlIjoicFdNcm9Gb2RxUEk5K1VLblVCUGVyY2FCMlh2MGVDRThOSDhDYTkybEFXNk1Iblh0VWtHcmhnK0JHdzZSNHJxSkI2YkhnYTFCNE96RURucFRTQkdDWG5vSVU3YXNkSjlGaUFjWVVXbWN4bWR2YTRVZ1daQjhNZmVEejl0OUp5aUx4YVRrT2hCRWFZWTdvTVB6bHRPNko2MDF4cEZRNEFrWTZrK0RVUUJwUU43cmt3YXIxcWRaWUFtd2NPQVNGaHJPYlZoeWhwUEtRSUQzYmRwQ2JIV0FXRlk3T25VQmM2YU5hQ1dheUNFZVdRUmJMSzYvWHVhcGVmTWs1RUVFcGVoRlczU0RpemRMMkViQzVCMS9PbzM4OWRnQ1EwTGlXUys0eWNuMGZaVWg1SldqbzFKUmtxWXVocWZyQWRMeldaZFRLSUR2cWNJMmJlV3lMUXhTQjkvUlJFNFM1RjdZWStSalhDclY2Q0dpbnZuSXREV1VHakdCcjU5ajVYenNMVVcyY0NtbVBuSi9hb01sZGhCcnU3QkpSb3hnbFVQRzJ1MmZvTi94eE01WXZFUWRxTWRwWFVwUFRDbUZIbmtxR0ttaFFERTJZMnFxRmk3RFZETkhSbFBXaERGUDdBV3hhUnlaSVlDVVRaR0hYRGFXaWhhNS9reGcvSHp3MlBVb2F6RlciLCJtYWMiOiJhYTBkMzgxMzRkOTY5ZmEzODMxNTQwMjI2M2Y0NjkxMDc2OTM0MzVmNDVjODY3NmQ1YTYxYTE1OTBkMWU3NmZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-928901420\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-984326507 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21K3ZmFzZyr4s2pH9aKWRqwEKhgdUCnDfhl6jx78</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-984326507\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1510546898 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:34:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlUxM3dCMERsTlVMT2ZiRTBCakxtYlE9PSIsInZhbHVlIjoiTm1nUE1zK3JaM25Dck0xcE5COVpoS3hpUWJ6OEJRSG1qcE5tSzFlTjRhYzJOSEdkb3RJUDdRcHlMbElXRm8zRURHUUo4L0NmdW50Ymw4eWtub0ZwSGNpYkhFbnB0Wi9vM1pveEp1bFEvWS85R2dVaWQzeGZWc0xhWDdJbVlpQ20vRFJLMm12dk5tY2ZTVVNsNldUbzZwbS9wU1pqbThYYlJEbDJLMG9rbWRmWmthQURuKzA1Nm5zWEVjT0prWjNrOWp5TmtQMHo3ZmpsaVhtRm05MXBPa0xDVW82Vm5OSFBZRlhDLzkxMXloUmxHMGlhUEVBMkI3M05DbzY2Q25mck5PdWlIQmlBV3hubFd6S1BkTXVlUHlOZjVLNjVRTHlnVmRhVElWdXM2Nk1RRXBLV2c2RVlYUTRibzAyT0FLcU5sdmIyODl0SXBxT1VVYnZRaGRpUzZLU0Y4VmJTMlZ4N200bmYzYmJEcUo4Tzh1R20yT1Nad0dkZFhoQS9OaXVTWlJyWmEyOXo3ejRpK0pIQkh3MjBXWkIzNzk2OFU4NGRWdFNRRmZ3OTR3T095b1Zvd0Y3TTBtNW9EVmh3Q0x2VDlSRzRrM3JBaEhkS3FoSzhtMFE1cWt5ZjErNld1TDdpSG1QeGhZMXAzRENlL1VMUGZEQmtQamxvbVIrcHdaZXYiLCJtYWMiOiIzYzA5NGEwMTcxM2ZkOTI0YmVhZjYxZGFmYjBmM2Y3Y2I4NDNkNzZhNjYyOTE5Njc3ZDFhNjI5NTEyZmQ1N2RhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:34:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IkwraWF4TlI0RE1VVEJNL290MEZNQ3c9PSIsInZhbHVlIjoiNG92YkZXZnBnc056RHE4V2JWMWZ6S1QrMFQ1aEJ0b1dSc3VYWlhjYW02NFYzS25BNTRmMk9VS2xuQVdMSys4UFVwOTZqYWt2aENVcXJPNVNHQS9lTExJSXhNSWF1akhyWjIrdVc2cTg5K0pLczBqT3RSclJRSHNWRFA5M3ZxZTN2NmJqaXBkL3dmWDUyZVRkK3hHckpERVlrZWt1RDljNllyQ2ppQXRCOU1laFFGeG9ZZHhJaVRZQzlHVzU3RWsxUTZBd3RoLzFyQlJsUHNrYUZXUjQ5WTJHbnI5YmVKVVh0VG0xUW9RUm9HVW1zTS9IWVAxWjQyRWtBT3ZYRU1UWGNYaWlzalBWeUFRdU1NenFhaHkyN3Naejg0YUowQVBCUHFVeEFrN3VpcWNWellldXh2UEIyNTBIY2JjMEM5dStMNkF5SDN3TnZsdURqUnptTTV5KytrakhRc0hOOHB1MzlnTUQ1TExDZVdUNDM4b2VXRFdWRkRXbGJqQ3dMOUovMkVaU0dDOCtzUUJaOUlGUThlTkhqYUlKcGNrUGVKNWZ2c2hqL2VZRUtTNklDVFEwcFVwbmdESG9pSlJKeUpKR09VWUkrTmQ1blgvNzJ6L040Z3lHNTdvNkhiWjEyTzZLUDE5U3VFbDJkeUh2T092REpPNE1zb0RHM1VnZHh3eUIiLCJtYWMiOiI5OWE4YmU5ODZjNTUyYWFmYzJlZTU4NzQ1OWZlOTZlOTE5NzM3MDlhM2VjNWI5MTMzN2VmMDI0NmNmZmQ5NTkyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:34:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlUxM3dCMERsTlVMT2ZiRTBCakxtYlE9PSIsInZhbHVlIjoiTm1nUE1zK3JaM25Dck0xcE5COVpoS3hpUWJ6OEJRSG1qcE5tSzFlTjRhYzJOSEdkb3RJUDdRcHlMbElXRm8zRURHUUo4L0NmdW50Ymw4eWtub0ZwSGNpYkhFbnB0Wi9vM1pveEp1bFEvWS85R2dVaWQzeGZWc0xhWDdJbVlpQ20vRFJLMm12dk5tY2ZTVVNsNldUbzZwbS9wU1pqbThYYlJEbDJLMG9rbWRmWmthQURuKzA1Nm5zWEVjT0prWjNrOWp5TmtQMHo3ZmpsaVhtRm05MXBPa0xDVW82Vm5OSFBZRlhDLzkxMXloUmxHMGlhUEVBMkI3M05DbzY2Q25mck5PdWlIQmlBV3hubFd6S1BkTXVlUHlOZjVLNjVRTHlnVmRhVElWdXM2Nk1RRXBLV2c2RVlYUTRibzAyT0FLcU5sdmIyODl0SXBxT1VVYnZRaGRpUzZLU0Y4VmJTMlZ4N200bmYzYmJEcUo4Tzh1R20yT1Nad0dkZFhoQS9OaXVTWlJyWmEyOXo3ejRpK0pIQkh3MjBXWkIzNzk2OFU4NGRWdFNRRmZ3OTR3T095b1Zvd0Y3TTBtNW9EVmh3Q0x2VDlSRzRrM3JBaEhkS3FoSzhtMFE1cWt5ZjErNld1TDdpSG1QeGhZMXAzRENlL1VMUGZEQmtQamxvbVIrcHdaZXYiLCJtYWMiOiIzYzA5NGEwMTcxM2ZkOTI0YmVhZjYxZGFmYjBmM2Y3Y2I4NDNkNzZhNjYyOTE5Njc3ZDFhNjI5NTEyZmQ1N2RhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:34:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IkwraWF4TlI0RE1VVEJNL290MEZNQ3c9PSIsInZhbHVlIjoiNG92YkZXZnBnc056RHE4V2JWMWZ6S1QrMFQ1aEJ0b1dSc3VYWlhjYW02NFYzS25BNTRmMk9VS2xuQVdMSys4UFVwOTZqYWt2aENVcXJPNVNHQS9lTExJSXhNSWF1akhyWjIrdVc2cTg5K0pLczBqT3RSclJRSHNWRFA5M3ZxZTN2NmJqaXBkL3dmWDUyZVRkK3hHckpERVlrZWt1RDljNllyQ2ppQXRCOU1laFFGeG9ZZHhJaVRZQzlHVzU3RWsxUTZBd3RoLzFyQlJsUHNrYUZXUjQ5WTJHbnI5YmVKVVh0VG0xUW9RUm9HVW1zTS9IWVAxWjQyRWtBT3ZYRU1UWGNYaWlzalBWeUFRdU1NenFhaHkyN3Naejg0YUowQVBCUHFVeEFrN3VpcWNWellldXh2UEIyNTBIY2JjMEM5dStMNkF5SDN3TnZsdURqUnptTTV5KytrakhRc0hOOHB1MzlnTUQ1TExDZVdUNDM4b2VXRFdWRkRXbGJqQ3dMOUovMkVaU0dDOCtzUUJaOUlGUThlTkhqYUlKcGNrUGVKNWZ2c2hqL2VZRUtTNklDVFEwcFVwbmdESG9pSlJKeUpKR09VWUkrTmQ1blgvNzJ6L040Z3lHNTdvNkhiWjEyTzZLUDE5U3VFbDJkeUh2T092REpPNE1zb0RHM1VnZHh3eUIiLCJtYWMiOiI5OWE4YmU5ODZjNTUyYWFmYzJlZTU4NzQ1OWZlOTZlOTE5NzM3MDlhM2VjNWI5MTMzN2VmMDI0NmNmZmQ5NTkyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:34:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1510546898\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-235039785 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/expense/1/description</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-235039785\", {\"maxDepth\":0})</script>\n"}}