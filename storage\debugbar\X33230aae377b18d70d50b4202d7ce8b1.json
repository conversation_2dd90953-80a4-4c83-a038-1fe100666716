{"__meta": {"id": "X33230aae377b18d70d50b4202d7ce8b1", "datetime": "2025-07-31 06:25:38", "utime": **********.096191, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:25:38] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.087701, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753943135.701188, "end": **********.096252, "duration": 2.395063877105713, "duration_str": "2.4s", "measures": [{"label": "Booting", "start": 1753943135.701188, "relative_start": 0, "end": **********.849397, "relative_end": **********.849397, "duration": 2.1482088565826416, "duration_str": "2.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.849433, "relative_start": 2.148244857788086, "end": **********.096258, "relative_end": 5.9604644775390625e-06, "duration": 0.2468249797821045, "duration_str": "247ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45918384, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0265, "accumulated_duration_str": "26.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.967508, "duration": 0.023530000000000002, "duration_str": "23.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 88.792}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.0307271, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 88.792, "width_percent": 4.528}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.041059, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 93.321, "width_percent": 3.321}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0512009, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 96.642, "width_percent": 3.358}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-512149759 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-512149759\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1718624657 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1718624657\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1211799777 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1211799777\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1130613036 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/pricing-plans/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkxSajQySHJWZVJLY0hjeTRjMFZTenc9PSIsInZhbHVlIjoiQWlacXkvaWJKLzcwRjRWMU9sQUxITXBXNnhpRkJybG8wMUhqZDlOb25oZklDOGl3UzZ4clo0RkNJMGpSbXYydnZ6Y3FWdlhqam1aYVlqRm1iMnZBTWM4WWdqYWg5TXZTb0x3dTFxQWVQR3huSldKRTZQT2QzRGxrTkdHZW5XQ21vZUxaN29IOEdSdTNIU3h5ZUIrTnpsT0dZcHUwL2RvVDFycnhOeTk2MTRMcWN6dVQrRGp6bVNMM2g3TnozaERwUnFqRGFSc1VkcFROcURINDd4ZnBZN0M3UTZDMENzelhRQk8vUno0dnFwQUdxNFpHek1iMjNqdzQwQWJxWlJQc3pNWGwwV2V2NVd0L1NTL25Mc1E5c1M3TC84Z2FUbURoVHE1TlczSUtqdmg4MFBQQ3ZqM0EyQm1tYkRSNlFCRVBvK1NkeFRUV0xEUHNZcmlJaXltK1pvb3BFWi9SQlNxdXlQUjc0V3FabjdneTlBM1dyQ3V1ZzBwenk4S3M4eS9WZFN3dXlhSWlPUmNMeDc3RGRVTlhVQk1VWXhZajFGaGhQaXMrek42eHNtY3RscFRyckU3NEFjSFNCcG1MYmpmUWR0VitJRTl3aE0xR0lzaHpBREVhcFhZcG0zWjdFWFVVL3Bvb2hPY1ZrZHozNjVGSVZsY1NpYXJHKzQ4ZVVZMFQiLCJtYWMiOiJlZjQ0ZTE0YTRiNzFjOTk4OWUzMDkxZTA2YzFiNDVkMWVkY2M3YWJjNzUzMjBmMDk2OWE4MjRmZTQ4OWRiODk4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InpxMTh4M2pZalBTSVRBc0ZaVkFQZmc9PSIsInZhbHVlIjoiN01Yd1BYdDVUbG5RSFJ0S1RrNytXWnFTSzFzL0dMcDZ1T3RUc2tJVW1jZEhxd2FQa25YaitWODByNFROL1IvMEdFMWtOeFFFZHp0djN5ZFk5MG5ycmwyWEJ2RUE1ZjdFWUxaanpPRVRFS2l1R21xbm9Rb0pVcVNDYnVxK25MTUhTaklBT3RycHB3eW40djdJSmp3aWN3NGE0NmIwMUwzdVdod1NqZmV1bmlBSWJpMUwrQ3lVZjVxQ1U2OStiamNwVStrMkY1MWk0MU5PTnhYbjcrNytJRDQvdi9nRUc0M1M3NEd6dU9EUHlzRVc3cU1POG9PZitBWDg5SzlmWUNDakZ0WWd6WG1ocEFnNTZIa28yWXVLYWM2VkpTZDNLR25ZL2RtcDA4SDRNUGsra2NDMmtvcXdGemNLQnZMcmlsRnhSa2RpYklWNldwdU1wa0NZSjl0VmNSZTl2YjBIeVE2dGtwNW1ZejF6RkZRMlhBTXMzMHVzSHJiZU8vUU9BWXV3WWN1WjJkN2dmcDIrSkw5bjhmUVdOYS9OK2liSTdvL2FLOUdIL29pN1V6SC9lQU9sYXdIMVVvNDh0QVBRb3NZNW15c2I1dXlJZXhjeWhNYzNVR2lMYnpYRE9xR2FoSko4TjJuOUtFTy9ya0V6Y1R1VmVYdkx3Z090TDd4eXhoTlUiLCJtYWMiOiI5OTI5ODY1Yzg5ZjExNzA3ZTViZDc5NjRmNDM4MWIyMzFjMjVlY2ViOWZkOTYxYzY5NmY2MDMwYTczMTQ1NDM0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkxFbEpqS2NCK2NvNjNkTmQyN0h6QVE9PSIsInZhbHVlIjoiUXArbjU0WWt5RkhXcDJoY0tHM0cwYTg3aVBhK3l3ZGk2U0RNS2NKZ1RJSnNnbkZBdk4vU0EvR2drSVdLMTdEaDlDTGpqR1h3MEUrRHM2RE4yWmg1Nk1ud3lmT09yUml5cE8zMThDZlRtc3pBeXNtWmVOVEk4Y1d4OEIyZXpoYjUveUpRV2dUK2VQaHN2SWoxUU1DZlVmbzdKM3Yzem1Bd0g0QVVjUEhCbkU3SnFBT3NHcExUSmtsTUJVMk1HNElmdk9NcHJ1MkpjdDFaL0k0U3JGNUFzU0JWOURlNFRBVUpjRUwxMjNucXVuV0dRNlBBSm4rUmQ1WUIzTUZJTXpnYitoK1VqR1lYL0JxdG9ocExYbEtQMkNKZmlsQXdPaVUwOUNSOFZZMVZzV1pzL2dTQVBpOVJjRks0Q3F1YzBnNElBVWUvLzNrc0puOUIvUmhtSUhhSy8yT0NlSXAvdGFROW50YzBUeTRVdXhMR0NOZEZYQU1jc21QSlpESElmU3RDQU5KdUpnNnV1dE5wc1pRZVBzTFB5Z2IrM0hSUnNROGtHL0xjYm1ySWZyWTkrRmZnckQ0c1htTjlXd0d6b0tTUjVhR0tSRWFIaklrNlhVU1VMK05YTW43dnlyVUlUN1FpRWQ0VW9tcWpnS0dyZjhLcTMwK1pCSVN2R2FBVlJ1ZmkiLCJtYWMiOiJjNzY0ODE5NmVkZTUzZWQyNjJjM2NlYmYxMGMwNjdhYjlmMzA4NWFmNzA4MDNkM2RjOTgyYWVhN2FiYTJkNTExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130613036\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-941187128 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sP63YnCaiXbJo1Yr9kWemvIJkyOfRLuWDEB0EhHF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-941187128\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:25:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Illlc2o2UlJIWlN0SjBDdkV6dFE0N2c9PSIsInZhbHVlIjoiRDlSQmx1SjFheFRLTjJBNEdNbGF5dE1vY3BQMlgrbXRLT1lyTmpza0R2Ny94ZUtEOWNUaEtWTElycTBRMHhMb1dCbldSbU0vQlVBUEpGekN5NVRBM1pDUEk4QUFVempBNG1jbkUxWGRUenV4eW5udnczWDhxVEZPajlzSW9adFMzOUJHeGFUUG9WWENJeWlzMVpwU3RoYWU2dlQybHdzY05xQXZuZ2pKVjdld21Fei91RmRiWG1EUTJwVlgvUzdhYjBIVXpBZW1VK2Q1Vmp4Q1FnZTljUVo1NitsK0l5QUQ3d0dWVGxheFA2Nk5ydndraURIcUtpYXZwMUVlNnZlTUEvenhQWUx2UlJBa1lSajB3MUo1ajU5R3J1NE4yakc5Z1dvQmVtSmlsVkVRZ3k3OGNjanF6QnA4OUpsVmd5VmwxVTlmSnVPajYzbXBJV1BYWlNlSGhLS1FxOURXSTQ0TW5HN0hPdklTbURTaVJ4aDJYcVVCaVFXL2lvN0p2M09Vcm4zbG5ibjVFcG5lSlp0UE4vdkd4dWhzL09Qa3RxcXQ3cW95Y2R0dTdNMjhGOW1kMTB2NS92T3pvMkVGcHVJVTk1cDlEQ1RCWEVWemdqR1RoTGdHUTkrRUtmZkFuSnI3aTRkZ1BXMmx0ZTkrdnM2clJ0OUJiTmdVZ014MUp2cFQiLCJtYWMiOiI4NjllMTdmNGM5NjQ5YTk4ZTcwMzY2Yzk0YmI0MmRiYmYwNTFjYTZmZmVjZGRlMWI2Nzg0YmVlYzBkMDEwMDcwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:25:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImNZdUlqbVYwSjZzNWxlVDFSTjRZWkE9PSIsInZhbHVlIjoiMEtuTnYvSzNtWUlSaGhlSGpjQXord2JPR3RmN0svVlVlM3VRTVFuWW1mVE9EcGNBUXV0aDBSWDdqSUpabFIvTm9SSHZiZi9HWGNydEN4UXNMeHZtek9qU29BLzlvemFhOHF6U284aHBqdVBHV2U4Y2IveUZNM2xVRU1KSWFOZXhqMGdFajRqTnhjZ1laUXVFenZFYzQ4ZWwzMVE5SVViTGpISEF2RkdjTmVXY3R6dGhXaFRKSDRmRkZzUmc5cllQMGR4b3NKZ0F3S1p1WTZnOGMyK1NuNWJMbERmZzFQbE1FT29vOWJqM1RHZVJ2T0luWVBWV2VIeGVOb3ZHVlJ6VFc5VVl0SmV1cWkyc1o2TXpCMUZsSHZabVlSVzZTSFhFRjNmMlk0ZERVS3gvSjN2c2ZPbDRXRk5JSUN0Q3JSRkZhNVdXUWkyWFd0VEtLeU9OWWJ4c2pIR2svU3RuTEQ2dE9lV3FSaGFMODdOSkNia21aQTdOVVQ3UUltWkU4QnFKWEQ0V1N4YWJ1L0N4K2VPMTlrMEc1M0FZajNnRWNzc2h6Zkp1YWl1YndaSVhRMmZGaVBtWVJRZDlGNi94ZnhsWDZJbG51TWtyeGRVby9aZXB5eWc2L2VzaUtFei9hQnlOMHltd3kyTEZMNlBFZEl4Q1VPQjRiejJHNlp6ZlhmM0QiLCJtYWMiOiIwOWViMWY4ODY1YThkMGQ1OTk2ZDc5MDM3NjJlZDQyZjYwZmQ1MzZkNzFjNGNmZDRhMjM3NjU4M2IzOWRjNmJlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:25:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Illlc2o2UlJIWlN0SjBDdkV6dFE0N2c9PSIsInZhbHVlIjoiRDlSQmx1SjFheFRLTjJBNEdNbGF5dE1vY3BQMlgrbXRLT1lyTmpza0R2Ny94ZUtEOWNUaEtWTElycTBRMHhMb1dCbldSbU0vQlVBUEpGekN5NVRBM1pDUEk4QUFVempBNG1jbkUxWGRUenV4eW5udnczWDhxVEZPajlzSW9adFMzOUJHeGFUUG9WWENJeWlzMVpwU3RoYWU2dlQybHdzY05xQXZuZ2pKVjdld21Fei91RmRiWG1EUTJwVlgvUzdhYjBIVXpBZW1VK2Q1Vmp4Q1FnZTljUVo1NitsK0l5QUQ3d0dWVGxheFA2Nk5ydndraURIcUtpYXZwMUVlNnZlTUEvenhQWUx2UlJBa1lSajB3MUo1ajU5R3J1NE4yakc5Z1dvQmVtSmlsVkVRZ3k3OGNjanF6QnA4OUpsVmd5VmwxVTlmSnVPajYzbXBJV1BYWlNlSGhLS1FxOURXSTQ0TW5HN0hPdklTbURTaVJ4aDJYcVVCaVFXL2lvN0p2M09Vcm4zbG5ibjVFcG5lSlp0UE4vdkd4dWhzL09Qa3RxcXQ3cW95Y2R0dTdNMjhGOW1kMTB2NS92T3pvMkVGcHVJVTk1cDlEQ1RCWEVWemdqR1RoTGdHUTkrRUtmZkFuSnI3aTRkZ1BXMmx0ZTkrdnM2clJ0OUJiTmdVZ014MUp2cFQiLCJtYWMiOiI4NjllMTdmNGM5NjQ5YTk4ZTcwMzY2Yzk0YmI0MmRiYmYwNTFjYTZmZmVjZGRlMWI2Nzg0YmVlYzBkMDEwMDcwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:25:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImNZdUlqbVYwSjZzNWxlVDFSTjRZWkE9PSIsInZhbHVlIjoiMEtuTnYvSzNtWUlSaGhlSGpjQXord2JPR3RmN0svVlVlM3VRTVFuWW1mVE9EcGNBUXV0aDBSWDdqSUpabFIvTm9SSHZiZi9HWGNydEN4UXNMeHZtek9qU29BLzlvemFhOHF6U284aHBqdVBHV2U4Y2IveUZNM2xVRU1KSWFOZXhqMGdFajRqTnhjZ1laUXVFenZFYzQ4ZWwzMVE5SVViTGpISEF2RkdjTmVXY3R6dGhXaFRKSDRmRkZzUmc5cllQMGR4b3NKZ0F3S1p1WTZnOGMyK1NuNWJMbERmZzFQbE1FT29vOWJqM1RHZVJ2T0luWVBWV2VIeGVOb3ZHVlJ6VFc5VVl0SmV1cWkyc1o2TXpCMUZsSHZabVlSVzZTSFhFRjNmMlk0ZERVS3gvSjN2c2ZPbDRXRk5JSUN0Q3JSRkZhNVdXUWkyWFd0VEtLeU9OWWJ4c2pIR2svU3RuTEQ2dE9lV3FSaGFMODdOSkNia21aQTdOVVQ3UUltWkU4QnFKWEQ0V1N4YWJ1L0N4K2VPMTlrMEc1M0FZajNnRWNzc2h6Zkp1YWl1YndaSVhRMmZGaVBtWVJRZDlGNi94ZnhsWDZJbG51TWtyeGRVby9aZXB5eWc2L2VzaUtFei9hQnlOMHltd3kyTEZMNlBFZEl4Q1VPQjRiejJHNlp6ZlhmM0QiLCJtYWMiOiIwOWViMWY4ODY1YThkMGQ1OTk2ZDc5MDM3NjJlZDQyZjYwZmQ1MzZkNzFjNGNmZDRhMjM3NjU4M2IzOWRjNmJlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:25:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/pricing-plans/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}