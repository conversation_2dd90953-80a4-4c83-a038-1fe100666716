{"__meta": {"id": "X2cee8fd2371878c9150a95476d04420b", "datetime": "2025-07-31 06:13:56", "utime": **********.407111, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:13:56] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.381422, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753942434.597471, "end": **********.407186, "duration": 1.8097150325775146, "duration_str": "1.81s", "measures": [{"label": "Booting", "start": 1753942434.597471, "relative_start": 0, "end": 1753942435.974523, "relative_end": 1753942435.974523, "duration": 1.3770520687103271, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753942435.974549, "relative_start": 1.3770780563354492, "end": **********.407193, "relative_end": 6.9141387939453125e-06, "duration": 0.4326438903808594, "duration_str": "433ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50612352, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.317583, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.07724, "accumulated_duration_str": "77.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.074353, "duration": 0.020210000000000002, "duration_str": "20.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 26.165}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.157347, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 26.165, "width_percent": 2.654}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.182064, "duration": 0.0519, "duration_str": "51.9ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 28.819, "width_percent": 67.193}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2559562, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 96.012, "width_percent": 3.988}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-175536120 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-175536120\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1871283641 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1871283641\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1832511572 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1832511572\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2051263324 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im9xT1BBOElDL0pZcTJvZ2ZBOWhoaFE9PSIsInZhbHVlIjoiS0JtS25XNFF3UlQyVFNoVUVrd2xjbDd1cmU5ajVpaVY1THM4ekJ5UVU2eGlnQkp3OGdvSVNhaGhjbnFGZG9qVFlSVklGTTB1b1BnelVZYkRtV2ZsTm1sRDFGOU45QTh2OUtIcWRmeUNYb0Y5aSsxS3ROT3diMkpITHByT3N0UUFBbUtZZGlTTURKMTFaWktrRU9zN2pBUFM1UVJFeS9DRDYxUUpFaENZMGg3TFZFK3pQbFNwUk9MTnpZNlFjMm1Yc0ppZnIremZpR3Q1Vkk3VWJUMTgrQkltOVh1SGhUZGx3cVRlWHVQbkJoNGRlVmpKd1U4Q0N6SFNrR0NrRmtqOTdCaG9ybW9jQXBhN1lPSkg5VXJMY2ErcXE3S283RktZdFJPTUluZ3ZCTW85Y0hvQ3ZaZFVqZ0ducDJYQ1hDc2crVE9ZZzhvRjdpWXNuS2xmck45UUNDOFBaTzM1ZjlTTU92eHNmbzZjM2hLOU8zTTIvRURmVktpMkVjeVBKSnZEQ0VLdnZSRVNnRmJXQmRubzRJL3A3RTY0dDU4ZHRYajNwV0MrSW9sYWhUcHR1UnBrM1dXNTFnL3F5dVNCTThCNzBZeGhDeFRkV3R2Rm14WWxialNkMWU2YllSR1o3dWJDVi8wUHpHamdqL1ZoMm05TlNJV3RqMW5Tajc2R1RWSGgiLCJtYWMiOiIzMDRlNTVhMDZhMWEzZGQ2Y2M2MGJkYzRhMjQwMzFkNmM2ODY4ZDk1NjBiNGM3MjQ4YzEwMmM2ZDMyNWRmODNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InBlTzZ1QzV4RmtLb1U2YVFrdThTSFE9PSIsInZhbHVlIjoibEZ1cVV3RU9MbHphTENGUkpkVCt6Kzc1K0V2UmZTaFNhVm5aOUtVSVpsVDNsOHRqQ2dRTG8rcFVlRWlIblRLV2xid2c0RjM3ZlM0SlIzTmgxTy93WXorYkVXMy9BeXZuVll3eHdUV1diVEdIRnMyaDFZOENlRkdIT2d6L0Q4TktpU3JUWnZKYXN2R2diTDFWbEFmUnJlWHh2TmtzWEVhN3FOVjZtcGxtbW5Ld0tTTGRodGdXczFNZk5ocVhtb3I3Qll3NFhOdEhvczdDODRqVEM1T0prMXpYaWxuaExPSWd5NjRqb3JUdlJUbzJXQ3ErcWtIS1JRVmZ3VzJnV1RCY01Od1FkcWIrazVCRWJSUWhhOTBNTFZ5TExxTFVPR2hWaEY5VE96STF0bVpnTDFaU3FKK1daM1ZLYnh1TEhiTXdwUWQrc0h5dkNsTzZwTmNRSm5TQVJvV2lDNWJEREdGR0czNnlLYnN2VVkzNmV3SjNJbzBiNUdGc3lESXB6OStlSWoxaVBrQTRhbEk2eHdhblF1SWRLKzhQUnVmSFZFbzcwbTVGNWdUdW8yQzQzeEhGLzRwbC9XV0MvZjJBSFdaTWxkOXBENnU1M3dzWVhNM1JkRnlpMTRhOXNhR0J2Z2dqNWlyYVY3L1NLWlFpdEZuWWZhQWUrNXlBUldsMHB0SmQiLCJtYWMiOiJhYzAzNjE4MDZmOWZhMmU2OTI3M2QwMjFhMjUyMWM4NzU4NmNjNzQ3ZDUxNmFiNGRlMzk1OWIwYjEzMjk2MjVlIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Im5SRnhtVW9EMnloelZRbVJUd0FKZ0E9PSIsInZhbHVlIjoiaUpmRDgzWk4vUTUxcVo0RERlMnp0eVJlUENkSlR4RUh1TTZCenlXNExMclNkTUNJK2w1Qndsa0Q3NDRXNEtXY0h4cTRtcVRoWmhoZTJZVEgwREVna3p0ZDNnNTk5a29xUFBSeDhpSFlad3JBeEVveUc2ZFlZbGU3cVkzZUp3UkxFLzRkejlXeEZnTytmZ1NrcERuU2ZwbEtQMi83blYyVmJJK3lKTzJWcDN1ZGJqVkhnRVlwMlUvRnFkWGVJTERPTzRBd0tXVVF1dnFIc3hSMjhKbDNMYXRVaGphTHFqT3ZKYlNKNVUzcTlPMXAvUnNHcmNzTUQranFVNDBJNWx2VUxMYXltNnpBVnpvWTVuMGtFc2VTN3IyN2VtVTZ0SW1GSXpmWjczRHViN29icEdOVVJhYkY3dEFFWmJKSWhWQXc1aThEVXB1UjhHc0Z6bTVIT3Z5bHBEUlltc2IwNFd5UklaOFVJdHRLOFlzY2t5QkpaN3N0Sm5zS3VZRHQzQ1FEL2ZyMWhUTlc0TzkrNXBEaHhrZ25NdlhtSUpOZVlqUFdlZTlMb0pGc0hCS2FtNnVaQjdWaktiUENibXZTREZ1TFJmbkNvQloyRExQYkZtV0UyVFc4T1dtdERUQU1XVS9SanpYMUpCek9yTlk4eVhGa004MUlremhJbHBoRHNTK2oiLCJtYWMiOiJjODE4ZDBjYWM3M2Y1OTFjYTBhMDdiYzRkZDM1N2ZhZWY0ZTI3YTE3ZDI2N2E4MTZiMmVlOWM3NDIwOWM3YjdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2051263324\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1800299588 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5LJND5bUclZnLzzClCMEaxKzZ5Yt8AAmGjprz0I</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sUbnPperdA1I3PaRxBpMGV4YsR5AVGq0ERh2LBdJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1800299588\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1453791644 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:13:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImQ5V0hYRGRZZm51UHU2SUo5bThtcUE9PSIsInZhbHVlIjoiK1UwVXBOQzRHeVFFNjdGSUliZ2h6dUhCa2N0V2laTmM0eHJvZ1MxQlV1ck9lTlJqS1hXdTlybHZaUHI5WkMzRURCTnM2Q0llVFlQb1hhL21pbnBaUjlkRFJKaVhLd01WYUozMW9CU1B2cWh4bldZdmYySjVDRjVrd2M1RzlubHNQdERNQnpPa3FyM3Jwcm4yNjBpdlE0Rk8vd0NLTjgwVWN6cXpuekdab25RSG00dTRwcE1GSWJnN09GbGd1ajBNWDAva3pSbEU4bm9STEM4aTI5NSs1VTByQlFPODcweHZnRWhBVmJnYjRFTTl3LysxUk1zVktITGpYeXl0ZFZuUFdzdHJXU1o4UGtWQzMzUEZiQ3JTeE1kblRqUkZ5d0tqTXpmVlluOEE0SnpZS2tqTnJwNm1mV0tSMDFUdldxT3VoOFAyQzZhaGZ6SkV4aWZDOFRycm1POWdrcGRVWTA3R2U5ZjVrV0RnZlh4eGFCMTBsYWR0WURqV2JqWXNYcWNWck52NEhNVXBydzllNEg4UHdSaGE2MVNJeVMvSXVjbitXeEQzMThGZW1VOE1udXQ1emxxYm5VRU9FV2srelVybURUZGlUSkE1TllKYTFJQWpEQVFxUDdjVExEY1FyZ1dVSVRjOXJWZHUvT0gxZThWMEl0WWlQaXNrRzFieG9INHMiLCJtYWMiOiI4NjFhMTI2ZGEwYzQ0ZGQ2NjRkMjYzOGEwMmMxZjg1ODI0ZWE3YzZhYjAyZjUxM2Q2MDk4NThlOTIyY2ZkYzEzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:13:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6Ik9LQkRpQkFJVHF0Y0xsS2s3VTVQL1E9PSIsInZhbHVlIjoiUDlZcXo3UHZpVGk0ZndGdmdwLzRvRk9hUTg5V0VUT3FVUkl0R1lqTG9CWkRqK29zakdPRUs3b3Z3Y1pLemtIdUw2V3NWTUhwRDdIYVQ1cFZsL2w5MUtzVDBzM0lGdXlXTzMzYnFHdXkrQS8zZksyUTBPVjRDV1UrNkNNOHZJWmtvUnphQ0M3Y0VvaUFBK2c0YW5wUnRKWmJWaFpzWVUveTZYMFo3bmlJczBJbEdDcnh6UWY5UDh3V1VCMDBWUUgrZi9kSXlDZUZuQ0Q1YlVNZy9DUkczZ21lcUFHNUNuVGU3bElkMERnQTBmMFB2R2VmQXRQRmViUEtKZDJaQ0cyRmxxdEFIQjFSNHFEWG9aYUlGZ3VTRVhweG4wSDBPNDBxTU5PZ3RQNXhnVmduampGNlpVNEw3Kzl0TDBGYlRrOGpabWp3Qmh3NlNSYUlveWVoaGJ6MDZyb2tJWW1pd3lHOS9kNUFZZThaK1lSYkxHZU5RR0MxdjgrUXE2UEVublNGNmxIOUFoSjc4d2RlbXpxYUNBbW0vOTBnb3NoN0JyeXFGVDBxQ051bGFCblBmeDNselVhVmpTV0dLWjBKOXh0VkFFakJxWTVWVHgzdEZZNkhUcnlCUnRoRXphY3ptZS9wTTZnTEN1eFZkczNQZDZEVmRDYjBuT3BPNXBZdkhsWm4iLCJtYWMiOiJiZTQwMDAzMDljODA0OTA3NzZlNWVmMTcwZTBhNjQzZDgyNjljNTYwMjM0MTIwM2UyNWQyZGFkNDg5ZGNiMjQ3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:13:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImQ5V0hYRGRZZm51UHU2SUo5bThtcUE9PSIsInZhbHVlIjoiK1UwVXBOQzRHeVFFNjdGSUliZ2h6dUhCa2N0V2laTmM0eHJvZ1MxQlV1ck9lTlJqS1hXdTlybHZaUHI5WkMzRURCTnM2Q0llVFlQb1hhL21pbnBaUjlkRFJKaVhLd01WYUozMW9CU1B2cWh4bldZdmYySjVDRjVrd2M1RzlubHNQdERNQnpPa3FyM3Jwcm4yNjBpdlE0Rk8vd0NLTjgwVWN6cXpuekdab25RSG00dTRwcE1GSWJnN09GbGd1ajBNWDAva3pSbEU4bm9STEM4aTI5NSs1VTByQlFPODcweHZnRWhBVmJnYjRFTTl3LysxUk1zVktITGpYeXl0ZFZuUFdzdHJXU1o4UGtWQzMzUEZiQ3JTeE1kblRqUkZ5d0tqTXpmVlluOEE0SnpZS2tqTnJwNm1mV0tSMDFUdldxT3VoOFAyQzZhaGZ6SkV4aWZDOFRycm1POWdrcGRVWTA3R2U5ZjVrV0RnZlh4eGFCMTBsYWR0WURqV2JqWXNYcWNWck52NEhNVXBydzllNEg4UHdSaGE2MVNJeVMvSXVjbitXeEQzMThGZW1VOE1udXQ1emxxYm5VRU9FV2srelVybURUZGlUSkE1TllKYTFJQWpEQVFxUDdjVExEY1FyZ1dVSVRjOXJWZHUvT0gxZThWMEl0WWlQaXNrRzFieG9INHMiLCJtYWMiOiI4NjFhMTI2ZGEwYzQ0ZGQ2NjRkMjYzOGEwMmMxZjg1ODI0ZWE3YzZhYjAyZjUxM2Q2MDk4NThlOTIyY2ZkYzEzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:13:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6Ik9LQkRpQkFJVHF0Y0xsS2s3VTVQL1E9PSIsInZhbHVlIjoiUDlZcXo3UHZpVGk0ZndGdmdwLzRvRk9hUTg5V0VUT3FVUkl0R1lqTG9CWkRqK29zakdPRUs3b3Z3Y1pLemtIdUw2V3NWTUhwRDdIYVQ1cFZsL2w5MUtzVDBzM0lGdXlXTzMzYnFHdXkrQS8zZksyUTBPVjRDV1UrNkNNOHZJWmtvUnphQ0M3Y0VvaUFBK2c0YW5wUnRKWmJWaFpzWVUveTZYMFo3bmlJczBJbEdDcnh6UWY5UDh3V1VCMDBWUUgrZi9kSXlDZUZuQ0Q1YlVNZy9DUkczZ21lcUFHNUNuVGU3bElkMERnQTBmMFB2R2VmQXRQRmViUEtKZDJaQ0cyRmxxdEFIQjFSNHFEWG9aYUlGZ3VTRVhweG4wSDBPNDBxTU5PZ3RQNXhnVmduampGNlpVNEw3Kzl0TDBGYlRrOGpabWp3Qmh3NlNSYUlveWVoaGJ6MDZyb2tJWW1pd3lHOS9kNUFZZThaK1lSYkxHZU5RR0MxdjgrUXE2UEVublNGNmxIOUFoSjc4d2RlbXpxYUNBbW0vOTBnb3NoN0JyeXFGVDBxQ051bGFCblBmeDNselVhVmpTV0dLWjBKOXh0VkFFakJxWTVWVHgzdEZZNkhUcnlCUnRoRXphY3ptZS9wTTZnTEN1eFZkczNQZDZEVmRDYjBuT3BPNXBZdkhsWm4iLCJtYWMiOiJiZTQwMDAzMDljODA0OTA3NzZlNWVmMTcwZTBhNjQzZDgyNjljNTYwMjM0MTIwM2UyNWQyZGFkNDg5ZGNiMjQ3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:13:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1453791644\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-527778824 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-527778824\", {\"maxDepth\":0})</script>\n"}}