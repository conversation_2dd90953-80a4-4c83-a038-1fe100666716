{"__meta": {"id": "X272b47910b72f901760be6f2543629a3", "datetime": "2025-07-31 05:20:26", "utime": **********.66657, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939225.795473, "end": **********.666599, "duration": 0.8711259365081787, "duration_str": "871ms", "measures": [{"label": "Booting", "start": 1753939225.795473, "relative_start": 0, "end": **********.586845, "relative_end": **********.586845, "duration": 0.7913718223571777, "duration_str": "791ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.586866, "relative_start": 0.****************, "end": **********.666602, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "79.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nqCFNuY0ToydARyF98VVWSuBfMKh5Qh5UzSi5rRH", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-176175764 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-176175764\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1778759090 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1778759090\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1356200155 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1356200155\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-797733110 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-797733110\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2075151205 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2075151205\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-840290609 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:20:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVhWjJ1alJCOG9wNjk1aHR2T28vNWc9PSIsInZhbHVlIjoiVEFRbmtLcnJCdDY1c20yeGFtOVJRR3AzMU40VStXQ3dKSjVhTklkRUcxdjdwYmVsRnNFRVBzaUZkclZ6clFiSURERkJtMDlGNWxhcVd6TGE5a2FuUUM5UmozNUtzbkFOVkxuWlVwajJpZnBleUxSaVdzcEJORlRoTkJ6cDJMR2haTG1TTllvY3ZCK2h2WW1wZklUZERQZkZlRlJHSXZwZ1dMeHY5c0tlUjFjZkdYUC8wK0E5aC9JMTNRME52U3VScWljbUJ1eWR6dVJkczdqTVp0cTg0MWVLR0prZ1BvMm5JZlpjbGFWQ3loSk9vOWRyV2hzZGZrSm9QOW4xTGpGeElqTkROYUNza3BRMmdwSFhQUmFVTmIyYjJOVjE3cVQ1Y0tCQjhnellseVBFdlFRL3lKMHBIM2RkY3JFNngrRGdhUjNWTzNEOUgyNXE2cTFkWkRLbUc0MnZYZmNkSGVzbWt4T0V0S011aVFsVTJFVkExZzlPOXVhMFF6cGlsSU1zVDlFVmYzdnVCcGVYY1VLVklselN6UHIySWhQcTc4enppN3gvcGdkeXppT1JvR0xvQkJkNytnL21BcUNEZ0FxNEg3OUE5LzZubm9VTXViYUJHWjE4aE42Q0NSY3B3OVpGQ2RqazUzMVcydGFhaW1aZWgrMzVRV0ptc2xWZUdEaW8iLCJtYWMiOiI1MjUzYzA5NzY0OGEzNDJmODdhMzY2MGNmMDEzMzEyOGM3NGE5ZjgwM2MzMTE2NDQyZTk3OWY4OTM5ZTExMTZjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:20:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InBuZlYzSVBCMlh4RjdZUmNWT3A2UXc9PSIsInZhbHVlIjoiMXZJMmlxMENLODllekkrMzduaUZvYUdnNCtUQWl5b2RacHNOaS9CSHczbVlaajIvalhjK0hpQmdHYWoyRks5WTR0TEFHTlBuUHdmOWdVVWhZTENaaU5FU1BrTXNpMkdaTHBwR0xNaUdWWWdGZHVWRkRicFJCem81VldlajZ5VDNhRTBiU1NJNFJqN09USGR2SjIrV3pyRnVxdEttOUtmeHZIOVdWSmhQNENMQUFFanRoN1RWRGlyVEh0b2dnKzdDTTJCVnRxNWd0bzdTTEFnTVpwamF2alBJSEoxc0QvaThUTWRvdmN6aDBJSVc4ajNNV3dSTmc1RWx4TnVBNWc2djBlTms2UmI2N0V1VzA3bUxYaHFzZ1FtbTN0b3VybE1WUlhGZ3p1K0FMUzd6Vzg1c2c1RXB4UjZtd20rekxsbzlQWm5LTWtTY2lCRUhQMi9tcWlvSVBzL00veU14a2J5NkhBWjJDcmJmWWwzQmdxY2cxMHV4ZWZjSEZlZ0RaMTVrdUwyVm1xK1ZqK2ljUm5tdG9lUnVwdHVzUDR5djlJSEZpY0F2RVM1SUo5eENrODFlemhEeVdEYUxvRG83bG1ZdWgwcE9aWnlVenEvandrSGpUYy9tQnVHcncwQ2VyOVV2S09zL0VGZGpHVmpuSE9Qa2RSV2t5SUNMcytKbWh4YkoiLCJtYWMiOiJkNDMyNzMwYjdhOTcyMGJkMDVkYzE5OTM5ZmY3ODI4YzE1ODY0ZWI0MDVhZTNkZGMwNjllYzE3ZGFjNmUzZDYwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:20:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVhWjJ1alJCOG9wNjk1aHR2T28vNWc9PSIsInZhbHVlIjoiVEFRbmtLcnJCdDY1c20yeGFtOVJRR3AzMU40VStXQ3dKSjVhTklkRUcxdjdwYmVsRnNFRVBzaUZkclZ6clFiSURERkJtMDlGNWxhcVd6TGE5a2FuUUM5UmozNUtzbkFOVkxuWlVwajJpZnBleUxSaVdzcEJORlRoTkJ6cDJMR2haTG1TTllvY3ZCK2h2WW1wZklUZERQZkZlRlJHSXZwZ1dMeHY5c0tlUjFjZkdYUC8wK0E5aC9JMTNRME52U3VScWljbUJ1eWR6dVJkczdqTVp0cTg0MWVLR0prZ1BvMm5JZlpjbGFWQ3loSk9vOWRyV2hzZGZrSm9QOW4xTGpGeElqTkROYUNza3BRMmdwSFhQUmFVTmIyYjJOVjE3cVQ1Y0tCQjhnellseVBFdlFRL3lKMHBIM2RkY3JFNngrRGdhUjNWTzNEOUgyNXE2cTFkWkRLbUc0MnZYZmNkSGVzbWt4T0V0S011aVFsVTJFVkExZzlPOXVhMFF6cGlsSU1zVDlFVmYzdnVCcGVYY1VLVklselN6UHIySWhQcTc4enppN3gvcGdkeXppT1JvR0xvQkJkNytnL21BcUNEZ0FxNEg3OUE5LzZubm9VTXViYUJHWjE4aE42Q0NSY3B3OVpGQ2RqazUzMVcydGFhaW1aZWgrMzVRV0ptc2xWZUdEaW8iLCJtYWMiOiI1MjUzYzA5NzY0OGEzNDJmODdhMzY2MGNmMDEzMzEyOGM3NGE5ZjgwM2MzMTE2NDQyZTk3OWY4OTM5ZTExMTZjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:20:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InBuZlYzSVBCMlh4RjdZUmNWT3A2UXc9PSIsInZhbHVlIjoiMXZJMmlxMENLODllekkrMzduaUZvYUdnNCtUQWl5b2RacHNOaS9CSHczbVlaajIvalhjK0hpQmdHYWoyRks5WTR0TEFHTlBuUHdmOWdVVWhZTENaaU5FU1BrTXNpMkdaTHBwR0xNaUdWWWdGZHVWRkRicFJCem81VldlajZ5VDNhRTBiU1NJNFJqN09USGR2SjIrV3pyRnVxdEttOUtmeHZIOVdWSmhQNENMQUFFanRoN1RWRGlyVEh0b2dnKzdDTTJCVnRxNWd0bzdTTEFnTVpwamF2alBJSEoxc0QvaThUTWRvdmN6aDBJSVc4ajNNV3dSTmc1RWx4TnVBNWc2djBlTms2UmI2N0V1VzA3bUxYaHFzZ1FtbTN0b3VybE1WUlhGZ3p1K0FMUzd6Vzg1c2c1RXB4UjZtd20rekxsbzlQWm5LTWtTY2lCRUhQMi9tcWlvSVBzL00veU14a2J5NkhBWjJDcmJmWWwzQmdxY2cxMHV4ZWZjSEZlZ0RaMTVrdUwyVm1xK1ZqK2ljUm5tdG9lUnVwdHVzUDR5djlJSEZpY0F2RVM1SUo5eENrODFlemhEeVdEYUxvRG83bG1ZdWgwcE9aWnlVenEvandrSGpUYy9tQnVHcncwQ2VyOVV2S09zL0VGZGpHVmpuSE9Qa2RSV2t5SUNMcytKbWh4YkoiLCJtYWMiOiJkNDMyNzMwYjdhOTcyMGJkMDVkYzE5OTM5ZmY3ODI4YzE1ODY0ZWI0MDVhZTNkZGMwNjllYzE3ZGFjNmUzZDYwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:20:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-840290609\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-876148840 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nqCFNuY0ToydARyF98VVWSuBfMKh5Qh5UzSi5rRH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876148840\", {\"maxDepth\":0})</script>\n"}}