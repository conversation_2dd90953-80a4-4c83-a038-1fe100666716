{"__meta": {"id": "X92be9712fc51eac762bedae884205b85", "datetime": "2025-07-31 06:31:07", "utime": **********.965897, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943465.769502, "end": **********.96599, "duration": 2.19648814201355, "duration_str": "2.2s", "measures": [{"label": "Booting", "start": 1753943465.769502, "relative_start": 0, "end": **********.774947, "relative_end": **********.774947, "duration": 2.0054450035095215, "duration_str": "2.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.774976, "relative_start": 2.***************, "end": **********.965998, "relative_end": 7.867813110351562e-06, "duration": 0.*****************, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fePvptJjdTaPXuUGxp4mV0Hs6NPrmO7Rr3JKKKzq", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1567171074 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1567171074\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1518614138 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1518614138\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-497904061 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-497904061\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1322846212 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1322846212\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1894492995 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1894492995\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-163682754 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:31:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRCU1JJNGJGN0g1cThLUzU3amg2Ymc9PSIsInZhbHVlIjoiL1BHTk1NWVQrMWJORXJHVFpvdjdDM2JZS1JOWHJlQ29VZEdscmFFSWMrMnRiMmxzWHovblJIQ0VkU01TMkNpYStkNFZsZHdOTnFBaVlpVDJqZGVBemtxRnNmSUtoNkVlY0t5YmNtdWw0dzN4a2tSdElCemlBRHk5ak9ic3J4bDFHVWNLTkJ0UDdseU9XQVJVZmx5bGwrNENUNC9kbXRuMGN6UEl3bml5bFd3NzJCc3VoZjEvRmMzNHBLdFBDRnFlV1pSY2xjeHE3dXhkN2xabmsxdDhxdFh1VVNGZ0dmK1R3Y2RIMG51bUtMa1FRdjhnZDVMRWtzbWRhMExTRjZBYjk2bi9sbFFpSk1ENHVOY3hDOTdDZ0xPR0FCcENpbFNQZWp6dFlaN0JBOUliTU52NnJob2ovdU4xRmVVdGorR0cvdE5SZGVuemxMSk0vRGY0czlJVm14eXlKZXk3MFd0bGtlZnNvTW53UGZkMEtCakozNG9QK1dUTmY2bDh4VlhEcWpPRU9rVFJkL3k0dTY5WnlCeHR6S0xZY2Y0aUVlcWpBdHBrdGpaQnU3TXNoVUJJbWgvQU4za01VdWxBVzFSQTBmanA5c050aFBpR1JOZWEwM0R4ZGZPbGd5Q05OS3o5T3ZIenQ5Y0dMYmNoaW55WUlBd0RXQzlHaGdTaGM4ZUIiLCJtYWMiOiJhODczNWI5YWMwYzk3OTEyMTc4NGM5ZWU0MjhjMjY5ZDQ1Y2YwODk0OTg5OWQ2YmQ0ZDA1ZDJhZWVlNTlmYzgwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IktLbG53MitIQVU0Vkt6b1F6bXJDL0E9PSIsInZhbHVlIjoiTWE5aEc3cnRJa3NSaFRLVXlSblY2L2x5L2JjOHFkMEVORVBvdFRxa29LWER2OWszVXNIT2ovRC9FT2VGbHh1cVB1ZUJCeFA5VFdhOUh5K3E2ekZNUktQUHZZUnRJUjFGb3d0cXdXbnNZaVVhckgwNzZvalB6OFBiVmcxZlBvVUpZRDlsQ3hKSklpMkZvYlBhSWpBRVNvQ1JpVUJ2YlZRdG4vYjhOVnZTK3A1V3FHQXA4OGl6a0FBSkhtRGVZaTM0K3k0SHMycXN2TTFwUjhhUWYvS2p0SHNCOUIyRTVoVkpHQzFkMGlNQWRrN2lWSnQvQlFHZTA0MWJ2bU9Qc05IU0dOdnA0WGdPUmtqbGFjY0o4WGp5UFd5NEthRU5hdDN5NEMwZUlCdDRtZ1FWUDJIN2ZLYmdmeFdNUFZPQUUyL3RDK3kvVnlBOWV5T3BXVFBvRmM5eXo5cXRYNGMwK2o5Y2VzODE0alJodlZ4REZVUmx0MysvKzZpRXB1OHRNd0t1QzlBTTdha2FNREc0OXl2eC9pclZVaGZjMjNmZzNmZDZzMFNJRDF5ZVEwTXIxNmlOK1pUak1KUCs0M3VYd1kwT29UUlRnS0g1L0FtdVBVQ3lUbzNUZ3ozUHJhRTVlQUhMdEt4N1BtRTM0WElRSlN3M2kxZGdrME5BMHR5OW9EdDciLCJtYWMiOiIyM2IwYTNlNTQ3YjI1NmNjMjE4MTk4MjcxMzM2ZDNkNjk1YjFhNGQxZTE3MzY2YmE3MGExMjEwYzI5NzEwMTEyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:31:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRCU1JJNGJGN0g1cThLUzU3amg2Ymc9PSIsInZhbHVlIjoiL1BHTk1NWVQrMWJORXJHVFpvdjdDM2JZS1JOWHJlQ29VZEdscmFFSWMrMnRiMmxzWHovblJIQ0VkU01TMkNpYStkNFZsZHdOTnFBaVlpVDJqZGVBemtxRnNmSUtoNkVlY0t5YmNtdWw0dzN4a2tSdElCemlBRHk5ak9ic3J4bDFHVWNLTkJ0UDdseU9XQVJVZmx5bGwrNENUNC9kbXRuMGN6UEl3bml5bFd3NzJCc3VoZjEvRmMzNHBLdFBDRnFlV1pSY2xjeHE3dXhkN2xabmsxdDhxdFh1VVNGZ0dmK1R3Y2RIMG51bUtMa1FRdjhnZDVMRWtzbWRhMExTRjZBYjk2bi9sbFFpSk1ENHVOY3hDOTdDZ0xPR0FCcENpbFNQZWp6dFlaN0JBOUliTU52NnJob2ovdU4xRmVVdGorR0cvdE5SZGVuemxMSk0vRGY0czlJVm14eXlKZXk3MFd0bGtlZnNvTW53UGZkMEtCakozNG9QK1dUTmY2bDh4VlhEcWpPRU9rVFJkL3k0dTY5WnlCeHR6S0xZY2Y0aUVlcWpBdHBrdGpaQnU3TXNoVUJJbWgvQU4za01VdWxBVzFSQTBmanA5c050aFBpR1JOZWEwM0R4ZGZPbGd5Q05OS3o5T3ZIenQ5Y0dMYmNoaW55WUlBd0RXQzlHaGdTaGM4ZUIiLCJtYWMiOiJhODczNWI5YWMwYzk3OTEyMTc4NGM5ZWU0MjhjMjY5ZDQ1Y2YwODk0OTg5OWQ2YmQ0ZDA1ZDJhZWVlNTlmYzgwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IktLbG53MitIQVU0Vkt6b1F6bXJDL0E9PSIsInZhbHVlIjoiTWE5aEc3cnRJa3NSaFRLVXlSblY2L2x5L2JjOHFkMEVORVBvdFRxa29LWER2OWszVXNIT2ovRC9FT2VGbHh1cVB1ZUJCeFA5VFdhOUh5K3E2ekZNUktQUHZZUnRJUjFGb3d0cXdXbnNZaVVhckgwNzZvalB6OFBiVmcxZlBvVUpZRDlsQ3hKSklpMkZvYlBhSWpBRVNvQ1JpVUJ2YlZRdG4vYjhOVnZTK3A1V3FHQXA4OGl6a0FBSkhtRGVZaTM0K3k0SHMycXN2TTFwUjhhUWYvS2p0SHNCOUIyRTVoVkpHQzFkMGlNQWRrN2lWSnQvQlFHZTA0MWJ2bU9Qc05IU0dOdnA0WGdPUmtqbGFjY0o4WGp5UFd5NEthRU5hdDN5NEMwZUlCdDRtZ1FWUDJIN2ZLYmdmeFdNUFZPQUUyL3RDK3kvVnlBOWV5T3BXVFBvRmM5eXo5cXRYNGMwK2o5Y2VzODE0alJodlZ4REZVUmx0MysvKzZpRXB1OHRNd0t1QzlBTTdha2FNREc0OXl2eC9pclZVaGZjMjNmZzNmZDZzMFNJRDF5ZVEwTXIxNmlOK1pUak1KUCs0M3VYd1kwT29UUlRnS0g1L0FtdVBVQ3lUbzNUZ3ozUHJhRTVlQUhMdEt4N1BtRTM0WElRSlN3M2kxZGdrME5BMHR5OW9EdDciLCJtYWMiOiIyM2IwYTNlNTQ3YjI1NmNjMjE4MTk4MjcxMzM2ZDNkNjk1YjFhNGQxZTE3MzY2YmE3MGExMjEwYzI5NzEwMTEyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:31:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-163682754\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fePvptJjdTaPXuUGxp4mV0Hs6NPrmO7Rr3JKKKzq</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}