{"__meta": {"id": "X2a19331399884443b7d13d082a41787c", "datetime": "2025-07-31 07:57:02", "utime": **********.816967, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[07:57:02] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.810465, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753948621.501223, "end": **********.816998, "duration": 1.315774917602539, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1753948621.501223, "relative_start": 0, "end": **********.516713, "relative_end": **********.516713, "duration": 1.0154898166656494, "duration_str": "1.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.516729, "relative_start": 1.0155060291290283, "end": **********.817001, "relative_end": 3.0994415283203125e-06, "duration": 0.30027198791503906, "duration_str": "300ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51854408, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.679954, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.03663, "accumulated_duration_str": "36.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.589404, "duration": 0.00315, "duration_str": "3.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 8.6}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.6165252, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 8.6, "width_percent": 3.631}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 79 or `ch_messages`.`to_id` = 79 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["79", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.627571, "duration": 0.023350000000000003, "duration_str": "23.35ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 12.23, "width_percent": 63.746}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 79", "type": "query", "params": [], "bindings": ["client", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 364}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.657663, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:364", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=364", "ajax": false, "filename": "MessagesController.php", "line": "364"}, "connection": "radhe_same", "start_percent": 75.976, "width_percent": 2.293}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.700912, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 78.269, "width_percent": 1.529}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7164319, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 79.798, "width_percent": 2.02}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7246811, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 81.818, "width_percent": 1.802}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.731503, "duration": 0.006, "duration_str": "6ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 83.62, "width_percent": 16.38}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 551, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1050181895 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1050181895\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1318720554 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1318720554\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2127108329 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2127108329\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1391904700 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlRLVXZySTZDYUFLcnBiZkFJaUp2Y2c9PSIsInZhbHVlIjoiUzEyMHZ4YSt2bFlIKzZ4MXBnT2FQUmVGc0RsbEtTazRaYjFaQWxSTHRQenl5TzVyTW5wMlRibmlYYXRvaVlWOVFXNzY0dmM1dWVJZEo1L29DVzJ3WTZ3YUZRSE5NOC95TXlXN3BVc1hZZmRBNzdpRGtwRUpDdjArcmtWSjYxMnMweVhLTVNyZnRsS3RZWWxnMXU4bVMvZmovdFRXRW1nVkVyemxSMWNkUUdVem1Ea0RBUEJ0cTFpY2x4UHZyUkxsSEVFVjEvUjdLdldTSVlwcENwa2l4YUVnRUhoK3paTnQ2MHhodEYwS3oxTlFPV0Y4S1NQeU1ITmNwcG5PQnlpRWdBT3hydkcvZUZMa3dzMmJQN0RYQUpwRDBjU2p4SXBrY2plc0pscWYzSVNoT3kvVzl3TlNkaTN4a3BHS0Eydmo2aTFwa3RiWTNlN1Z2YjFTLzFkaXJLd2szTVR6UVVtODRYODNRUC9NV0dQMGhWdWxYTjUwWnRxWWkvanF6MHZtb2JzcSsxMW9NOEVZU1RKYjNQNkcrYi8vc3pRSHFGem9Gb25wUzl0aUNzOXlmRWpaU2RPcm9BWlZJUzZPSTRsVWdCTnVZSmF1TytXeWNXYjFFa0VFUHQxNkJsNHR3bVNvZ3UxUlA5c2ZkbjlZc3U3QWdlYnhiOGIwNHptSGJsTHgiLCJtYWMiOiI3MTBkNDQ1ODcyMTM3NTVjOWFkOGJlNjJiMmFhNzg0MDZiYjAxY2ZiYzNhODliZGRiYjgxZTcxYWVmZDQ1ZWNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImppaWlQMU9uV29QLysyclFkYXdFOGc9PSIsInZhbHVlIjoibngwbnU5eDFNaVptSVhzOWVVRzhTV3I4bXgzYUFqYlFOQTEzSDY0SlVWTnUxODBBZ05qY1FVVlhOL2xNYWViZjNrN21tdkdoT0k4VVU2akJSTFBQdWRZdEV5K0p6emVHZjJORnVVeWZYNXF3NEJ4Mm1mdVBIdjZwdHQzWVFaWFhVREdiZ0pJMUVMTmRielZacGVjRnJLdUVHaW9ZaVpIS2RoTzI2SGxHV0JteUJCR2JxTzdNQXNNOExlZ0ZRZHFXY2Y1S1JPSExERWRRdnJIdERocWgzRTc5VlpPVlhoWFdMR3hTNFJXeVVBRktmVHYwQ094K0wvR1NoQ0krb2U0cjU2SUdpYW9mTHRQdGFxcjJxUUZjRWRWbjNjRktlaU1uSzdabi9mUFc4eFlZYW1kWldISTJydFZZdTcrZ1FudGZIZC9VcU5MWUZHS0JURkt3M1YwRnZPaGdYWG9mamllWkZYOHlGbDc0QzNIcS9NWG5ZNTUyUXJwRTBwSXBSMkcrTy9JRHF6ZHk4aUoyUzlsSVlpRnlJUGRKRkJLblRmYS9GVHdzUWx0VHNoNFduMUJNK29rSG9QYzhHRExLU2VFZCtpb0M2clh0OWhUK3FoWXpjV1dWeVhxNStNcjArYjNXUS9mWHBGdVNvZnJ3VVZFL3lQMkxBOFFueHZaOE05NFAiLCJtYWMiOiIxZTYzZDdmNzQxZTNlM2U4ZDU2Y2Y1MDM5MTBlNzc2MzNhNjllMjJkZGEwMWZlZTk4OGM2YTQ1MmYyNDBkMjY4IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Ik4wYjZtWnM0Y1FkTnpZUnZYNm5jdWc9PSIsInZhbHVlIjoiVkhoT0d5K2xBeDRJOUl6cjQxWk9DdjNaYmtucFBPNXVETDAvOG9lZW10TmN0NGxFQ2l4eVFlSlZUUGh5c2k2MmlxVUVJbFlmODRCZ0VIMEVRb3ZETlVwYllvZ3hUYlpKZnRWMG5VNTYxVWxwS090aWJGKytPc25maTE4cWp6dnlxOE4xRlhDSFFmZUc1NThqTGswKzdpakVPYmc3TU5LazFOYm5nR2lSSEpqaUI4aHBQVGlja1QxWHpITEVnU2NtblJncHJSVTFzLzcvRjAwblZkbnlaNlkwdXUrZ05SOHdFZlNzbjFRQjV0SUZMOGtlTGhKZWVIUVdiWU5RZVhRWkxWdDl6UkhIVHhCbE5nQk5jMUMvaVBZdFBqM21qYmNDMVlzc1o1RU5zYlJrL2pxTlRhRjNPZEJyVHBsa3BQMisxQ3VXNnJiblVNWHhwamg1RjZudGIyanBpNndDSlVSTEFvS0NoVWVRUXVORkxYdE5nbkhEa1VIbTlrZkxTalduYW9wVXQ2Z29ucmpuSzhrQ3dSaTE4TzY1OXNuRHVxRElMckI0N2tpZWJPUEJiUmgrVVRIQkJpbGJUenh1eW9XK09XVFVaeXJLODgySEF5ckZMWTVORXQwMzlsL0hmNzNQYVZTVjNFNjhWTG9RdmoxZG1nelY3VlJqS3BpeEhZR0oiLCJtYWMiOiI3NTc1OThjODVjMTdlNDA5ZjRlMTdkYmI2YjA2NGQ0YWQ3Y2JjMGM0NmQ4MGQ3NzdlZDVmNzEyNGRjNjdhOGIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1391904700\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-325468905 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21K3ZmFzZyr4s2pH9aKWRqwEKhgdUCnDfhl6jx78</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-325468905\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-877145006 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:57:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZtN2pUQ0k4YkNmUGtkcHloN0p3bWc9PSIsInZhbHVlIjoiS3lzeFVVbm4weW13dnBSYURzakdsMmdsSkg4RW9nVXh0cDk0aEhCREgrSm9rYmpYaGpDeHNXTExMWkhnTUR4WWdCeVN5bm1uWERFS05BYi8wWldLUzlCTjl0c2djdVpzeDRjQXgyN0tKcHJ5TlEwekZkbXZ6c2svYVNWa2Z6K1lGbXV1eGpyT3UxdldJVUpXWm9JVTRudzUvd0swRHoyUHhUeVRnTVpreGVHR2l6OTkwWnY5SDRtQlBud1F3ZG9MKzNEbXB1UXI2UWFndERJenExbkhFbHM4V2F4ZnpuY1RRNHNtS2ZmUjAvWTg4bFRjM0h0UnpralhHRXRJYlQ0Z2RWSFZzUDVGWGh2alVsN2Jvams3ZC9UMDFvY1ZTRHB3TEhZelRma3QzZDJKLytnNVRWSkNYckZmL2lqZXNZYnQxVllYN0VhR21UTm1zK2dRN2wzOUk1bG9KU1RnTG00S0tIZm41Rk4zdG1NWVVyZVZRemZ5dy95SVJPV2hlb0tyRzZKZ2pOU1hMZStKTEx0M1I1MlFqdTZVQWIyNktnSFEzUlZhc0s0dUs3azl1VEFyNGVqSG9SWmhpbTlFcFM4TGc1eUJBRmRnK2J5eXBrTkxsTzVsOGttOHhZdWtFenFHWldwTUxpZ2hkMTJvZlhocERudVU0S1kvZWtVRU8xM2siLCJtYWMiOiJmYzRkZWM1NDAzODI3OGNjYjQ0Y2YwYmNhYTE1ZmRlZjZmODk2MGZmN2U1ZGRiYTYzMjAyNWM2YjVhZDIyN2RhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:57:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IjRzZnBsdXFlZjhDa25NK2ptVXE0L1E9PSIsInZhbHVlIjoiUmFMUmIycjNRZGtYQ1lzKzZmdUdRMmlaOTJ1OG9LVStDeWtjU1E4OWRUb1NZR3FkanhIbERpUjdZMGR0R0VhRTFGc0Z1c2pRaHErTXQrU3BlbWoxV21mLzBTU3FvdFlUTE9vaEg0aDZ2b2FvWUFWWjJxb2JPMXhlSmR0ZzFjdkRKNXQ0TXVnWHRjdUFuWk9MTFd3dHZMdXdleWpGZmdqTUVTRlp3cUJFZEtNTXI1ODdPMW94SjU0WWp4THA5VjZSbXNhc2d2Z2wxYnFxTXdUVHJIT1lCQzl1L091S3lkQ1MwUjUwRDJxSVdtRWNWa2laNUlNY0hhbmRzYklDQndscDJnQm1LcGhRMGxudjFmUStMMGM4dUtieWVicGd3VWlPS3JpK1NRUG40VmMwbHJTblM5SVl3alVPdW5xY01yUEh5RjlZM09Bek4zMHN2NnBFNHJzdjZqcTFBa2xpRXZWcXlUWFA5clR2OTU0Mm44YWo3bkJaSXVGdnpnMmZXMmxLMVNnK1d3ckFEWkNoR2pKYjRxenAvS2lVQmh2RSsyZWlVMUcyU1FtclUxVDBxczhDRVlMUDlObnZ6R01FU0grWm9KRnJnNTYwZzJ2OVJ3bjJ5NHV0bDA0MndidFhIM1RrbmIwK2RQbFhwdlFQNWZSL2xjKzZtcTRZeXpwdzBIMG4iLCJtYWMiOiI2YmFjYmFkNWRiNjU3YjVkMTMyNWQ2ZDU4YWU3M2ZiYThhMDA1YmE0N2FlMjg4MzE5Yjc3OTdkZjkwZjk2NzI1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 09:57:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZtN2pUQ0k4YkNmUGtkcHloN0p3bWc9PSIsInZhbHVlIjoiS3lzeFVVbm4weW13dnBSYURzakdsMmdsSkg4RW9nVXh0cDk0aEhCREgrSm9rYmpYaGpDeHNXTExMWkhnTUR4WWdCeVN5bm1uWERFS05BYi8wWldLUzlCTjl0c2djdVpzeDRjQXgyN0tKcHJ5TlEwekZkbXZ6c2svYVNWa2Z6K1lGbXV1eGpyT3UxdldJVUpXWm9JVTRudzUvd0swRHoyUHhUeVRnTVpreGVHR2l6OTkwWnY5SDRtQlBud1F3ZG9MKzNEbXB1UXI2UWFndERJenExbkhFbHM4V2F4ZnpuY1RRNHNtS2ZmUjAvWTg4bFRjM0h0UnpralhHRXRJYlQ0Z2RWSFZzUDVGWGh2alVsN2Jvams3ZC9UMDFvY1ZTRHB3TEhZelRma3QzZDJKLytnNVRWSkNYckZmL2lqZXNZYnQxVllYN0VhR21UTm1zK2dRN2wzOUk1bG9KU1RnTG00S0tIZm41Rk4zdG1NWVVyZVZRemZ5dy95SVJPV2hlb0tyRzZKZ2pOU1hMZStKTEx0M1I1MlFqdTZVQWIyNktnSFEzUlZhc0s0dUs3azl1VEFyNGVqSG9SWmhpbTlFcFM4TGc1eUJBRmRnK2J5eXBrTkxsTzVsOGttOHhZdWtFenFHWldwTUxpZ2hkMTJvZlhocERudVU0S1kvZWtVRU8xM2siLCJtYWMiOiJmYzRkZWM1NDAzODI3OGNjYjQ0Y2YwYmNhYTE1ZmRlZjZmODk2MGZmN2U1ZGRiYTYzMjAyNWM2YjVhZDIyN2RhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:57:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IjRzZnBsdXFlZjhDa25NK2ptVXE0L1E9PSIsInZhbHVlIjoiUmFMUmIycjNRZGtYQ1lzKzZmdUdRMmlaOTJ1OG9LVStDeWtjU1E4OWRUb1NZR3FkanhIbERpUjdZMGR0R0VhRTFGc0Z1c2pRaHErTXQrU3BlbWoxV21mLzBTU3FvdFlUTE9vaEg0aDZ2b2FvWUFWWjJxb2JPMXhlSmR0ZzFjdkRKNXQ0TXVnWHRjdUFuWk9MTFd3dHZMdXdleWpGZmdqTUVTRlp3cUJFZEtNTXI1ODdPMW94SjU0WWp4THA5VjZSbXNhc2d2Z2wxYnFxTXdUVHJIT1lCQzl1L091S3lkQ1MwUjUwRDJxSVdtRWNWa2laNUlNY0hhbmRzYklDQndscDJnQm1LcGhRMGxudjFmUStMMGM4dUtieWVicGd3VWlPS3JpK1NRUG40VmMwbHJTblM5SVl3alVPdW5xY01yUEh5RjlZM09Bek4zMHN2NnBFNHJzdjZqcTFBa2xpRXZWcXlUWFA5clR2OTU0Mm44YWo3bkJaSXVGdnpnMmZXMmxLMVNnK1d3ckFEWkNoR2pKYjRxenAvS2lVQmh2RSsyZWlVMUcyU1FtclUxVDBxczhDRVlMUDlObnZ6R01FU0grWm9KRnJnNTYwZzJ2OVJ3bjJ5NHV0bDA0MndidFhIM1RrbmIwK2RQbFhwdlFQNWZSL2xjKzZtcTRZeXpwdzBIMG4iLCJtYWMiOiI2YmFjYmFkNWRiNjU3YjVkMTMyNWQ2ZDU4YWU3M2ZiYThhMDA1YmE0N2FlMjg4MzE5Yjc3OTdkZjkwZjk2NzI1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 09:57:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-877145006\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1429966315 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429966315\", {\"maxDepth\":0})</script>\n"}}