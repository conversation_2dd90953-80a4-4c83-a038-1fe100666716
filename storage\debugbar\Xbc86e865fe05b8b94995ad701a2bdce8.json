{"__meta": {"id": "Xbc86e865fe05b8b94995ad701a2bdce8", "datetime": "2025-07-31 06:28:59", "utime": **********.965065, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943337.283926, "end": **********.96511, "duration": 2.6811840534210205, "duration_str": "2.68s", "measures": [{"label": "Booting", "start": 1753943337.283926, "relative_start": 0, "end": **********.76461, "relative_end": **********.76461, "duration": 2.4806840419769287, "duration_str": "2.48s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.764648, "relative_start": 2.***************, "end": **********.965115, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "200ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NUkDY7TT6s6nmDMq752fXjbRB7casB1t6uKXLhI8", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-553493520 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-553493520\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1780506830 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1780506830\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2136653203 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2136653203\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-390857735 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-390857735\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1745766622 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1745766622\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1547941625 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:28:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdXeVR1UW8wQjFxNkVlcTdDRFBQSVE9PSIsInZhbHVlIjoiZWFQTjEzVFgydFgxTGs5U2Zsa2VTRVBlaVZnSkxXUWYzWERKSUcyNkNCdU1VQmhTZFdSanZXQXZMNzJOWUQ0Sm1idURCSXJKQjdzVExyZWs2YlhNUVFkSjhGOGdWSXpIdWVuQ09CaXZZZWIzR2xVSGFNUmhYSXRibTdIbkpIWEllZTAvRzYvZ0o1RG1Kc2xiMFJvN3RxSUoxSWRwM3h5RjZoYWdlYlBjMVBsYzdsb1ZaYTcrU293WnlrRk9CMGVEN2NyOE9RRHBQRDJrNmNQZHpEWTRPYm03VlloUjgrd2ZCQy9rcUl4ZDhtU0thczhLYWE2ZTlmWkZ2NnRvTFhFY2NheEhHMitiQmpuUlhMb0lWdm5RUlZrMkd3N1ZtTEFZRXBvUDQ4RDZFK3BWRmZFakQ1b3FtNldiUFAyc2ZjUk5hd2lkckRNeTVQdm1nZUxHN05JOVIxS2ZXekJERW5qR09HbFRuTU84NlRsb2VXMDF4N2RYU3Jrc3pvdXY4eGJrcWU3MWkxbGM5cUE1OFRPejd3SktlSzlMbjllL0FxMXNmblc3SmNiU29VVitDWHNUWXpTV0xUNEdLL3lWeDBoMnVGV2Vrc3RKbmNXd1BGUlUxOVVmdWQ0SWs4WlRiaEtCckZERVMxa3daK0ovYmZDRW5CZTVhM2tqM091Nm1Ja0wiLCJtYWMiOiI3NWJkYTQ2NWM4NmI3NjcwNDk5MzFhNmIxZDM5MjFhZTdlMzU2ZjBiNjlkOWUwMDMxYmYyM2M3NDIwYzRmNDA2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:28:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlNGYzdWRHJTNFAwVHlheVVTNmMrTlE9PSIsInZhbHVlIjoiU2c4S1MyM3ZITHNLUEpTS0x5Z1pnbWRmZnlNWHZmaTBiWDdNazVPWGM4ZzRrTFRqODVQbnJzZExMQ3B0R0ZpY25OWnRNWnNld0pJWERXV1FFbUVsMFdmQ0NQSitMWjNrSG1rdmxtbXNEeUpVSzBScWZMWXRyWDA1d0lnU29BeDUzOGNZMjNPQjJiWUkweTlJWWkvSHZuZ25wcGpJWHNKS2E0Vi9GekZyUmJQWnpZM1d0NGRPTlFNeGdjcm5qalVXZHdHNEY1cXYxak1Yd1Y4THBTZWdqWWhlczQ1MXUxTEZJc0NlYzNJbkp6ajdCZ3l6c3FnSldRUzZIWFY3NGp0RUM2c1h1dDAxSHhSN2ltQmREQnA3eFYzVmJWU1lPRmxQNXVNVm5ENzlCQnFVb3c1TGZxTTVrQUJIelBwQ1VUbG51WEV1Z1pRSG0wVU94UUdFb3hQUHRJYUVhUGlneXE1VStHclp0bVRkbENxa3FLejZxZi9od0dmdEdmRGVvVjFrSTFwc3NmL2lTYTRHYlhWSVJpV0FrZlYzU2JjbXlZMEVtUWNkd3JpUkNzc2UraTdKTTZqRlNxQTlTN0d6U0pvUWh4dlBaNUQwUlR6UFRCOGRFclFoM0M5MUx6bGdjVk16THk3Wk5YRExiclVjN2xBblExYVdBZ3JCNjFiQjM5S2ciLCJtYWMiOiI4YjZhYjFlZjFiZWQ0ZjJmMTkwYmUwYjZjYzIwZGI4MjQxNjI1NjNjOGQzYjcwZTUxYWI4ZWU0NTkwYTQxMDgyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:28:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdXeVR1UW8wQjFxNkVlcTdDRFBQSVE9PSIsInZhbHVlIjoiZWFQTjEzVFgydFgxTGs5U2Zsa2VTRVBlaVZnSkxXUWYzWERKSUcyNkNCdU1VQmhTZFdSanZXQXZMNzJOWUQ0Sm1idURCSXJKQjdzVExyZWs2YlhNUVFkSjhGOGdWSXpIdWVuQ09CaXZZZWIzR2xVSGFNUmhYSXRibTdIbkpIWEllZTAvRzYvZ0o1RG1Kc2xiMFJvN3RxSUoxSWRwM3h5RjZoYWdlYlBjMVBsYzdsb1ZaYTcrU293WnlrRk9CMGVEN2NyOE9RRHBQRDJrNmNQZHpEWTRPYm03VlloUjgrd2ZCQy9rcUl4ZDhtU0thczhLYWE2ZTlmWkZ2NnRvTFhFY2NheEhHMitiQmpuUlhMb0lWdm5RUlZrMkd3N1ZtTEFZRXBvUDQ4RDZFK3BWRmZFakQ1b3FtNldiUFAyc2ZjUk5hd2lkckRNeTVQdm1nZUxHN05JOVIxS2ZXekJERW5qR09HbFRuTU84NlRsb2VXMDF4N2RYU3Jrc3pvdXY4eGJrcWU3MWkxbGM5cUE1OFRPejd3SktlSzlMbjllL0FxMXNmblc3SmNiU29VVitDWHNUWXpTV0xUNEdLL3lWeDBoMnVGV2Vrc3RKbmNXd1BGUlUxOVVmdWQ0SWs4WlRiaEtCckZERVMxa3daK0ovYmZDRW5CZTVhM2tqM091Nm1Ja0wiLCJtYWMiOiI3NWJkYTQ2NWM4NmI3NjcwNDk5MzFhNmIxZDM5MjFhZTdlMzU2ZjBiNjlkOWUwMDMxYmYyM2M3NDIwYzRmNDA2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:28:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlNGYzdWRHJTNFAwVHlheVVTNmMrTlE9PSIsInZhbHVlIjoiU2c4S1MyM3ZITHNLUEpTS0x5Z1pnbWRmZnlNWHZmaTBiWDdNazVPWGM4ZzRrTFRqODVQbnJzZExMQ3B0R0ZpY25OWnRNWnNld0pJWERXV1FFbUVsMFdmQ0NQSitMWjNrSG1rdmxtbXNEeUpVSzBScWZMWXRyWDA1d0lnU29BeDUzOGNZMjNPQjJiWUkweTlJWWkvSHZuZ25wcGpJWHNKS2E0Vi9GekZyUmJQWnpZM1d0NGRPTlFNeGdjcm5qalVXZHdHNEY1cXYxak1Yd1Y4THBTZWdqWWhlczQ1MXUxTEZJc0NlYzNJbkp6ajdCZ3l6c3FnSldRUzZIWFY3NGp0RUM2c1h1dDAxSHhSN2ltQmREQnA3eFYzVmJWU1lPRmxQNXVNVm5ENzlCQnFVb3c1TGZxTTVrQUJIelBwQ1VUbG51WEV1Z1pRSG0wVU94UUdFb3hQUHRJYUVhUGlneXE1VStHclp0bVRkbENxa3FLejZxZi9od0dmdEdmRGVvVjFrSTFwc3NmL2lTYTRHYlhWSVJpV0FrZlYzU2JjbXlZMEVtUWNkd3JpUkNzc2UraTdKTTZqRlNxQTlTN0d6U0pvUWh4dlBaNUQwUlR6UFRCOGRFclFoM0M5MUx6bGdjVk16THk3Wk5YRExiclVjN2xBblExYVdBZ3JCNjFiQjM5S2ciLCJtYWMiOiI4YjZhYjFlZjFiZWQ0ZjJmMTkwYmUwYjZjYzIwZGI4MjQxNjI1NjNjOGQzYjcwZTUxYWI4ZWU0NTkwYTQxMDgyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:28:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1547941625\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-441473327 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NUkDY7TT6s6nmDMq752fXjbRB7casB1t6uKXLhI8</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-441473327\", {\"maxDepth\":0})</script>\n"}}