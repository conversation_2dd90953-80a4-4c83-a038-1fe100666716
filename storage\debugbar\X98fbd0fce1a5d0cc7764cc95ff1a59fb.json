{"__meta": {"id": "X98fbd0fce1a5d0cc7764cc95ff1a59fb", "datetime": "2025-07-31 06:22:14", "utime": **********.329569, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753942933.272432, "end": **********.329603, "duration": 1.0571708679199219, "duration_str": "1.06s", "measures": [{"label": "Booting", "start": 1753942933.272432, "relative_start": 0, "end": **********.209384, "relative_end": **********.209384, "duration": 0.9369518756866455, "duration_str": "937ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.209409, "relative_start": 0.****************, "end": **********.329606, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "120ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "psVOLcOuHZ5YwHGR9G5NuCWkUhV3A7sNzLq0NE5x", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-883556880 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-883556880\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-227226241 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-227226241\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-190412616 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-190412616\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1216513932 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1216513932\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-626487371 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-626487371\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-614706552 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:22:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii8zTHB4ZXVPSC9hNGxpTjRqUnpZOVE9PSIsInZhbHVlIjoiRG1LdDJsTTY2WE15YnB5SUVkVEpsdTN3a2ZKNGlUZy9hc1ZrL054YW9nQzU1akhXWnMvbUlpWjYzclYrV3FaWE9ONDRqSkpmbGYvQTBaQ3MvSU1Ec1NSaytOZTc3Y05ONW5YdHRQYVgwUWQyN0RBRjk1L3hnY1pBRVg2c2hQMURpU3ZMOXZwRFVwSWZrd3BaMUdCMHF2NEI5aVJ6YjE0QTN2cXNaeXZWcVNEQzF2K1EvQ00yc29SMzR0SUsrUnNUVWN5b29Tayt6cy9oWW96WUNpcjF4OWlnWXozVHAyclNUenVqU094aERrR0QxV28rdjFaSnNoNCsyditaaXB1ejh2Nk1YYVpkMmJJNVAwSHd4SjJFcDlUZFRONVREQ3o4TWJ6cDBmZTMwL3lBN3ZqRXRSYVBVQ0E0Z0N5dExyTEdGRTJidW9ic3FFbUFmUVlPRUJqUGdwN21iWXBuMmJ5emx3UXJkN3h6QldzTnowRUVIbHJCWFJNOXliTEgyWjBXZWhMZ1hneStlVmZ0ZXFCMEZFZWZDNzNoWEJKZFRUK1Nhck5HUlQyU25sYm51SDZJb2xsYjEzWWZFOEl5eVZtbVBtL0NSUmJNU2Zva2xDZHpOVWFHU3B2YkU5Q3diQks3UDM1MlU4Z0NKc2xzcitiQjd4Z2ZoWTMySEVQK1V6Z0EiLCJtYWMiOiJiZDI0NDQzN2E5NGNhYzYzYWE3YjFkMGEyY2JiOGNhMzg3NjRiM2Y5MGRiNTlhYzAwY2FlMmU4YzA5MGRhMWMzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:22:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImVkeDRLcVJ2bm5EQkYxb2RkeGhRc1E9PSIsInZhbHVlIjoiUmo4WGFJZXhkVU1pem1Kc0NmVFpzcGt5b040NmNqaURsdFM5N0RQdzd3RUFjcE9VUHJwdTdXYVAvTEcvNks4VmhrZTU5NmxsM29XL1pCRjZjeDJZU1ZKVFVzOVFDTXp0amVlSGdZR09UY3UyQWFqVTlNbHF5bUhGaG1NVEpTVjgwNjdLT2gvdEZmOGgyQjZxRTFmbm9xZjlaL2hHc0hoWHVpTEtEd2JWZzExNHRycVhUSWFRQzhtTTNMOTY5clZwVy8yL2NNKzF2eXg5YXJyVnV6UXRLRVZURFdwNW5IRDNtRjNCTGY1ZFMxQWt0UEUwSi8zTnZJZWxQOGo5S29MQW44VXhjVC84WnczajYvSVdaeFZycnhOTHRVZDVMYnJ0bFpLTEd6SFhhQ204ZnlrbFNuMWxKY2QrazNvSEdKTWZmTFF6bU1xVnpKQmRhS1F0YnFBREZqcm5uTGVCNXdDMjgydlpxRnY5OWVINWdMNk1YQitybFZCVnBLelJHYmxVaFMyUUxWelVMZk5PemlFRmNtV3NaOGV3b3Nnc1FEV0FPR093U2x4aXIxNExHZS9WL1NCejFlTUltRzB5OFE1aFo5S1l4dkhXUlNJNnNIVTRBLyswQXZpbUFTbThCblZuSXFNSys0alZlMmRCemR2K3hNdTBUY1FnZHZ2VkFTZXgiLCJtYWMiOiIwOGYzZjA2ZjFlMTk2ZDkxOGQxNDQ3YmE2NTc1ZjQ3YTgxZDE1N2U0YmI0M2Q0ODEzOTU3M2EwYTZjYmMwODhjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:22:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii8zTHB4ZXVPSC9hNGxpTjRqUnpZOVE9PSIsInZhbHVlIjoiRG1LdDJsTTY2WE15YnB5SUVkVEpsdTN3a2ZKNGlUZy9hc1ZrL054YW9nQzU1akhXWnMvbUlpWjYzclYrV3FaWE9ONDRqSkpmbGYvQTBaQ3MvSU1Ec1NSaytOZTc3Y05ONW5YdHRQYVgwUWQyN0RBRjk1L3hnY1pBRVg2c2hQMURpU3ZMOXZwRFVwSWZrd3BaMUdCMHF2NEI5aVJ6YjE0QTN2cXNaeXZWcVNEQzF2K1EvQ00yc29SMzR0SUsrUnNUVWN5b29Tayt6cy9oWW96WUNpcjF4OWlnWXozVHAyclNUenVqU094aERrR0QxV28rdjFaSnNoNCsyditaaXB1ejh2Nk1YYVpkMmJJNVAwSHd4SjJFcDlUZFRONVREQ3o4TWJ6cDBmZTMwL3lBN3ZqRXRSYVBVQ0E0Z0N5dExyTEdGRTJidW9ic3FFbUFmUVlPRUJqUGdwN21iWXBuMmJ5emx3UXJkN3h6QldzTnowRUVIbHJCWFJNOXliTEgyWjBXZWhMZ1hneStlVmZ0ZXFCMEZFZWZDNzNoWEJKZFRUK1Nhck5HUlQyU25sYm51SDZJb2xsYjEzWWZFOEl5eVZtbVBtL0NSUmJNU2Zva2xDZHpOVWFHU3B2YkU5Q3diQks3UDM1MlU4Z0NKc2xzcitiQjd4Z2ZoWTMySEVQK1V6Z0EiLCJtYWMiOiJiZDI0NDQzN2E5NGNhYzYzYWE3YjFkMGEyY2JiOGNhMzg3NjRiM2Y5MGRiNTlhYzAwY2FlMmU4YzA5MGRhMWMzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:22:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImVkeDRLcVJ2bm5EQkYxb2RkeGhRc1E9PSIsInZhbHVlIjoiUmo4WGFJZXhkVU1pem1Kc0NmVFpzcGt5b040NmNqaURsdFM5N0RQdzd3RUFjcE9VUHJwdTdXYVAvTEcvNks4VmhrZTU5NmxsM29XL1pCRjZjeDJZU1ZKVFVzOVFDTXp0amVlSGdZR09UY3UyQWFqVTlNbHF5bUhGaG1NVEpTVjgwNjdLT2gvdEZmOGgyQjZxRTFmbm9xZjlaL2hHc0hoWHVpTEtEd2JWZzExNHRycVhUSWFRQzhtTTNMOTY5clZwVy8yL2NNKzF2eXg5YXJyVnV6UXRLRVZURFdwNW5IRDNtRjNCTGY1ZFMxQWt0UEUwSi8zTnZJZWxQOGo5S29MQW44VXhjVC84WnczajYvSVdaeFZycnhOTHRVZDVMYnJ0bFpLTEd6SFhhQ204ZnlrbFNuMWxKY2QrazNvSEdKTWZmTFF6bU1xVnpKQmRhS1F0YnFBREZqcm5uTGVCNXdDMjgydlpxRnY5OWVINWdMNk1YQitybFZCVnBLelJHYmxVaFMyUUxWelVMZk5PemlFRmNtV3NaOGV3b3Nnc1FEV0FPR093U2x4aXIxNExHZS9WL1NCejFlTUltRzB5OFE1aFo5S1l4dkhXUlNJNnNIVTRBLyswQXZpbUFTbThCblZuSXFNSys0alZlMmRCemR2K3hNdTBUY1FnZHZ2VkFTZXgiLCJtYWMiOiIwOGYzZjA2ZjFlMTk2ZDkxOGQxNDQ3YmE2NTc1ZjQ3YTgxZDE1N2U0YmI0M2Q0ODEzOTU3M2EwYTZjYmMwODhjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:22:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614706552\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1160357775 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">psVOLcOuHZ5YwHGR9G5NuCWkUhV3A7sNzLq0NE5x</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1160357775\", {\"maxDepth\":0})</script>\n"}}