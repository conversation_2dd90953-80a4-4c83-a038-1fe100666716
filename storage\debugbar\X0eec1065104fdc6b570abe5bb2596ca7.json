{"__meta": {"id": "X0eec1065104fdc6b570abe5bb2596ca7", "datetime": "2025-07-31 06:51:56", "utime": **********.523347, "method": "GET", "uri": "/expense/1/description", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753944715.514038, "end": **********.523381, "duration": 1.009342908859253, "duration_str": "1.01s", "measures": [{"label": "Booting", "start": 1753944715.514038, "relative_start": 0, "end": **********.371391, "relative_end": **********.371391, "duration": 0.8573529720306396, "duration_str": "857ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.371404, "relative_start": 0.8573658466339111, "end": **********.523384, "relative_end": 3.0994415283203125e-06, "duration": 0.15198016166687012, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47262352, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET expense/{id}/description", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ExpenseController@getDescription", "namespace": null, "prefix": "", "where": [], "as": "expense.description", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=1211\" onclick=\"\">app/Http/Controllers/ExpenseController.php:1211-1240</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02681, "accumulated_duration_str": "26.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4316452, "duration": 0.02332, "duration_str": "23.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 86.982}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4777591, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 86.982, "width_percent": 4.961}, {"sql": "select * from `bills` where `id` = '1' and `type` = 'Expense' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["1", "Expense", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 1218}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.487695, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "ExpenseController.php:1218", "source": "app/Http/Controllers/ExpenseController.php:1218", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=1218", "ajax": false, "filename": "ExpenseController.php", "line": "1218"}, "connection": "radhe_same", "start_percent": 91.943, "width_percent": 4.439}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ExpenseController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ExpenseController.php", "line": 1218}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.498984, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ExpenseController.php:1218", "source": "app/Http/Controllers/ExpenseController.php:1218", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FExpenseController.php&line=1218", "ajax": false, "filename": "ExpenseController.php", "line": "1218"}, "connection": "radhe_same", "start_percent": 96.382, "width_percent": 3.618}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Bill": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBill.php&line=1", "ajax": false, "filename": "Bill.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/expense/1/description\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/expense/1/description", "status_code": "<pre class=sf-dump id=sf-dump-636613012 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-636613012\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-793159024 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-793159024\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1744474857 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1744474857\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1129448174 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlRLVXZySTZDYUFLcnBiZkFJaUp2Y2c9PSIsInZhbHVlIjoiUzEyMHZ4YSt2bFlIKzZ4MXBnT2FQUmVGc0RsbEtTazRaYjFaQWxSTHRQenl5TzVyTW5wMlRibmlYYXRvaVlWOVFXNzY0dmM1dWVJZEo1L29DVzJ3WTZ3YUZRSE5NOC95TXlXN3BVc1hZZmRBNzdpRGtwRUpDdjArcmtWSjYxMnMweVhLTVNyZnRsS3RZWWxnMXU4bVMvZmovdFRXRW1nVkVyemxSMWNkUUdVem1Ea0RBUEJ0cTFpY2x4UHZyUkxsSEVFVjEvUjdLdldTSVlwcENwa2l4YUVnRUhoK3paTnQ2MHhodEYwS3oxTlFPV0Y4S1NQeU1ITmNwcG5PQnlpRWdBT3hydkcvZUZMa3dzMmJQN0RYQUpwRDBjU2p4SXBrY2plc0pscWYzSVNoT3kvVzl3TlNkaTN4a3BHS0Eydmo2aTFwa3RiWTNlN1Z2YjFTLzFkaXJLd2szTVR6UVVtODRYODNRUC9NV0dQMGhWdWxYTjUwWnRxWWkvanF6MHZtb2JzcSsxMW9NOEVZU1RKYjNQNkcrYi8vc3pRSHFGem9Gb25wUzl0aUNzOXlmRWpaU2RPcm9BWlZJUzZPSTRsVWdCTnVZSmF1TytXeWNXYjFFa0VFUHQxNkJsNHR3bVNvZ3UxUlA5c2ZkbjlZc3U3QWdlYnhiOGIwNHptSGJsTHgiLCJtYWMiOiI3MTBkNDQ1ODcyMTM3NTVjOWFkOGJlNjJiMmFhNzg0MDZiYjAxY2ZiYzNhODliZGRiYjgxZTcxYWVmZDQ1ZWNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik1waTZObFFIV1VaNkdqY0FHWG5NQUE9PSIsInZhbHVlIjoiNVBFQUkwQzJFZlNQYzR4eWcwS0FBVEdTb0dZQVl0cWJxanJXSU8rcDQxS3gwYVNPUzdJd2pJdTNsNEIrcDUyREUvcDc4QTVsbWdUK0ZmREI1d0RVc0RubWo1bEIwMlRLRnlKd2k4MXJwOGVROThlbGRkNlIzc05GeTFqcDkwUlJiUlIvMkZjMGZKOXRLOTM3WXZjS0Mra08waWlZVXlxdTg2ZHQ3MTc2NXM0UStPTTJvMS9XTmx3dzdMcEU3US9UUjlCTkdndi9oVFRaeU9BY1poNHhXaUdmYTV3cFlqdDlIaEROQlpQVDk4UUt2Q0wzRis3ZFFxZ2hvc3lxcklSU3poUnVtSnI4VHIxam1oRlNkRDkycWdyb1lVc0VZTmZ1YWpZOFZiRmgyN1drb1FxN0tnRGJIcWlZNUVLM1VtSFlVNk13d05wWjgydmovR1d3SG04dHBOdkR0RmJ3aS8zME5mR3FKSHZHKzdOMjBUK2xmSS9qdEFoNE45b1FHb1NwOGNxZzc1RHMxbjdJc3JZY1JPTTY5dkRmS3NJUGZNemo5dDhEYmNnSjg4eDJtZFdSTFF0aEx0S0tRaXVWSGoxeExyL3NJWFJwT2NLT2VDc1FoTDRINjRaVFpYMnd5SXloRjVQRlZlTXFVVjQzWTRiQW40QjVnN2NyQmV0Qy9tamUiLCJtYWMiOiIyMmI5ZDA2MmNkMGFkMmJmZWYxMGJjZGU5YzM0M2EwMjc1Njk1NWQxNWFiMThmODdlNzg3ZmYxZmE0MWFiNTJhIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Imc3RlZaOWxiMm5lbEIrcDJNeStDSHc9PSIsInZhbHVlIjoiVEp6SHVpOS9KY2c2N1h3QnlpYy9GK1BOSlJzVWllcE51SXlLcEkrOG9uUjBMT0NtTjE3L2xFYXlPQmpuZ3RtRFVhREVZd0lITnZBWVFjd2s5d2FKTTYzK3o2LzRTNHJqZndNV1ZwL29vdTdWQnBJQWx6UE1CZ2NkVFFsRTczRmRaVEJmTVp4SFVvS3pBa3RoTDR4RnBzVmlRZ3lIUGgzUHprSTFMNWJtTWJzUzEvanQ2NG5wUlBWZlVJU29DMnoxdVU3Z3lMb2xBK2lpaGcyZk5mVTJUSjAzKzA5aU4yTEI1MVRnWTdUay83RWpZdGsxYUxaWW9kWWcxM3U0V2wxdXJoZ0szREtlS29lallkc2t1L0QzL2xIek9PUG50WTVhQ1o2MXBwZENyeVQvOTVCREpoZFVESHp3SFZib0RXdVNsemZ2dGxzS1RWMmFmODEvUS8rYUt3RTFHTnd6SHU5SWx5T1VjbWJIdStldzBFY3E1Q3NwSjVTZXRkSFE0R0UrY082RlJoTTVZMjVGSnQwcG5yd01LWW9iN1RSM09tTFR0UWRnbTB1dTcyWlczb3dnaE5QemYvcTVkV2FSOFpocGhsZlN4SW8vQUlBaWFMN0cyUGlTQW9RMEpETlU0VXNLUHU1L3dFbWJNUmZnZUFsOWhtMitUWmRGT2Q1N2hVRTEiLCJtYWMiOiI0MTk0MTdkNWVjZjc0ZTZkZGQxOGMzMDgyYmM5NmE4OTUyMjFkMTBmOTkyYjI3NTAyZWM0Y2E4N2U2NDQ2NTY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1129448174\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1113843482 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21K3ZmFzZyr4s2pH9aKWRqwEKhgdUCnDfhl6jx78</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113843482\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1379577635 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:51:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJ2aVN4RGdwM0NxaFBqZm9JWW5aS3c9PSIsInZhbHVlIjoic3BvdnhVcC8rNVVtbC9PaEJkcDFOYmNqc3U2RzNtcUFLTSt3NVZxZmd0Wi9uTzh4YWtPSm9ISm5lUFV6K1htVEU5cGxOWTNNMnlVNXVzemJtV0hKYUc3KzhDaCs2KzNPYktjSlVGTHBXM1hQbHVJLzNHT3lYTXY2bkZuSUFpankyazBFTHFLTFN4TWFNZldVbzFJWjFwQVJyaW84UWNZcHRicVNhSWN6L0JWT1BraGdGaDFRMDFWVGFpNklwbmdySFRzb2tBVGEyOE5DL01ZdjBPYnk4cDhNcTJnWlFxVDBINm9vY2E3WDhRenk0bFB5K05ueXArWkF6b0VWZGlzUFM2Vk16RWJLZmhYQWVDWE5OYkdZMEF6R3JSSVdyOWJCYUpPNnVDY1U0Q0tlRm5jZnVhdzNXK1RLNXhyQzgrMHgrd2ZrcDd0T08wTEgxUkJrNFpmR2lwWWNEMlZzZlJuWFJIbkM0WVhwakJwaTZHb3FxY1hWYU9wcXg1clliMkhOS0FFdVgrZXVQblVmclE2ZEFvYXhZdzZ1YVBMR0IydUVPMG9SYjBWbjByNzZvSVFrbndiQVZreFljQlhBaUY5L3NpUWVFVTZLME1JZHRDcDhXK2xLNGtlU3puVDVJMm93amhJb3NvZzdrNlpRbGhJY2UvR2tlUzBBeldES1E3R2siLCJtYWMiOiI5ODg5OTRlZDJhODIzODViY2YwNTgyNjY5OTE0MTE0ZjkwNzcyMjBkZjE2YTNlNjQ3OGYxNzRmODJlZGJmY2NhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:51:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6Ik51UndUekRRYTdLWmVlMkg2bncrR0E9PSIsInZhbHVlIjoiN2M4ZUVSenE1bUdFSDUvaDdkQzhWSEJ1SDJCZDRrYWZqOVZiSENaQU8vVnZBS2ZhNnRqZERCNW5IeTI0ZG5VekFFd09leWRveWYrZ09LcUNxZ3VtNEZ6VjROaGxsNU5ZWGpseDFLMVpWM2xEQjhFbHBqclFxdGNsYWJ5SllTRXdCSytWSTREMHJqYjhzZk9qbTQ5RmIrcG81dmkxekdJNmpac2lIM3NSbG13VEltYWljZjBrZE80YWgyOUVUQ2lTUzE5RXhoODRzSVZPMW5yVk8wNWhJZjY2VmcyYTNMMjR5Y3l1L1NrR2wxQmR6RlJMVmYyN1lmUmhqb0pBU1NZLzlsb05HYURacHdjMm9kbHl6M0tTZ1BPc0xsaEQ4UEhqdG02SHppYnVXYkFsOU5Ua1cwR213WUEzejdSc0xGVlprVkJGbVEzVG9ycGZQQmRTNjh5enpYQ000TSt3Z1NwbmRQb1FXcGRqbWUwUkV4KzFoNUVyOHBHZStXMmdxekVKL2R2VjAvUmFCWDdTNy9KeklPTjQwRTc0RTlCSnB3SFAvL0svSnJab0ZqNW1HbHo5UEV1NDVpd25pNG1saEJWbGxRam9GeGFuc1NxaVhEYWFid3hpUVJkNlZkNzBaZG04SUpqZEJiMFc3RmdUdUh0NVUxNGYxSGhyczNyaVhUblkiLCJtYWMiOiIyMDhlNjQ0YjIyNzhlNjM4YzdjNWI0MTc2ZDA4ODIzMTc5MTM5NzliN2I0MmRlMGEwMGE3OTgzMTc0MWVlMjQ4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:51:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJ2aVN4RGdwM0NxaFBqZm9JWW5aS3c9PSIsInZhbHVlIjoic3BvdnhVcC8rNVVtbC9PaEJkcDFOYmNqc3U2RzNtcUFLTSt3NVZxZmd0Wi9uTzh4YWtPSm9ISm5lUFV6K1htVEU5cGxOWTNNMnlVNXVzemJtV0hKYUc3KzhDaCs2KzNPYktjSlVGTHBXM1hQbHVJLzNHT3lYTXY2bkZuSUFpankyazBFTHFLTFN4TWFNZldVbzFJWjFwQVJyaW84UWNZcHRicVNhSWN6L0JWT1BraGdGaDFRMDFWVGFpNklwbmdySFRzb2tBVGEyOE5DL01ZdjBPYnk4cDhNcTJnWlFxVDBINm9vY2E3WDhRenk0bFB5K05ueXArWkF6b0VWZGlzUFM2Vk16RWJLZmhYQWVDWE5OYkdZMEF6R3JSSVdyOWJCYUpPNnVDY1U0Q0tlRm5jZnVhdzNXK1RLNXhyQzgrMHgrd2ZrcDd0T08wTEgxUkJrNFpmR2lwWWNEMlZzZlJuWFJIbkM0WVhwakJwaTZHb3FxY1hWYU9wcXg1clliMkhOS0FFdVgrZXVQblVmclE2ZEFvYXhZdzZ1YVBMR0IydUVPMG9SYjBWbjByNzZvSVFrbndiQVZreFljQlhBaUY5L3NpUWVFVTZLME1JZHRDcDhXK2xLNGtlU3puVDVJMm93amhJb3NvZzdrNlpRbGhJY2UvR2tlUzBBeldES1E3R2siLCJtYWMiOiI5ODg5OTRlZDJhODIzODViY2YwNTgyNjY5OTE0MTE0ZjkwNzcyMjBkZjE2YTNlNjQ3OGYxNzRmODJlZGJmY2NhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:51:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6Ik51UndUekRRYTdLWmVlMkg2bncrR0E9PSIsInZhbHVlIjoiN2M4ZUVSenE1bUdFSDUvaDdkQzhWSEJ1SDJCZDRrYWZqOVZiSENaQU8vVnZBS2ZhNnRqZERCNW5IeTI0ZG5VekFFd09leWRveWYrZ09LcUNxZ3VtNEZ6VjROaGxsNU5ZWGpseDFLMVpWM2xEQjhFbHBqclFxdGNsYWJ5SllTRXdCSytWSTREMHJqYjhzZk9qbTQ5RmIrcG81dmkxekdJNmpac2lIM3NSbG13VEltYWljZjBrZE80YWgyOUVUQ2lTUzE5RXhoODRzSVZPMW5yVk8wNWhJZjY2VmcyYTNMMjR5Y3l1L1NrR2wxQmR6RlJMVmYyN1lmUmhqb0pBU1NZLzlsb05HYURacHdjMm9kbHl6M0tTZ1BPc0xsaEQ4UEhqdG02SHppYnVXYkFsOU5Ua1cwR213WUEzejdSc0xGVlprVkJGbVEzVG9ycGZQQmRTNjh5enpYQ000TSt3Z1NwbmRQb1FXcGRqbWUwUkV4KzFoNUVyOHBHZStXMmdxekVKL2R2VjAvUmFCWDdTNy9KeklPTjQwRTc0RTlCSnB3SFAvL0svSnJab0ZqNW1HbHo5UEV1NDVpd25pNG1saEJWbGxRam9GeGFuc1NxaVhEYWFid3hpUVJkNlZkNzBaZG04SUpqZEJiMFc3RmdUdUh0NVUxNGYxSGhyczNyaVhUblkiLCJtYWMiOiIyMDhlNjQ0YjIyNzhlNjM4YzdjNWI0MTc2ZDA4ODIzMTc5MTM5NzliN2I0MmRlMGEwMGE3OTgzMTc0MWVlMjQ4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:51:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1379577635\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1552118721 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/expense/1/description</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552118721\", {\"maxDepth\":0})</script>\n"}}