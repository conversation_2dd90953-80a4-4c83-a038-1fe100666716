{"__meta": {"id": "Xa65c3c41ef48ad9e997364cbfe947313", "datetime": "2025-07-31 05:37:20", "utime": **********.344571, "method": "GET", "uri": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940238.506553, "end": **********.344619, "duration": 1.8380661010742188, "duration_str": "1.84s", "measures": [{"label": "Booting", "start": 1753940238.506553, "relative_start": 0, "end": **********.185336, "relative_end": **********.185336, "duration": 1.6787831783294678, "duration_str": "1.68s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.185364, "relative_start": 1.****************, "end": **********.344624, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "159ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JfaVNefxfsxls8zpACzirWVPCkHQQnK95TVINR5w", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "status_code": "<pre class=sf-dump id=sf-dump-92819958 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-92819958\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1699554883 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1699554883\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-489518099 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-489518099\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1910874151 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1910874151\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1849794827 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1849794827\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1551077733 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:37:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImI0VE1HMktYMDhCY3JySzRyaGFDMWc9PSIsInZhbHVlIjoiVTRUM2ZNMGpZUStseXA2b3dKSXZHMk14bmRCUGdaSkc5K3ZTMUMzcitHYnRWOTVQcG8zeEc4d1hHTzFHRlQ4QzlSRGtFbXpXdFRBRzhjNkFIV29xbDdZRGhvdTl3TGZlUFd0dUpwU2Z3UDdpbmNmRkN1a1IzMzVtMlkvVWljQ3Raa016VUpIZ2lMSWNJalM4NGNiOGJ3aGMxdnFrZGVxWG9LZDJXSkpxMkhTY0JVdHBvc1FtOGwzMDNqRGtXUFVMS3FJSWZ5aGF6dTNRUzMvMnROV0tWcFYwc1FJRFhKZmJQMW9OOGlCVUhpVUZnWjM3VVpOdWpCR1VYUHl1U1ZCcWduVHJyRFNUQnlacENQeDMvRHdJakpwM2ZoK0RtQXgrNEkxU1pCTTFkY1lrU0JxV3FzZmQraExad3gzQkwxazk1S25FQlo2YmdXY2tnRVZYN0Zmdkk5eVhkQmhyRlZNSCtkUi81UmpINERmbmhOZi9CbjhLR0F2QTgzaHZ2UGE5UHFiLzNqOUp0cnFqSDR3OC9TbjdmektvU1hBOWxRWEJMakNQZFlHM2tVakNhRnUyVlF2M0MvNCticFBReEhQalBzOG5WODh3SEtEUTc5MDlHRUJBT0YrZENhbkZRY1BTNmNVT0JJMWxPM0VaOFBjdGZaSTBsNFRDSjdwdDRvNlEiLCJtYWMiOiIxMDhiODU1OGRkMjlkYjg4NDYwZGQ1M2U0MWY3YzA3NTI4MGQzYTliMWE2Y2MwOWMwYTc0NjZjMTYzZDQ4OTdiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:37:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjFweUdHYThNcGQ0azJ3ZThKajJGT3c9PSIsInZhbHVlIjoiaUxlMzVteThiNThTVzB1OVJrS0pOT3VlVmtxdDZjNm1tSjk1cjRQcXVZNjNncmRYSkdTQXJDSG5Hb1FWOHAxZ3JkN3JxZmJ5UGdLUXlYUWs5TmVPYjFISlBwa3lwdU82RHVOKzI0M2JiUUoxR0x5NWllOEtYM2t5NHNidUhSTkdjMDcrU1B0V2x6MGRsRTZFWGwvNklqU2xGYjcveld4Mm5DcTZOdk8yMFFwdVJFTDVCUjZwaEVobTBzS1BVbU1LUWd4V1FlVXJ5VzVxN0NIR2EwR0dUcExrajhyUXR6Z3JlQnRGYUxBbkxRZkJiMUdyaVRHN2NpMXNXLzFhNzcrRmRId2prSmFXYjFNOGpoZThpWFMrc1RPNGFyZmVoZFRrU2NUVEFaVnhZdTVjY2xnZEdmR1B6ZW9IQnQyeW1hWTdib0xjZEJ2L2JVY0JTZGVkaFk1eFZVMm9kZkJtOHZVOHJ4NnRCTHZFbXdTYzQ2L282Qk9hQ2VHeHdzTjY4TzZlNEhheVNRTWN2MW9rVjBZajk2QUJPcG0waW9yaVFlRzdWcEh6d1JDbWh2QXRXTzMyS0VFdUR6bU9ma0pxcmd6cjQ0Q2VkUEpudHpKdXBxNEhWWldjTkJWM3REQnhabFdENmhFelF1Rk1XVjVQa3c3VlhYdTd4eGZKSGI3M0F4bEUiLCJtYWMiOiIwZThlNGVkMTA2NzI2NDQ1Y2ZhMjEwYjE5NzJiNjA2MDU2OThhODdjNDAyOTM2MDk1NTZhZmFkMDIxNTEyZWZhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:37:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImI0VE1HMktYMDhCY3JySzRyaGFDMWc9PSIsInZhbHVlIjoiVTRUM2ZNMGpZUStseXA2b3dKSXZHMk14bmRCUGdaSkc5K3ZTMUMzcitHYnRWOTVQcG8zeEc4d1hHTzFHRlQ4QzlSRGtFbXpXdFRBRzhjNkFIV29xbDdZRGhvdTl3TGZlUFd0dUpwU2Z3UDdpbmNmRkN1a1IzMzVtMlkvVWljQ3Raa016VUpIZ2lMSWNJalM4NGNiOGJ3aGMxdnFrZGVxWG9LZDJXSkpxMkhTY0JVdHBvc1FtOGwzMDNqRGtXUFVMS3FJSWZ5aGF6dTNRUzMvMnROV0tWcFYwc1FJRFhKZmJQMW9OOGlCVUhpVUZnWjM3VVpOdWpCR1VYUHl1U1ZCcWduVHJyRFNUQnlacENQeDMvRHdJakpwM2ZoK0RtQXgrNEkxU1pCTTFkY1lrU0JxV3FzZmQraExad3gzQkwxazk1S25FQlo2YmdXY2tnRVZYN0Zmdkk5eVhkQmhyRlZNSCtkUi81UmpINERmbmhOZi9CbjhLR0F2QTgzaHZ2UGE5UHFiLzNqOUp0cnFqSDR3OC9TbjdmektvU1hBOWxRWEJMakNQZFlHM2tVakNhRnUyVlF2M0MvNCticFBReEhQalBzOG5WODh3SEtEUTc5MDlHRUJBT0YrZENhbkZRY1BTNmNVT0JJMWxPM0VaOFBjdGZaSTBsNFRDSjdwdDRvNlEiLCJtYWMiOiIxMDhiODU1OGRkMjlkYjg4NDYwZGQ1M2U0MWY3YzA3NTI4MGQzYTliMWE2Y2MwOWMwYTc0NjZjMTYzZDQ4OTdiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:37:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjFweUdHYThNcGQ0azJ3ZThKajJGT3c9PSIsInZhbHVlIjoiaUxlMzVteThiNThTVzB1OVJrS0pOT3VlVmtxdDZjNm1tSjk1cjRQcXVZNjNncmRYSkdTQXJDSG5Hb1FWOHAxZ3JkN3JxZmJ5UGdLUXlYUWs5TmVPYjFISlBwa3lwdU82RHVOKzI0M2JiUUoxR0x5NWllOEtYM2t5NHNidUhSTkdjMDcrU1B0V2x6MGRsRTZFWGwvNklqU2xGYjcveld4Mm5DcTZOdk8yMFFwdVJFTDVCUjZwaEVobTBzS1BVbU1LUWd4V1FlVXJ5VzVxN0NIR2EwR0dUcExrajhyUXR6Z3JlQnRGYUxBbkxRZkJiMUdyaVRHN2NpMXNXLzFhNzcrRmRId2prSmFXYjFNOGpoZThpWFMrc1RPNGFyZmVoZFRrU2NUVEFaVnhZdTVjY2xnZEdmR1B6ZW9IQnQyeW1hWTdib0xjZEJ2L2JVY0JTZGVkaFk1eFZVMm9kZkJtOHZVOHJ4NnRCTHZFbXdTYzQ2L282Qk9hQ2VHeHdzTjY4TzZlNEhheVNRTWN2MW9rVjBZajk2QUJPcG0waW9yaVFlRzdWcEh6d1JDbWh2QXRXTzMyS0VFdUR6bU9ma0pxcmd6cjQ0Q2VkUEpudHpKdXBxNEhWWldjTkJWM3REQnhabFdENmhFelF1Rk1XVjVQa3c3VlhYdTd4eGZKSGI3M0F4bEUiLCJtYWMiOiIwZThlNGVkMTA2NzI2NDQ1Y2ZhMjEwYjE5NzJiNjA2MDU2OThhODdjNDAyOTM2MDk1NTZhZmFkMDIxNTEyZWZhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:37:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1551077733\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-295707540 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JfaVNefxfsxls8zpACzirWVPCkHQQnK95TVINR5w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-295707540\", {\"maxDepth\":0})</script>\n"}}