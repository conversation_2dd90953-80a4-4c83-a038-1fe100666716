{"__meta": {"id": "X54cb5171e72e61da18f53d6202da4add", "datetime": "2025-07-31 05:35:41", "utime": **********.717883, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:35:41] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.711274, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753940139.687565, "end": **********.717931, "duration": 2.0303659439086914, "duration_str": "2.03s", "measures": [{"label": "Booting", "start": 1753940139.687565, "relative_start": 0, "end": **********.497794, "relative_end": **********.497794, "duration": 1.8102288246154785, "duration_str": "1.81s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.497812, "relative_start": 1.8102469444274902, "end": **********.717935, "relative_end": 4.0531158447265625e-06, "duration": 0.2201230525970459, "duration_str": "220ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45913856, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.034039999999999994, "accumulated_duration_str": "34.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.596571, "duration": 0.031149999999999997, "duration_str": "31.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 91.51}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.659781, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 91.51, "width_percent": 3.114}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.670879, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 94.624, "width_percent": 2.791}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.679712, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 97.415, "width_percent": 2.585}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans/10/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1044933316 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1044933316\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1744365124 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1744365124\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-234237168 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-234237168\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1677104192 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pricing-plans/10/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImZvUU9sd29FNnI0SUtrek5hbm15OWc9PSIsInZhbHVlIjoiMng5Z0tZUlkvOHlwQVpySVBYMitRaUttOW1GNWdjNC9zQ0JyUnpFZjdpb3c5NUtKZGlOTXZ6amd3aktCaGk4L25oN2REemRETFYxbzhiZjhCM2dvTHdUaCtqQWxlcU5LTVFVTHdjTXprYkR2NDh4eGVURWFlclpsUUVMMjJ5TjhsU3dGSmpyRXNLcnRhUVpMeVpTQUFDOFFRSmlJbm5Rc2lnQmpuVm4ycU96dlhuNG95cExyTmZ0TXFpMU5RN2ZlS21SNEFiZ2pRZng2UUNQcDk0YnF2eHdnZllMTEFsMGFyTG5KRGo2bFJrdlY0NTI4Rmc2N3N4QkRBNUNMZFkxalBwc3l0dHd3Q3Rta3pac2ZHSkxGb2JXREphQnNzNGZpQlZSWndKSkdDQUpmZld2aytpZkZQVWRNY3V0MnFhU3Zta2Ywd1pFWWI5cFhKT0o1WmttOE1YU3ZVdDlsMUYzSjF1R01BS1NpWGZkNnpmZENaWTRhU25XK1p2SzVHa2U2S203ckVnVjVCNmJDZk1xbG50dk1XRjM0MS9DcEF0dFF0WTZsWkdXQldiUi82OHplTlNFRVFWZ2ZnUzk0L2hSUHlyVnNvWW5FZXU2SFE1bm1ORHU3MzVYTllFN1JLRVkydWJ4RGZJZ2RVTnNiY00rSThFMHlaNkJ1cnNraDNjMHIiLCJtYWMiOiJhYTU2YzEzOTBhOTk4OTI2ODhmMjBkMDY5MTEyMGNhNmRkYjRhYmJjNTNkMTA3MDRhOTFmZmRkMDA4ZWE0OTdkIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Iklva0I2cFNadFJlR3YxZitYdERJNUE9PSIsInZhbHVlIjoiR2RkNzhyYmFCMUZabU1kUGdtcEptbmVSWDJNWllGRnN5eFNQdzN1TjNreS9jSGxOMmVOMHhXb0lWZFdObHhldnp5c3dGVFJib0Vxd0k4SDk2MENKbXJaRFZIR1FDSkpyRGdPajNSd0h3VzhaKzRyNW1FSDFQQmFNM2VOanE1ZjVuRUJzUkh3ZE81MVdKR0pwNEtRRGh6bEdKMHhpK3NHN0IvSk9WL0F1UTdDK2NJb2hUTjZCVkoveENmZlBDdVBvb1ZLWFZYUktXWnhLak9ZS0t3MzVYS2k5eU9GMEdYZjk5bWo5dVBhaU11d0UyblVBbXpCWE9ZRVd2WWhsOHJyalYrN1hMcS9hdUIvNDBFSi92a0s3VUFwV3diaDUzSU5BNGhEa2xiOU15RFo2NlZ5S1dxQXlNalFTNFhKeTZQVmEyM09wQU12K1JnWUxqa3drTWV4emdNYjFWZlVZbGJzZjljUTZONytaWm05QW80cUtQMVV3d0Q3czFpaDNscTJVY2syblJMVVRLZ2IwUm5LS09lbk5TcW9WZ0dCYUwwaW1paEM1YWo5UDFJejBtRWlvcjNtVjk2L2lNN0RWdVEyZkkxZW5sMSs5SlhKcjllaDBXUkRTOXZWOURiNUxPME9sQ3gzVE9mV0tVS1YwbXVFelUzYXg0dHRKMzRLRkRQc1ciLCJtYWMiOiJhMGYzN2VlNWQ2N2UzZjE2OGM3OGQzZjVhYWRlMTA2OThjNmViZTNiODQ4YzI1ZDlmMjNjNzE2YjZkOTNlMTI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677104192\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1610218077 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7lOWuOQi5K5zalKq6lxC9bmXS2G5Yh3URND5pGg9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1610218077\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-371188081 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:35:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVFejBLQ1dDT3BIRS9HVUVKaTdPNHc9PSIsInZhbHVlIjoia1VLaTFiNHJtNmVpRlBvYytoWjVsM1pYQ0MxeEd2VFVzWUhSM1dieFptejdyOXVCZHZ1RWE5VE1FWFBZNTJZOWpDV3BtM3FmSmxORG1TcStIcElmWTRRVGdqc0libUFzZ00zdnRhdUZWUHZnZ29JV0tNVlFtRVFpdWZTZkVnbnVVQUVsR0ViWEE3dk1rbmVSdnFwZkZoOW41eDdUc0EzQW1YTTVMR0dmQjUvVExBby9GQkkraGtrR2xTY1N3bDVQZmt1Z0lLbFlzQ0pUei9UdXpCVWJrMmttM3pXVlNWUEJ4cUdIOHVtc3RpNC9RZHpkUjlBRjZ0UXRFeFlmcUhBMHJZZ3FLT0dpN2xyOG5FT1pNRXpLVEI5NTZVbHZYdEg3OHBJZGdhUHkvRWRRZmRyOXdoYjFZZEZrdXlsOFdFMmh2REdnZDdiSEp2N0czNUc0bjhjWFFCdUhuYjhCVVFOZUZFZlRleno3c3ZvK2pXMTNPazNYN2xSeXpkbDlNREJqanh2T0FYSE9nVGFNakpDT1RjZzJkcEEwQU9kblU3MHVGQk9VQ2huNCtGRXNPdVlkOWF0eXljRWF5Z2hjNkhwR1VwaEhGeTh4NEZ4bW1pemJwVnhraUdyY0hvQjNoT09ZVmJ3R2xheEx0anE0d3l5YitmbTdNTGlDek4zbFd2Z3oiLCJtYWMiOiJiZDVhNDNkZGY5N2VmNGE3MzNmYjc4ODA2YzRkNjZiODI1NTc5YTBiYmVmYmE4OGNjNTUxMTVmMmY0N2ZlZTA2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:35:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkNYbWVVbUtRb0hkM3NnWXB1YnZWSEE9PSIsInZhbHVlIjoicVdUa3R3VE5mTzJoNUhGR0FFVXFtZ3ltamprdXREK0xXbHpndkZDaER6R3RPQWV3VkoxdU0xQUM4OG1OeUlxVHRZSzdicWV5YkNSRi8waHgzQkQxL29HZ2JPM3E0ZzljQWpFRjIxU1E0ekZkdzRnVXZJa1FLZzRreit0ZzFRZVhLLzI1cTFMUjREcGtZanIxb21EWTZremVrNTRvQWJNN2prNWNGLzN0T1E1aUQyMTY2ZTJBVnFhMkU4SmVqd1I2TUhPQXhFZVV1VUxvYkZuS0VHOHBLNDJCdnhrZ2VtbjZOZzhPV3NnQTJnaG1pREN5eGxxUjdKRXRUUW1PSkxlTElzemczOU9xZEZHOERHN1pud0g5czQ4b3J4TC9NUE1WOWxMeXI2QmpyWG5nek5kM0ZWaDQ5bllDMURrOXJjQU5LaUhvTXFmWFFHZ0VGVkFraEo2MVhwb3lLMUp0RGJhUzZ1c1J5OWhSUVM4bkpocWpiVkVWWUlENEZSeGExdnlzU1hnbXdMaHR5RDRBcXNrOStrMGdjajZGRXZ5U3BSdHRaVjBrUUdWU215VUY3YWx2KzZKbEdaNE9tekt0aUZyODRxM1I2d20wSHdsekRUTFRNS0ZzaGNUNUFYckRMeGZUWERZV09PUkZxQXV3WE0zU2N3eGovZTFHditvajl5b3YiLCJtYWMiOiI3NmJlN2E0MmUzNGZmNzNkNDYxY2JlMDE3MjhjNjM1YTMyMDQzMTA2ODc2MzgwY2Q5ZjE4MjZjM2EzZDlhZDBkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:35:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVFejBLQ1dDT3BIRS9HVUVKaTdPNHc9PSIsInZhbHVlIjoia1VLaTFiNHJtNmVpRlBvYytoWjVsM1pYQ0MxeEd2VFVzWUhSM1dieFptejdyOXVCZHZ1RWE5VE1FWFBZNTJZOWpDV3BtM3FmSmxORG1TcStIcElmWTRRVGdqc0libUFzZ00zdnRhdUZWUHZnZ29JV0tNVlFtRVFpdWZTZkVnbnVVQUVsR0ViWEE3dk1rbmVSdnFwZkZoOW41eDdUc0EzQW1YTTVMR0dmQjUvVExBby9GQkkraGtrR2xTY1N3bDVQZmt1Z0lLbFlzQ0pUei9UdXpCVWJrMmttM3pXVlNWUEJ4cUdIOHVtc3RpNC9RZHpkUjlBRjZ0UXRFeFlmcUhBMHJZZ3FLT0dpN2xyOG5FT1pNRXpLVEI5NTZVbHZYdEg3OHBJZGdhUHkvRWRRZmRyOXdoYjFZZEZrdXlsOFdFMmh2REdnZDdiSEp2N0czNUc0bjhjWFFCdUhuYjhCVVFOZUZFZlRleno3c3ZvK2pXMTNPazNYN2xSeXpkbDlNREJqanh2T0FYSE9nVGFNakpDT1RjZzJkcEEwQU9kblU3MHVGQk9VQ2huNCtGRXNPdVlkOWF0eXljRWF5Z2hjNkhwR1VwaEhGeTh4NEZ4bW1pemJwVnhraUdyY0hvQjNoT09ZVmJ3R2xheEx0anE0d3l5YitmbTdNTGlDek4zbFd2Z3oiLCJtYWMiOiJiZDVhNDNkZGY5N2VmNGE3MzNmYjc4ODA2YzRkNjZiODI1NTc5YTBiYmVmYmE4OGNjNTUxMTVmMmY0N2ZlZTA2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:35:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkNYbWVVbUtRb0hkM3NnWXB1YnZWSEE9PSIsInZhbHVlIjoicVdUa3R3VE5mTzJoNUhGR0FFVXFtZ3ltamprdXREK0xXbHpndkZDaER6R3RPQWV3VkoxdU0xQUM4OG1OeUlxVHRZSzdicWV5YkNSRi8waHgzQkQxL29HZ2JPM3E0ZzljQWpFRjIxU1E0ekZkdzRnVXZJa1FLZzRreit0ZzFRZVhLLzI1cTFMUjREcGtZanIxb21EWTZremVrNTRvQWJNN2prNWNGLzN0T1E1aUQyMTY2ZTJBVnFhMkU4SmVqd1I2TUhPQXhFZVV1VUxvYkZuS0VHOHBLNDJCdnhrZ2VtbjZOZzhPV3NnQTJnaG1pREN5eGxxUjdKRXRUUW1PSkxlTElzemczOU9xZEZHOERHN1pud0g5czQ4b3J4TC9NUE1WOWxMeXI2QmpyWG5nek5kM0ZWaDQ5bllDMURrOXJjQU5LaUhvTXFmWFFHZ0VGVkFraEo2MVhwb3lLMUp0RGJhUzZ1c1J5OWhSUVM4bkpocWpiVkVWWUlENEZSeGExdnlzU1hnbXdMaHR5RDRBcXNrOStrMGdjajZGRXZ5U3BSdHRaVjBrUUdWU215VUY3YWx2KzZKbEdaNE9tekt0aUZyODRxM1I2d20wSHdsekRUTFRNS0ZzaGNUNUFYckRMeGZUWERZV09PUkZxQXV3WE0zU2N3eGovZTFHditvajl5b3YiLCJtYWMiOiI3NmJlN2E0MmUzNGZmNzNkNDYxY2JlMDE3MjhjNjM1YTMyMDQzMTA2ODc2MzgwY2Q5ZjE4MjZjM2EzZDlhZDBkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:35:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-371188081\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1361240141 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pricing-plans/10/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1361240141\", {\"maxDepth\":0})</script>\n"}}