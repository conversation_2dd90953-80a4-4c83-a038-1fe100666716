{"__meta": {"id": "X8961e0c93f0ed82cd8e3dcb9dad66633", "datetime": "2025-07-31 05:36:48", "utime": **********.892074, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940207.065015, "end": **********.892168, "duration": 1.827152967453003, "duration_str": "1.83s", "measures": [{"label": "Booting", "start": 1753940207.065015, "relative_start": 0, "end": **********.669703, "relative_end": **********.669703, "duration": 1.6046879291534424, "duration_str": "1.6s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.66974, "relative_start": 1.****************, "end": **********.892177, "relative_end": 9.059906005859375e-06, "duration": 0.*****************, "duration_str": "222ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Q22ugCuI8P2K11MdVqTbCWL6nOaSsDbPku6LGgL9", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-1134265388 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1134265388\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1814205580 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1814205580\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-675381554 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-675381554\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-76204126 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-76204126\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-966211699 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-966211699\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1455188308 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:36:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldWUzJodUVtMHVDWjNzUkQwWWtLMkE9PSIsInZhbHVlIjoiRkZtVXVTTDdHb0ZMcDVUdGdRYkpxZElSd0Y3bzUwTHBUcTJ2Qld2cHJHUHFxMDI4bGIvYnNwQzhyclVsWXNYamJnSHBnNWQyNnMwMmtHSHo1U2lLN3o2OHBqV01ZcUhWNXlrdWlUcXBsNlJodCtrc1JHZGZEUlBrbVBmaFBpbUNaYjRQbnR1Y3hnTzNOUVBheDFpWDB5RkcreEJXM1ZiWUIzcEhsMWhwL2xpanZJOW5uZ3dFWmo3T05NZ3RXVFcyY1UvQ2JPRWVadjc2VDMyTFNKT1lCTnlkQy9PazFxd3cyUlJ1UG9rbWxnaGpiYWp3ZjVaNzcvMkMvaUtuTWxFYTlubjFXcitsSTh1ckNOckc5Mmp1aTE3MktwOTdpb2hMeHNLeXE0ekxHSG5IL1ZxK0p0dzRRbWFySG5OZ3puUFEzV0puUHRlUG5OMnZhSnFkZDJtQzkyamdVb3BBVUxnMVFLbzFtTlFIbXdwR1NnOUI3MHJRR3laaElNZWN3MGN4ajVGQzVCZXRra3dKUlBrb0hJQXlGVVYvamovengrWnhaWU56ZUdCMlczNS9JZ1MxR1EzYVNHRDFZbTZxVm4xQXVDY2VodmkzV1AvbjZqUjlDUkpWZ1JzNXNtb2cvK0Q5dnNGSnFWWFV3U29Md2F1b2hRZm9xSGQ2cFhOcjNJMHciLCJtYWMiOiIwNTY0YjUyODgzODdkY2YwZDI1ODMyZWZjMmQ0MjE0NmEzZTc4MjM0NTE1NWM4YTNjMDU1MTgxMmMzNjIyODQyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:36:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImdLTlZVMlBFUFh4TTdVU3NaZWtvMHc9PSIsInZhbHVlIjoiT1o0cGtQUGpNdEYrY3kyejRyVCsyOXZMbmlNZERPaHlERFo3cnJ1OXNiK1dlTFlqNnU3NkZZQk1lelZ3TGUvcHg4R0hYU3FUakZYeGRYOHFSMDMzVnliOWdnbGdmT09TNDc4em5qUEd2ZmNycjZTUnZYZzQyc2NoSkRKTkZMLzFJS3dzT014Y1JOVko0aFNCUUtndWQycTFuV09XNjJHTzEzRlZQelIyamorZ0ZlS20yRzI0R0ZSci9qR0JXTzhlV3NNdWJkUWIrMVNUbDZBOCtpVmM0SHpENFY5N3ZKVHBObVlVcHgwZEhUZHA1Z2txSS9KOG95dGNycno3WWllMlc3dENCUTRNWWtESWhlMTZ1Z0EzRTZRUkJrOUpiQ2hkN0FPeis2bE5JaXlVMzVGOVU4dUtlSGNXbmxpWk1NNkZ1a0VobG40WUZiRHJvSWp4SUFvQ0ZSWCtxd09EaHp4YSsrQ2k0QVczai84S0NGcGJUZEZnTzlPQWVwTzcwVHlyRTh0SkJPZmhOeXVnNk01bWZwZG9YczRMOHdWVnd1ZWZaWGliMlMzTXJTVXhNY1JtNlRPd1FOVldycnh1aFdGNE51d09RdW10cGM2d2puZTVCV0pJazlyZHFXMGEvaWQ4L0orRTkreStuR2xuU2Y4TzJOdUFyeHFYN0dZeWRnQmciLCJtYWMiOiI4ODllZmUwMzEwZWU3ZWNmMzUxNzY0ZDRkYjRlZTI5Y2U0YzI1ZThlYmViZGQ4NjZkOTBiNjkzM2RmOWFiZTdjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:36:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldWUzJodUVtMHVDWjNzUkQwWWtLMkE9PSIsInZhbHVlIjoiRkZtVXVTTDdHb0ZMcDVUdGdRYkpxZElSd0Y3bzUwTHBUcTJ2Qld2cHJHUHFxMDI4bGIvYnNwQzhyclVsWXNYamJnSHBnNWQyNnMwMmtHSHo1U2lLN3o2OHBqV01ZcUhWNXlrdWlUcXBsNlJodCtrc1JHZGZEUlBrbVBmaFBpbUNaYjRQbnR1Y3hnTzNOUVBheDFpWDB5RkcreEJXM1ZiWUIzcEhsMWhwL2xpanZJOW5uZ3dFWmo3T05NZ3RXVFcyY1UvQ2JPRWVadjc2VDMyTFNKT1lCTnlkQy9PazFxd3cyUlJ1UG9rbWxnaGpiYWp3ZjVaNzcvMkMvaUtuTWxFYTlubjFXcitsSTh1ckNOckc5Mmp1aTE3MktwOTdpb2hMeHNLeXE0ekxHSG5IL1ZxK0p0dzRRbWFySG5OZ3puUFEzV0puUHRlUG5OMnZhSnFkZDJtQzkyamdVb3BBVUxnMVFLbzFtTlFIbXdwR1NnOUI3MHJRR3laaElNZWN3MGN4ajVGQzVCZXRra3dKUlBrb0hJQXlGVVYvamovengrWnhaWU56ZUdCMlczNS9JZ1MxR1EzYVNHRDFZbTZxVm4xQXVDY2VodmkzV1AvbjZqUjlDUkpWZ1JzNXNtb2cvK0Q5dnNGSnFWWFV3U29Md2F1b2hRZm9xSGQ2cFhOcjNJMHciLCJtYWMiOiIwNTY0YjUyODgzODdkY2YwZDI1ODMyZWZjMmQ0MjE0NmEzZTc4MjM0NTE1NWM4YTNjMDU1MTgxMmMzNjIyODQyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:36:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImdLTlZVMlBFUFh4TTdVU3NaZWtvMHc9PSIsInZhbHVlIjoiT1o0cGtQUGpNdEYrY3kyejRyVCsyOXZMbmlNZERPaHlERFo3cnJ1OXNiK1dlTFlqNnU3NkZZQk1lelZ3TGUvcHg4R0hYU3FUakZYeGRYOHFSMDMzVnliOWdnbGdmT09TNDc4em5qUEd2ZmNycjZTUnZYZzQyc2NoSkRKTkZMLzFJS3dzT014Y1JOVko0aFNCUUtndWQycTFuV09XNjJHTzEzRlZQelIyamorZ0ZlS20yRzI0R0ZSci9qR0JXTzhlV3NNdWJkUWIrMVNUbDZBOCtpVmM0SHpENFY5N3ZKVHBObVlVcHgwZEhUZHA1Z2txSS9KOG95dGNycno3WWllMlc3dENCUTRNWWtESWhlMTZ1Z0EzRTZRUkJrOUpiQ2hkN0FPeis2bE5JaXlVMzVGOVU4dUtlSGNXbmxpWk1NNkZ1a0VobG40WUZiRHJvSWp4SUFvQ0ZSWCtxd09EaHp4YSsrQ2k0QVczai84S0NGcGJUZEZnTzlPQWVwTzcwVHlyRTh0SkJPZmhOeXVnNk01bWZwZG9YczRMOHdWVnd1ZWZaWGliMlMzTXJTVXhNY1JtNlRPd1FOVldycnh1aFdGNE51d09RdW10cGM2d2puZTVCV0pJazlyZHFXMGEvaWQ4L0orRTkreStuR2xuU2Y4TzJOdUFyeHFYN0dZeWRnQmciLCJtYWMiOiI4ODllZmUwMzEwZWU3ZWNmMzUxNzY0ZDRkYjRlZTI5Y2U0YzI1ZThlYmViZGQ4NjZkOTBiNjkzM2RmOWFiZTdjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:36:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1455188308\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-337201049 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Q22ugCuI8P2K11MdVqTbCWL6nOaSsDbPku6LGgL9</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-337201049\", {\"maxDepth\":0})</script>\n"}}