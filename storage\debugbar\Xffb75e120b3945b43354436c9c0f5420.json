{"__meta": {"id": "Xffb75e120b3945b43354436c9c0f5420", "datetime": "2025-07-31 06:15:54", "utime": 1753942554.016374, "method": "PUT", "uri": "/pricing-plans/10", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 32, "messages": [{"message": "[06:15:35] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 74,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2038 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.184493, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:35] LOG.info: Observer: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 74,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"deep sha\",\n        \"company_description\": \"Company updated via pricing plan integration (Observer)\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.185756, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:35] LOG.info: Observer: Updated user module permissions after pricing plan change {\n    \"user_id\": 74,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"account\": [\n            \"create customer\",\n            \"create vender\",\n            \"create invoice\",\n            \"create bill\",\n            \"create revenue\",\n            \"create payment\",\n            \"create proposal\",\n            \"create goal\",\n            \"create credit note\",\n            \"create debit note\",\n            \"create bank account\",\n            \"create bank transfer\",\n            \"create transaction\",\n            \"create chart of account\",\n            \"create journal entry\",\n            \"create assets\",\n            \"create constant custom field\",\n            \"view account dashboard\",\n            \"view customer\",\n            \"view vender\",\n            \"view invoice\",\n            \"view bill\",\n            \"view revenue\",\n            \"view payment\",\n            \"view proposal\",\n            \"view goal\",\n            \"view credit note\",\n            \"view debit note\",\n            \"view bank account\",\n            \"view bank transfer\",\n            \"view transaction\",\n            \"view chart of account\",\n            \"view journal entry\",\n            \"view assets\",\n            \"view constant custom field\",\n            \"view report\",\n            \"delete customer\",\n            \"delete vender\",\n            \"delete invoice\",\n            \"delete bill\",\n            \"delete revenue\",\n            \"delete payment\",\n            \"delete proposal\",\n            \"delete goal\",\n            \"delete credit note\",\n            \"delete debit note\",\n            \"delete bank account\",\n            \"delete bank transfer\",\n            \"delete transaction\",\n            \"delete chart of account\",\n            \"delete journal entry\",\n            \"delete assets\",\n            \"delete constant custom field\",\n            \"manage customer\",\n            \"edit customer\",\n            \"manage vender\",\n            \"edit vender\",\n            \"manage invoice\",\n            \"edit invoice\",\n            \"manage bill\",\n            \"edit bill\",\n            \"manage revenue\",\n            \"edit revenue\",\n            \"manage payment\",\n            \"edit payment\",\n            \"manage proposal\",\n            \"edit proposal\",\n            \"manage goal\",\n            \"edit goal\",\n            \"manage credit note\",\n            \"edit credit note\",\n            \"manage debit note\",\n            \"edit debit note\",\n            \"manage bank account\",\n            \"edit bank account\",\n            \"manage bank transfer\",\n            \"edit bank transfer\",\n            \"manage transaction\",\n            \"edit transaction\",\n            \"manage chart of account\",\n            \"edit chart of account\",\n            \"manage journal entry\",\n            \"edit journal entry\",\n            \"manage assets\",\n            \"edit assets\",\n            \"manage constant custom field\",\n            \"edit constant custom field\",\n            \"manage report\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.188587, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:37] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 79,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.271596, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:37] LOG.info: Observer: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 79,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"Parichay Singha AI\",\n        \"company_description\": \"Company updated via pricing plan integration (Observer)\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.272861, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:37] LOG.info: Observer: Updated user module permissions after pricing plan change {\n    \"user_id\": 79,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"account\": [\n            \"create customer\",\n            \"create vender\",\n            \"create invoice\",\n            \"create bill\",\n            \"create revenue\",\n            \"create payment\",\n            \"create proposal\",\n            \"create goal\",\n            \"create credit note\",\n            \"create debit note\",\n            \"create bank account\",\n            \"create bank transfer\",\n            \"create transaction\",\n            \"create chart of account\",\n            \"create journal entry\",\n            \"create assets\",\n            \"create constant custom field\",\n            \"view account dashboard\",\n            \"view customer\",\n            \"view vender\",\n            \"view invoice\",\n            \"view bill\",\n            \"view revenue\",\n            \"view payment\",\n            \"view proposal\",\n            \"view goal\",\n            \"view credit note\",\n            \"view debit note\",\n            \"view bank account\",\n            \"view bank transfer\",\n            \"view transaction\",\n            \"view chart of account\",\n            \"view journal entry\",\n            \"view assets\",\n            \"view constant custom field\",\n            \"view report\",\n            \"delete customer\",\n            \"delete vender\",\n            \"delete invoice\",\n            \"delete bill\",\n            \"delete revenue\",\n            \"delete payment\",\n            \"delete proposal\",\n            \"delete goal\",\n            \"delete credit note\",\n            \"delete debit note\",\n            \"delete bank account\",\n            \"delete bank transfer\",\n            \"delete transaction\",\n            \"delete chart of account\",\n            \"delete journal entry\",\n            \"delete assets\",\n            \"delete constant custom field\",\n            \"manage customer\",\n            \"edit customer\",\n            \"manage vender\",\n            \"edit vender\",\n            \"manage invoice\",\n            \"edit invoice\",\n            \"manage bill\",\n            \"edit bill\",\n            \"manage revenue\",\n            \"edit revenue\",\n            \"manage payment\",\n            \"edit payment\",\n            \"manage proposal\",\n            \"edit proposal\",\n            \"manage goal\",\n            \"edit goal\",\n            \"manage credit note\",\n            \"edit credit note\",\n            \"manage debit note\",\n            \"edit debit note\",\n            \"manage bank account\",\n            \"edit bank account\",\n            \"manage bank transfer\",\n            \"edit bank transfer\",\n            \"manage transaction\",\n            \"edit transaction\",\n            \"manage chart of account\",\n            \"edit chart of account\",\n            \"manage journal entry\",\n            \"edit journal entry\",\n            \"manage assets\",\n            \"edit assets\",\n            \"manage constant custom field\",\n            \"edit constant custom field\",\n            \"manage report\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.275158, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:39] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 82,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2041 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.371489, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:39] LOG.info: Observer: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 82,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"Raja JI\",\n        \"company_description\": \"Company updated via pricing plan integration (Observer)\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.373922, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:39] LOG.info: Observer: Updated user module permissions after pricing plan change {\n    \"user_id\": 82,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"account\": [\n            \"create customer\",\n            \"create vender\",\n            \"create invoice\",\n            \"create bill\",\n            \"create revenue\",\n            \"create payment\",\n            \"create proposal\",\n            \"create goal\",\n            \"create credit note\",\n            \"create debit note\",\n            \"create bank account\",\n            \"create bank transfer\",\n            \"create transaction\",\n            \"create chart of account\",\n            \"create journal entry\",\n            \"create assets\",\n            \"create constant custom field\",\n            \"view account dashboard\",\n            \"view customer\",\n            \"view vender\",\n            \"view invoice\",\n            \"view bill\",\n            \"view revenue\",\n            \"view payment\",\n            \"view proposal\",\n            \"view goal\",\n            \"view credit note\",\n            \"view debit note\",\n            \"view bank account\",\n            \"view bank transfer\",\n            \"view transaction\",\n            \"view chart of account\",\n            \"view journal entry\",\n            \"view assets\",\n            \"view constant custom field\",\n            \"view report\",\n            \"delete customer\",\n            \"delete vender\",\n            \"delete invoice\",\n            \"delete bill\",\n            \"delete revenue\",\n            \"delete payment\",\n            \"delete proposal\",\n            \"delete goal\",\n            \"delete credit note\",\n            \"delete debit note\",\n            \"delete bank account\",\n            \"delete bank transfer\",\n            \"delete transaction\",\n            \"delete chart of account\",\n            \"delete journal entry\",\n            \"delete assets\",\n            \"delete constant custom field\",\n            \"manage customer\",\n            \"edit customer\",\n            \"manage vender\",\n            \"edit vender\",\n            \"manage invoice\",\n            \"edit invoice\",\n            \"manage bill\",\n            \"edit bill\",\n            \"manage revenue\",\n            \"edit revenue\",\n            \"manage payment\",\n            \"edit payment\",\n            \"manage proposal\",\n            \"edit proposal\",\n            \"manage goal\",\n            \"edit goal\",\n            \"manage credit note\",\n            \"edit credit note\",\n            \"manage debit note\",\n            \"edit debit note\",\n            \"manage bank account\",\n            \"edit bank account\",\n            \"manage bank transfer\",\n            \"edit bank transfer\",\n            \"manage transaction\",\n            \"edit transaction\",\n            \"manage chart of account\",\n            \"edit chart of account\",\n            \"manage journal entry\",\n            \"edit journal entry\",\n            \"manage assets\",\n            \"edit assets\",\n            \"manage constant custom field\",\n            \"edit constant custom field\",\n            \"manage report\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.377021, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:41] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 83,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2037 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.473421, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:41] LOG.info: Observer: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 83,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"Mr. X\",\n        \"company_description\": \"Company updated via pricing plan integration (Observer)\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.474194, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:41] LOG.info: Observer: Updated user module permissions after pricing plan change {\n    \"user_id\": 83,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"account\": [\n            \"create customer\",\n            \"create vender\",\n            \"create invoice\",\n            \"create bill\",\n            \"create revenue\",\n            \"create payment\",\n            \"create proposal\",\n            \"create goal\",\n            \"create credit note\",\n            \"create debit note\",\n            \"create bank account\",\n            \"create bank transfer\",\n            \"create transaction\",\n            \"create chart of account\",\n            \"create journal entry\",\n            \"create assets\",\n            \"create constant custom field\",\n            \"view account dashboard\",\n            \"view customer\",\n            \"view vender\",\n            \"view invoice\",\n            \"view bill\",\n            \"view revenue\",\n            \"view payment\",\n            \"view proposal\",\n            \"view goal\",\n            \"view credit note\",\n            \"view debit note\",\n            \"view bank account\",\n            \"view bank transfer\",\n            \"view transaction\",\n            \"view chart of account\",\n            \"view journal entry\",\n            \"view assets\",\n            \"view constant custom field\",\n            \"view report\",\n            \"delete customer\",\n            \"delete vender\",\n            \"delete invoice\",\n            \"delete bill\",\n            \"delete revenue\",\n            \"delete payment\",\n            \"delete proposal\",\n            \"delete goal\",\n            \"delete credit note\",\n            \"delete debit note\",\n            \"delete bank account\",\n            \"delete bank transfer\",\n            \"delete transaction\",\n            \"delete chart of account\",\n            \"delete journal entry\",\n            \"delete assets\",\n            \"delete constant custom field\",\n            \"manage customer\",\n            \"edit customer\",\n            \"manage vender\",\n            \"edit vender\",\n            \"manage invoice\",\n            \"edit invoice\",\n            \"manage bill\",\n            \"edit bill\",\n            \"manage revenue\",\n            \"edit revenue\",\n            \"manage payment\",\n            \"edit payment\",\n            \"manage proposal\",\n            \"edit proposal\",\n            \"manage goal\",\n            \"edit goal\",\n            \"manage credit note\",\n            \"edit credit note\",\n            \"manage debit note\",\n            \"edit debit note\",\n            \"manage bank account\",\n            \"edit bank account\",\n            \"manage bank transfer\",\n            \"edit bank transfer\",\n            \"manage transaction\",\n            \"edit transaction\",\n            \"manage chart of account\",\n            \"edit chart of account\",\n            \"manage journal entry\",\n            \"edit journal entry\",\n            \"manage assets\",\n            \"edit assets\",\n            \"manage constant custom field\",\n            \"edit constant custom field\",\n            \"manage report\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.475721, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:43] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 84,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2047 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.554748, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:43] LOG.info: Observer: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 84,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"parichay  Bot flow\",\n        \"company_description\": \"Company updated via pricing plan integration (Observer)\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.557437, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:43] LOG.info: Observer: Updated user module permissions after pricing plan change {\n    \"user_id\": 84,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"account\": [\n            \"create customer\",\n            \"create vender\",\n            \"create invoice\",\n            \"create bill\",\n            \"create revenue\",\n            \"create payment\",\n            \"create proposal\",\n            \"create goal\",\n            \"create credit note\",\n            \"create debit note\",\n            \"create bank account\",\n            \"create bank transfer\",\n            \"create transaction\",\n            \"create chart of account\",\n            \"create journal entry\",\n            \"create assets\",\n            \"create constant custom field\",\n            \"view account dashboard\",\n            \"view customer\",\n            \"view vender\",\n            \"view invoice\",\n            \"view bill\",\n            \"view revenue\",\n            \"view payment\",\n            \"view proposal\",\n            \"view goal\",\n            \"view credit note\",\n            \"view debit note\",\n            \"view bank account\",\n            \"view bank transfer\",\n            \"view transaction\",\n            \"view chart of account\",\n            \"view journal entry\",\n            \"view assets\",\n            \"view constant custom field\",\n            \"view report\",\n            \"delete customer\",\n            \"delete vender\",\n            \"delete invoice\",\n            \"delete bill\",\n            \"delete revenue\",\n            \"delete payment\",\n            \"delete proposal\",\n            \"delete goal\",\n            \"delete credit note\",\n            \"delete debit note\",\n            \"delete bank account\",\n            \"delete bank transfer\",\n            \"delete transaction\",\n            \"delete chart of account\",\n            \"delete journal entry\",\n            \"delete assets\",\n            \"delete constant custom field\",\n            \"manage customer\",\n            \"edit customer\",\n            \"manage vender\",\n            \"edit vender\",\n            \"manage invoice\",\n            \"edit invoice\",\n            \"manage bill\",\n            \"edit bill\",\n            \"manage revenue\",\n            \"edit revenue\",\n            \"manage payment\",\n            \"edit payment\",\n            \"manage proposal\",\n            \"edit proposal\",\n            \"manage goal\",\n            \"edit goal\",\n            \"manage credit note\",\n            \"edit credit note\",\n            \"manage debit note\",\n            \"edit debit note\",\n            \"manage bank account\",\n            \"edit bank account\",\n            \"manage bank transfer\",\n            \"edit bank transfer\",\n            \"manage transaction\",\n            \"edit transaction\",\n            \"manage chart of account\",\n            \"edit chart of account\",\n            \"manage journal entry\",\n            \"edit journal entry\",\n            \"manage assets\",\n            \"edit assets\",\n            \"manage constant custom field\",\n            \"edit constant custom field\",\n            \"manage report\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.561692, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:43] LOG.info: Observer: Successfully updated module permissions for all users in pricing plan {\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"users_updated\": 5\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.563202, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:45] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 74,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2047 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.686297, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:45] LOG.info: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 74,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"deep sha\",\n        \"company_description\": \"Company updated via pricing plan integration\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.687829, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:45] LOG.info: Updated user module permissions after pricing plan change {\n    \"user_id\": 74,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"account\": [\n            \"create customer\",\n            \"create vender\",\n            \"create invoice\",\n            \"create bill\",\n            \"create revenue\",\n            \"create payment\",\n            \"create proposal\",\n            \"create goal\",\n            \"create credit note\",\n            \"create debit note\",\n            \"create bank account\",\n            \"create bank transfer\",\n            \"create transaction\",\n            \"create chart of account\",\n            \"create journal entry\",\n            \"create assets\",\n            \"create constant custom field\",\n            \"view account dashboard\",\n            \"view customer\",\n            \"view vender\",\n            \"view invoice\",\n            \"view bill\",\n            \"view revenue\",\n            \"view payment\",\n            \"view proposal\",\n            \"view goal\",\n            \"view credit note\",\n            \"view debit note\",\n            \"view bank account\",\n            \"view bank transfer\",\n            \"view transaction\",\n            \"view chart of account\",\n            \"view journal entry\",\n            \"view assets\",\n            \"view constant custom field\",\n            \"view report\",\n            \"delete customer\",\n            \"delete vender\",\n            \"delete invoice\",\n            \"delete bill\",\n            \"delete revenue\",\n            \"delete payment\",\n            \"delete proposal\",\n            \"delete goal\",\n            \"delete credit note\",\n            \"delete debit note\",\n            \"delete bank account\",\n            \"delete bank transfer\",\n            \"delete transaction\",\n            \"delete chart of account\",\n            \"delete journal entry\",\n            \"delete assets\",\n            \"delete constant custom field\",\n            \"manage customer\",\n            \"edit customer\",\n            \"manage vender\",\n            \"edit vender\",\n            \"manage invoice\",\n            \"edit invoice\",\n            \"manage bill\",\n            \"edit bill\",\n            \"manage revenue\",\n            \"edit revenue\",\n            \"manage payment\",\n            \"edit payment\",\n            \"manage proposal\",\n            \"edit proposal\",\n            \"manage goal\",\n            \"edit goal\",\n            \"manage credit note\",\n            \"edit credit note\",\n            \"manage debit note\",\n            \"edit debit note\",\n            \"manage bank account\",\n            \"edit bank account\",\n            \"manage bank transfer\",\n            \"edit bank transfer\",\n            \"manage transaction\",\n            \"edit transaction\",\n            \"manage chart of account\",\n            \"edit chart of account\",\n            \"manage journal entry\",\n            \"edit journal entry\",\n            \"manage assets\",\n            \"edit assets\",\n            \"manage constant custom field\",\n            \"edit constant custom field\",\n            \"manage report\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.691477, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:47] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 79,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.741943, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:47] LOG.info: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 79,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"Parichay Singha AI\",\n        \"company_description\": \"Company updated via pricing plan integration\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.743458, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:47] LOG.info: Updated user module permissions after pricing plan change {\n    \"user_id\": 79,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"account\": [\n            \"create customer\",\n            \"create vender\",\n            \"create invoice\",\n            \"create bill\",\n            \"create revenue\",\n            \"create payment\",\n            \"create proposal\",\n            \"create goal\",\n            \"create credit note\",\n            \"create debit note\",\n            \"create bank account\",\n            \"create bank transfer\",\n            \"create transaction\",\n            \"create chart of account\",\n            \"create journal entry\",\n            \"create assets\",\n            \"create constant custom field\",\n            \"view account dashboard\",\n            \"view customer\",\n            \"view vender\",\n            \"view invoice\",\n            \"view bill\",\n            \"view revenue\",\n            \"view payment\",\n            \"view proposal\",\n            \"view goal\",\n            \"view credit note\",\n            \"view debit note\",\n            \"view bank account\",\n            \"view bank transfer\",\n            \"view transaction\",\n            \"view chart of account\",\n            \"view journal entry\",\n            \"view assets\",\n            \"view constant custom field\",\n            \"view report\",\n            \"delete customer\",\n            \"delete vender\",\n            \"delete invoice\",\n            \"delete bill\",\n            \"delete revenue\",\n            \"delete payment\",\n            \"delete proposal\",\n            \"delete goal\",\n            \"delete credit note\",\n            \"delete debit note\",\n            \"delete bank account\",\n            \"delete bank transfer\",\n            \"delete transaction\",\n            \"delete chart of account\",\n            \"delete journal entry\",\n            \"delete assets\",\n            \"delete constant custom field\",\n            \"manage customer\",\n            \"edit customer\",\n            \"manage vender\",\n            \"edit vender\",\n            \"manage invoice\",\n            \"edit invoice\",\n            \"manage bill\",\n            \"edit bill\",\n            \"manage revenue\",\n            \"edit revenue\",\n            \"manage payment\",\n            \"edit payment\",\n            \"manage proposal\",\n            \"edit proposal\",\n            \"manage goal\",\n            \"edit goal\",\n            \"manage credit note\",\n            \"edit credit note\",\n            \"manage debit note\",\n            \"edit debit note\",\n            \"manage bank account\",\n            \"edit bank account\",\n            \"manage bank transfer\",\n            \"edit bank transfer\",\n            \"manage transaction\",\n            \"edit transaction\",\n            \"manage chart of account\",\n            \"edit chart of account\",\n            \"manage journal entry\",\n            \"edit journal entry\",\n            \"manage assets\",\n            \"edit assets\",\n            \"manage constant custom field\",\n            \"edit constant custom field\",\n            \"manage report\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.745296, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:49] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 82,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.811522, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:49] LOG.info: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 82,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"Raja JI\",\n        \"company_description\": \"Company updated via pricing plan integration\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.812855, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:49] LOG.info: Updated user module permissions after pricing plan change {\n    \"user_id\": 82,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"account\": [\n            \"create customer\",\n            \"create vender\",\n            \"create invoice\",\n            \"create bill\",\n            \"create revenue\",\n            \"create payment\",\n            \"create proposal\",\n            \"create goal\",\n            \"create credit note\",\n            \"create debit note\",\n            \"create bank account\",\n            \"create bank transfer\",\n            \"create transaction\",\n            \"create chart of account\",\n            \"create journal entry\",\n            \"create assets\",\n            \"create constant custom field\",\n            \"view account dashboard\",\n            \"view customer\",\n            \"view vender\",\n            \"view invoice\",\n            \"view bill\",\n            \"view revenue\",\n            \"view payment\",\n            \"view proposal\",\n            \"view goal\",\n            \"view credit note\",\n            \"view debit note\",\n            \"view bank account\",\n            \"view bank transfer\",\n            \"view transaction\",\n            \"view chart of account\",\n            \"view journal entry\",\n            \"view assets\",\n            \"view constant custom field\",\n            \"view report\",\n            \"delete customer\",\n            \"delete vender\",\n            \"delete invoice\",\n            \"delete bill\",\n            \"delete revenue\",\n            \"delete payment\",\n            \"delete proposal\",\n            \"delete goal\",\n            \"delete credit note\",\n            \"delete debit note\",\n            \"delete bank account\",\n            \"delete bank transfer\",\n            \"delete transaction\",\n            \"delete chart of account\",\n            \"delete journal entry\",\n            \"delete assets\",\n            \"delete constant custom field\",\n            \"manage customer\",\n            \"edit customer\",\n            \"manage vender\",\n            \"edit vender\",\n            \"manage invoice\",\n            \"edit invoice\",\n            \"manage bill\",\n            \"edit bill\",\n            \"manage revenue\",\n            \"edit revenue\",\n            \"manage payment\",\n            \"edit payment\",\n            \"manage proposal\",\n            \"edit proposal\",\n            \"manage goal\",\n            \"edit goal\",\n            \"manage credit note\",\n            \"edit credit note\",\n            \"manage debit note\",\n            \"edit debit note\",\n            \"manage bank account\",\n            \"edit bank account\",\n            \"manage bank transfer\",\n            \"edit bank transfer\",\n            \"manage transaction\",\n            \"edit transaction\",\n            \"manage chart of account\",\n            \"edit chart of account\",\n            \"manage journal entry\",\n            \"edit journal entry\",\n            \"manage assets\",\n            \"edit assets\",\n            \"manage constant custom field\",\n            \"edit constant custom field\",\n            \"manage report\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.815635, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:51] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 83,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2031 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.876993, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:51] LOG.info: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 83,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"Mr. X\",\n        \"company_description\": \"Company updated via pricing plan integration\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.877725, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:51] LOG.info: Updated user module permissions after pricing plan change {\n    \"user_id\": 83,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"account\": [\n            \"create customer\",\n            \"create vender\",\n            \"create invoice\",\n            \"create bill\",\n            \"create revenue\",\n            \"create payment\",\n            \"create proposal\",\n            \"create goal\",\n            \"create credit note\",\n            \"create debit note\",\n            \"create bank account\",\n            \"create bank transfer\",\n            \"create transaction\",\n            \"create chart of account\",\n            \"create journal entry\",\n            \"create assets\",\n            \"create constant custom field\",\n            \"view account dashboard\",\n            \"view customer\",\n            \"view vender\",\n            \"view invoice\",\n            \"view bill\",\n            \"view revenue\",\n            \"view payment\",\n            \"view proposal\",\n            \"view goal\",\n            \"view credit note\",\n            \"view debit note\",\n            \"view bank account\",\n            \"view bank transfer\",\n            \"view transaction\",\n            \"view chart of account\",\n            \"view journal entry\",\n            \"view assets\",\n            \"view constant custom field\",\n            \"view report\",\n            \"delete customer\",\n            \"delete vender\",\n            \"delete invoice\",\n            \"delete bill\",\n            \"delete revenue\",\n            \"delete payment\",\n            \"delete proposal\",\n            \"delete goal\",\n            \"delete credit note\",\n            \"delete debit note\",\n            \"delete bank account\",\n            \"delete bank transfer\",\n            \"delete transaction\",\n            \"delete chart of account\",\n            \"delete journal entry\",\n            \"delete assets\",\n            \"delete constant custom field\",\n            \"manage customer\",\n            \"edit customer\",\n            \"manage vender\",\n            \"edit vender\",\n            \"manage invoice\",\n            \"edit invoice\",\n            \"manage bill\",\n            \"edit bill\",\n            \"manage revenue\",\n            \"edit revenue\",\n            \"manage payment\",\n            \"edit payment\",\n            \"manage proposal\",\n            \"edit proposal\",\n            \"manage goal\",\n            \"edit goal\",\n            \"manage credit note\",\n            \"edit credit note\",\n            \"manage debit note\",\n            \"edit debit note\",\n            \"manage bank account\",\n            \"edit bank account\",\n            \"manage bank transfer\",\n            \"edit bank transfer\",\n            \"manage transaction\",\n            \"edit transaction\",\n            \"manage chart of account\",\n            \"edit chart of account\",\n            \"manage journal entry\",\n            \"edit journal entry\",\n            \"manage assets\",\n            \"edit assets\",\n            \"manage constant custom field\",\n            \"edit constant custom field\",\n            \"manage report\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.878657, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:53] LOG.error: User sync failed {\n    \"module\": \"OMX FLOW\",\n    \"user_id\": 84,\n    \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2014 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/api\\/sync-user\"\n}", "message_html": null, "is_string": false, "label": "error", "time": 1753942553.912764, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:53] LOG.info: OMX Flow sync result for company after pricing plan update {\n    \"user_id\": 84,\n    \"user_email\": \"<EMAIL>\",\n    \"module_name\": \"OMX FLOW\",\n    \"permissions\": [\n        \"access omx flow\",\n        \"whatsapp_flows\",\n        \"whatsapp_orders\",\n        \"campaigns\",\n        \"templates\",\n        \"chatbot\"\n    ],\n    \"additional_fields\": {\n        \"company_name\": \"parichay  Bot flow\",\n        \"company_description\": \"Company updated via pricing plan integration\",\n        \"super_admin_email\": \"<EMAIL>\"\n    },\n    \"sync_successful\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753942553.914669, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:53] LOG.info: Updated user module permissions after pricing plan change {\n    \"user_id\": 84,\n    \"user_email\": \"<EMAIL>\",\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"new_permissions\": {\n        \"crm\": [\n            \"create lead\",\n            \"create deal\",\n            \"create form builder\",\n            \"create contract\",\n            \"create pipeline\",\n            \"create stage\",\n            \"create source\",\n            \"create label\",\n            \"view crm dashboard\",\n            \"view lead\",\n            \"view deal\",\n            \"view form builder\",\n            \"view contract\",\n            \"view pipeline\",\n            \"view stage\",\n            \"view source\",\n            \"view label\",\n            \"delete lead\",\n            \"delete deal\",\n            \"delete form builder\",\n            \"delete contract\",\n            \"delete pipeline\",\n            \"delete stage\",\n            \"delete source\",\n            \"delete label\",\n            \"manage lead\",\n            \"edit lead\",\n            \"manage deal\",\n            \"edit deal\",\n            \"manage form builder\",\n            \"edit form builder\",\n            \"manage contract\",\n            \"edit contract\",\n            \"manage pipeline\",\n            \"edit pipeline\",\n            \"manage stage\",\n            \"edit stage\",\n            \"manage source\",\n            \"edit source\",\n            \"manage label\",\n            \"edit label\"\n        ],\n        \"hrm\": [\n            \"create employee\",\n            \"create set salary\",\n            \"create pay slip\",\n            \"create leave\",\n            \"create attendance\",\n            \"create training\",\n            \"create award\",\n            \"create branch\",\n            \"create department\",\n            \"create designation\",\n            \"create document type\",\n            \"view hrm dashboard\",\n            \"view employee\",\n            \"view set salary\",\n            \"view pay slip\",\n            \"view leave\",\n            \"view attendance\",\n            \"view training\",\n            \"view award\",\n            \"view branch\",\n            \"view department\",\n            \"view designation\",\n            \"view document type\",\n            \"delete employee\",\n            \"delete set salary\",\n            \"delete pay slip\",\n            \"delete leave\",\n            \"delete attendance\",\n            \"delete training\",\n            \"delete award\",\n            \"delete branch\",\n            \"delete department\",\n            \"delete designation\",\n            \"delete document type\",\n            \"manage employee\",\n            \"edit employee\",\n            \"manage set salary\",\n            \"edit set salary\",\n            \"manage pay slip\",\n            \"edit pay slip\",\n            \"manage leave\",\n            \"edit leave\",\n            \"manage attendance\",\n            \"edit attendance\",\n            \"manage training\",\n            \"edit training\",\n            \"manage award\",\n            \"edit award\",\n            \"manage branch\",\n            \"edit branch\",\n            \"manage department\",\n            \"edit department\",\n            \"manage designation\",\n            \"edit designation\",\n            \"manage document type\",\n            \"edit document type\"\n        ],\n        \"account\": [\n            \"create customer\",\n            \"create vender\",\n            \"create invoice\",\n            \"create bill\",\n            \"create revenue\",\n            \"create payment\",\n            \"create proposal\",\n            \"create goal\",\n            \"create credit note\",\n            \"create debit note\",\n            \"create bank account\",\n            \"create bank transfer\",\n            \"create transaction\",\n            \"create chart of account\",\n            \"create journal entry\",\n            \"create assets\",\n            \"create constant custom field\",\n            \"view account dashboard\",\n            \"view customer\",\n            \"view vender\",\n            \"view invoice\",\n            \"view bill\",\n            \"view revenue\",\n            \"view payment\",\n            \"view proposal\",\n            \"view goal\",\n            \"view credit note\",\n            \"view debit note\",\n            \"view bank account\",\n            \"view bank transfer\",\n            \"view transaction\",\n            \"view chart of account\",\n            \"view journal entry\",\n            \"view assets\",\n            \"view constant custom field\",\n            \"view report\",\n            \"delete customer\",\n            \"delete vender\",\n            \"delete invoice\",\n            \"delete bill\",\n            \"delete revenue\",\n            \"delete payment\",\n            \"delete proposal\",\n            \"delete goal\",\n            \"delete credit note\",\n            \"delete debit note\",\n            \"delete bank account\",\n            \"delete bank transfer\",\n            \"delete transaction\",\n            \"delete chart of account\",\n            \"delete journal entry\",\n            \"delete assets\",\n            \"delete constant custom field\",\n            \"manage customer\",\n            \"edit customer\",\n            \"manage vender\",\n            \"edit vender\",\n            \"manage invoice\",\n            \"edit invoice\",\n            \"manage bill\",\n            \"edit bill\",\n            \"manage revenue\",\n            \"edit revenue\",\n            \"manage payment\",\n            \"edit payment\",\n            \"manage proposal\",\n            \"edit proposal\",\n            \"manage goal\",\n            \"edit goal\",\n            \"manage credit note\",\n            \"edit credit note\",\n            \"manage debit note\",\n            \"edit debit note\",\n            \"manage bank account\",\n            \"edit bank account\",\n            \"manage bank transfer\",\n            \"edit bank transfer\",\n            \"manage transaction\",\n            \"edit transaction\",\n            \"manage chart of account\",\n            \"edit chart of account\",\n            \"manage journal entry\",\n            \"edit journal entry\",\n            \"manage assets\",\n            \"edit assets\",\n            \"manage constant custom field\",\n            \"edit constant custom field\",\n            \"manage report\"\n        ],\n        \"project\": [\n            \"create project\",\n            \"create project task\",\n            \"create timesheet\",\n            \"create bug report\",\n            \"create milestone\",\n            \"create project stage\",\n            \"create project task stage\",\n            \"create project expense\",\n            \"create activity\",\n            \"create bug status\",\n            \"view project dashboard\",\n            \"view project\",\n            \"view project task\",\n            \"view timesheet\",\n            \"view bug report\",\n            \"view milestone\",\n            \"view project stage\",\n            \"view project task stage\",\n            \"view project expense\",\n            \"view activity\",\n            \"view bug status\",\n            \"delete project\",\n            \"delete project task\",\n            \"delete timesheet\",\n            \"delete bug report\",\n            \"delete milestone\",\n            \"delete project stage\",\n            \"delete project task stage\",\n            \"delete project expense\",\n            \"delete activity\",\n            \"delete bug status\",\n            \"manage project\",\n            \"edit project\",\n            \"manage project task\",\n            \"edit project task\",\n            \"manage timesheet\",\n            \"edit timesheet\",\n            \"manage bug report\",\n            \"edit bug report\",\n            \"manage milestone\",\n            \"edit milestone\",\n            \"manage project stage\",\n            \"edit project stage\",\n            \"manage project task stage\",\n            \"edit project task stage\",\n            \"manage project expense\",\n            \"edit project expense\",\n            \"manage activity\",\n            \"edit activity\",\n            \"manage bug status\",\n            \"edit bug status\"\n        ],\n        \"pos\": [\n            \"create warehouse\",\n            \"create purchase\",\n            \"create quotation\",\n            \"create pos\",\n            \"create barcode\",\n            \"create product\",\n            \"create product category\",\n            \"create product unit\",\n            \"view pos dashboard\",\n            \"view warehouse\",\n            \"view purchase\",\n            \"view quotation\",\n            \"view pos\",\n            \"view product\",\n            \"view product category\",\n            \"view product unit\",\n            \"delete warehouse\",\n            \"delete purchase\",\n            \"delete quotation\",\n            \"delete pos\",\n            \"delete product\",\n            \"delete product category\",\n            \"delete product unit\",\n            \"manage warehouse\",\n            \"edit warehouse\",\n            \"manage purchase\",\n            \"edit purchase\",\n            \"manage quotation\",\n            \"edit quotation\",\n            \"manage pos\",\n            \"edit pos\",\n            \"manage product\",\n            \"edit product\",\n            \"manage product category\",\n            \"edit product category\",\n            \"manage product unit\",\n            \"edit product unit\"\n        ],\n        \"support\": [\n            \"create support\",\n            \"view support dashboard\",\n            \"view support\",\n            \"delete support\",\n            \"manage support\",\n            \"edit support\",\n            \"reply support\"\n        ],\n        \"user_management\": [\n            \"create user\",\n            \"create client\",\n            \"view user\",\n            \"view client\",\n            \"delete user\",\n            \"delete client\",\n            \"manage user\",\n            \"edit user\",\n            \"manage client\",\n            \"edit client\"\n        ],\n        \"booking\": [\n            \"create booking\",\n            \"create appointment\",\n            \"create appointment booking\",\n            \"create calendar event\",\n            \"view booking dashboard\",\n            \"view booking\",\n            \"show booking\",\n            \"view appointment\",\n            \"show appointment\",\n            \"view appointment booking\",\n            \"show appointment booking\",\n            \"view calendar event\",\n            \"show calendar event\",\n            \"delete booking\",\n            \"delete appointment\",\n            \"delete appointment booking\",\n            \"delete calendar event\",\n            \"manage booking\",\n            \"edit booking\",\n            \"manage appointment\",\n            \"edit appointment\",\n            \"manage appointment booking\",\n            \"edit appointment booking\",\n            \"manage calendar event\",\n            \"edit calendar event\"\n        ],\n        \"omx_flow\": [\n            \"access omx flow\",\n            \"whatsapp_flows\",\n            \"whatsapp_orders\",\n            \"campaigns\",\n            \"templates\",\n            \"chatbot\"\n        ],\n        \"personal_tasks\": [\n            \"create personal task\",\n            \"create personal task comment\",\n            \"create personal task file\",\n            \"create personal task checklist\",\n            \"view personal task\",\n            \"delete personal task\",\n            \"delete personal task comment\",\n            \"delete personal task file\",\n            \"delete personal task checklist\",\n            \"manage personal task\",\n            \"edit personal task\",\n            \"edit personal task comment\",\n            \"edit personal task checklist\",\n            \"manage personal task time tracking\"\n        ],\n        \"automatish\": [\n            \"access automatish\"\n        ]\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753942553.917645, "xdebug_link": null, "collector": "log"}, {"message": "[06:15:53] LOG.info: Successfully updated module permissions for all users in pricing plan {\n    \"pricing_plan_id\": 10,\n    \"pricing_plan_name\": \"My Business Flow\",\n    \"users_updated\": 5\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753942553.918916, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753942531.33066, "end": 1753942554.016705, "duration": 22.686044931411743, "duration_str": "22.69s", "measures": [{"label": "Booting", "start": 1753942531.33066, "relative_start": 0, "end": **********.79299, "relative_end": **********.79299, "duration": 1.4623298645019531, "duration_str": "1.46s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.793005, "relative_start": 1.4623448848724365, "end": 1753942554.01672, "relative_end": 1.5020370483398438e-05, "duration": 21.22371506690979, "duration_str": "21.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52597960, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT pricing-plans/{pricing_plan}", "middleware": "web, auth, XSS", "as": "pricing-plans.update", "controller": "App\\Http\\Controllers\\PricingPlanController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=164\" onclick=\"\">app/Http/Controllers/PricingPlanController.php:164-231</a>"}, "queries": {"nb_statements": 32, "nb_failed_statements": 0, "accumulated_duration": 0.08738000000000001, "accumulated_duration_str": "87.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.861221, "duration": 0.00563, "duration_str": "5.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 6.443}, {"sql": "select * from `pricing_plans` where `id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.8818479, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "radhe_same", "start_percent": 6.443, "width_percent": 1.27}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.9050999, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 7.713, "width_percent": 1.705}, {"sql": "select count(*) as aggregate from `pricing_plans` where `name` = 'My Business Flow' and `id` <> '10'", "type": "query", "params": [], "bindings": ["My Business Flow", "10"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 983}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.950229, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "radhe_same", "start_percent": 9.419, "width_percent": 1.865}, {"sql": "update `pricing_plans` set `module_permissions` = '{\\\"crm\\\":[\\\"create lead\\\",\\\"create deal\\\",\\\"create form builder\\\",\\\"create contract\\\",\\\"create pipeline\\\",\\\"create stage\\\",\\\"create source\\\",\\\"create label\\\",\\\"view crm dashboard\\\",\\\"view lead\\\",\\\"view deal\\\",\\\"view form builder\\\",\\\"view contract\\\",\\\"view pipeline\\\",\\\"view stage\\\",\\\"view source\\\",\\\"view label\\\",\\\"delete lead\\\",\\\"delete deal\\\",\\\"delete form builder\\\",\\\"delete contract\\\",\\\"delete pipeline\\\",\\\"delete stage\\\",\\\"delete source\\\",\\\"delete label\\\",\\\"manage lead\\\",\\\"edit lead\\\",\\\"manage deal\\\",\\\"edit deal\\\",\\\"manage form builder\\\",\\\"edit form builder\\\",\\\"manage contract\\\",\\\"edit contract\\\",\\\"manage pipeline\\\",\\\"edit pipeline\\\",\\\"manage stage\\\",\\\"edit stage\\\",\\\"manage source\\\",\\\"edit source\\\",\\\"manage label\\\",\\\"edit label\\\"],\\\"hrm\\\":[\\\"create employee\\\",\\\"create set salary\\\",\\\"create pay slip\\\",\\\"create leave\\\",\\\"create attendance\\\",\\\"create training\\\",\\\"create award\\\",\\\"create branch\\\",\\\"create department\\\",\\\"create designation\\\",\\\"create document type\\\",\\\"view hrm dashboard\\\",\\\"view employee\\\",\\\"view set salary\\\",\\\"view pay slip\\\",\\\"view leave\\\",\\\"view attendance\\\",\\\"view training\\\",\\\"view award\\\",\\\"view branch\\\",\\\"view department\\\",\\\"view designation\\\",\\\"view document type\\\",\\\"delete employee\\\",\\\"delete set salary\\\",\\\"delete pay slip\\\",\\\"delete leave\\\",\\\"delete attendance\\\",\\\"delete training\\\",\\\"delete award\\\",\\\"delete branch\\\",\\\"delete department\\\",\\\"delete designation\\\",\\\"delete document type\\\",\\\"manage employee\\\",\\\"edit employee\\\",\\\"manage set salary\\\",\\\"edit set salary\\\",\\\"manage pay slip\\\",\\\"edit pay slip\\\",\\\"manage leave\\\",\\\"edit leave\\\",\\\"manage attendance\\\",\\\"edit attendance\\\",\\\"manage training\\\",\\\"edit training\\\",\\\"manage award\\\",\\\"edit award\\\",\\\"manage branch\\\",\\\"edit branch\\\",\\\"manage department\\\",\\\"edit department\\\",\\\"manage designation\\\",\\\"edit designation\\\",\\\"manage document type\\\",\\\"edit document type\\\"],\\\"account\\\":[\\\"create customer\\\",\\\"create vender\\\",\\\"create invoice\\\",\\\"create bill\\\",\\\"create revenue\\\",\\\"create payment\\\",\\\"create proposal\\\",\\\"create goal\\\",\\\"create credit note\\\",\\\"create debit note\\\",\\\"create bank account\\\",\\\"create bank transfer\\\",\\\"create transaction\\\",\\\"create chart of account\\\",\\\"create journal entry\\\",\\\"create assets\\\",\\\"create constant custom field\\\",\\\"view account dashboard\\\",\\\"view customer\\\",\\\"view vender\\\",\\\"view invoice\\\",\\\"view bill\\\",\\\"view revenue\\\",\\\"view payment\\\",\\\"view proposal\\\",\\\"view goal\\\",\\\"view credit note\\\",\\\"view debit note\\\",\\\"view bank account\\\",\\\"view bank transfer\\\",\\\"view transaction\\\",\\\"view chart of account\\\",\\\"view journal entry\\\",\\\"view assets\\\",\\\"view constant custom field\\\",\\\"view report\\\",\\\"delete customer\\\",\\\"delete vender\\\",\\\"delete invoice\\\",\\\"delete bill\\\",\\\"delete revenue\\\",\\\"delete payment\\\",\\\"delete proposal\\\",\\\"delete goal\\\",\\\"delete credit note\\\",\\\"delete debit note\\\",\\\"delete bank account\\\",\\\"delete bank transfer\\\",\\\"delete transaction\\\",\\\"delete chart of account\\\",\\\"delete journal entry\\\",\\\"delete assets\\\",\\\"delete constant custom field\\\",\\\"manage customer\\\",\\\"edit customer\\\",\\\"manage vender\\\",\\\"edit vender\\\",\\\"manage invoice\\\",\\\"edit invoice\\\",\\\"manage bill\\\",\\\"edit bill\\\",\\\"manage revenue\\\",\\\"edit revenue\\\",\\\"manage payment\\\",\\\"edit payment\\\",\\\"manage proposal\\\",\\\"edit proposal\\\",\\\"manage goal\\\",\\\"edit goal\\\",\\\"manage credit note\\\",\\\"edit credit note\\\",\\\"manage debit note\\\",\\\"edit debit note\\\",\\\"manage bank account\\\",\\\"edit bank account\\\",\\\"manage bank transfer\\\",\\\"edit bank transfer\\\",\\\"manage transaction\\\",\\\"edit transaction\\\",\\\"manage chart of account\\\",\\\"edit chart of account\\\",\\\"manage journal entry\\\",\\\"edit journal entry\\\",\\\"manage assets\\\",\\\"edit assets\\\",\\\"manage constant custom field\\\",\\\"edit constant custom field\\\",\\\"manage report\\\"],\\\"project\\\":[\\\"create project\\\",\\\"create project task\\\",\\\"create timesheet\\\",\\\"create bug report\\\",\\\"create milestone\\\",\\\"create project stage\\\",\\\"create project task stage\\\",\\\"create project expense\\\",\\\"create activity\\\",\\\"create bug status\\\",\\\"view project dashboard\\\",\\\"view project\\\",\\\"view project task\\\",\\\"view timesheet\\\",\\\"view bug report\\\",\\\"view milestone\\\",\\\"view project stage\\\",\\\"view project task stage\\\",\\\"view project expense\\\",\\\"view activity\\\",\\\"view bug status\\\",\\\"delete project\\\",\\\"delete project task\\\",\\\"delete timesheet\\\",\\\"delete bug report\\\",\\\"delete milestone\\\",\\\"delete project stage\\\",\\\"delete project task stage\\\",\\\"delete project expense\\\",\\\"delete activity\\\",\\\"delete bug status\\\",\\\"manage project\\\",\\\"edit project\\\",\\\"manage project task\\\",\\\"edit project task\\\",\\\"manage timesheet\\\",\\\"edit timesheet\\\",\\\"manage bug report\\\",\\\"edit bug report\\\",\\\"manage milestone\\\",\\\"edit milestone\\\",\\\"manage project stage\\\",\\\"edit project stage\\\",\\\"manage project task stage\\\",\\\"edit project task stage\\\",\\\"manage project expense\\\",\\\"edit project expense\\\",\\\"manage activity\\\",\\\"edit activity\\\",\\\"manage bug status\\\",\\\"edit bug status\\\"],\\\"pos\\\":[\\\"create warehouse\\\",\\\"create purchase\\\",\\\"create quotation\\\",\\\"create pos\\\",\\\"create barcode\\\",\\\"create product\\\",\\\"create product category\\\",\\\"create product unit\\\",\\\"view pos dashboard\\\",\\\"view warehouse\\\",\\\"view purchase\\\",\\\"view quotation\\\",\\\"view pos\\\",\\\"view product\\\",\\\"view product category\\\",\\\"view product unit\\\",\\\"delete warehouse\\\",\\\"delete purchase\\\",\\\"delete quotation\\\",\\\"delete pos\\\",\\\"delete product\\\",\\\"delete product category\\\",\\\"delete product unit\\\",\\\"manage warehouse\\\",\\\"edit warehouse\\\",\\\"manage purchase\\\",\\\"edit purchase\\\",\\\"manage quotation\\\",\\\"edit quotation\\\",\\\"manage pos\\\",\\\"edit pos\\\",\\\"manage product\\\",\\\"edit product\\\",\\\"manage product category\\\",\\\"edit product category\\\",\\\"manage product unit\\\",\\\"edit product unit\\\"],\\\"support\\\":[\\\"create support\\\",\\\"view support dashboard\\\",\\\"view support\\\",\\\"delete support\\\",\\\"manage support\\\",\\\"edit support\\\",\\\"reply support\\\"],\\\"user_management\\\":[\\\"create user\\\",\\\"create client\\\",\\\"view user\\\",\\\"view client\\\",\\\"delete user\\\",\\\"delete client\\\",\\\"manage user\\\",\\\"edit user\\\",\\\"manage client\\\",\\\"edit client\\\"],\\\"booking\\\":[\\\"create booking\\\",\\\"create appointment\\\",\\\"create appointment booking\\\",\\\"create calendar event\\\",\\\"view booking dashboard\\\",\\\"view booking\\\",\\\"show booking\\\",\\\"view appointment\\\",\\\"show appointment\\\",\\\"view appointment booking\\\",\\\"show appointment booking\\\",\\\"view calendar event\\\",\\\"show calendar event\\\",\\\"delete booking\\\",\\\"delete appointment\\\",\\\"delete appointment booking\\\",\\\"delete calendar event\\\",\\\"manage booking\\\",\\\"edit booking\\\",\\\"manage appointment\\\",\\\"edit appointment\\\",\\\"manage appointment booking\\\",\\\"edit appointment booking\\\",\\\"manage calendar event\\\",\\\"edit calendar event\\\"],\\\"omx_flow\\\":[\\\"access omx flow\\\",\\\"whatsapp_flows\\\",\\\"whatsapp_orders\\\",\\\"campaigns\\\",\\\"templates\\\",\\\"chatbot\\\"],\\\"personal_tasks\\\":[\\\"create personal task\\\",\\\"create personal task comment\\\",\\\"create personal task file\\\",\\\"create personal task checklist\\\",\\\"view personal task\\\",\\\"delete personal task\\\",\\\"delete personal task comment\\\",\\\"delete personal task file\\\",\\\"delete personal task checklist\\\",\\\"manage personal task\\\",\\\"edit personal task\\\",\\\"edit personal task comment\\\",\\\"edit personal task checklist\\\",\\\"manage personal task time tracking\\\"],\\\"automatish\\\":[\\\"access automatish\\\"]}', `pricing_plans`.`updated_at` = '2025-07-31 06:15:32' where `id` = 10", "type": "query", "params": [], "bindings": ["{&quot;crm&quot;:[&quot;create lead&quot;,&quot;create deal&quot;,&quot;create form builder&quot;,&quot;create contract&quot;,&quot;create pipeline&quot;,&quot;create stage&quot;,&quot;create source&quot;,&quot;create label&quot;,&quot;view crm dashboard&quot;,&quot;view lead&quot;,&quot;view deal&quot;,&quot;view form builder&quot;,&quot;view contract&quot;,&quot;view pipeline&quot;,&quot;view stage&quot;,&quot;view source&quot;,&quot;view label&quot;,&quot;delete lead&quot;,&quot;delete deal&quot;,&quot;delete form builder&quot;,&quot;delete contract&quot;,&quot;delete pipeline&quot;,&quot;delete stage&quot;,&quot;delete source&quot;,&quot;delete label&quot;,&quot;manage lead&quot;,&quot;edit lead&quot;,&quot;manage deal&quot;,&quot;edit deal&quot;,&quot;manage form builder&quot;,&quot;edit form builder&quot;,&quot;manage contract&quot;,&quot;edit contract&quot;,&quot;manage pipeline&quot;,&quot;edit pipeline&quot;,&quot;manage stage&quot;,&quot;edit stage&quot;,&quot;manage source&quot;,&quot;edit source&quot;,&quot;manage label&quot;,&quot;edit label&quot;],&quot;hrm&quot;:[&quot;create employee&quot;,&quot;create set salary&quot;,&quot;create pay slip&quot;,&quot;create leave&quot;,&quot;create attendance&quot;,&quot;create training&quot;,&quot;create award&quot;,&quot;create branch&quot;,&quot;create department&quot;,&quot;create designation&quot;,&quot;create document type&quot;,&quot;view hrm dashboard&quot;,&quot;view employee&quot;,&quot;view set salary&quot;,&quot;view pay slip&quot;,&quot;view leave&quot;,&quot;view attendance&quot;,&quot;view training&quot;,&quot;view award&quot;,&quot;view branch&quot;,&quot;view department&quot;,&quot;view designation&quot;,&quot;view document type&quot;,&quot;delete employee&quot;,&quot;delete set salary&quot;,&quot;delete pay slip&quot;,&quot;delete leave&quot;,&quot;delete attendance&quot;,&quot;delete training&quot;,&quot;delete award&quot;,&quot;delete branch&quot;,&quot;delete department&quot;,&quot;delete designation&quot;,&quot;delete document type&quot;,&quot;manage employee&quot;,&quot;edit employee&quot;,&quot;manage set salary&quot;,&quot;edit set salary&quot;,&quot;manage pay slip&quot;,&quot;edit pay slip&quot;,&quot;manage leave&quot;,&quot;edit leave&quot;,&quot;manage attendance&quot;,&quot;edit attendance&quot;,&quot;manage training&quot;,&quot;edit training&quot;,&quot;manage award&quot;,&quot;edit award&quot;,&quot;manage branch&quot;,&quot;edit branch&quot;,&quot;manage department&quot;,&quot;edit department&quot;,&quot;manage designation&quot;,&quot;edit designation&quot;,&quot;manage document type&quot;,&quot;edit document type&quot;],&quot;account&quot;:[&quot;create customer&quot;,&quot;create vender&quot;,&quot;create invoice&quot;,&quot;create bill&quot;,&quot;create revenue&quot;,&quot;create payment&quot;,&quot;create proposal&quot;,&quot;create goal&quot;,&quot;create credit note&quot;,&quot;create debit note&quot;,&quot;create bank account&quot;,&quot;create bank transfer&quot;,&quot;create transaction&quot;,&quot;create chart of account&quot;,&quot;create journal entry&quot;,&quot;create assets&quot;,&quot;create constant custom field&quot;,&quot;view account dashboard&quot;,&quot;view customer&quot;,&quot;view vender&quot;,&quot;view invoice&quot;,&quot;view bill&quot;,&quot;view revenue&quot;,&quot;view payment&quot;,&quot;view proposal&quot;,&quot;view goal&quot;,&quot;view credit note&quot;,&quot;view debit note&quot;,&quot;view bank account&quot;,&quot;view bank transfer&quot;,&quot;view transaction&quot;,&quot;view chart of account&quot;,&quot;view journal entry&quot;,&quot;view assets&quot;,&quot;view constant custom field&quot;,&quot;view report&quot;,&quot;delete customer&quot;,&quot;delete vender&quot;,&quot;delete invoice&quot;,&quot;delete bill&quot;,&quot;delete revenue&quot;,&quot;delete payment&quot;,&quot;delete proposal&quot;,&quot;delete goal&quot;,&quot;delete credit note&quot;,&quot;delete debit note&quot;,&quot;delete bank account&quot;,&quot;delete bank transfer&quot;,&quot;delete transaction&quot;,&quot;delete chart of account&quot;,&quot;delete journal entry&quot;,&quot;delete assets&quot;,&quot;delete constant custom field&quot;,&quot;manage customer&quot;,&quot;edit customer&quot;,&quot;manage vender&quot;,&quot;edit vender&quot;,&quot;manage invoice&quot;,&quot;edit invoice&quot;,&quot;manage bill&quot;,&quot;edit bill&quot;,&quot;manage revenue&quot;,&quot;edit revenue&quot;,&quot;manage payment&quot;,&quot;edit payment&quot;,&quot;manage proposal&quot;,&quot;edit proposal&quot;,&quot;manage goal&quot;,&quot;edit goal&quot;,&quot;manage credit note&quot;,&quot;edit credit note&quot;,&quot;manage debit note&quot;,&quot;edit debit note&quot;,&quot;manage bank account&quot;,&quot;edit bank account&quot;,&quot;manage bank transfer&quot;,&quot;edit bank transfer&quot;,&quot;manage transaction&quot;,&quot;edit transaction&quot;,&quot;manage chart of account&quot;,&quot;edit chart of account&quot;,&quot;manage journal entry&quot;,&quot;edit journal entry&quot;,&quot;manage assets&quot;,&quot;edit assets&quot;,&quot;manage constant custom field&quot;,&quot;edit constant custom field&quot;,&quot;manage report&quot;],&quot;project&quot;:[&quot;create project&quot;,&quot;create project task&quot;,&quot;create timesheet&quot;,&quot;create bug report&quot;,&quot;create milestone&quot;,&quot;create project stage&quot;,&quot;create project task stage&quot;,&quot;create project expense&quot;,&quot;create activity&quot;,&quot;create bug status&quot;,&quot;view project dashboard&quot;,&quot;view project&quot;,&quot;view project task&quot;,&quot;view timesheet&quot;,&quot;view bug report&quot;,&quot;view milestone&quot;,&quot;view project stage&quot;,&quot;view project task stage&quot;,&quot;view project expense&quot;,&quot;view activity&quot;,&quot;view bug status&quot;,&quot;delete project&quot;,&quot;delete project task&quot;,&quot;delete timesheet&quot;,&quot;delete bug report&quot;,&quot;delete milestone&quot;,&quot;delete project stage&quot;,&quot;delete project task stage&quot;,&quot;delete project expense&quot;,&quot;delete activity&quot;,&quot;delete bug status&quot;,&quot;manage project&quot;,&quot;edit project&quot;,&quot;manage project task&quot;,&quot;edit project task&quot;,&quot;manage timesheet&quot;,&quot;edit timesheet&quot;,&quot;manage bug report&quot;,&quot;edit bug report&quot;,&quot;manage milestone&quot;,&quot;edit milestone&quot;,&quot;manage project stage&quot;,&quot;edit project stage&quot;,&quot;manage project task stage&quot;,&quot;edit project task stage&quot;,&quot;manage project expense&quot;,&quot;edit project expense&quot;,&quot;manage activity&quot;,&quot;edit activity&quot;,&quot;manage bug status&quot;,&quot;edit bug status&quot;],&quot;pos&quot;:[&quot;create warehouse&quot;,&quot;create purchase&quot;,&quot;create quotation&quot;,&quot;create pos&quot;,&quot;create barcode&quot;,&quot;create product&quot;,&quot;create product category&quot;,&quot;create product unit&quot;,&quot;view pos dashboard&quot;,&quot;view warehouse&quot;,&quot;view purchase&quot;,&quot;view quotation&quot;,&quot;view pos&quot;,&quot;view product&quot;,&quot;view product category&quot;,&quot;view product unit&quot;,&quot;delete warehouse&quot;,&quot;delete purchase&quot;,&quot;delete quotation&quot;,&quot;delete pos&quot;,&quot;delete product&quot;,&quot;delete product category&quot;,&quot;delete product unit&quot;,&quot;manage warehouse&quot;,&quot;edit warehouse&quot;,&quot;manage purchase&quot;,&quot;edit purchase&quot;,&quot;manage quotation&quot;,&quot;edit quotation&quot;,&quot;manage pos&quot;,&quot;edit pos&quot;,&quot;manage product&quot;,&quot;edit product&quot;,&quot;manage product category&quot;,&quot;edit product category&quot;,&quot;manage product unit&quot;,&quot;edit product unit&quot;],&quot;support&quot;:[&quot;create support&quot;,&quot;view support dashboard&quot;,&quot;view support&quot;,&quot;delete support&quot;,&quot;manage support&quot;,&quot;edit support&quot;,&quot;reply support&quot;],&quot;user_management&quot;:[&quot;create user&quot;,&quot;create client&quot;,&quot;view user&quot;,&quot;view client&quot;,&quot;delete user&quot;,&quot;delete client&quot;,&quot;manage user&quot;,&quot;edit user&quot;,&quot;manage client&quot;,&quot;edit client&quot;],&quot;booking&quot;:[&quot;create booking&quot;,&quot;create appointment&quot;,&quot;create appointment booking&quot;,&quot;create calendar event&quot;,&quot;view booking dashboard&quot;,&quot;view booking&quot;,&quot;show booking&quot;,&quot;view appointment&quot;,&quot;show appointment&quot;,&quot;view appointment booking&quot;,&quot;show appointment booking&quot;,&quot;view calendar event&quot;,&quot;show calendar event&quot;,&quot;delete booking&quot;,&quot;delete appointment&quot;,&quot;delete appointment booking&quot;,&quot;delete calendar event&quot;,&quot;manage booking&quot;,&quot;edit booking&quot;,&quot;manage appointment&quot;,&quot;edit appointment&quot;,&quot;manage appointment booking&quot;,&quot;edit appointment booking&quot;,&quot;manage calendar event&quot;,&quot;edit calendar event&quot;],&quot;omx_flow&quot;:[&quot;access omx flow&quot;,&quot;whatsapp_flows&quot;,&quot;whatsapp_orders&quot;,&quot;campaigns&quot;,&quot;templates&quot;,&quot;chatbot&quot;],&quot;personal_tasks&quot;:[&quot;create personal task&quot;,&quot;create personal task comment&quot;,&quot;create personal task file&quot;,&quot;create personal task checklist&quot;,&quot;view personal task&quot;,&quot;delete personal task&quot;,&quot;delete personal task comment&quot;,&quot;delete personal task file&quot;,&quot;delete personal task checklist&quot;,&quot;manage personal task&quot;,&quot;edit personal task&quot;,&quot;edit personal task comment&quot;,&quot;edit personal task checklist&quot;,&quot;manage personal task time tracking&quot;],&quot;automatish&quot;:[&quot;access automatish&quot;]}", "2025-07-31 06:15:32", "10"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.974943, "duration": 0.007940000000000001, "duration_str": "7.94ms", "memory": 0, "memory_str": null, "filename": "PricingPlanController.php:217", "source": "app/Http/Controllers/PricingPlanController.php:217", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=217", "ajax": false, "filename": "PricingPlanController.php", "line": "217"}, "connection": "radhe_same", "start_percent": 11.284, "width_percent": 9.087}, {"sql": "select * from `users` where `plan` = 10 and `type` = 'company'", "type": "query", "params": [], "bindings": ["10", "company"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 30}, {"index": 16, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.994125, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:30", "source": "app/Observers/PricingPlanObserver.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=30", "ajax": false, "filename": "PricingPlanObserver.php", "line": "30"}, "connection": "radhe_same", "start_percent": 20.371, "width_percent": 2.049}, {"sql": "update `users` set `module_permissions` = '{\\\"crm\\\":[\\\"create lead\\\",\\\"create deal\\\",\\\"create form builder\\\",\\\"create contract\\\",\\\"create pipeline\\\",\\\"create stage\\\",\\\"create source\\\",\\\"create label\\\",\\\"view crm dashboard\\\",\\\"view lead\\\",\\\"view deal\\\",\\\"view form builder\\\",\\\"view contract\\\",\\\"view pipeline\\\",\\\"view stage\\\",\\\"view source\\\",\\\"view label\\\",\\\"delete lead\\\",\\\"delete deal\\\",\\\"delete form builder\\\",\\\"delete contract\\\",\\\"delete pipeline\\\",\\\"delete stage\\\",\\\"delete source\\\",\\\"delete label\\\",\\\"manage lead\\\",\\\"edit lead\\\",\\\"manage deal\\\",\\\"edit deal\\\",\\\"manage form builder\\\",\\\"edit form builder\\\",\\\"manage contract\\\",\\\"edit contract\\\",\\\"manage pipeline\\\",\\\"edit pipeline\\\",\\\"manage stage\\\",\\\"edit stage\\\",\\\"manage source\\\",\\\"edit source\\\",\\\"manage label\\\",\\\"edit label\\\"],\\\"hrm\\\":[\\\"create employee\\\",\\\"create set salary\\\",\\\"create pay slip\\\",\\\"create leave\\\",\\\"create attendance\\\",\\\"create training\\\",\\\"create award\\\",\\\"create branch\\\",\\\"create department\\\",\\\"create designation\\\",\\\"create document type\\\",\\\"view hrm dashboard\\\",\\\"view employee\\\",\\\"view set salary\\\",\\\"view pay slip\\\",\\\"view leave\\\",\\\"view attendance\\\",\\\"view training\\\",\\\"view award\\\",\\\"view branch\\\",\\\"view department\\\",\\\"view designation\\\",\\\"view document type\\\",\\\"delete employee\\\",\\\"delete set salary\\\",\\\"delete pay slip\\\",\\\"delete leave\\\",\\\"delete attendance\\\",\\\"delete training\\\",\\\"delete award\\\",\\\"delete branch\\\",\\\"delete department\\\",\\\"delete designation\\\",\\\"delete document type\\\",\\\"manage employee\\\",\\\"edit employee\\\",\\\"manage set salary\\\",\\\"edit set salary\\\",\\\"manage pay slip\\\",\\\"edit pay slip\\\",\\\"manage leave\\\",\\\"edit leave\\\",\\\"manage attendance\\\",\\\"edit attendance\\\",\\\"manage training\\\",\\\"edit training\\\",\\\"manage award\\\",\\\"edit award\\\",\\\"manage branch\\\",\\\"edit branch\\\",\\\"manage department\\\",\\\"edit department\\\",\\\"manage designation\\\",\\\"edit designation\\\",\\\"manage document type\\\",\\\"edit document type\\\"],\\\"account\\\":[\\\"create customer\\\",\\\"create vender\\\",\\\"create invoice\\\",\\\"create bill\\\",\\\"create revenue\\\",\\\"create payment\\\",\\\"create proposal\\\",\\\"create goal\\\",\\\"create credit note\\\",\\\"create debit note\\\",\\\"create bank account\\\",\\\"create bank transfer\\\",\\\"create transaction\\\",\\\"create chart of account\\\",\\\"create journal entry\\\",\\\"create assets\\\",\\\"create constant custom field\\\",\\\"view account dashboard\\\",\\\"view customer\\\",\\\"view vender\\\",\\\"view invoice\\\",\\\"view bill\\\",\\\"view revenue\\\",\\\"view payment\\\",\\\"view proposal\\\",\\\"view goal\\\",\\\"view credit note\\\",\\\"view debit note\\\",\\\"view bank account\\\",\\\"view bank transfer\\\",\\\"view transaction\\\",\\\"view chart of account\\\",\\\"view journal entry\\\",\\\"view assets\\\",\\\"view constant custom field\\\",\\\"view report\\\",\\\"delete customer\\\",\\\"delete vender\\\",\\\"delete invoice\\\",\\\"delete bill\\\",\\\"delete revenue\\\",\\\"delete payment\\\",\\\"delete proposal\\\",\\\"delete goal\\\",\\\"delete credit note\\\",\\\"delete debit note\\\",\\\"delete bank account\\\",\\\"delete bank transfer\\\",\\\"delete transaction\\\",\\\"delete chart of account\\\",\\\"delete journal entry\\\",\\\"delete assets\\\",\\\"delete constant custom field\\\",\\\"manage customer\\\",\\\"edit customer\\\",\\\"manage vender\\\",\\\"edit vender\\\",\\\"manage invoice\\\",\\\"edit invoice\\\",\\\"manage bill\\\",\\\"edit bill\\\",\\\"manage revenue\\\",\\\"edit revenue\\\",\\\"manage payment\\\",\\\"edit payment\\\",\\\"manage proposal\\\",\\\"edit proposal\\\",\\\"manage goal\\\",\\\"edit goal\\\",\\\"manage credit note\\\",\\\"edit credit note\\\",\\\"manage debit note\\\",\\\"edit debit note\\\",\\\"manage bank account\\\",\\\"edit bank account\\\",\\\"manage bank transfer\\\",\\\"edit bank transfer\\\",\\\"manage transaction\\\",\\\"edit transaction\\\",\\\"manage chart of account\\\",\\\"edit chart of account\\\",\\\"manage journal entry\\\",\\\"edit journal entry\\\",\\\"manage assets\\\",\\\"edit assets\\\",\\\"manage constant custom field\\\",\\\"edit constant custom field\\\",\\\"manage report\\\"],\\\"project\\\":[\\\"create project\\\",\\\"create project task\\\",\\\"create timesheet\\\",\\\"create bug report\\\",\\\"create milestone\\\",\\\"create project stage\\\",\\\"create project task stage\\\",\\\"create project expense\\\",\\\"create activity\\\",\\\"create bug status\\\",\\\"view project dashboard\\\",\\\"view project\\\",\\\"view project task\\\",\\\"view timesheet\\\",\\\"view bug report\\\",\\\"view milestone\\\",\\\"view project stage\\\",\\\"view project task stage\\\",\\\"view project expense\\\",\\\"view activity\\\",\\\"view bug status\\\",\\\"delete project\\\",\\\"delete project task\\\",\\\"delete timesheet\\\",\\\"delete bug report\\\",\\\"delete milestone\\\",\\\"delete project stage\\\",\\\"delete project task stage\\\",\\\"delete project expense\\\",\\\"delete activity\\\",\\\"delete bug status\\\",\\\"manage project\\\",\\\"edit project\\\",\\\"manage project task\\\",\\\"edit project task\\\",\\\"manage timesheet\\\",\\\"edit timesheet\\\",\\\"manage bug report\\\",\\\"edit bug report\\\",\\\"manage milestone\\\",\\\"edit milestone\\\",\\\"manage project stage\\\",\\\"edit project stage\\\",\\\"manage project task stage\\\",\\\"edit project task stage\\\",\\\"manage project expense\\\",\\\"edit project expense\\\",\\\"manage activity\\\",\\\"edit activity\\\",\\\"manage bug status\\\",\\\"edit bug status\\\"],\\\"pos\\\":[\\\"create warehouse\\\",\\\"create purchase\\\",\\\"create quotation\\\",\\\"create pos\\\",\\\"create barcode\\\",\\\"create product\\\",\\\"create product category\\\",\\\"create product unit\\\",\\\"view pos dashboard\\\",\\\"view warehouse\\\",\\\"view purchase\\\",\\\"view quotation\\\",\\\"view pos\\\",\\\"view product\\\",\\\"view product category\\\",\\\"view product unit\\\",\\\"delete warehouse\\\",\\\"delete purchase\\\",\\\"delete quotation\\\",\\\"delete pos\\\",\\\"delete product\\\",\\\"delete product category\\\",\\\"delete product unit\\\",\\\"manage warehouse\\\",\\\"edit warehouse\\\",\\\"manage purchase\\\",\\\"edit purchase\\\",\\\"manage quotation\\\",\\\"edit quotation\\\",\\\"manage pos\\\",\\\"edit pos\\\",\\\"manage product\\\",\\\"edit product\\\",\\\"manage product category\\\",\\\"edit product category\\\",\\\"manage product unit\\\",\\\"edit product unit\\\"],\\\"support\\\":[\\\"create support\\\",\\\"view support dashboard\\\",\\\"view support\\\",\\\"delete support\\\",\\\"manage support\\\",\\\"edit support\\\",\\\"reply support\\\"],\\\"user_management\\\":[\\\"create user\\\",\\\"create client\\\",\\\"view user\\\",\\\"view client\\\",\\\"delete user\\\",\\\"delete client\\\",\\\"manage user\\\",\\\"edit user\\\",\\\"manage client\\\",\\\"edit client\\\"],\\\"booking\\\":[\\\"create booking\\\",\\\"create appointment\\\",\\\"create appointment booking\\\",\\\"create calendar event\\\",\\\"view booking dashboard\\\",\\\"view booking\\\",\\\"show booking\\\",\\\"view appointment\\\",\\\"show appointment\\\",\\\"view appointment booking\\\",\\\"show appointment booking\\\",\\\"view calendar event\\\",\\\"show calendar event\\\",\\\"delete booking\\\",\\\"delete appointment\\\",\\\"delete appointment booking\\\",\\\"delete calendar event\\\",\\\"manage booking\\\",\\\"edit booking\\\",\\\"manage appointment\\\",\\\"edit appointment\\\",\\\"manage appointment booking\\\",\\\"edit appointment booking\\\",\\\"manage calendar event\\\",\\\"edit calendar event\\\"],\\\"omx_flow\\\":[\\\"access omx flow\\\",\\\"whatsapp_flows\\\",\\\"whatsapp_orders\\\",\\\"campaigns\\\",\\\"templates\\\",\\\"chatbot\\\"],\\\"personal_tasks\\\":[\\\"create personal task\\\",\\\"create personal task comment\\\",\\\"create personal task file\\\",\\\"create personal task checklist\\\",\\\"view personal task\\\",\\\"delete personal task\\\",\\\"delete personal task comment\\\",\\\"delete personal task file\\\",\\\"delete personal task checklist\\\",\\\"manage personal task\\\",\\\"edit personal task\\\",\\\"edit personal task comment\\\",\\\"edit personal task checklist\\\",\\\"manage personal task time tracking\\\"],\\\"automatish\\\":[\\\"access automatish\\\"]}', `users`.`updated_at` = '2025-07-31 06:15:33' where `id` = 74", "type": "query", "params": [], "bindings": ["{&quot;crm&quot;:[&quot;create lead&quot;,&quot;create deal&quot;,&quot;create form builder&quot;,&quot;create contract&quot;,&quot;create pipeline&quot;,&quot;create stage&quot;,&quot;create source&quot;,&quot;create label&quot;,&quot;view crm dashboard&quot;,&quot;view lead&quot;,&quot;view deal&quot;,&quot;view form builder&quot;,&quot;view contract&quot;,&quot;view pipeline&quot;,&quot;view stage&quot;,&quot;view source&quot;,&quot;view label&quot;,&quot;delete lead&quot;,&quot;delete deal&quot;,&quot;delete form builder&quot;,&quot;delete contract&quot;,&quot;delete pipeline&quot;,&quot;delete stage&quot;,&quot;delete source&quot;,&quot;delete label&quot;,&quot;manage lead&quot;,&quot;edit lead&quot;,&quot;manage deal&quot;,&quot;edit deal&quot;,&quot;manage form builder&quot;,&quot;edit form builder&quot;,&quot;manage contract&quot;,&quot;edit contract&quot;,&quot;manage pipeline&quot;,&quot;edit pipeline&quot;,&quot;manage stage&quot;,&quot;edit stage&quot;,&quot;manage source&quot;,&quot;edit source&quot;,&quot;manage label&quot;,&quot;edit label&quot;],&quot;hrm&quot;:[&quot;create employee&quot;,&quot;create set salary&quot;,&quot;create pay slip&quot;,&quot;create leave&quot;,&quot;create attendance&quot;,&quot;create training&quot;,&quot;create award&quot;,&quot;create branch&quot;,&quot;create department&quot;,&quot;create designation&quot;,&quot;create document type&quot;,&quot;view hrm dashboard&quot;,&quot;view employee&quot;,&quot;view set salary&quot;,&quot;view pay slip&quot;,&quot;view leave&quot;,&quot;view attendance&quot;,&quot;view training&quot;,&quot;view award&quot;,&quot;view branch&quot;,&quot;view department&quot;,&quot;view designation&quot;,&quot;view document type&quot;,&quot;delete employee&quot;,&quot;delete set salary&quot;,&quot;delete pay slip&quot;,&quot;delete leave&quot;,&quot;delete attendance&quot;,&quot;delete training&quot;,&quot;delete award&quot;,&quot;delete branch&quot;,&quot;delete department&quot;,&quot;delete designation&quot;,&quot;delete document type&quot;,&quot;manage employee&quot;,&quot;edit employee&quot;,&quot;manage set salary&quot;,&quot;edit set salary&quot;,&quot;manage pay slip&quot;,&quot;edit pay slip&quot;,&quot;manage leave&quot;,&quot;edit leave&quot;,&quot;manage attendance&quot;,&quot;edit attendance&quot;,&quot;manage training&quot;,&quot;edit training&quot;,&quot;manage award&quot;,&quot;edit award&quot;,&quot;manage branch&quot;,&quot;edit branch&quot;,&quot;manage department&quot;,&quot;edit department&quot;,&quot;manage designation&quot;,&quot;edit designation&quot;,&quot;manage document type&quot;,&quot;edit document type&quot;],&quot;account&quot;:[&quot;create customer&quot;,&quot;create vender&quot;,&quot;create invoice&quot;,&quot;create bill&quot;,&quot;create revenue&quot;,&quot;create payment&quot;,&quot;create proposal&quot;,&quot;create goal&quot;,&quot;create credit note&quot;,&quot;create debit note&quot;,&quot;create bank account&quot;,&quot;create bank transfer&quot;,&quot;create transaction&quot;,&quot;create chart of account&quot;,&quot;create journal entry&quot;,&quot;create assets&quot;,&quot;create constant custom field&quot;,&quot;view account dashboard&quot;,&quot;view customer&quot;,&quot;view vender&quot;,&quot;view invoice&quot;,&quot;view bill&quot;,&quot;view revenue&quot;,&quot;view payment&quot;,&quot;view proposal&quot;,&quot;view goal&quot;,&quot;view credit note&quot;,&quot;view debit note&quot;,&quot;view bank account&quot;,&quot;view bank transfer&quot;,&quot;view transaction&quot;,&quot;view chart of account&quot;,&quot;view journal entry&quot;,&quot;view assets&quot;,&quot;view constant custom field&quot;,&quot;view report&quot;,&quot;delete customer&quot;,&quot;delete vender&quot;,&quot;delete invoice&quot;,&quot;delete bill&quot;,&quot;delete revenue&quot;,&quot;delete payment&quot;,&quot;delete proposal&quot;,&quot;delete goal&quot;,&quot;delete credit note&quot;,&quot;delete debit note&quot;,&quot;delete bank account&quot;,&quot;delete bank transfer&quot;,&quot;delete transaction&quot;,&quot;delete chart of account&quot;,&quot;delete journal entry&quot;,&quot;delete assets&quot;,&quot;delete constant custom field&quot;,&quot;manage customer&quot;,&quot;edit customer&quot;,&quot;manage vender&quot;,&quot;edit vender&quot;,&quot;manage invoice&quot;,&quot;edit invoice&quot;,&quot;manage bill&quot;,&quot;edit bill&quot;,&quot;manage revenue&quot;,&quot;edit revenue&quot;,&quot;manage payment&quot;,&quot;edit payment&quot;,&quot;manage proposal&quot;,&quot;edit proposal&quot;,&quot;manage goal&quot;,&quot;edit goal&quot;,&quot;manage credit note&quot;,&quot;edit credit note&quot;,&quot;manage debit note&quot;,&quot;edit debit note&quot;,&quot;manage bank account&quot;,&quot;edit bank account&quot;,&quot;manage bank transfer&quot;,&quot;edit bank transfer&quot;,&quot;manage transaction&quot;,&quot;edit transaction&quot;,&quot;manage chart of account&quot;,&quot;edit chart of account&quot;,&quot;manage journal entry&quot;,&quot;edit journal entry&quot;,&quot;manage assets&quot;,&quot;edit assets&quot;,&quot;manage constant custom field&quot;,&quot;edit constant custom field&quot;,&quot;manage report&quot;],&quot;project&quot;:[&quot;create project&quot;,&quot;create project task&quot;,&quot;create timesheet&quot;,&quot;create bug report&quot;,&quot;create milestone&quot;,&quot;create project stage&quot;,&quot;create project task stage&quot;,&quot;create project expense&quot;,&quot;create activity&quot;,&quot;create bug status&quot;,&quot;view project dashboard&quot;,&quot;view project&quot;,&quot;view project task&quot;,&quot;view timesheet&quot;,&quot;view bug report&quot;,&quot;view milestone&quot;,&quot;view project stage&quot;,&quot;view project task stage&quot;,&quot;view project expense&quot;,&quot;view activity&quot;,&quot;view bug status&quot;,&quot;delete project&quot;,&quot;delete project task&quot;,&quot;delete timesheet&quot;,&quot;delete bug report&quot;,&quot;delete milestone&quot;,&quot;delete project stage&quot;,&quot;delete project task stage&quot;,&quot;delete project expense&quot;,&quot;delete activity&quot;,&quot;delete bug status&quot;,&quot;manage project&quot;,&quot;edit project&quot;,&quot;manage project task&quot;,&quot;edit project task&quot;,&quot;manage timesheet&quot;,&quot;edit timesheet&quot;,&quot;manage bug report&quot;,&quot;edit bug report&quot;,&quot;manage milestone&quot;,&quot;edit milestone&quot;,&quot;manage project stage&quot;,&quot;edit project stage&quot;,&quot;manage project task stage&quot;,&quot;edit project task stage&quot;,&quot;manage project expense&quot;,&quot;edit project expense&quot;,&quot;manage activity&quot;,&quot;edit activity&quot;,&quot;manage bug status&quot;,&quot;edit bug status&quot;],&quot;pos&quot;:[&quot;create warehouse&quot;,&quot;create purchase&quot;,&quot;create quotation&quot;,&quot;create pos&quot;,&quot;create barcode&quot;,&quot;create product&quot;,&quot;create product category&quot;,&quot;create product unit&quot;,&quot;view pos dashboard&quot;,&quot;view warehouse&quot;,&quot;view purchase&quot;,&quot;view quotation&quot;,&quot;view pos&quot;,&quot;view product&quot;,&quot;view product category&quot;,&quot;view product unit&quot;,&quot;delete warehouse&quot;,&quot;delete purchase&quot;,&quot;delete quotation&quot;,&quot;delete pos&quot;,&quot;delete product&quot;,&quot;delete product category&quot;,&quot;delete product unit&quot;,&quot;manage warehouse&quot;,&quot;edit warehouse&quot;,&quot;manage purchase&quot;,&quot;edit purchase&quot;,&quot;manage quotation&quot;,&quot;edit quotation&quot;,&quot;manage pos&quot;,&quot;edit pos&quot;,&quot;manage product&quot;,&quot;edit product&quot;,&quot;manage product category&quot;,&quot;edit product category&quot;,&quot;manage product unit&quot;,&quot;edit product unit&quot;],&quot;support&quot;:[&quot;create support&quot;,&quot;view support dashboard&quot;,&quot;view support&quot;,&quot;delete support&quot;,&quot;manage support&quot;,&quot;edit support&quot;,&quot;reply support&quot;],&quot;user_management&quot;:[&quot;create user&quot;,&quot;create client&quot;,&quot;view user&quot;,&quot;view client&quot;,&quot;delete user&quot;,&quot;delete client&quot;,&quot;manage user&quot;,&quot;edit user&quot;,&quot;manage client&quot;,&quot;edit client&quot;],&quot;booking&quot;:[&quot;create booking&quot;,&quot;create appointment&quot;,&quot;create appointment booking&quot;,&quot;create calendar event&quot;,&quot;view booking dashboard&quot;,&quot;view booking&quot;,&quot;show booking&quot;,&quot;view appointment&quot;,&quot;show appointment&quot;,&quot;view appointment booking&quot;,&quot;show appointment booking&quot;,&quot;view calendar event&quot;,&quot;show calendar event&quot;,&quot;delete booking&quot;,&quot;delete appointment&quot;,&quot;delete appointment booking&quot;,&quot;delete calendar event&quot;,&quot;manage booking&quot;,&quot;edit booking&quot;,&quot;manage appointment&quot;,&quot;edit appointment&quot;,&quot;manage appointment booking&quot;,&quot;edit appointment booking&quot;,&quot;manage calendar event&quot;,&quot;edit calendar event&quot;],&quot;omx_flow&quot;:[&quot;access omx flow&quot;,&quot;whatsapp_flows&quot;,&quot;whatsapp_orders&quot;,&quot;campaigns&quot;,&quot;templates&quot;,&quot;chatbot&quot;],&quot;personal_tasks&quot;:[&quot;create personal task&quot;,&quot;create personal task comment&quot;,&quot;create personal task file&quot;,&quot;create personal task checklist&quot;,&quot;view personal task&quot;,&quot;delete personal task&quot;,&quot;delete personal task comment&quot;,&quot;delete personal task file&quot;,&quot;delete personal task checklist&quot;,&quot;manage personal task&quot;,&quot;edit personal task&quot;,&quot;edit personal task comment&quot;,&quot;edit personal task checklist&quot;,&quot;manage personal task time tracking&quot;],&quot;automatish&quot;:[&quot;access automatish&quot;]}", "2025-07-31 06:15:33", "74"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 35}, {"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.008344, "duration": 0.0048200000000000005, "duration_str": "4.82ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:35", "source": "app/Observers/PricingPlanObserver.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=35", "ajax": false, "filename": "PricingPlanObserver.php", "line": "35"}, "connection": "radhe_same", "start_percent": 22.419, "width_percent": 5.516}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 76}, {"index": 16, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.02219, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:76", "source": "app/Observers/PricingPlanObserver.php:76", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=76", "ajax": false, "filename": "PricingPlanObserver.php", "line": "76"}, "connection": "radhe_same", "start_percent": 27.935, "width_percent": 1.385}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 91}, {"index": 18, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 19, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}], "start": **********.032758, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 29.32, "width_percent": 1.339}, {"sql": "update `users` set `module_permissions` = '{\\\"crm\\\":[\\\"create lead\\\",\\\"create deal\\\",\\\"create form builder\\\",\\\"create contract\\\",\\\"create pipeline\\\",\\\"create stage\\\",\\\"create source\\\",\\\"create label\\\",\\\"view crm dashboard\\\",\\\"view lead\\\",\\\"view deal\\\",\\\"view form builder\\\",\\\"view contract\\\",\\\"view pipeline\\\",\\\"view stage\\\",\\\"view source\\\",\\\"view label\\\",\\\"delete lead\\\",\\\"delete deal\\\",\\\"delete form builder\\\",\\\"delete contract\\\",\\\"delete pipeline\\\",\\\"delete stage\\\",\\\"delete source\\\",\\\"delete label\\\",\\\"manage lead\\\",\\\"edit lead\\\",\\\"manage deal\\\",\\\"edit deal\\\",\\\"manage form builder\\\",\\\"edit form builder\\\",\\\"manage contract\\\",\\\"edit contract\\\",\\\"manage pipeline\\\",\\\"edit pipeline\\\",\\\"manage stage\\\",\\\"edit stage\\\",\\\"manage source\\\",\\\"edit source\\\",\\\"manage label\\\",\\\"edit label\\\"],\\\"hrm\\\":[\\\"create employee\\\",\\\"create set salary\\\",\\\"create pay slip\\\",\\\"create leave\\\",\\\"create attendance\\\",\\\"create training\\\",\\\"create award\\\",\\\"create branch\\\",\\\"create department\\\",\\\"create designation\\\",\\\"create document type\\\",\\\"view hrm dashboard\\\",\\\"view employee\\\",\\\"view set salary\\\",\\\"view pay slip\\\",\\\"view leave\\\",\\\"view attendance\\\",\\\"view training\\\",\\\"view award\\\",\\\"view branch\\\",\\\"view department\\\",\\\"view designation\\\",\\\"view document type\\\",\\\"delete employee\\\",\\\"delete set salary\\\",\\\"delete pay slip\\\",\\\"delete leave\\\",\\\"delete attendance\\\",\\\"delete training\\\",\\\"delete award\\\",\\\"delete branch\\\",\\\"delete department\\\",\\\"delete designation\\\",\\\"delete document type\\\",\\\"manage employee\\\",\\\"edit employee\\\",\\\"manage set salary\\\",\\\"edit set salary\\\",\\\"manage pay slip\\\",\\\"edit pay slip\\\",\\\"manage leave\\\",\\\"edit leave\\\",\\\"manage attendance\\\",\\\"edit attendance\\\",\\\"manage training\\\",\\\"edit training\\\",\\\"manage award\\\",\\\"edit award\\\",\\\"manage branch\\\",\\\"edit branch\\\",\\\"manage department\\\",\\\"edit department\\\",\\\"manage designation\\\",\\\"edit designation\\\",\\\"manage document type\\\",\\\"edit document type\\\"],\\\"account\\\":[\\\"create customer\\\",\\\"create vender\\\",\\\"create invoice\\\",\\\"create bill\\\",\\\"create revenue\\\",\\\"create payment\\\",\\\"create proposal\\\",\\\"create goal\\\",\\\"create credit note\\\",\\\"create debit note\\\",\\\"create bank account\\\",\\\"create bank transfer\\\",\\\"create transaction\\\",\\\"create chart of account\\\",\\\"create journal entry\\\",\\\"create assets\\\",\\\"create constant custom field\\\",\\\"view account dashboard\\\",\\\"view customer\\\",\\\"view vender\\\",\\\"view invoice\\\",\\\"view bill\\\",\\\"view revenue\\\",\\\"view payment\\\",\\\"view proposal\\\",\\\"view goal\\\",\\\"view credit note\\\",\\\"view debit note\\\",\\\"view bank account\\\",\\\"view bank transfer\\\",\\\"view transaction\\\",\\\"view chart of account\\\",\\\"view journal entry\\\",\\\"view assets\\\",\\\"view constant custom field\\\",\\\"view report\\\",\\\"delete customer\\\",\\\"delete vender\\\",\\\"delete invoice\\\",\\\"delete bill\\\",\\\"delete revenue\\\",\\\"delete payment\\\",\\\"delete proposal\\\",\\\"delete goal\\\",\\\"delete credit note\\\",\\\"delete debit note\\\",\\\"delete bank account\\\",\\\"delete bank transfer\\\",\\\"delete transaction\\\",\\\"delete chart of account\\\",\\\"delete journal entry\\\",\\\"delete assets\\\",\\\"delete constant custom field\\\",\\\"manage customer\\\",\\\"edit customer\\\",\\\"manage vender\\\",\\\"edit vender\\\",\\\"manage invoice\\\",\\\"edit invoice\\\",\\\"manage bill\\\",\\\"edit bill\\\",\\\"manage revenue\\\",\\\"edit revenue\\\",\\\"manage payment\\\",\\\"edit payment\\\",\\\"manage proposal\\\",\\\"edit proposal\\\",\\\"manage goal\\\",\\\"edit goal\\\",\\\"manage credit note\\\",\\\"edit credit note\\\",\\\"manage debit note\\\",\\\"edit debit note\\\",\\\"manage bank account\\\",\\\"edit bank account\\\",\\\"manage bank transfer\\\",\\\"edit bank transfer\\\",\\\"manage transaction\\\",\\\"edit transaction\\\",\\\"manage chart of account\\\",\\\"edit chart of account\\\",\\\"manage journal entry\\\",\\\"edit journal entry\\\",\\\"manage assets\\\",\\\"edit assets\\\",\\\"manage constant custom field\\\",\\\"edit constant custom field\\\",\\\"manage report\\\"],\\\"project\\\":[\\\"create project\\\",\\\"create project task\\\",\\\"create timesheet\\\",\\\"create bug report\\\",\\\"create milestone\\\",\\\"create project stage\\\",\\\"create project task stage\\\",\\\"create project expense\\\",\\\"create activity\\\",\\\"create bug status\\\",\\\"view project dashboard\\\",\\\"view project\\\",\\\"view project task\\\",\\\"view timesheet\\\",\\\"view bug report\\\",\\\"view milestone\\\",\\\"view project stage\\\",\\\"view project task stage\\\",\\\"view project expense\\\",\\\"view activity\\\",\\\"view bug status\\\",\\\"delete project\\\",\\\"delete project task\\\",\\\"delete timesheet\\\",\\\"delete bug report\\\",\\\"delete milestone\\\",\\\"delete project stage\\\",\\\"delete project task stage\\\",\\\"delete project expense\\\",\\\"delete activity\\\",\\\"delete bug status\\\",\\\"manage project\\\",\\\"edit project\\\",\\\"manage project task\\\",\\\"edit project task\\\",\\\"manage timesheet\\\",\\\"edit timesheet\\\",\\\"manage bug report\\\",\\\"edit bug report\\\",\\\"manage milestone\\\",\\\"edit milestone\\\",\\\"manage project stage\\\",\\\"edit project stage\\\",\\\"manage project task stage\\\",\\\"edit project task stage\\\",\\\"manage project expense\\\",\\\"edit project expense\\\",\\\"manage activity\\\",\\\"edit activity\\\",\\\"manage bug status\\\",\\\"edit bug status\\\"],\\\"pos\\\":[\\\"create warehouse\\\",\\\"create purchase\\\",\\\"create quotation\\\",\\\"create pos\\\",\\\"create barcode\\\",\\\"create product\\\",\\\"create product category\\\",\\\"create product unit\\\",\\\"view pos dashboard\\\",\\\"view warehouse\\\",\\\"view purchase\\\",\\\"view quotation\\\",\\\"view pos\\\",\\\"view product\\\",\\\"view product category\\\",\\\"view product unit\\\",\\\"delete warehouse\\\",\\\"delete purchase\\\",\\\"delete quotation\\\",\\\"delete pos\\\",\\\"delete product\\\",\\\"delete product category\\\",\\\"delete product unit\\\",\\\"manage warehouse\\\",\\\"edit warehouse\\\",\\\"manage purchase\\\",\\\"edit purchase\\\",\\\"manage quotation\\\",\\\"edit quotation\\\",\\\"manage pos\\\",\\\"edit pos\\\",\\\"manage product\\\",\\\"edit product\\\",\\\"manage product category\\\",\\\"edit product category\\\",\\\"manage product unit\\\",\\\"edit product unit\\\"],\\\"support\\\":[\\\"create support\\\",\\\"view support dashboard\\\",\\\"view support\\\",\\\"delete support\\\",\\\"manage support\\\",\\\"edit support\\\",\\\"reply support\\\"],\\\"user_management\\\":[\\\"create user\\\",\\\"create client\\\",\\\"view user\\\",\\\"view client\\\",\\\"delete user\\\",\\\"delete client\\\",\\\"manage user\\\",\\\"edit user\\\",\\\"manage client\\\",\\\"edit client\\\"],\\\"booking\\\":[\\\"create booking\\\",\\\"create appointment\\\",\\\"create appointment booking\\\",\\\"create calendar event\\\",\\\"view booking dashboard\\\",\\\"view booking\\\",\\\"show booking\\\",\\\"view appointment\\\",\\\"show appointment\\\",\\\"view appointment booking\\\",\\\"show appointment booking\\\",\\\"view calendar event\\\",\\\"show calendar event\\\",\\\"delete booking\\\",\\\"delete appointment\\\",\\\"delete appointment booking\\\",\\\"delete calendar event\\\",\\\"manage booking\\\",\\\"edit booking\\\",\\\"manage appointment\\\",\\\"edit appointment\\\",\\\"manage appointment booking\\\",\\\"edit appointment booking\\\",\\\"manage calendar event\\\",\\\"edit calendar event\\\"],\\\"omx_flow\\\":[\\\"access omx flow\\\",\\\"whatsapp_flows\\\",\\\"whatsapp_orders\\\",\\\"campaigns\\\",\\\"templates\\\",\\\"chatbot\\\"],\\\"personal_tasks\\\":[\\\"create personal task\\\",\\\"create personal task comment\\\",\\\"create personal task file\\\",\\\"create personal task checklist\\\",\\\"view personal task\\\",\\\"delete personal task\\\",\\\"delete personal task comment\\\",\\\"delete personal task file\\\",\\\"delete personal task checklist\\\",\\\"manage personal task\\\",\\\"edit personal task\\\",\\\"edit personal task comment\\\",\\\"edit personal task checklist\\\",\\\"manage personal task time tracking\\\"],\\\"automatish\\\":[\\\"access automatish\\\"]}', `users`.`updated_at` = '2025-07-31 06:15:35' where `id` = 79", "type": "query", "params": [], "bindings": ["{&quot;crm&quot;:[&quot;create lead&quot;,&quot;create deal&quot;,&quot;create form builder&quot;,&quot;create contract&quot;,&quot;create pipeline&quot;,&quot;create stage&quot;,&quot;create source&quot;,&quot;create label&quot;,&quot;view crm dashboard&quot;,&quot;view lead&quot;,&quot;view deal&quot;,&quot;view form builder&quot;,&quot;view contract&quot;,&quot;view pipeline&quot;,&quot;view stage&quot;,&quot;view source&quot;,&quot;view label&quot;,&quot;delete lead&quot;,&quot;delete deal&quot;,&quot;delete form builder&quot;,&quot;delete contract&quot;,&quot;delete pipeline&quot;,&quot;delete stage&quot;,&quot;delete source&quot;,&quot;delete label&quot;,&quot;manage lead&quot;,&quot;edit lead&quot;,&quot;manage deal&quot;,&quot;edit deal&quot;,&quot;manage form builder&quot;,&quot;edit form builder&quot;,&quot;manage contract&quot;,&quot;edit contract&quot;,&quot;manage pipeline&quot;,&quot;edit pipeline&quot;,&quot;manage stage&quot;,&quot;edit stage&quot;,&quot;manage source&quot;,&quot;edit source&quot;,&quot;manage label&quot;,&quot;edit label&quot;],&quot;hrm&quot;:[&quot;create employee&quot;,&quot;create set salary&quot;,&quot;create pay slip&quot;,&quot;create leave&quot;,&quot;create attendance&quot;,&quot;create training&quot;,&quot;create award&quot;,&quot;create branch&quot;,&quot;create department&quot;,&quot;create designation&quot;,&quot;create document type&quot;,&quot;view hrm dashboard&quot;,&quot;view employee&quot;,&quot;view set salary&quot;,&quot;view pay slip&quot;,&quot;view leave&quot;,&quot;view attendance&quot;,&quot;view training&quot;,&quot;view award&quot;,&quot;view branch&quot;,&quot;view department&quot;,&quot;view designation&quot;,&quot;view document type&quot;,&quot;delete employee&quot;,&quot;delete set salary&quot;,&quot;delete pay slip&quot;,&quot;delete leave&quot;,&quot;delete attendance&quot;,&quot;delete training&quot;,&quot;delete award&quot;,&quot;delete branch&quot;,&quot;delete department&quot;,&quot;delete designation&quot;,&quot;delete document type&quot;,&quot;manage employee&quot;,&quot;edit employee&quot;,&quot;manage set salary&quot;,&quot;edit set salary&quot;,&quot;manage pay slip&quot;,&quot;edit pay slip&quot;,&quot;manage leave&quot;,&quot;edit leave&quot;,&quot;manage attendance&quot;,&quot;edit attendance&quot;,&quot;manage training&quot;,&quot;edit training&quot;,&quot;manage award&quot;,&quot;edit award&quot;,&quot;manage branch&quot;,&quot;edit branch&quot;,&quot;manage department&quot;,&quot;edit department&quot;,&quot;manage designation&quot;,&quot;edit designation&quot;,&quot;manage document type&quot;,&quot;edit document type&quot;],&quot;account&quot;:[&quot;create customer&quot;,&quot;create vender&quot;,&quot;create invoice&quot;,&quot;create bill&quot;,&quot;create revenue&quot;,&quot;create payment&quot;,&quot;create proposal&quot;,&quot;create goal&quot;,&quot;create credit note&quot;,&quot;create debit note&quot;,&quot;create bank account&quot;,&quot;create bank transfer&quot;,&quot;create transaction&quot;,&quot;create chart of account&quot;,&quot;create journal entry&quot;,&quot;create assets&quot;,&quot;create constant custom field&quot;,&quot;view account dashboard&quot;,&quot;view customer&quot;,&quot;view vender&quot;,&quot;view invoice&quot;,&quot;view bill&quot;,&quot;view revenue&quot;,&quot;view payment&quot;,&quot;view proposal&quot;,&quot;view goal&quot;,&quot;view credit note&quot;,&quot;view debit note&quot;,&quot;view bank account&quot;,&quot;view bank transfer&quot;,&quot;view transaction&quot;,&quot;view chart of account&quot;,&quot;view journal entry&quot;,&quot;view assets&quot;,&quot;view constant custom field&quot;,&quot;view report&quot;,&quot;delete customer&quot;,&quot;delete vender&quot;,&quot;delete invoice&quot;,&quot;delete bill&quot;,&quot;delete revenue&quot;,&quot;delete payment&quot;,&quot;delete proposal&quot;,&quot;delete goal&quot;,&quot;delete credit note&quot;,&quot;delete debit note&quot;,&quot;delete bank account&quot;,&quot;delete bank transfer&quot;,&quot;delete transaction&quot;,&quot;delete chart of account&quot;,&quot;delete journal entry&quot;,&quot;delete assets&quot;,&quot;delete constant custom field&quot;,&quot;manage customer&quot;,&quot;edit customer&quot;,&quot;manage vender&quot;,&quot;edit vender&quot;,&quot;manage invoice&quot;,&quot;edit invoice&quot;,&quot;manage bill&quot;,&quot;edit bill&quot;,&quot;manage revenue&quot;,&quot;edit revenue&quot;,&quot;manage payment&quot;,&quot;edit payment&quot;,&quot;manage proposal&quot;,&quot;edit proposal&quot;,&quot;manage goal&quot;,&quot;edit goal&quot;,&quot;manage credit note&quot;,&quot;edit credit note&quot;,&quot;manage debit note&quot;,&quot;edit debit note&quot;,&quot;manage bank account&quot;,&quot;edit bank account&quot;,&quot;manage bank transfer&quot;,&quot;edit bank transfer&quot;,&quot;manage transaction&quot;,&quot;edit transaction&quot;,&quot;manage chart of account&quot;,&quot;edit chart of account&quot;,&quot;manage journal entry&quot;,&quot;edit journal entry&quot;,&quot;manage assets&quot;,&quot;edit assets&quot;,&quot;manage constant custom field&quot;,&quot;edit constant custom field&quot;,&quot;manage report&quot;],&quot;project&quot;:[&quot;create project&quot;,&quot;create project task&quot;,&quot;create timesheet&quot;,&quot;create bug report&quot;,&quot;create milestone&quot;,&quot;create project stage&quot;,&quot;create project task stage&quot;,&quot;create project expense&quot;,&quot;create activity&quot;,&quot;create bug status&quot;,&quot;view project dashboard&quot;,&quot;view project&quot;,&quot;view project task&quot;,&quot;view timesheet&quot;,&quot;view bug report&quot;,&quot;view milestone&quot;,&quot;view project stage&quot;,&quot;view project task stage&quot;,&quot;view project expense&quot;,&quot;view activity&quot;,&quot;view bug status&quot;,&quot;delete project&quot;,&quot;delete project task&quot;,&quot;delete timesheet&quot;,&quot;delete bug report&quot;,&quot;delete milestone&quot;,&quot;delete project stage&quot;,&quot;delete project task stage&quot;,&quot;delete project expense&quot;,&quot;delete activity&quot;,&quot;delete bug status&quot;,&quot;manage project&quot;,&quot;edit project&quot;,&quot;manage project task&quot;,&quot;edit project task&quot;,&quot;manage timesheet&quot;,&quot;edit timesheet&quot;,&quot;manage bug report&quot;,&quot;edit bug report&quot;,&quot;manage milestone&quot;,&quot;edit milestone&quot;,&quot;manage project stage&quot;,&quot;edit project stage&quot;,&quot;manage project task stage&quot;,&quot;edit project task stage&quot;,&quot;manage project expense&quot;,&quot;edit project expense&quot;,&quot;manage activity&quot;,&quot;edit activity&quot;,&quot;manage bug status&quot;,&quot;edit bug status&quot;],&quot;pos&quot;:[&quot;create warehouse&quot;,&quot;create purchase&quot;,&quot;create quotation&quot;,&quot;create pos&quot;,&quot;create barcode&quot;,&quot;create product&quot;,&quot;create product category&quot;,&quot;create product unit&quot;,&quot;view pos dashboard&quot;,&quot;view warehouse&quot;,&quot;view purchase&quot;,&quot;view quotation&quot;,&quot;view pos&quot;,&quot;view product&quot;,&quot;view product category&quot;,&quot;view product unit&quot;,&quot;delete warehouse&quot;,&quot;delete purchase&quot;,&quot;delete quotation&quot;,&quot;delete pos&quot;,&quot;delete product&quot;,&quot;delete product category&quot;,&quot;delete product unit&quot;,&quot;manage warehouse&quot;,&quot;edit warehouse&quot;,&quot;manage purchase&quot;,&quot;edit purchase&quot;,&quot;manage quotation&quot;,&quot;edit quotation&quot;,&quot;manage pos&quot;,&quot;edit pos&quot;,&quot;manage product&quot;,&quot;edit product&quot;,&quot;manage product category&quot;,&quot;edit product category&quot;,&quot;manage product unit&quot;,&quot;edit product unit&quot;],&quot;support&quot;:[&quot;create support&quot;,&quot;view support dashboard&quot;,&quot;view support&quot;,&quot;delete support&quot;,&quot;manage support&quot;,&quot;edit support&quot;,&quot;reply support&quot;],&quot;user_management&quot;:[&quot;create user&quot;,&quot;create client&quot;,&quot;view user&quot;,&quot;view client&quot;,&quot;delete user&quot;,&quot;delete client&quot;,&quot;manage user&quot;,&quot;edit user&quot;,&quot;manage client&quot;,&quot;edit client&quot;],&quot;booking&quot;:[&quot;create booking&quot;,&quot;create appointment&quot;,&quot;create appointment booking&quot;,&quot;create calendar event&quot;,&quot;view booking dashboard&quot;,&quot;view booking&quot;,&quot;show booking&quot;,&quot;view appointment&quot;,&quot;show appointment&quot;,&quot;view appointment booking&quot;,&quot;show appointment booking&quot;,&quot;view calendar event&quot;,&quot;show calendar event&quot;,&quot;delete booking&quot;,&quot;delete appointment&quot;,&quot;delete appointment booking&quot;,&quot;delete calendar event&quot;,&quot;manage booking&quot;,&quot;edit booking&quot;,&quot;manage appointment&quot;,&quot;edit appointment&quot;,&quot;manage appointment booking&quot;,&quot;edit appointment booking&quot;,&quot;manage calendar event&quot;,&quot;edit calendar event&quot;],&quot;omx_flow&quot;:[&quot;access omx flow&quot;,&quot;whatsapp_flows&quot;,&quot;whatsapp_orders&quot;,&quot;campaigns&quot;,&quot;templates&quot;,&quot;chatbot&quot;],&quot;personal_tasks&quot;:[&quot;create personal task&quot;,&quot;create personal task comment&quot;,&quot;create personal task file&quot;,&quot;create personal task checklist&quot;,&quot;view personal task&quot;,&quot;delete personal task&quot;,&quot;delete personal task comment&quot;,&quot;delete personal task file&quot;,&quot;delete personal task checklist&quot;,&quot;manage personal task&quot;,&quot;edit personal task&quot;,&quot;edit personal task comment&quot;,&quot;edit personal task checklist&quot;,&quot;manage personal task time tracking&quot;],&quot;automatish&quot;:[&quot;access automatish&quot;]}", "2025-07-31 06:15:35", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 35}, {"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1926172, "duration": 0.004690000000000001, "duration_str": "4.69ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:35", "source": "app/Observers/PricingPlanObserver.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=35", "ajax": false, "filename": "PricingPlanObserver.php", "line": "35"}, "connection": "radhe_same", "start_percent": 30.659, "width_percent": 5.367}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 76}, {"index": 16, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2151291, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:76", "source": "app/Observers/PricingPlanObserver.php:76", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=76", "ajax": false, "filename": "PricingPlanObserver.php", "line": "76"}, "connection": "radhe_same", "start_percent": 36.027, "width_percent": 2.14}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 91}, {"index": 18, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 19, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}], "start": **********.228833, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 38.167, "width_percent": 2.621}, {"sql": "update `users` set `module_permissions` = '{\\\"crm\\\":[\\\"create lead\\\",\\\"create deal\\\",\\\"create form builder\\\",\\\"create contract\\\",\\\"create pipeline\\\",\\\"create stage\\\",\\\"create source\\\",\\\"create label\\\",\\\"view crm dashboard\\\",\\\"view lead\\\",\\\"view deal\\\",\\\"view form builder\\\",\\\"view contract\\\",\\\"view pipeline\\\",\\\"view stage\\\",\\\"view source\\\",\\\"view label\\\",\\\"delete lead\\\",\\\"delete deal\\\",\\\"delete form builder\\\",\\\"delete contract\\\",\\\"delete pipeline\\\",\\\"delete stage\\\",\\\"delete source\\\",\\\"delete label\\\",\\\"manage lead\\\",\\\"edit lead\\\",\\\"manage deal\\\",\\\"edit deal\\\",\\\"manage form builder\\\",\\\"edit form builder\\\",\\\"manage contract\\\",\\\"edit contract\\\",\\\"manage pipeline\\\",\\\"edit pipeline\\\",\\\"manage stage\\\",\\\"edit stage\\\",\\\"manage source\\\",\\\"edit source\\\",\\\"manage label\\\",\\\"edit label\\\"],\\\"hrm\\\":[\\\"create employee\\\",\\\"create set salary\\\",\\\"create pay slip\\\",\\\"create leave\\\",\\\"create attendance\\\",\\\"create training\\\",\\\"create award\\\",\\\"create branch\\\",\\\"create department\\\",\\\"create designation\\\",\\\"create document type\\\",\\\"view hrm dashboard\\\",\\\"view employee\\\",\\\"view set salary\\\",\\\"view pay slip\\\",\\\"view leave\\\",\\\"view attendance\\\",\\\"view training\\\",\\\"view award\\\",\\\"view branch\\\",\\\"view department\\\",\\\"view designation\\\",\\\"view document type\\\",\\\"delete employee\\\",\\\"delete set salary\\\",\\\"delete pay slip\\\",\\\"delete leave\\\",\\\"delete attendance\\\",\\\"delete training\\\",\\\"delete award\\\",\\\"delete branch\\\",\\\"delete department\\\",\\\"delete designation\\\",\\\"delete document type\\\",\\\"manage employee\\\",\\\"edit employee\\\",\\\"manage set salary\\\",\\\"edit set salary\\\",\\\"manage pay slip\\\",\\\"edit pay slip\\\",\\\"manage leave\\\",\\\"edit leave\\\",\\\"manage attendance\\\",\\\"edit attendance\\\",\\\"manage training\\\",\\\"edit training\\\",\\\"manage award\\\",\\\"edit award\\\",\\\"manage branch\\\",\\\"edit branch\\\",\\\"manage department\\\",\\\"edit department\\\",\\\"manage designation\\\",\\\"edit designation\\\",\\\"manage document type\\\",\\\"edit document type\\\"],\\\"account\\\":[\\\"create customer\\\",\\\"create vender\\\",\\\"create invoice\\\",\\\"create bill\\\",\\\"create revenue\\\",\\\"create payment\\\",\\\"create proposal\\\",\\\"create goal\\\",\\\"create credit note\\\",\\\"create debit note\\\",\\\"create bank account\\\",\\\"create bank transfer\\\",\\\"create transaction\\\",\\\"create chart of account\\\",\\\"create journal entry\\\",\\\"create assets\\\",\\\"create constant custom field\\\",\\\"view account dashboard\\\",\\\"view customer\\\",\\\"view vender\\\",\\\"view invoice\\\",\\\"view bill\\\",\\\"view revenue\\\",\\\"view payment\\\",\\\"view proposal\\\",\\\"view goal\\\",\\\"view credit note\\\",\\\"view debit note\\\",\\\"view bank account\\\",\\\"view bank transfer\\\",\\\"view transaction\\\",\\\"view chart of account\\\",\\\"view journal entry\\\",\\\"view assets\\\",\\\"view constant custom field\\\",\\\"view report\\\",\\\"delete customer\\\",\\\"delete vender\\\",\\\"delete invoice\\\",\\\"delete bill\\\",\\\"delete revenue\\\",\\\"delete payment\\\",\\\"delete proposal\\\",\\\"delete goal\\\",\\\"delete credit note\\\",\\\"delete debit note\\\",\\\"delete bank account\\\",\\\"delete bank transfer\\\",\\\"delete transaction\\\",\\\"delete chart of account\\\",\\\"delete journal entry\\\",\\\"delete assets\\\",\\\"delete constant custom field\\\",\\\"manage customer\\\",\\\"edit customer\\\",\\\"manage vender\\\",\\\"edit vender\\\",\\\"manage invoice\\\",\\\"edit invoice\\\",\\\"manage bill\\\",\\\"edit bill\\\",\\\"manage revenue\\\",\\\"edit revenue\\\",\\\"manage payment\\\",\\\"edit payment\\\",\\\"manage proposal\\\",\\\"edit proposal\\\",\\\"manage goal\\\",\\\"edit goal\\\",\\\"manage credit note\\\",\\\"edit credit note\\\",\\\"manage debit note\\\",\\\"edit debit note\\\",\\\"manage bank account\\\",\\\"edit bank account\\\",\\\"manage bank transfer\\\",\\\"edit bank transfer\\\",\\\"manage transaction\\\",\\\"edit transaction\\\",\\\"manage chart of account\\\",\\\"edit chart of account\\\",\\\"manage journal entry\\\",\\\"edit journal entry\\\",\\\"manage assets\\\",\\\"edit assets\\\",\\\"manage constant custom field\\\",\\\"edit constant custom field\\\",\\\"manage report\\\"],\\\"project\\\":[\\\"create project\\\",\\\"create project task\\\",\\\"create timesheet\\\",\\\"create bug report\\\",\\\"create milestone\\\",\\\"create project stage\\\",\\\"create project task stage\\\",\\\"create project expense\\\",\\\"create activity\\\",\\\"create bug status\\\",\\\"view project dashboard\\\",\\\"view project\\\",\\\"view project task\\\",\\\"view timesheet\\\",\\\"view bug report\\\",\\\"view milestone\\\",\\\"view project stage\\\",\\\"view project task stage\\\",\\\"view project expense\\\",\\\"view activity\\\",\\\"view bug status\\\",\\\"delete project\\\",\\\"delete project task\\\",\\\"delete timesheet\\\",\\\"delete bug report\\\",\\\"delete milestone\\\",\\\"delete project stage\\\",\\\"delete project task stage\\\",\\\"delete project expense\\\",\\\"delete activity\\\",\\\"delete bug status\\\",\\\"manage project\\\",\\\"edit project\\\",\\\"manage project task\\\",\\\"edit project task\\\",\\\"manage timesheet\\\",\\\"edit timesheet\\\",\\\"manage bug report\\\",\\\"edit bug report\\\",\\\"manage milestone\\\",\\\"edit milestone\\\",\\\"manage project stage\\\",\\\"edit project stage\\\",\\\"manage project task stage\\\",\\\"edit project task stage\\\",\\\"manage project expense\\\",\\\"edit project expense\\\",\\\"manage activity\\\",\\\"edit activity\\\",\\\"manage bug status\\\",\\\"edit bug status\\\"],\\\"pos\\\":[\\\"create warehouse\\\",\\\"create purchase\\\",\\\"create quotation\\\",\\\"create pos\\\",\\\"create barcode\\\",\\\"create product\\\",\\\"create product category\\\",\\\"create product unit\\\",\\\"view pos dashboard\\\",\\\"view warehouse\\\",\\\"view purchase\\\",\\\"view quotation\\\",\\\"view pos\\\",\\\"view product\\\",\\\"view product category\\\",\\\"view product unit\\\",\\\"delete warehouse\\\",\\\"delete purchase\\\",\\\"delete quotation\\\",\\\"delete pos\\\",\\\"delete product\\\",\\\"delete product category\\\",\\\"delete product unit\\\",\\\"manage warehouse\\\",\\\"edit warehouse\\\",\\\"manage purchase\\\",\\\"edit purchase\\\",\\\"manage quotation\\\",\\\"edit quotation\\\",\\\"manage pos\\\",\\\"edit pos\\\",\\\"manage product\\\",\\\"edit product\\\",\\\"manage product category\\\",\\\"edit product category\\\",\\\"manage product unit\\\",\\\"edit product unit\\\"],\\\"support\\\":[\\\"create support\\\",\\\"view support dashboard\\\",\\\"view support\\\",\\\"delete support\\\",\\\"manage support\\\",\\\"edit support\\\",\\\"reply support\\\"],\\\"user_management\\\":[\\\"create user\\\",\\\"create client\\\",\\\"view user\\\",\\\"view client\\\",\\\"delete user\\\",\\\"delete client\\\",\\\"manage user\\\",\\\"edit user\\\",\\\"manage client\\\",\\\"edit client\\\"],\\\"booking\\\":[\\\"create booking\\\",\\\"create appointment\\\",\\\"create appointment booking\\\",\\\"create calendar event\\\",\\\"view booking dashboard\\\",\\\"view booking\\\",\\\"show booking\\\",\\\"view appointment\\\",\\\"show appointment\\\",\\\"view appointment booking\\\",\\\"show appointment booking\\\",\\\"view calendar event\\\",\\\"show calendar event\\\",\\\"delete booking\\\",\\\"delete appointment\\\",\\\"delete appointment booking\\\",\\\"delete calendar event\\\",\\\"manage booking\\\",\\\"edit booking\\\",\\\"manage appointment\\\",\\\"edit appointment\\\",\\\"manage appointment booking\\\",\\\"edit appointment booking\\\",\\\"manage calendar event\\\",\\\"edit calendar event\\\"],\\\"omx_flow\\\":[\\\"access omx flow\\\",\\\"whatsapp_flows\\\",\\\"whatsapp_orders\\\",\\\"campaigns\\\",\\\"templates\\\",\\\"chatbot\\\"],\\\"personal_tasks\\\":[\\\"create personal task\\\",\\\"create personal task comment\\\",\\\"create personal task file\\\",\\\"create personal task checklist\\\",\\\"view personal task\\\",\\\"delete personal task\\\",\\\"delete personal task comment\\\",\\\"delete personal task file\\\",\\\"delete personal task checklist\\\",\\\"manage personal task\\\",\\\"edit personal task\\\",\\\"edit personal task comment\\\",\\\"edit personal task checklist\\\",\\\"manage personal task time tracking\\\"],\\\"automatish\\\":[\\\"access automatish\\\"]}', `users`.`updated_at` = '2025-07-31 06:15:37' where `id` = 82", "type": "query", "params": [], "bindings": ["{&quot;crm&quot;:[&quot;create lead&quot;,&quot;create deal&quot;,&quot;create form builder&quot;,&quot;create contract&quot;,&quot;create pipeline&quot;,&quot;create stage&quot;,&quot;create source&quot;,&quot;create label&quot;,&quot;view crm dashboard&quot;,&quot;view lead&quot;,&quot;view deal&quot;,&quot;view form builder&quot;,&quot;view contract&quot;,&quot;view pipeline&quot;,&quot;view stage&quot;,&quot;view source&quot;,&quot;view label&quot;,&quot;delete lead&quot;,&quot;delete deal&quot;,&quot;delete form builder&quot;,&quot;delete contract&quot;,&quot;delete pipeline&quot;,&quot;delete stage&quot;,&quot;delete source&quot;,&quot;delete label&quot;,&quot;manage lead&quot;,&quot;edit lead&quot;,&quot;manage deal&quot;,&quot;edit deal&quot;,&quot;manage form builder&quot;,&quot;edit form builder&quot;,&quot;manage contract&quot;,&quot;edit contract&quot;,&quot;manage pipeline&quot;,&quot;edit pipeline&quot;,&quot;manage stage&quot;,&quot;edit stage&quot;,&quot;manage source&quot;,&quot;edit source&quot;,&quot;manage label&quot;,&quot;edit label&quot;],&quot;hrm&quot;:[&quot;create employee&quot;,&quot;create set salary&quot;,&quot;create pay slip&quot;,&quot;create leave&quot;,&quot;create attendance&quot;,&quot;create training&quot;,&quot;create award&quot;,&quot;create branch&quot;,&quot;create department&quot;,&quot;create designation&quot;,&quot;create document type&quot;,&quot;view hrm dashboard&quot;,&quot;view employee&quot;,&quot;view set salary&quot;,&quot;view pay slip&quot;,&quot;view leave&quot;,&quot;view attendance&quot;,&quot;view training&quot;,&quot;view award&quot;,&quot;view branch&quot;,&quot;view department&quot;,&quot;view designation&quot;,&quot;view document type&quot;,&quot;delete employee&quot;,&quot;delete set salary&quot;,&quot;delete pay slip&quot;,&quot;delete leave&quot;,&quot;delete attendance&quot;,&quot;delete training&quot;,&quot;delete award&quot;,&quot;delete branch&quot;,&quot;delete department&quot;,&quot;delete designation&quot;,&quot;delete document type&quot;,&quot;manage employee&quot;,&quot;edit employee&quot;,&quot;manage set salary&quot;,&quot;edit set salary&quot;,&quot;manage pay slip&quot;,&quot;edit pay slip&quot;,&quot;manage leave&quot;,&quot;edit leave&quot;,&quot;manage attendance&quot;,&quot;edit attendance&quot;,&quot;manage training&quot;,&quot;edit training&quot;,&quot;manage award&quot;,&quot;edit award&quot;,&quot;manage branch&quot;,&quot;edit branch&quot;,&quot;manage department&quot;,&quot;edit department&quot;,&quot;manage designation&quot;,&quot;edit designation&quot;,&quot;manage document type&quot;,&quot;edit document type&quot;],&quot;account&quot;:[&quot;create customer&quot;,&quot;create vender&quot;,&quot;create invoice&quot;,&quot;create bill&quot;,&quot;create revenue&quot;,&quot;create payment&quot;,&quot;create proposal&quot;,&quot;create goal&quot;,&quot;create credit note&quot;,&quot;create debit note&quot;,&quot;create bank account&quot;,&quot;create bank transfer&quot;,&quot;create transaction&quot;,&quot;create chart of account&quot;,&quot;create journal entry&quot;,&quot;create assets&quot;,&quot;create constant custom field&quot;,&quot;view account dashboard&quot;,&quot;view customer&quot;,&quot;view vender&quot;,&quot;view invoice&quot;,&quot;view bill&quot;,&quot;view revenue&quot;,&quot;view payment&quot;,&quot;view proposal&quot;,&quot;view goal&quot;,&quot;view credit note&quot;,&quot;view debit note&quot;,&quot;view bank account&quot;,&quot;view bank transfer&quot;,&quot;view transaction&quot;,&quot;view chart of account&quot;,&quot;view journal entry&quot;,&quot;view assets&quot;,&quot;view constant custom field&quot;,&quot;view report&quot;,&quot;delete customer&quot;,&quot;delete vender&quot;,&quot;delete invoice&quot;,&quot;delete bill&quot;,&quot;delete revenue&quot;,&quot;delete payment&quot;,&quot;delete proposal&quot;,&quot;delete goal&quot;,&quot;delete credit note&quot;,&quot;delete debit note&quot;,&quot;delete bank account&quot;,&quot;delete bank transfer&quot;,&quot;delete transaction&quot;,&quot;delete chart of account&quot;,&quot;delete journal entry&quot;,&quot;delete assets&quot;,&quot;delete constant custom field&quot;,&quot;manage customer&quot;,&quot;edit customer&quot;,&quot;manage vender&quot;,&quot;edit vender&quot;,&quot;manage invoice&quot;,&quot;edit invoice&quot;,&quot;manage bill&quot;,&quot;edit bill&quot;,&quot;manage revenue&quot;,&quot;edit revenue&quot;,&quot;manage payment&quot;,&quot;edit payment&quot;,&quot;manage proposal&quot;,&quot;edit proposal&quot;,&quot;manage goal&quot;,&quot;edit goal&quot;,&quot;manage credit note&quot;,&quot;edit credit note&quot;,&quot;manage debit note&quot;,&quot;edit debit note&quot;,&quot;manage bank account&quot;,&quot;edit bank account&quot;,&quot;manage bank transfer&quot;,&quot;edit bank transfer&quot;,&quot;manage transaction&quot;,&quot;edit transaction&quot;,&quot;manage chart of account&quot;,&quot;edit chart of account&quot;,&quot;manage journal entry&quot;,&quot;edit journal entry&quot;,&quot;manage assets&quot;,&quot;edit assets&quot;,&quot;manage constant custom field&quot;,&quot;edit constant custom field&quot;,&quot;manage report&quot;],&quot;project&quot;:[&quot;create project&quot;,&quot;create project task&quot;,&quot;create timesheet&quot;,&quot;create bug report&quot;,&quot;create milestone&quot;,&quot;create project stage&quot;,&quot;create project task stage&quot;,&quot;create project expense&quot;,&quot;create activity&quot;,&quot;create bug status&quot;,&quot;view project dashboard&quot;,&quot;view project&quot;,&quot;view project task&quot;,&quot;view timesheet&quot;,&quot;view bug report&quot;,&quot;view milestone&quot;,&quot;view project stage&quot;,&quot;view project task stage&quot;,&quot;view project expense&quot;,&quot;view activity&quot;,&quot;view bug status&quot;,&quot;delete project&quot;,&quot;delete project task&quot;,&quot;delete timesheet&quot;,&quot;delete bug report&quot;,&quot;delete milestone&quot;,&quot;delete project stage&quot;,&quot;delete project task stage&quot;,&quot;delete project expense&quot;,&quot;delete activity&quot;,&quot;delete bug status&quot;,&quot;manage project&quot;,&quot;edit project&quot;,&quot;manage project task&quot;,&quot;edit project task&quot;,&quot;manage timesheet&quot;,&quot;edit timesheet&quot;,&quot;manage bug report&quot;,&quot;edit bug report&quot;,&quot;manage milestone&quot;,&quot;edit milestone&quot;,&quot;manage project stage&quot;,&quot;edit project stage&quot;,&quot;manage project task stage&quot;,&quot;edit project task stage&quot;,&quot;manage project expense&quot;,&quot;edit project expense&quot;,&quot;manage activity&quot;,&quot;edit activity&quot;,&quot;manage bug status&quot;,&quot;edit bug status&quot;],&quot;pos&quot;:[&quot;create warehouse&quot;,&quot;create purchase&quot;,&quot;create quotation&quot;,&quot;create pos&quot;,&quot;create barcode&quot;,&quot;create product&quot;,&quot;create product category&quot;,&quot;create product unit&quot;,&quot;view pos dashboard&quot;,&quot;view warehouse&quot;,&quot;view purchase&quot;,&quot;view quotation&quot;,&quot;view pos&quot;,&quot;view product&quot;,&quot;view product category&quot;,&quot;view product unit&quot;,&quot;delete warehouse&quot;,&quot;delete purchase&quot;,&quot;delete quotation&quot;,&quot;delete pos&quot;,&quot;delete product&quot;,&quot;delete product category&quot;,&quot;delete product unit&quot;,&quot;manage warehouse&quot;,&quot;edit warehouse&quot;,&quot;manage purchase&quot;,&quot;edit purchase&quot;,&quot;manage quotation&quot;,&quot;edit quotation&quot;,&quot;manage pos&quot;,&quot;edit pos&quot;,&quot;manage product&quot;,&quot;edit product&quot;,&quot;manage product category&quot;,&quot;edit product category&quot;,&quot;manage product unit&quot;,&quot;edit product unit&quot;],&quot;support&quot;:[&quot;create support&quot;,&quot;view support dashboard&quot;,&quot;view support&quot;,&quot;delete support&quot;,&quot;manage support&quot;,&quot;edit support&quot;,&quot;reply support&quot;],&quot;user_management&quot;:[&quot;create user&quot;,&quot;create client&quot;,&quot;view user&quot;,&quot;view client&quot;,&quot;delete user&quot;,&quot;delete client&quot;,&quot;manage user&quot;,&quot;edit user&quot;,&quot;manage client&quot;,&quot;edit client&quot;],&quot;booking&quot;:[&quot;create booking&quot;,&quot;create appointment&quot;,&quot;create appointment booking&quot;,&quot;create calendar event&quot;,&quot;view booking dashboard&quot;,&quot;view booking&quot;,&quot;show booking&quot;,&quot;view appointment&quot;,&quot;show appointment&quot;,&quot;view appointment booking&quot;,&quot;show appointment booking&quot;,&quot;view calendar event&quot;,&quot;show calendar event&quot;,&quot;delete booking&quot;,&quot;delete appointment&quot;,&quot;delete appointment booking&quot;,&quot;delete calendar event&quot;,&quot;manage booking&quot;,&quot;edit booking&quot;,&quot;manage appointment&quot;,&quot;edit appointment&quot;,&quot;manage appointment booking&quot;,&quot;edit appointment booking&quot;,&quot;manage calendar event&quot;,&quot;edit calendar event&quot;],&quot;omx_flow&quot;:[&quot;access omx flow&quot;,&quot;whatsapp_flows&quot;,&quot;whatsapp_orders&quot;,&quot;campaigns&quot;,&quot;templates&quot;,&quot;chatbot&quot;],&quot;personal_tasks&quot;:[&quot;create personal task&quot;,&quot;create personal task comment&quot;,&quot;create personal task file&quot;,&quot;create personal task checklist&quot;,&quot;view personal task&quot;,&quot;delete personal task&quot;,&quot;delete personal task comment&quot;,&quot;delete personal task file&quot;,&quot;delete personal task checklist&quot;,&quot;manage personal task&quot;,&quot;edit personal task&quot;,&quot;edit personal task comment&quot;,&quot;edit personal task checklist&quot;,&quot;manage personal task time tracking&quot;],&quot;automatish&quot;:[&quot;access automatish&quot;]}", "2025-07-31 06:15:37", "82"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 35}, {"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2797809, "duration": 0.00745, "duration_str": "7.45ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:35", "source": "app/Observers/PricingPlanObserver.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=35", "ajax": false, "filename": "PricingPlanObserver.php", "line": "35"}, "connection": "radhe_same", "start_percent": 40.787, "width_percent": 8.526}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 76}, {"index": 16, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.302214, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:76", "source": "app/Observers/PricingPlanObserver.php:76", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=76", "ajax": false, "filename": "PricingPlanObserver.php", "line": "76"}, "connection": "radhe_same", "start_percent": 49.313, "width_percent": 2.415}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 91}, {"index": 18, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 19, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}], "start": **********.3143952, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 51.728, "width_percent": 2.335}, {"sql": "update `users` set `module_permissions` = '{\\\"crm\\\":[\\\"create lead\\\",\\\"create deal\\\",\\\"create form builder\\\",\\\"create contract\\\",\\\"create pipeline\\\",\\\"create stage\\\",\\\"create source\\\",\\\"create label\\\",\\\"view crm dashboard\\\",\\\"view lead\\\",\\\"view deal\\\",\\\"view form builder\\\",\\\"view contract\\\",\\\"view pipeline\\\",\\\"view stage\\\",\\\"view source\\\",\\\"view label\\\",\\\"delete lead\\\",\\\"delete deal\\\",\\\"delete form builder\\\",\\\"delete contract\\\",\\\"delete pipeline\\\",\\\"delete stage\\\",\\\"delete source\\\",\\\"delete label\\\",\\\"manage lead\\\",\\\"edit lead\\\",\\\"manage deal\\\",\\\"edit deal\\\",\\\"manage form builder\\\",\\\"edit form builder\\\",\\\"manage contract\\\",\\\"edit contract\\\",\\\"manage pipeline\\\",\\\"edit pipeline\\\",\\\"manage stage\\\",\\\"edit stage\\\",\\\"manage source\\\",\\\"edit source\\\",\\\"manage label\\\",\\\"edit label\\\"],\\\"hrm\\\":[\\\"create employee\\\",\\\"create set salary\\\",\\\"create pay slip\\\",\\\"create leave\\\",\\\"create attendance\\\",\\\"create training\\\",\\\"create award\\\",\\\"create branch\\\",\\\"create department\\\",\\\"create designation\\\",\\\"create document type\\\",\\\"view hrm dashboard\\\",\\\"view employee\\\",\\\"view set salary\\\",\\\"view pay slip\\\",\\\"view leave\\\",\\\"view attendance\\\",\\\"view training\\\",\\\"view award\\\",\\\"view branch\\\",\\\"view department\\\",\\\"view designation\\\",\\\"view document type\\\",\\\"delete employee\\\",\\\"delete set salary\\\",\\\"delete pay slip\\\",\\\"delete leave\\\",\\\"delete attendance\\\",\\\"delete training\\\",\\\"delete award\\\",\\\"delete branch\\\",\\\"delete department\\\",\\\"delete designation\\\",\\\"delete document type\\\",\\\"manage employee\\\",\\\"edit employee\\\",\\\"manage set salary\\\",\\\"edit set salary\\\",\\\"manage pay slip\\\",\\\"edit pay slip\\\",\\\"manage leave\\\",\\\"edit leave\\\",\\\"manage attendance\\\",\\\"edit attendance\\\",\\\"manage training\\\",\\\"edit training\\\",\\\"manage award\\\",\\\"edit award\\\",\\\"manage branch\\\",\\\"edit branch\\\",\\\"manage department\\\",\\\"edit department\\\",\\\"manage designation\\\",\\\"edit designation\\\",\\\"manage document type\\\",\\\"edit document type\\\"],\\\"account\\\":[\\\"create customer\\\",\\\"create vender\\\",\\\"create invoice\\\",\\\"create bill\\\",\\\"create revenue\\\",\\\"create payment\\\",\\\"create proposal\\\",\\\"create goal\\\",\\\"create credit note\\\",\\\"create debit note\\\",\\\"create bank account\\\",\\\"create bank transfer\\\",\\\"create transaction\\\",\\\"create chart of account\\\",\\\"create journal entry\\\",\\\"create assets\\\",\\\"create constant custom field\\\",\\\"view account dashboard\\\",\\\"view customer\\\",\\\"view vender\\\",\\\"view invoice\\\",\\\"view bill\\\",\\\"view revenue\\\",\\\"view payment\\\",\\\"view proposal\\\",\\\"view goal\\\",\\\"view credit note\\\",\\\"view debit note\\\",\\\"view bank account\\\",\\\"view bank transfer\\\",\\\"view transaction\\\",\\\"view chart of account\\\",\\\"view journal entry\\\",\\\"view assets\\\",\\\"view constant custom field\\\",\\\"view report\\\",\\\"delete customer\\\",\\\"delete vender\\\",\\\"delete invoice\\\",\\\"delete bill\\\",\\\"delete revenue\\\",\\\"delete payment\\\",\\\"delete proposal\\\",\\\"delete goal\\\",\\\"delete credit note\\\",\\\"delete debit note\\\",\\\"delete bank account\\\",\\\"delete bank transfer\\\",\\\"delete transaction\\\",\\\"delete chart of account\\\",\\\"delete journal entry\\\",\\\"delete assets\\\",\\\"delete constant custom field\\\",\\\"manage customer\\\",\\\"edit customer\\\",\\\"manage vender\\\",\\\"edit vender\\\",\\\"manage invoice\\\",\\\"edit invoice\\\",\\\"manage bill\\\",\\\"edit bill\\\",\\\"manage revenue\\\",\\\"edit revenue\\\",\\\"manage payment\\\",\\\"edit payment\\\",\\\"manage proposal\\\",\\\"edit proposal\\\",\\\"manage goal\\\",\\\"edit goal\\\",\\\"manage credit note\\\",\\\"edit credit note\\\",\\\"manage debit note\\\",\\\"edit debit note\\\",\\\"manage bank account\\\",\\\"edit bank account\\\",\\\"manage bank transfer\\\",\\\"edit bank transfer\\\",\\\"manage transaction\\\",\\\"edit transaction\\\",\\\"manage chart of account\\\",\\\"edit chart of account\\\",\\\"manage journal entry\\\",\\\"edit journal entry\\\",\\\"manage assets\\\",\\\"edit assets\\\",\\\"manage constant custom field\\\",\\\"edit constant custom field\\\",\\\"manage report\\\"],\\\"project\\\":[\\\"create project\\\",\\\"create project task\\\",\\\"create timesheet\\\",\\\"create bug report\\\",\\\"create milestone\\\",\\\"create project stage\\\",\\\"create project task stage\\\",\\\"create project expense\\\",\\\"create activity\\\",\\\"create bug status\\\",\\\"view project dashboard\\\",\\\"view project\\\",\\\"view project task\\\",\\\"view timesheet\\\",\\\"view bug report\\\",\\\"view milestone\\\",\\\"view project stage\\\",\\\"view project task stage\\\",\\\"view project expense\\\",\\\"view activity\\\",\\\"view bug status\\\",\\\"delete project\\\",\\\"delete project task\\\",\\\"delete timesheet\\\",\\\"delete bug report\\\",\\\"delete milestone\\\",\\\"delete project stage\\\",\\\"delete project task stage\\\",\\\"delete project expense\\\",\\\"delete activity\\\",\\\"delete bug status\\\",\\\"manage project\\\",\\\"edit project\\\",\\\"manage project task\\\",\\\"edit project task\\\",\\\"manage timesheet\\\",\\\"edit timesheet\\\",\\\"manage bug report\\\",\\\"edit bug report\\\",\\\"manage milestone\\\",\\\"edit milestone\\\",\\\"manage project stage\\\",\\\"edit project stage\\\",\\\"manage project task stage\\\",\\\"edit project task stage\\\",\\\"manage project expense\\\",\\\"edit project expense\\\",\\\"manage activity\\\",\\\"edit activity\\\",\\\"manage bug status\\\",\\\"edit bug status\\\"],\\\"pos\\\":[\\\"create warehouse\\\",\\\"create purchase\\\",\\\"create quotation\\\",\\\"create pos\\\",\\\"create barcode\\\",\\\"create product\\\",\\\"create product category\\\",\\\"create product unit\\\",\\\"view pos dashboard\\\",\\\"view warehouse\\\",\\\"view purchase\\\",\\\"view quotation\\\",\\\"view pos\\\",\\\"view product\\\",\\\"view product category\\\",\\\"view product unit\\\",\\\"delete warehouse\\\",\\\"delete purchase\\\",\\\"delete quotation\\\",\\\"delete pos\\\",\\\"delete product\\\",\\\"delete product category\\\",\\\"delete product unit\\\",\\\"manage warehouse\\\",\\\"edit warehouse\\\",\\\"manage purchase\\\",\\\"edit purchase\\\",\\\"manage quotation\\\",\\\"edit quotation\\\",\\\"manage pos\\\",\\\"edit pos\\\",\\\"manage product\\\",\\\"edit product\\\",\\\"manage product category\\\",\\\"edit product category\\\",\\\"manage product unit\\\",\\\"edit product unit\\\"],\\\"support\\\":[\\\"create support\\\",\\\"view support dashboard\\\",\\\"view support\\\",\\\"delete support\\\",\\\"manage support\\\",\\\"edit support\\\",\\\"reply support\\\"],\\\"user_management\\\":[\\\"create user\\\",\\\"create client\\\",\\\"view user\\\",\\\"view client\\\",\\\"delete user\\\",\\\"delete client\\\",\\\"manage user\\\",\\\"edit user\\\",\\\"manage client\\\",\\\"edit client\\\"],\\\"booking\\\":[\\\"create booking\\\",\\\"create appointment\\\",\\\"create appointment booking\\\",\\\"create calendar event\\\",\\\"view booking dashboard\\\",\\\"view booking\\\",\\\"show booking\\\",\\\"view appointment\\\",\\\"show appointment\\\",\\\"view appointment booking\\\",\\\"show appointment booking\\\",\\\"view calendar event\\\",\\\"show calendar event\\\",\\\"delete booking\\\",\\\"delete appointment\\\",\\\"delete appointment booking\\\",\\\"delete calendar event\\\",\\\"manage booking\\\",\\\"edit booking\\\",\\\"manage appointment\\\",\\\"edit appointment\\\",\\\"manage appointment booking\\\",\\\"edit appointment booking\\\",\\\"manage calendar event\\\",\\\"edit calendar event\\\"],\\\"omx_flow\\\":[\\\"access omx flow\\\",\\\"whatsapp_flows\\\",\\\"whatsapp_orders\\\",\\\"campaigns\\\",\\\"templates\\\",\\\"chatbot\\\"],\\\"personal_tasks\\\":[\\\"create personal task\\\",\\\"create personal task comment\\\",\\\"create personal task file\\\",\\\"create personal task checklist\\\",\\\"view personal task\\\",\\\"delete personal task\\\",\\\"delete personal task comment\\\",\\\"delete personal task file\\\",\\\"delete personal task checklist\\\",\\\"manage personal task\\\",\\\"edit personal task\\\",\\\"edit personal task comment\\\",\\\"edit personal task checklist\\\",\\\"manage personal task time tracking\\\"],\\\"automatish\\\":[\\\"access automatish\\\"]}', `users`.`updated_at` = '2025-07-31 06:15:39' where `id` = 83", "type": "query", "params": [], "bindings": ["{&quot;crm&quot;:[&quot;create lead&quot;,&quot;create deal&quot;,&quot;create form builder&quot;,&quot;create contract&quot;,&quot;create pipeline&quot;,&quot;create stage&quot;,&quot;create source&quot;,&quot;create label&quot;,&quot;view crm dashboard&quot;,&quot;view lead&quot;,&quot;view deal&quot;,&quot;view form builder&quot;,&quot;view contract&quot;,&quot;view pipeline&quot;,&quot;view stage&quot;,&quot;view source&quot;,&quot;view label&quot;,&quot;delete lead&quot;,&quot;delete deal&quot;,&quot;delete form builder&quot;,&quot;delete contract&quot;,&quot;delete pipeline&quot;,&quot;delete stage&quot;,&quot;delete source&quot;,&quot;delete label&quot;,&quot;manage lead&quot;,&quot;edit lead&quot;,&quot;manage deal&quot;,&quot;edit deal&quot;,&quot;manage form builder&quot;,&quot;edit form builder&quot;,&quot;manage contract&quot;,&quot;edit contract&quot;,&quot;manage pipeline&quot;,&quot;edit pipeline&quot;,&quot;manage stage&quot;,&quot;edit stage&quot;,&quot;manage source&quot;,&quot;edit source&quot;,&quot;manage label&quot;,&quot;edit label&quot;],&quot;hrm&quot;:[&quot;create employee&quot;,&quot;create set salary&quot;,&quot;create pay slip&quot;,&quot;create leave&quot;,&quot;create attendance&quot;,&quot;create training&quot;,&quot;create award&quot;,&quot;create branch&quot;,&quot;create department&quot;,&quot;create designation&quot;,&quot;create document type&quot;,&quot;view hrm dashboard&quot;,&quot;view employee&quot;,&quot;view set salary&quot;,&quot;view pay slip&quot;,&quot;view leave&quot;,&quot;view attendance&quot;,&quot;view training&quot;,&quot;view award&quot;,&quot;view branch&quot;,&quot;view department&quot;,&quot;view designation&quot;,&quot;view document type&quot;,&quot;delete employee&quot;,&quot;delete set salary&quot;,&quot;delete pay slip&quot;,&quot;delete leave&quot;,&quot;delete attendance&quot;,&quot;delete training&quot;,&quot;delete award&quot;,&quot;delete branch&quot;,&quot;delete department&quot;,&quot;delete designation&quot;,&quot;delete document type&quot;,&quot;manage employee&quot;,&quot;edit employee&quot;,&quot;manage set salary&quot;,&quot;edit set salary&quot;,&quot;manage pay slip&quot;,&quot;edit pay slip&quot;,&quot;manage leave&quot;,&quot;edit leave&quot;,&quot;manage attendance&quot;,&quot;edit attendance&quot;,&quot;manage training&quot;,&quot;edit training&quot;,&quot;manage award&quot;,&quot;edit award&quot;,&quot;manage branch&quot;,&quot;edit branch&quot;,&quot;manage department&quot;,&quot;edit department&quot;,&quot;manage designation&quot;,&quot;edit designation&quot;,&quot;manage document type&quot;,&quot;edit document type&quot;],&quot;account&quot;:[&quot;create customer&quot;,&quot;create vender&quot;,&quot;create invoice&quot;,&quot;create bill&quot;,&quot;create revenue&quot;,&quot;create payment&quot;,&quot;create proposal&quot;,&quot;create goal&quot;,&quot;create credit note&quot;,&quot;create debit note&quot;,&quot;create bank account&quot;,&quot;create bank transfer&quot;,&quot;create transaction&quot;,&quot;create chart of account&quot;,&quot;create journal entry&quot;,&quot;create assets&quot;,&quot;create constant custom field&quot;,&quot;view account dashboard&quot;,&quot;view customer&quot;,&quot;view vender&quot;,&quot;view invoice&quot;,&quot;view bill&quot;,&quot;view revenue&quot;,&quot;view payment&quot;,&quot;view proposal&quot;,&quot;view goal&quot;,&quot;view credit note&quot;,&quot;view debit note&quot;,&quot;view bank account&quot;,&quot;view bank transfer&quot;,&quot;view transaction&quot;,&quot;view chart of account&quot;,&quot;view journal entry&quot;,&quot;view assets&quot;,&quot;view constant custom field&quot;,&quot;view report&quot;,&quot;delete customer&quot;,&quot;delete vender&quot;,&quot;delete invoice&quot;,&quot;delete bill&quot;,&quot;delete revenue&quot;,&quot;delete payment&quot;,&quot;delete proposal&quot;,&quot;delete goal&quot;,&quot;delete credit note&quot;,&quot;delete debit note&quot;,&quot;delete bank account&quot;,&quot;delete bank transfer&quot;,&quot;delete transaction&quot;,&quot;delete chart of account&quot;,&quot;delete journal entry&quot;,&quot;delete assets&quot;,&quot;delete constant custom field&quot;,&quot;manage customer&quot;,&quot;edit customer&quot;,&quot;manage vender&quot;,&quot;edit vender&quot;,&quot;manage invoice&quot;,&quot;edit invoice&quot;,&quot;manage bill&quot;,&quot;edit bill&quot;,&quot;manage revenue&quot;,&quot;edit revenue&quot;,&quot;manage payment&quot;,&quot;edit payment&quot;,&quot;manage proposal&quot;,&quot;edit proposal&quot;,&quot;manage goal&quot;,&quot;edit goal&quot;,&quot;manage credit note&quot;,&quot;edit credit note&quot;,&quot;manage debit note&quot;,&quot;edit debit note&quot;,&quot;manage bank account&quot;,&quot;edit bank account&quot;,&quot;manage bank transfer&quot;,&quot;edit bank transfer&quot;,&quot;manage transaction&quot;,&quot;edit transaction&quot;,&quot;manage chart of account&quot;,&quot;edit chart of account&quot;,&quot;manage journal entry&quot;,&quot;edit journal entry&quot;,&quot;manage assets&quot;,&quot;edit assets&quot;,&quot;manage constant custom field&quot;,&quot;edit constant custom field&quot;,&quot;manage report&quot;],&quot;project&quot;:[&quot;create project&quot;,&quot;create project task&quot;,&quot;create timesheet&quot;,&quot;create bug report&quot;,&quot;create milestone&quot;,&quot;create project stage&quot;,&quot;create project task stage&quot;,&quot;create project expense&quot;,&quot;create activity&quot;,&quot;create bug status&quot;,&quot;view project dashboard&quot;,&quot;view project&quot;,&quot;view project task&quot;,&quot;view timesheet&quot;,&quot;view bug report&quot;,&quot;view milestone&quot;,&quot;view project stage&quot;,&quot;view project task stage&quot;,&quot;view project expense&quot;,&quot;view activity&quot;,&quot;view bug status&quot;,&quot;delete project&quot;,&quot;delete project task&quot;,&quot;delete timesheet&quot;,&quot;delete bug report&quot;,&quot;delete milestone&quot;,&quot;delete project stage&quot;,&quot;delete project task stage&quot;,&quot;delete project expense&quot;,&quot;delete activity&quot;,&quot;delete bug status&quot;,&quot;manage project&quot;,&quot;edit project&quot;,&quot;manage project task&quot;,&quot;edit project task&quot;,&quot;manage timesheet&quot;,&quot;edit timesheet&quot;,&quot;manage bug report&quot;,&quot;edit bug report&quot;,&quot;manage milestone&quot;,&quot;edit milestone&quot;,&quot;manage project stage&quot;,&quot;edit project stage&quot;,&quot;manage project task stage&quot;,&quot;edit project task stage&quot;,&quot;manage project expense&quot;,&quot;edit project expense&quot;,&quot;manage activity&quot;,&quot;edit activity&quot;,&quot;manage bug status&quot;,&quot;edit bug status&quot;],&quot;pos&quot;:[&quot;create warehouse&quot;,&quot;create purchase&quot;,&quot;create quotation&quot;,&quot;create pos&quot;,&quot;create barcode&quot;,&quot;create product&quot;,&quot;create product category&quot;,&quot;create product unit&quot;,&quot;view pos dashboard&quot;,&quot;view warehouse&quot;,&quot;view purchase&quot;,&quot;view quotation&quot;,&quot;view pos&quot;,&quot;view product&quot;,&quot;view product category&quot;,&quot;view product unit&quot;,&quot;delete warehouse&quot;,&quot;delete purchase&quot;,&quot;delete quotation&quot;,&quot;delete pos&quot;,&quot;delete product&quot;,&quot;delete product category&quot;,&quot;delete product unit&quot;,&quot;manage warehouse&quot;,&quot;edit warehouse&quot;,&quot;manage purchase&quot;,&quot;edit purchase&quot;,&quot;manage quotation&quot;,&quot;edit quotation&quot;,&quot;manage pos&quot;,&quot;edit pos&quot;,&quot;manage product&quot;,&quot;edit product&quot;,&quot;manage product category&quot;,&quot;edit product category&quot;,&quot;manage product unit&quot;,&quot;edit product unit&quot;],&quot;support&quot;:[&quot;create support&quot;,&quot;view support dashboard&quot;,&quot;view support&quot;,&quot;delete support&quot;,&quot;manage support&quot;,&quot;edit support&quot;,&quot;reply support&quot;],&quot;user_management&quot;:[&quot;create user&quot;,&quot;create client&quot;,&quot;view user&quot;,&quot;view client&quot;,&quot;delete user&quot;,&quot;delete client&quot;,&quot;manage user&quot;,&quot;edit user&quot;,&quot;manage client&quot;,&quot;edit client&quot;],&quot;booking&quot;:[&quot;create booking&quot;,&quot;create appointment&quot;,&quot;create appointment booking&quot;,&quot;create calendar event&quot;,&quot;view booking dashboard&quot;,&quot;view booking&quot;,&quot;show booking&quot;,&quot;view appointment&quot;,&quot;show appointment&quot;,&quot;view appointment booking&quot;,&quot;show appointment booking&quot;,&quot;view calendar event&quot;,&quot;show calendar event&quot;,&quot;delete booking&quot;,&quot;delete appointment&quot;,&quot;delete appointment booking&quot;,&quot;delete calendar event&quot;,&quot;manage booking&quot;,&quot;edit booking&quot;,&quot;manage appointment&quot;,&quot;edit appointment&quot;,&quot;manage appointment booking&quot;,&quot;edit appointment booking&quot;,&quot;manage calendar event&quot;,&quot;edit calendar event&quot;],&quot;omx_flow&quot;:[&quot;access omx flow&quot;,&quot;whatsapp_flows&quot;,&quot;whatsapp_orders&quot;,&quot;campaigns&quot;,&quot;templates&quot;,&quot;chatbot&quot;],&quot;personal_tasks&quot;:[&quot;create personal task&quot;,&quot;create personal task comment&quot;,&quot;create personal task file&quot;,&quot;create personal task checklist&quot;,&quot;view personal task&quot;,&quot;delete personal task&quot;,&quot;delete personal task comment&quot;,&quot;delete personal task file&quot;,&quot;delete personal task checklist&quot;,&quot;manage personal task&quot;,&quot;edit personal task&quot;,&quot;edit personal task comment&quot;,&quot;edit personal task checklist&quot;,&quot;manage personal task time tracking&quot;],&quot;automatish&quot;:[&quot;access automatish&quot;]}", "2025-07-31 06:15:39", "83"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 35}, {"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3809988, "duration": 0.00529, "duration_str": "5.29ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:35", "source": "app/Observers/PricingPlanObserver.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=35", "ajax": false, "filename": "PricingPlanObserver.php", "line": "35"}, "connection": "radhe_same", "start_percent": 54.063, "width_percent": 6.054}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 76}, {"index": 16, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4059062, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:76", "source": "app/Observers/PricingPlanObserver.php:76", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=76", "ajax": false, "filename": "PricingPlanObserver.php", "line": "76"}, "connection": "radhe_same", "start_percent": 60.117, "width_percent": 2.529}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 91}, {"index": 18, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 19, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}], "start": **********.422577, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 62.646, "width_percent": 3.056}, {"sql": "update `users` set `module_permissions` = '{\\\"crm\\\":[\\\"create lead\\\",\\\"create deal\\\",\\\"create form builder\\\",\\\"create contract\\\",\\\"create pipeline\\\",\\\"create stage\\\",\\\"create source\\\",\\\"create label\\\",\\\"view crm dashboard\\\",\\\"view lead\\\",\\\"view deal\\\",\\\"view form builder\\\",\\\"view contract\\\",\\\"view pipeline\\\",\\\"view stage\\\",\\\"view source\\\",\\\"view label\\\",\\\"delete lead\\\",\\\"delete deal\\\",\\\"delete form builder\\\",\\\"delete contract\\\",\\\"delete pipeline\\\",\\\"delete stage\\\",\\\"delete source\\\",\\\"delete label\\\",\\\"manage lead\\\",\\\"edit lead\\\",\\\"manage deal\\\",\\\"edit deal\\\",\\\"manage form builder\\\",\\\"edit form builder\\\",\\\"manage contract\\\",\\\"edit contract\\\",\\\"manage pipeline\\\",\\\"edit pipeline\\\",\\\"manage stage\\\",\\\"edit stage\\\",\\\"manage source\\\",\\\"edit source\\\",\\\"manage label\\\",\\\"edit label\\\"],\\\"hrm\\\":[\\\"create employee\\\",\\\"create set salary\\\",\\\"create pay slip\\\",\\\"create leave\\\",\\\"create attendance\\\",\\\"create training\\\",\\\"create award\\\",\\\"create branch\\\",\\\"create department\\\",\\\"create designation\\\",\\\"create document type\\\",\\\"view hrm dashboard\\\",\\\"view employee\\\",\\\"view set salary\\\",\\\"view pay slip\\\",\\\"view leave\\\",\\\"view attendance\\\",\\\"view training\\\",\\\"view award\\\",\\\"view branch\\\",\\\"view department\\\",\\\"view designation\\\",\\\"view document type\\\",\\\"delete employee\\\",\\\"delete set salary\\\",\\\"delete pay slip\\\",\\\"delete leave\\\",\\\"delete attendance\\\",\\\"delete training\\\",\\\"delete award\\\",\\\"delete branch\\\",\\\"delete department\\\",\\\"delete designation\\\",\\\"delete document type\\\",\\\"manage employee\\\",\\\"edit employee\\\",\\\"manage set salary\\\",\\\"edit set salary\\\",\\\"manage pay slip\\\",\\\"edit pay slip\\\",\\\"manage leave\\\",\\\"edit leave\\\",\\\"manage attendance\\\",\\\"edit attendance\\\",\\\"manage training\\\",\\\"edit training\\\",\\\"manage award\\\",\\\"edit award\\\",\\\"manage branch\\\",\\\"edit branch\\\",\\\"manage department\\\",\\\"edit department\\\",\\\"manage designation\\\",\\\"edit designation\\\",\\\"manage document type\\\",\\\"edit document type\\\"],\\\"account\\\":[\\\"create customer\\\",\\\"create vender\\\",\\\"create invoice\\\",\\\"create bill\\\",\\\"create revenue\\\",\\\"create payment\\\",\\\"create proposal\\\",\\\"create goal\\\",\\\"create credit note\\\",\\\"create debit note\\\",\\\"create bank account\\\",\\\"create bank transfer\\\",\\\"create transaction\\\",\\\"create chart of account\\\",\\\"create journal entry\\\",\\\"create assets\\\",\\\"create constant custom field\\\",\\\"view account dashboard\\\",\\\"view customer\\\",\\\"view vender\\\",\\\"view invoice\\\",\\\"view bill\\\",\\\"view revenue\\\",\\\"view payment\\\",\\\"view proposal\\\",\\\"view goal\\\",\\\"view credit note\\\",\\\"view debit note\\\",\\\"view bank account\\\",\\\"view bank transfer\\\",\\\"view transaction\\\",\\\"view chart of account\\\",\\\"view journal entry\\\",\\\"view assets\\\",\\\"view constant custom field\\\",\\\"view report\\\",\\\"delete customer\\\",\\\"delete vender\\\",\\\"delete invoice\\\",\\\"delete bill\\\",\\\"delete revenue\\\",\\\"delete payment\\\",\\\"delete proposal\\\",\\\"delete goal\\\",\\\"delete credit note\\\",\\\"delete debit note\\\",\\\"delete bank account\\\",\\\"delete bank transfer\\\",\\\"delete transaction\\\",\\\"delete chart of account\\\",\\\"delete journal entry\\\",\\\"delete assets\\\",\\\"delete constant custom field\\\",\\\"manage customer\\\",\\\"edit customer\\\",\\\"manage vender\\\",\\\"edit vender\\\",\\\"manage invoice\\\",\\\"edit invoice\\\",\\\"manage bill\\\",\\\"edit bill\\\",\\\"manage revenue\\\",\\\"edit revenue\\\",\\\"manage payment\\\",\\\"edit payment\\\",\\\"manage proposal\\\",\\\"edit proposal\\\",\\\"manage goal\\\",\\\"edit goal\\\",\\\"manage credit note\\\",\\\"edit credit note\\\",\\\"manage debit note\\\",\\\"edit debit note\\\",\\\"manage bank account\\\",\\\"edit bank account\\\",\\\"manage bank transfer\\\",\\\"edit bank transfer\\\",\\\"manage transaction\\\",\\\"edit transaction\\\",\\\"manage chart of account\\\",\\\"edit chart of account\\\",\\\"manage journal entry\\\",\\\"edit journal entry\\\",\\\"manage assets\\\",\\\"edit assets\\\",\\\"manage constant custom field\\\",\\\"edit constant custom field\\\",\\\"manage report\\\"],\\\"project\\\":[\\\"create project\\\",\\\"create project task\\\",\\\"create timesheet\\\",\\\"create bug report\\\",\\\"create milestone\\\",\\\"create project stage\\\",\\\"create project task stage\\\",\\\"create project expense\\\",\\\"create activity\\\",\\\"create bug status\\\",\\\"view project dashboard\\\",\\\"view project\\\",\\\"view project task\\\",\\\"view timesheet\\\",\\\"view bug report\\\",\\\"view milestone\\\",\\\"view project stage\\\",\\\"view project task stage\\\",\\\"view project expense\\\",\\\"view activity\\\",\\\"view bug status\\\",\\\"delete project\\\",\\\"delete project task\\\",\\\"delete timesheet\\\",\\\"delete bug report\\\",\\\"delete milestone\\\",\\\"delete project stage\\\",\\\"delete project task stage\\\",\\\"delete project expense\\\",\\\"delete activity\\\",\\\"delete bug status\\\",\\\"manage project\\\",\\\"edit project\\\",\\\"manage project task\\\",\\\"edit project task\\\",\\\"manage timesheet\\\",\\\"edit timesheet\\\",\\\"manage bug report\\\",\\\"edit bug report\\\",\\\"manage milestone\\\",\\\"edit milestone\\\",\\\"manage project stage\\\",\\\"edit project stage\\\",\\\"manage project task stage\\\",\\\"edit project task stage\\\",\\\"manage project expense\\\",\\\"edit project expense\\\",\\\"manage activity\\\",\\\"edit activity\\\",\\\"manage bug status\\\",\\\"edit bug status\\\"],\\\"pos\\\":[\\\"create warehouse\\\",\\\"create purchase\\\",\\\"create quotation\\\",\\\"create pos\\\",\\\"create barcode\\\",\\\"create product\\\",\\\"create product category\\\",\\\"create product unit\\\",\\\"view pos dashboard\\\",\\\"view warehouse\\\",\\\"view purchase\\\",\\\"view quotation\\\",\\\"view pos\\\",\\\"view product\\\",\\\"view product category\\\",\\\"view product unit\\\",\\\"delete warehouse\\\",\\\"delete purchase\\\",\\\"delete quotation\\\",\\\"delete pos\\\",\\\"delete product\\\",\\\"delete product category\\\",\\\"delete product unit\\\",\\\"manage warehouse\\\",\\\"edit warehouse\\\",\\\"manage purchase\\\",\\\"edit purchase\\\",\\\"manage quotation\\\",\\\"edit quotation\\\",\\\"manage pos\\\",\\\"edit pos\\\",\\\"manage product\\\",\\\"edit product\\\",\\\"manage product category\\\",\\\"edit product category\\\",\\\"manage product unit\\\",\\\"edit product unit\\\"],\\\"support\\\":[\\\"create support\\\",\\\"view support dashboard\\\",\\\"view support\\\",\\\"delete support\\\",\\\"manage support\\\",\\\"edit support\\\",\\\"reply support\\\"],\\\"user_management\\\":[\\\"create user\\\",\\\"create client\\\",\\\"view user\\\",\\\"view client\\\",\\\"delete user\\\",\\\"delete client\\\",\\\"manage user\\\",\\\"edit user\\\",\\\"manage client\\\",\\\"edit client\\\"],\\\"booking\\\":[\\\"create booking\\\",\\\"create appointment\\\",\\\"create appointment booking\\\",\\\"create calendar event\\\",\\\"view booking dashboard\\\",\\\"view booking\\\",\\\"show booking\\\",\\\"view appointment\\\",\\\"show appointment\\\",\\\"view appointment booking\\\",\\\"show appointment booking\\\",\\\"view calendar event\\\",\\\"show calendar event\\\",\\\"delete booking\\\",\\\"delete appointment\\\",\\\"delete appointment booking\\\",\\\"delete calendar event\\\",\\\"manage booking\\\",\\\"edit booking\\\",\\\"manage appointment\\\",\\\"edit appointment\\\",\\\"manage appointment booking\\\",\\\"edit appointment booking\\\",\\\"manage calendar event\\\",\\\"edit calendar event\\\"],\\\"omx_flow\\\":[\\\"access omx flow\\\",\\\"whatsapp_flows\\\",\\\"whatsapp_orders\\\",\\\"campaigns\\\",\\\"templates\\\",\\\"chatbot\\\"],\\\"personal_tasks\\\":[\\\"create personal task\\\",\\\"create personal task comment\\\",\\\"create personal task file\\\",\\\"create personal task checklist\\\",\\\"view personal task\\\",\\\"delete personal task\\\",\\\"delete personal task comment\\\",\\\"delete personal task file\\\",\\\"delete personal task checklist\\\",\\\"manage personal task\\\",\\\"edit personal task\\\",\\\"edit personal task comment\\\",\\\"edit personal task checklist\\\",\\\"manage personal task time tracking\\\"],\\\"automatish\\\":[\\\"access automatish\\\"]}', `users`.`updated_at` = '2025-07-31 06:15:41' where `id` = 84", "type": "query", "params": [], "bindings": ["{&quot;crm&quot;:[&quot;create lead&quot;,&quot;create deal&quot;,&quot;create form builder&quot;,&quot;create contract&quot;,&quot;create pipeline&quot;,&quot;create stage&quot;,&quot;create source&quot;,&quot;create label&quot;,&quot;view crm dashboard&quot;,&quot;view lead&quot;,&quot;view deal&quot;,&quot;view form builder&quot;,&quot;view contract&quot;,&quot;view pipeline&quot;,&quot;view stage&quot;,&quot;view source&quot;,&quot;view label&quot;,&quot;delete lead&quot;,&quot;delete deal&quot;,&quot;delete form builder&quot;,&quot;delete contract&quot;,&quot;delete pipeline&quot;,&quot;delete stage&quot;,&quot;delete source&quot;,&quot;delete label&quot;,&quot;manage lead&quot;,&quot;edit lead&quot;,&quot;manage deal&quot;,&quot;edit deal&quot;,&quot;manage form builder&quot;,&quot;edit form builder&quot;,&quot;manage contract&quot;,&quot;edit contract&quot;,&quot;manage pipeline&quot;,&quot;edit pipeline&quot;,&quot;manage stage&quot;,&quot;edit stage&quot;,&quot;manage source&quot;,&quot;edit source&quot;,&quot;manage label&quot;,&quot;edit label&quot;],&quot;hrm&quot;:[&quot;create employee&quot;,&quot;create set salary&quot;,&quot;create pay slip&quot;,&quot;create leave&quot;,&quot;create attendance&quot;,&quot;create training&quot;,&quot;create award&quot;,&quot;create branch&quot;,&quot;create department&quot;,&quot;create designation&quot;,&quot;create document type&quot;,&quot;view hrm dashboard&quot;,&quot;view employee&quot;,&quot;view set salary&quot;,&quot;view pay slip&quot;,&quot;view leave&quot;,&quot;view attendance&quot;,&quot;view training&quot;,&quot;view award&quot;,&quot;view branch&quot;,&quot;view department&quot;,&quot;view designation&quot;,&quot;view document type&quot;,&quot;delete employee&quot;,&quot;delete set salary&quot;,&quot;delete pay slip&quot;,&quot;delete leave&quot;,&quot;delete attendance&quot;,&quot;delete training&quot;,&quot;delete award&quot;,&quot;delete branch&quot;,&quot;delete department&quot;,&quot;delete designation&quot;,&quot;delete document type&quot;,&quot;manage employee&quot;,&quot;edit employee&quot;,&quot;manage set salary&quot;,&quot;edit set salary&quot;,&quot;manage pay slip&quot;,&quot;edit pay slip&quot;,&quot;manage leave&quot;,&quot;edit leave&quot;,&quot;manage attendance&quot;,&quot;edit attendance&quot;,&quot;manage training&quot;,&quot;edit training&quot;,&quot;manage award&quot;,&quot;edit award&quot;,&quot;manage branch&quot;,&quot;edit branch&quot;,&quot;manage department&quot;,&quot;edit department&quot;,&quot;manage designation&quot;,&quot;edit designation&quot;,&quot;manage document type&quot;,&quot;edit document type&quot;],&quot;account&quot;:[&quot;create customer&quot;,&quot;create vender&quot;,&quot;create invoice&quot;,&quot;create bill&quot;,&quot;create revenue&quot;,&quot;create payment&quot;,&quot;create proposal&quot;,&quot;create goal&quot;,&quot;create credit note&quot;,&quot;create debit note&quot;,&quot;create bank account&quot;,&quot;create bank transfer&quot;,&quot;create transaction&quot;,&quot;create chart of account&quot;,&quot;create journal entry&quot;,&quot;create assets&quot;,&quot;create constant custom field&quot;,&quot;view account dashboard&quot;,&quot;view customer&quot;,&quot;view vender&quot;,&quot;view invoice&quot;,&quot;view bill&quot;,&quot;view revenue&quot;,&quot;view payment&quot;,&quot;view proposal&quot;,&quot;view goal&quot;,&quot;view credit note&quot;,&quot;view debit note&quot;,&quot;view bank account&quot;,&quot;view bank transfer&quot;,&quot;view transaction&quot;,&quot;view chart of account&quot;,&quot;view journal entry&quot;,&quot;view assets&quot;,&quot;view constant custom field&quot;,&quot;view report&quot;,&quot;delete customer&quot;,&quot;delete vender&quot;,&quot;delete invoice&quot;,&quot;delete bill&quot;,&quot;delete revenue&quot;,&quot;delete payment&quot;,&quot;delete proposal&quot;,&quot;delete goal&quot;,&quot;delete credit note&quot;,&quot;delete debit note&quot;,&quot;delete bank account&quot;,&quot;delete bank transfer&quot;,&quot;delete transaction&quot;,&quot;delete chart of account&quot;,&quot;delete journal entry&quot;,&quot;delete assets&quot;,&quot;delete constant custom field&quot;,&quot;manage customer&quot;,&quot;edit customer&quot;,&quot;manage vender&quot;,&quot;edit vender&quot;,&quot;manage invoice&quot;,&quot;edit invoice&quot;,&quot;manage bill&quot;,&quot;edit bill&quot;,&quot;manage revenue&quot;,&quot;edit revenue&quot;,&quot;manage payment&quot;,&quot;edit payment&quot;,&quot;manage proposal&quot;,&quot;edit proposal&quot;,&quot;manage goal&quot;,&quot;edit goal&quot;,&quot;manage credit note&quot;,&quot;edit credit note&quot;,&quot;manage debit note&quot;,&quot;edit debit note&quot;,&quot;manage bank account&quot;,&quot;edit bank account&quot;,&quot;manage bank transfer&quot;,&quot;edit bank transfer&quot;,&quot;manage transaction&quot;,&quot;edit transaction&quot;,&quot;manage chart of account&quot;,&quot;edit chart of account&quot;,&quot;manage journal entry&quot;,&quot;edit journal entry&quot;,&quot;manage assets&quot;,&quot;edit assets&quot;,&quot;manage constant custom field&quot;,&quot;edit constant custom field&quot;,&quot;manage report&quot;],&quot;project&quot;:[&quot;create project&quot;,&quot;create project task&quot;,&quot;create timesheet&quot;,&quot;create bug report&quot;,&quot;create milestone&quot;,&quot;create project stage&quot;,&quot;create project task stage&quot;,&quot;create project expense&quot;,&quot;create activity&quot;,&quot;create bug status&quot;,&quot;view project dashboard&quot;,&quot;view project&quot;,&quot;view project task&quot;,&quot;view timesheet&quot;,&quot;view bug report&quot;,&quot;view milestone&quot;,&quot;view project stage&quot;,&quot;view project task stage&quot;,&quot;view project expense&quot;,&quot;view activity&quot;,&quot;view bug status&quot;,&quot;delete project&quot;,&quot;delete project task&quot;,&quot;delete timesheet&quot;,&quot;delete bug report&quot;,&quot;delete milestone&quot;,&quot;delete project stage&quot;,&quot;delete project task stage&quot;,&quot;delete project expense&quot;,&quot;delete activity&quot;,&quot;delete bug status&quot;,&quot;manage project&quot;,&quot;edit project&quot;,&quot;manage project task&quot;,&quot;edit project task&quot;,&quot;manage timesheet&quot;,&quot;edit timesheet&quot;,&quot;manage bug report&quot;,&quot;edit bug report&quot;,&quot;manage milestone&quot;,&quot;edit milestone&quot;,&quot;manage project stage&quot;,&quot;edit project stage&quot;,&quot;manage project task stage&quot;,&quot;edit project task stage&quot;,&quot;manage project expense&quot;,&quot;edit project expense&quot;,&quot;manage activity&quot;,&quot;edit activity&quot;,&quot;manage bug status&quot;,&quot;edit bug status&quot;],&quot;pos&quot;:[&quot;create warehouse&quot;,&quot;create purchase&quot;,&quot;create quotation&quot;,&quot;create pos&quot;,&quot;create barcode&quot;,&quot;create product&quot;,&quot;create product category&quot;,&quot;create product unit&quot;,&quot;view pos dashboard&quot;,&quot;view warehouse&quot;,&quot;view purchase&quot;,&quot;view quotation&quot;,&quot;view pos&quot;,&quot;view product&quot;,&quot;view product category&quot;,&quot;view product unit&quot;,&quot;delete warehouse&quot;,&quot;delete purchase&quot;,&quot;delete quotation&quot;,&quot;delete pos&quot;,&quot;delete product&quot;,&quot;delete product category&quot;,&quot;delete product unit&quot;,&quot;manage warehouse&quot;,&quot;edit warehouse&quot;,&quot;manage purchase&quot;,&quot;edit purchase&quot;,&quot;manage quotation&quot;,&quot;edit quotation&quot;,&quot;manage pos&quot;,&quot;edit pos&quot;,&quot;manage product&quot;,&quot;edit product&quot;,&quot;manage product category&quot;,&quot;edit product category&quot;,&quot;manage product unit&quot;,&quot;edit product unit&quot;],&quot;support&quot;:[&quot;create support&quot;,&quot;view support dashboard&quot;,&quot;view support&quot;,&quot;delete support&quot;,&quot;manage support&quot;,&quot;edit support&quot;,&quot;reply support&quot;],&quot;user_management&quot;:[&quot;create user&quot;,&quot;create client&quot;,&quot;view user&quot;,&quot;view client&quot;,&quot;delete user&quot;,&quot;delete client&quot;,&quot;manage user&quot;,&quot;edit user&quot;,&quot;manage client&quot;,&quot;edit client&quot;],&quot;booking&quot;:[&quot;create booking&quot;,&quot;create appointment&quot;,&quot;create appointment booking&quot;,&quot;create calendar event&quot;,&quot;view booking dashboard&quot;,&quot;view booking&quot;,&quot;show booking&quot;,&quot;view appointment&quot;,&quot;show appointment&quot;,&quot;view appointment booking&quot;,&quot;show appointment booking&quot;,&quot;view calendar event&quot;,&quot;show calendar event&quot;,&quot;delete booking&quot;,&quot;delete appointment&quot;,&quot;delete appointment booking&quot;,&quot;delete calendar event&quot;,&quot;manage booking&quot;,&quot;edit booking&quot;,&quot;manage appointment&quot;,&quot;edit appointment&quot;,&quot;manage appointment booking&quot;,&quot;edit appointment booking&quot;,&quot;manage calendar event&quot;,&quot;edit calendar event&quot;],&quot;omx_flow&quot;:[&quot;access omx flow&quot;,&quot;whatsapp_flows&quot;,&quot;whatsapp_orders&quot;,&quot;campaigns&quot;,&quot;templates&quot;,&quot;chatbot&quot;],&quot;personal_tasks&quot;:[&quot;create personal task&quot;,&quot;create personal task comment&quot;,&quot;create personal task file&quot;,&quot;create personal task checklist&quot;,&quot;view personal task&quot;,&quot;delete personal task&quot;,&quot;delete personal task comment&quot;,&quot;delete personal task file&quot;,&quot;delete personal task checklist&quot;,&quot;manage personal task&quot;,&quot;edit personal task&quot;,&quot;edit personal task comment&quot;,&quot;edit personal task checklist&quot;,&quot;manage personal task time tracking&quot;],&quot;automatish&quot;:[&quot;access automatish&quot;]}", "2025-07-31 06:15:41", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 35}, {"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.4784582, "duration": 0.00433, "duration_str": "4.33ms", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:35", "source": "app/Observers/PricingPlanObserver.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=35", "ajax": false, "filename": "PricingPlanObserver.php", "line": "35"}, "connection": "radhe_same", "start_percent": 65.702, "width_percent": 4.955}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 76}, {"index": 16, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4915411, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "PricingPlanObserver.php:76", "source": "app/Observers/PricingPlanObserver.php:76", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FObservers%2FPricingPlanObserver.php&line=76", "ajax": false, "filename": "PricingPlanObserver.php", "line": "76"}, "connection": "radhe_same", "start_percent": 70.657, "width_percent": 0.938}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 91}, {"index": 18, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 42}, {"index": 19, "namespace": null, "name": "app/Observers/PricingPlanObserver.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Observers\\PricingPlanObserver.php", "line": 17}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 217}], "start": **********.497228, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 71.595, "width_percent": 1.202}, {"sql": "select * from `users` where `plan` = 10 and `type` = 'company'", "type": "query", "params": [], "bindings": ["10", "company"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 278}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.567254, "duration": 0.007, "duration_str": "7ms", "memory": 0, "memory_str": null, "filename": "PricingPlanController.php:278", "source": "app/Http/Controllers/PricingPlanController.php:278", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=278", "ajax": false, "filename": "PricingPlanController.php", "line": "278"}, "connection": "radhe_same", "start_percent": 72.797, "width_percent": 8.011}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 324}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.59862, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "PricingPlanController.php:324", "source": "app/Http/Controllers/PricingPlanController.php:324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=324", "ajax": false, "filename": "PricingPlanController.php", "line": "324"}, "connection": "radhe_same", "start_percent": 80.808, "width_percent": 2.209}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 339}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6161141, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 83.017, "width_percent": 2.38}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 324}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.695627, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "PricingPlanController.php:324", "source": "app/Http/Controllers/PricingPlanController.php:324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=324", "ajax": false, "filename": "PricingPlanController.php", "line": "324"}, "connection": "radhe_same", "start_percent": 85.397, "width_percent": 2.277}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 339}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.708409, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 87.675, "width_percent": 2.232}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 324}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.748035, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "PricingPlanController.php:324", "source": "app/Http/Controllers/PricingPlanController.php:324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=324", "ajax": false, "filename": "PricingPlanController.php", "line": "324"}, "connection": "radhe_same", "start_percent": 89.906, "width_percent": 1.808}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 339}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7646391, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 91.714, "width_percent": 2.529}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 324}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.820564, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "PricingPlanController.php:324", "source": "app/Http/Controllers/PricingPlanController.php:324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=324", "ajax": false, "filename": "PricingPlanController.php", "line": "324"}, "connection": "radhe_same", "start_percent": 94.244, "width_percent": 1.431}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 339}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8321512, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 95.674, "width_percent": 1.522}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 324}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.880327, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "PricingPlanController.php:324", "source": "app/Http/Controllers/PricingPlanController.php:324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FPricingPlanController.php&line=324", "ajax": false, "filename": "PricingPlanController.php", "line": "324"}, "connection": "radhe_same", "start_percent": 97.196, "width_percent": 1.213}, {"sql": "select * from `users` where `id` = 7 and `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["7", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ModuleIntegrationController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ModuleIntegrationController.php", "line": 253}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 339}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 290}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/PricingPlanController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\PricingPlanController.php", "line": 220}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.889013, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "ModuleIntegrationController.php:253", "source": "app/Http/Controllers/ModuleIntegrationController.php:253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FModuleIntegrationController.php&line=253", "ajax": false, "filename": "ModuleIntegrationController.php", "line": "253"}, "connection": "radhe_same", "start_percent": 98.409, "width_percent": 1.591}]}, "models": {"data": {"App\\Models\\User": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ModuleIntegration": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FModuleIntegration.php&line=1", "ajax": false, "filename": "ModuleIntegration.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}}, "count": 22, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pricing-plans/10/edit\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7", "success": "Pricing plan updated successfully.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pricing-plans/10", "status_code": "<pre class=sf-dump id=sf-dump-683094042 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-683094042\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-389761411 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-389761411\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2069540949 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">My Business Flow</span>\"\n  \"<span class=sf-dump-key>plan_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">subaccount</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"8 characters\">80000.00</span>\"\n  \"<span class=sf-dump-key>duration</span>\" => \"<span class=sf-dump-str title=\"6 characters\">yearly</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  \"<span class=sf-dump-key>description</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"1429 characters\">Our Business Flow Plan is designed to streamline and automate critical operations across departments, thereby enhancing productivity, accountability, and growth. This plan focuses on aligning people, processes, and technology through a structured workflow system that minimises manual effort and maximises efficiency.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Key Features:<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Centralised Workflow Management: Coordinate tasks and approvals across teams with clearly defined stages and responsibilities.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Automation Integration: Automate repetitive tasks, notifications, and follow-ups using customizable logic flows.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Real-time Monitoring: Gain visibility into each step of your business processes with dashboards and activity logs.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Data-Driven Decisions: Collect actionable data at each stage to identify bottlenecks and optimise performance.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Collaboration Tools: Enhance team communication and ensure smooth transitions between workflow stages.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Customizable Flow Templates: Build flows tailored to your business model&#8212;sales, support, HR, finance, or operations.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Benefits:<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Reduce operational overhead<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Improve turnaround time on key tasks.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Enhance team accountability<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Increase scalability with repeatable processes.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">Deliver consistent experiences for customers and internal teams.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"1429 characters\">This Business Flow Plan is ideal for growing teams that want to modernise their operations and adopt a more structured, data-driven approach to working.</span>\n    \"\"\"\n  \"<span class=sf-dump-key>max_users</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>max_customers</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>max_vendors</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>max_clients</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  \"<span class=sf-dump-key>storage_limit</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n  \"<span class=sf-dump-key>sort_order</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>modules</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>crm</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>hrm</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>account</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>project</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>pos</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>support</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>user_management</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>booking</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>omx_flow</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>personal_tasks</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>automatish</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>permissions</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>crm</span>\" => <span class=sf-dump-note>array:41</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">create lead</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">create deal</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"19 characters\">create form builder</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"15 characters\">create contract</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"15 characters\">create pipeline</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">create stage</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"13 characters\">create source</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"12 characters\">create label</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"18 characters\">view crm dashboard</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"9 characters\">view lead</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"9 characters\">view deal</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"17 characters\">view form builder</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"13 characters\">view contract</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"13 characters\">view pipeline</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">view stage</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"11 characters\">view source</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"10 characters\">view label</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">delete lead</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"11 characters\">delete deal</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"19 characters\">delete form builder</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"15 characters\">delete contract</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"15 characters\">delete pipeline</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"12 characters\">delete stage</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"13 characters\">delete source</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">delete label</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"11 characters\">manage lead</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"9 characters\">edit lead</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">manage deal</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"9 characters\">edit deal</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"19 characters\">manage form builder</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"17 characters\">edit form builder</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"15 characters\">manage contract</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"13 characters\">edit contract</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"15 characters\">manage pipeline</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"13 characters\">edit pipeline</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"12 characters\">manage stage</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"10 characters\">edit stage</span>\"\n      <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"13 characters\">manage source</span>\"\n      <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"11 characters\">edit source</span>\"\n      <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"12 characters\">manage label</span>\"\n      <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"10 characters\">edit label</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>hrm</span>\" => <span class=sf-dump-note>array:56</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">create employee</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">create set salary</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">create pay slip</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"12 characters\">create leave</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"17 characters\">create attendance</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"15 characters\">create training</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"12 characters\">create award</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"13 characters\">create branch</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"17 characters\">create department</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"18 characters\">create designation</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"20 characters\">create document type</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"18 characters\">view hrm dashboard</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"13 characters\">view employee</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"15 characters\">view set salary</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"13 characters\">view pay slip</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"10 characters\">view leave</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"15 characters\">view attendance</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">view training</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"10 characters\">view award</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"11 characters\">view branch</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"15 characters\">view department</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"16 characters\">view designation</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"18 characters\">view document type</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"15 characters\">delete employee</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"17 characters\">delete set salary</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"15 characters\">delete pay slip</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"12 characters\">delete leave</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"17 characters\">delete attendance</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"15 characters\">delete training</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">delete award</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"13 characters\">delete branch</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"17 characters\">delete department</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"18 characters\">delete designation</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"20 characters\">delete document type</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"13 characters\">edit employee</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n      <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"15 characters\">edit set salary</span>\"\n      <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"15 characters\">manage pay slip</span>\"\n      <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"13 characters\">edit pay slip</span>\"\n      <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n      <span class=sf-dump-index>41</span> => \"<span class=sf-dump-str title=\"10 characters\">edit leave</span>\"\n      <span class=sf-dump-index>42</span> => \"<span class=sf-dump-str title=\"17 characters\">manage attendance</span>\"\n      <span class=sf-dump-index>43</span> => \"<span class=sf-dump-str title=\"15 characters\">edit attendance</span>\"\n      <span class=sf-dump-index>44</span> => \"<span class=sf-dump-str title=\"15 characters\">manage training</span>\"\n      <span class=sf-dump-index>45</span> => \"<span class=sf-dump-str title=\"13 characters\">edit training</span>\"\n      <span class=sf-dump-index>46</span> => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n      <span class=sf-dump-index>47</span> => \"<span class=sf-dump-str title=\"10 characters\">edit award</span>\"\n      <span class=sf-dump-index>48</span> => \"<span class=sf-dump-str title=\"13 characters\">manage branch</span>\"\n      <span class=sf-dump-index>49</span> => \"<span class=sf-dump-str title=\"11 characters\">edit branch</span>\"\n      <span class=sf-dump-index>50</span> => \"<span class=sf-dump-str title=\"17 characters\">manage department</span>\"\n      <span class=sf-dump-index>51</span> => \"<span class=sf-dump-str title=\"15 characters\">edit department</span>\"\n      <span class=sf-dump-index>52</span> => \"<span class=sf-dump-str title=\"18 characters\">manage designation</span>\"\n      <span class=sf-dump-index>53</span> => \"<span class=sf-dump-str title=\"16 characters\">edit designation</span>\"\n      <span class=sf-dump-index>54</span> => \"<span class=sf-dump-str title=\"20 characters\">manage document type</span>\"\n      <span class=sf-dump-index>55</span> => \"<span class=sf-dump-str title=\"18 characters\">edit document type</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>account</span>\" => <span class=sf-dump-note>array:88</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">create customer</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">create vender</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"14 characters\">create invoice</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">create bill</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"14 characters\">create revenue</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">create payment</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"15 characters\">create proposal</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"11 characters\">create goal</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"18 characters\">create credit note</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"17 characters\">create debit note</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"19 characters\">create bank account</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"20 characters\">create bank transfer</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"18 characters\">create transaction</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"23 characters\">create chart of account</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"20 characters\">create journal entry</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"13 characters\">create assets</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"28 characters\">create constant custom field</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"22 characters\">view account dashboard</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"13 characters\">view customer</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"11 characters\">view vender</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"12 characters\">view invoice</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"9 characters\">view bill</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"12 characters\">view revenue</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"12 characters\">view payment</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"13 characters\">view proposal</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"9 characters\">view goal</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"16 characters\">view credit note</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"15 characters\">view debit note</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"17 characters\">view bank account</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"18 characters\">view bank transfer</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"16 characters\">view transaction</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"21 characters\">view chart of account</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"18 characters\">view journal entry</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"11 characters\">view assets</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"26 characters\">view constant custom field</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"11 characters\">view report</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"15 characters\">delete customer</span>\"\n      <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"13 characters\">delete vender</span>\"\n      <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"14 characters\">delete invoice</span>\"\n      <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"11 characters\">delete bill</span>\"\n      <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"14 characters\">delete revenue</span>\"\n      <span class=sf-dump-index>41</span> => \"<span class=sf-dump-str title=\"14 characters\">delete payment</span>\"\n      <span class=sf-dump-index>42</span> => \"<span class=sf-dump-str title=\"15 characters\">delete proposal</span>\"\n      <span class=sf-dump-index>43</span> => \"<span class=sf-dump-str title=\"11 characters\">delete goal</span>\"\n      <span class=sf-dump-index>44</span> => \"<span class=sf-dump-str title=\"18 characters\">delete credit note</span>\"\n      <span class=sf-dump-index>45</span> => \"<span class=sf-dump-str title=\"17 characters\">delete debit note</span>\"\n      <span class=sf-dump-index>46</span> => \"<span class=sf-dump-str title=\"19 characters\">delete bank account</span>\"\n      <span class=sf-dump-index>47</span> => \"<span class=sf-dump-str title=\"20 characters\">delete bank transfer</span>\"\n      <span class=sf-dump-index>48</span> => \"<span class=sf-dump-str title=\"18 characters\">delete transaction</span>\"\n      <span class=sf-dump-index>49</span> => \"<span class=sf-dump-str title=\"23 characters\">delete chart of account</span>\"\n      <span class=sf-dump-index>50</span> => \"<span class=sf-dump-str title=\"20 characters\">delete journal entry</span>\"\n      <span class=sf-dump-index>51</span> => \"<span class=sf-dump-str title=\"13 characters\">delete assets</span>\"\n      <span class=sf-dump-index>52</span> => \"<span class=sf-dump-str title=\"28 characters\">delete constant custom field</span>\"\n      <span class=sf-dump-index>53</span> => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n      <span class=sf-dump-index>54</span> => \"<span class=sf-dump-str title=\"13 characters\">edit customer</span>\"\n      <span class=sf-dump-index>55</span> => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n      <span class=sf-dump-index>56</span> => \"<span class=sf-dump-str title=\"11 characters\">edit vender</span>\"\n      <span class=sf-dump-index>57</span> => \"<span class=sf-dump-str title=\"14 characters\">manage invoice</span>\"\n      <span class=sf-dump-index>58</span> => \"<span class=sf-dump-str title=\"12 characters\">edit invoice</span>\"\n      <span class=sf-dump-index>59</span> => \"<span class=sf-dump-str title=\"11 characters\">manage bill</span>\"\n      <span class=sf-dump-index>60</span> => \"<span class=sf-dump-str title=\"9 characters\">edit bill</span>\"\n      <span class=sf-dump-index>61</span> => \"<span class=sf-dump-str title=\"14 characters\">manage revenue</span>\"\n      <span class=sf-dump-index>62</span> => \"<span class=sf-dump-str title=\"12 characters\">edit revenue</span>\"\n      <span class=sf-dump-index>63</span> => \"<span class=sf-dump-str title=\"14 characters\">manage payment</span>\"\n      <span class=sf-dump-index>64</span> => \"<span class=sf-dump-str title=\"12 characters\">edit payment</span>\"\n      <span class=sf-dump-index>65</span> => \"<span class=sf-dump-str title=\"15 characters\">manage proposal</span>\"\n      <span class=sf-dump-index>66</span> => \"<span class=sf-dump-str title=\"13 characters\">edit proposal</span>\"\n      <span class=sf-dump-index>67</span> => \"<span class=sf-dump-str title=\"11 characters\">manage goal</span>\"\n      <span class=sf-dump-index>68</span> => \"<span class=sf-dump-str title=\"9 characters\">edit goal</span>\"\n      <span class=sf-dump-index>69</span> => \"<span class=sf-dump-str title=\"18 characters\">manage credit note</span>\"\n      <span class=sf-dump-index>70</span> => \"<span class=sf-dump-str title=\"16 characters\">edit credit note</span>\"\n      <span class=sf-dump-index>71</span> => \"<span class=sf-dump-str title=\"17 characters\">manage debit note</span>\"\n      <span class=sf-dump-index>72</span> => \"<span class=sf-dump-str title=\"15 characters\">edit debit note</span>\"\n      <span class=sf-dump-index>73</span> => \"<span class=sf-dump-str title=\"19 characters\">manage bank account</span>\"\n      <span class=sf-dump-index>74</span> => \"<span class=sf-dump-str title=\"17 characters\">edit bank account</span>\"\n      <span class=sf-dump-index>75</span> => \"<span class=sf-dump-str title=\"20 characters\">manage bank transfer</span>\"\n      <span class=sf-dump-index>76</span> => \"<span class=sf-dump-str title=\"18 characters\">edit bank transfer</span>\"\n      <span class=sf-dump-index>77</span> => \"<span class=sf-dump-str title=\"18 characters\">manage transaction</span>\"\n      <span class=sf-dump-index>78</span> => \"<span class=sf-dump-str title=\"16 characters\">edit transaction</span>\"\n      <span class=sf-dump-index>79</span> => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n      <span class=sf-dump-index>80</span> => \"<span class=sf-dump-str title=\"21 characters\">edit chart of account</span>\"\n      <span class=sf-dump-index>81</span> => \"<span class=sf-dump-str title=\"20 characters\">manage journal entry</span>\"\n      <span class=sf-dump-index>82</span> => \"<span class=sf-dump-str title=\"18 characters\">edit journal entry</span>\"\n      <span class=sf-dump-index>83</span> => \"<span class=sf-dump-str title=\"13 characters\">manage assets</span>\"\n      <span class=sf-dump-index>84</span> => \"<span class=sf-dump-str title=\"11 characters\">edit assets</span>\"\n      <span class=sf-dump-index>85</span> => \"<span class=sf-dump-str title=\"28 characters\">manage constant custom field</span>\"\n      <span class=sf-dump-index>86</span> => \"<span class=sf-dump-str title=\"26 characters\">edit constant custom field</span>\"\n      <span class=sf-dump-index>87</span> => \"<span class=sf-dump-str title=\"13 characters\">manage report</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>project</span>\" => <span class=sf-dump-note>array:51</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">create project</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"19 characters\">create project task</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">create timesheet</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"17 characters\">create bug report</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"16 characters\">create milestone</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"20 characters\">create project stage</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"25 characters\">create project task stage</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"22 characters\">create project expense</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"15 characters\">create activity</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"17 characters\">create bug status</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"22 characters\">view project dashboard</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"12 characters\">view project</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">view project task</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"14 characters\">view timesheet</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"15 characters\">view bug report</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">view milestone</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"18 characters\">view project stage</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"23 characters\">view project task stage</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"20 characters\">view project expense</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"13 characters\">view activity</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"15 characters\">view bug status</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"14 characters\">delete project</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"19 characters\">delete project task</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"16 characters\">delete timesheet</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"17 characters\">delete bug report</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"16 characters\">delete milestone</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"20 characters\">delete project stage</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"25 characters\">delete project task stage</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"22 characters\">delete project expense</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"15 characters\">delete activity</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"17 characters\">delete bug status</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">manage project</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"12 characters\">edit project</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"19 characters\">manage project task</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"17 characters\">edit project task</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"16 characters\">manage timesheet</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"14 characters\">edit timesheet</span>\"\n      <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"17 characters\">manage bug report</span>\"\n      <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"15 characters\">edit bug report</span>\"\n      <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"16 characters\">manage milestone</span>\"\n      <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"14 characters\">edit milestone</span>\"\n      <span class=sf-dump-index>41</span> => \"<span class=sf-dump-str title=\"20 characters\">manage project stage</span>\"\n      <span class=sf-dump-index>42</span> => \"<span class=sf-dump-str title=\"18 characters\">edit project stage</span>\"\n      <span class=sf-dump-index>43</span> => \"<span class=sf-dump-str title=\"25 characters\">manage project task stage</span>\"\n      <span class=sf-dump-index>44</span> => \"<span class=sf-dump-str title=\"23 characters\">edit project task stage</span>\"\n      <span class=sf-dump-index>45</span> => \"<span class=sf-dump-str title=\"22 characters\">manage project expense</span>\"\n      <span class=sf-dump-index>46</span> => \"<span class=sf-dump-str title=\"20 characters\">edit project expense</span>\"\n      <span class=sf-dump-index>47</span> => \"<span class=sf-dump-str title=\"15 characters\">manage activity</span>\"\n      <span class=sf-dump-index>48</span> => \"<span class=sf-dump-str title=\"13 characters\">edit activity</span>\"\n      <span class=sf-dump-index>49</span> => \"<span class=sf-dump-str title=\"17 characters\">manage bug status</span>\"\n      <span class=sf-dump-index>50</span> => \"<span class=sf-dump-str title=\"15 characters\">edit bug status</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:37</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">create warehouse</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">create quotation</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">create pos</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"14 characters\">create barcode</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"14 characters\">create product</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"23 characters\">create product category</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"19 characters\">create product unit</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"18 characters\">view pos dashboard</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"14 characters\">view warehouse</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"13 characters\">view purchase</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"14 characters\">view quotation</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"8 characters\">view pos</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"12 characters\">view product</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"21 characters\">view product category</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"17 characters\">view product unit</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"16 characters\">delete warehouse</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"15 characters\">delete purchase</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"16 characters\">delete quotation</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"10 characters\">delete pos</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"14 characters\">delete product</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"23 characters\">delete product category</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"19 characters\">delete product unit</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"14 characters\">edit warehouse</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"15 characters\">manage purchase</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">edit purchase</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"16 characters\">manage quotation</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"14 characters\">edit quotation</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"8 characters\">edit pos</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">manage product</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"12 characters\">edit product</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"23 characters\">manage product category</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"21 characters\">edit product category</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"19 characters\">manage product unit</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"17 characters\">edit product unit</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>support</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">create support</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"22 characters\">view support dashboard</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">view support</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"14 characters\">delete support</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"14 characters\">manage support</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">edit support</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"13 characters\">reply support</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>user_management</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">create user</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">create client</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">view user</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">view client</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"11 characters\">delete user</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">delete client</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">edit user</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"11 characters\">edit client</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>booking</span>\" => <span class=sf-dump-note>array:25</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">create booking</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"18 characters\">create appointment</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"26 characters\">create appointment booking</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"21 characters\">create calendar event</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"22 characters\">view booking dashboard</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">view booking</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"12 characters\">show booking</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"16 characters\">view appointment</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">show appointment</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"24 characters\">view appointment booking</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"24 characters\">show appointment booking</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"19 characters\">view calendar event</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"19 characters\">show calendar event</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"14 characters\">delete booking</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"18 characters\">delete appointment</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"26 characters\">delete appointment booking</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"21 characters\">delete calendar event</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"14 characters\">manage booking</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"12 characters\">edit booking</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"18 characters\">manage appointment</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"16 characters\">edit appointment</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"26 characters\">manage appointment booking</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"24 characters\">edit appointment booking</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"21 characters\">manage calendar event</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"19 characters\">edit calendar event</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>omx_flow</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">access omx flow</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">whatsapp_flows</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">whatsapp_orders</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">campaigns</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"9 characters\">templates</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">chatbot</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>personal_tasks</span>\" => <span class=sf-dump-note>array:14</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">create personal task</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"28 characters\">create personal task comment</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"25 characters\">create personal task file</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"30 characters\">create personal task checklist</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"18 characters\">view personal task</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"20 characters\">delete personal task</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"28 characters\">delete personal task comment</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"25 characters\">delete personal task file</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"30 characters\">delete personal task checklist</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"20 characters\">manage personal task</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"18 characters\">edit personal task</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"26 characters\">edit personal task comment</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"28 characters\">edit personal task checklist</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"34 characters\">manage personal task time tracking</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>automatish</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">access automatish</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069540949\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">17840</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pricing-plans/10/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Im9xT1BBOElDL0pZcTJvZ2ZBOWhoaFE9PSIsInZhbHVlIjoiS0JtS25XNFF3UlQyVFNoVUVrd2xjbDd1cmU5ajVpaVY1THM4ekJ5UVU2eGlnQkp3OGdvSVNhaGhjbnFGZG9qVFlSVklGTTB1b1BnelVZYkRtV2ZsTm1sRDFGOU45QTh2OUtIcWRmeUNYb0Y5aSsxS3ROT3diMkpITHByT3N0UUFBbUtZZGlTTURKMTFaWktrRU9zN2pBUFM1UVJFeS9DRDYxUUpFaENZMGg3TFZFK3pQbFNwUk9MTnpZNlFjMm1Yc0ppZnIremZpR3Q1Vkk3VWJUMTgrQkltOVh1SGhUZGx3cVRlWHVQbkJoNGRlVmpKd1U4Q0N6SFNrR0NrRmtqOTdCaG9ybW9jQXBhN1lPSkg5VXJMY2ErcXE3S283RktZdFJPTUluZ3ZCTW85Y0hvQ3ZaZFVqZ0ducDJYQ1hDc2crVE9ZZzhvRjdpWXNuS2xmck45UUNDOFBaTzM1ZjlTTU92eHNmbzZjM2hLOU8zTTIvRURmVktpMkVjeVBKSnZEQ0VLdnZSRVNnRmJXQmRubzRJL3A3RTY0dDU4ZHRYajNwV0MrSW9sYWhUcHR1UnBrM1dXNTFnL3F5dVNCTThCNzBZeGhDeFRkV3R2Rm14WWxialNkMWU2YllSR1o3dWJDVi8wUHpHamdqL1ZoMm05TlNJV3RqMW5Tajc2R1RWSGgiLCJtYWMiOiIzMDRlNTVhMDZhMWEzZGQ2Y2M2MGJkYzRhMjQwMzFkNmM2ODY4ZDk1NjBiNGM3MjQ4YzEwMmM2ZDMyNWRmODNmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImI4ckloSGVNem1wUlhlNEJ6YmRLbFE9PSIsInZhbHVlIjoiWks3SVpZZk4rbjNlNlBmK0dDM293TmJwOWZvd2RBSlkxS3VTMVliK0xBdmhCSUsrenUwakZyb1ZDT1QxT3JQVWtSRmZQTTNDdk1HalRvOUlxV2NDeEFVemdmRjQxWCt2eEhmSU9DT2tLUmM1anp5K3ZWSHhDOWs0Q0g0YmxNcU4xbitybE01b3BQM1BsakZvTWZpaEY0enhHMlI3VEtCVHhzUVdOU2ZKNXZDMzFTMGFtazI3R3BDTlJtNkVHdkZEWThXdmd2bEFvbnozNEt6dE8vY29iUTg4blZDZHJYcVl1K2M4alltV0hIUy9LOXBFT0NLLzZRY1lmM3JZMXZTbFIza1NaRGdYRzlDM3JvVXNxWk9pNHFCbnVzOURBa1lvUTdtc1ZGb0RKZmFrZjhra1ZyRUlzNVJkUCtieHgzUmJ2VTdmTDdlbWg4bitJZTlGVkNZWUdMUDZUTHVhMC83Tnp6ZGtPeW5zVU9qS1hyakUxcklsRkErbWpxcTcxUjZVZU1QYmJ0V2pmY3cvaENyZm9nNVpzZ3VMYkZ2dWVtSmsvUU5rRWZMUEpXNEw4Wk9DbFJOSnh0TWNOaDVXNU1nd2lnMVZ6dlAzQWVPL2NTZHc1RU81dWRHVGlDUkxGMUh3SjdNWXd5THRzd2czamJCNU96Nm9OOVRYOUJhM1BxVjgiLCJtYWMiOiI1MTliN2JkNTQyMGE3MWE1Mjc4YzQ4Zjg2N2JlNDM0Y2IzYzM5NmVmNDBhZGI2YjkwMmE1ZDhjMzI1NDgwN2M2IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IjVVMW5aNlhOUjNaVFgvZ001L1BQZ2c9PSIsInZhbHVlIjoiL3FCUzZkRTFtUVovL1VSd1JNcUFndTJIZUx5aHJaTnBramtnK21kR3NRbERtanhibmdlME54MzNhdUJqV2xwaFJaeUgyNW9WR3lKOGhYdWp5TkdVYzZwbDQ3emI4MG1qSzZ4eDNoeko5M0RXRERBeXBhZkZ0Q2hPZE9RSFpBVXFhb3FBV1JQMEduUGVXcXA4VTJSN0Z0N21udGF3cExVZWN2WWk1Nnd5dzZFSll4cFI2dXdFblpvZVFsMUdkdFhia2pJYzI0Ulk3bXZ6SGRQU0tBelgwV0o1bGhNYXduN2hyWGI3U3lQRVZabU5ZV2ZEYjhGRU1Ga3Vta0w5NlFzeVlBREdUTC9CMHdUaGhQSDhFeTFXSjFPSXB3OWhBVStTTXFFc2dBdlZZS0NjbXJaNGJRWnp6NHQ3M2xFT2p5azNmOVdpb1JhK0RxNGk3OFNQL0lCU3RjRFNpeGlqaGh0Q1puajBoeWxWaWFzdVFmNndjSVNlcWxVOUd0bGFtMldQWnB5RWk1MHFvbThlRHFoY3A4MWQ3U2UwNlNwQ21SQUFJMDNxSmFpRTBoVHhxdzEvdzZpMFZsdEg3MmZlR2t3TlZrWUxoZSt2bTMzRm9vWW9NUVk0SlpIRnVud1Z5bXdOSklxZWZubGFoYTNsRXBHVTVoWEJwYW9DLzUzL09pSjYiLCJtYWMiOiJkM2I3NWI3NmQ2OTAxZjc4M2YzNzdjY2MxM2YwMTM4YjU0Mjg5YjRiYzEyZmZlZjY5MjZkMjFmNDVmOTYwMzk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1367259570 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5LJND5bUclZnLzzClCMEaxKzZ5Yt8AAmGjprz0I</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sUbnPperdA1I3PaRxBpMGV4YsR5AVGq0ERh2LBdJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1367259570\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1197552581 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:15:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/pricing-plans</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImUvSCttZWhJKzJoaXhVL0xzZGE1M2c9PSIsInZhbHVlIjoidy9teFl6dk04RzZNTlBTUHdaWXhwSmtzMUM2dmVuejFtSStSZ09TL3VzZDNKMGllNmQzRWl1eWUzL0o4YjBGL1VHajJhWVNKalZpRllodUxOZE96d2JJSlBMaTgxR2wraktqZ0cvUENqT242WllpTWFKeXp3ZjB0clltMytpNmVta3o3NVl3djVJQ0FzTFpkK0NTbjFDb1ZkOVA3L1lqYVJpUVFST2duQ1IzMFc1VkFWMW5HWlRlN21HcG9lMjd1RlF2RjJYTlZMaTRLejFsREkwZ01Ha1BwWFYzaUpxVVVpcWlJVEIzcklPUTVHaGVEMFVDZFVWVUw2RFlmUFZBZXNGL0xzcCtWVjdVRWgxUkV4WFN1U1JMRmpQNWp5MU85Ynh3K3ZiTFZMeDhnWHhaTmJYVk5qSExMblZWVGw4UEpYNHBxckpwd0NMbTRyK3NjNU5yaUxldlh0R2pEZUhRSXYvSHF6SEhrYkFKU0U5eXIwM1BQdWltZWV3MWo3NFZicDgrUWd6UEQwc2Z2NjFBMSs2TVpwRUgvMmRob2Zmc3JmcGtEUWp2R3pqb0oxRmJsOEUyMGJnREs1dk1oclBjd1doTzNDaG9pZ0lCNUVpTFBLRHhocG1vaEtCelZyd2hITVVzNjNVMEhOaW1OSndTSTRBZFJ5R29ZcG84UlY5T3giLCJtYWMiOiIyZTQzNjQ1YmRkZmQ2ZTczOWEyMzI1ODMyMjlmZjcxNWFhZTdlMDAyN2JkM2E0Y2Q1MmNiYTUxMmNiODcxNDQzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:15:53 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6ImF2MjdjZk8wUUhiQXV5NFpGbkd6L3c9PSIsInZhbHVlIjoiSWo2YUxvTnNMRHJ0WXNIeHVCcUJMUWlqV3Q1dVNRbFZqQmc4dWtraDg1Z09rc1BQWEY3QWlCeTdPNEg0dEFMRnh0RGtRQkVsYVBPanVGdjdxK05XVnhUQklISVlXVWpmYitIbXRUdndWT25sYXorZlIxSjlLaXJZZXM4VkY5YUVpOXNEUGg2ZVROTU1NUkxhTWNQN2dNYXpNLzRqcm9mNHJoZG81U1kvSVlNNGlSWnlnYWhOb0Q0Z0g3U09xZEcyS1ZPVTlBdnFDQVFaS1pNeExhQTBxSWhPM0VaaVdRdm9ZYmdTVld0SHNLRjRyVERxakcxOUYvdHloR0VZU05aZDlsUXNVR29HWU1XaUhGRVV0Z3gxSEhPRElUdnFleTJGcFF1eGRML0dONkhJVklibGpoV2p6STB1OWV1QzBNYnBqdjAzZldDeFRyK3NoSFFQRmE4TEtjZ09OQ3dKTHJ2THk4K3NhTDJ1THd0cVpCeHlSZW1oNTU3M042QVpjNUZkQlZNOHozaGN3b0plZzJzellmQkNyVE54TnlVVnZwZ1ZPZG1jdkpibFhjZ0VWckpQUE1lUVltMS85Z2VJaWVkbWpqU0lNWUg4a0xZZzRuTG8yVDNHalRkblZwN25maWEvaXdZeFpDZnBhclk5VXdNM21PZHFycEVuVnVuQVRpRlQiLCJtYWMiOiJhM2YzODUzMDUxNWIyNWEwNGY3MDI5ZTZiMTJmZWYzNjFmOTZhMTZmMDFkMzM3ZTk2YmFlMDUzM2EyZDZkOWNmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:15:53 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImUvSCttZWhJKzJoaXhVL0xzZGE1M2c9PSIsInZhbHVlIjoidy9teFl6dk04RzZNTlBTUHdaWXhwSmtzMUM2dmVuejFtSStSZ09TL3VzZDNKMGllNmQzRWl1eWUzL0o4YjBGL1VHajJhWVNKalZpRllodUxOZE96d2JJSlBMaTgxR2wraktqZ0cvUENqT242WllpTWFKeXp3ZjB0clltMytpNmVta3o3NVl3djVJQ0FzTFpkK0NTbjFDb1ZkOVA3L1lqYVJpUVFST2duQ1IzMFc1VkFWMW5HWlRlN21HcG9lMjd1RlF2RjJYTlZMaTRLejFsREkwZ01Ha1BwWFYzaUpxVVVpcWlJVEIzcklPUTVHaGVEMFVDZFVWVUw2RFlmUFZBZXNGL0xzcCtWVjdVRWgxUkV4WFN1U1JMRmpQNWp5MU85Ynh3K3ZiTFZMeDhnWHhaTmJYVk5qSExMblZWVGw4UEpYNHBxckpwd0NMbTRyK3NjNU5yaUxldlh0R2pEZUhRSXYvSHF6SEhrYkFKU0U5eXIwM1BQdWltZWV3MWo3NFZicDgrUWd6UEQwc2Z2NjFBMSs2TVpwRUgvMmRob2Zmc3JmcGtEUWp2R3pqb0oxRmJsOEUyMGJnREs1dk1oclBjd1doTzNDaG9pZ0lCNUVpTFBLRHhocG1vaEtCelZyd2hITVVzNjNVMEhOaW1OSndTSTRBZFJ5R29ZcG84UlY5T3giLCJtYWMiOiIyZTQzNjQ1YmRkZmQ2ZTczOWEyMzI1ODMyMjlmZjcxNWFhZTdlMDAyN2JkM2E0Y2Q1MmNiYTUxMmNiODcxNDQzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:15:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6ImF2MjdjZk8wUUhiQXV5NFpGbkd6L3c9PSIsInZhbHVlIjoiSWo2YUxvTnNMRHJ0WXNIeHVCcUJMUWlqV3Q1dVNRbFZqQmc4dWtraDg1Z09rc1BQWEY3QWlCeTdPNEg0dEFMRnh0RGtRQkVsYVBPanVGdjdxK05XVnhUQklISVlXVWpmYitIbXRUdndWT25sYXorZlIxSjlLaXJZZXM4VkY5YUVpOXNEUGg2ZVROTU1NUkxhTWNQN2dNYXpNLzRqcm9mNHJoZG81U1kvSVlNNGlSWnlnYWhOb0Q0Z0g3U09xZEcyS1ZPVTlBdnFDQVFaS1pNeExhQTBxSWhPM0VaaVdRdm9ZYmdTVld0SHNLRjRyVERxakcxOUYvdHloR0VZU05aZDlsUXNVR29HWU1XaUhGRVV0Z3gxSEhPRElUdnFleTJGcFF1eGRML0dONkhJVklibGpoV2p6STB1OWV1QzBNYnBqdjAzZldDeFRyK3NoSFFQRmE4TEtjZ09OQ3dKTHJ2THk4K3NhTDJ1THd0cVpCeHlSZW1oNTU3M042QVpjNUZkQlZNOHozaGN3b0plZzJzellmQkNyVE54TnlVVnZwZ1ZPZG1jdkpibFhjZ0VWckpQUE1lUVltMS85Z2VJaWVkbWpqU0lNWUg4a0xZZzRuTG8yVDNHalRkblZwN25maWEvaXdZeFpDZnBhclk5VXdNM21PZHFycEVuVnVuQVRpRlQiLCJtYWMiOiJhM2YzODUzMDUxNWIyNWEwNGY3MDI5ZTZiMTJmZWYzNjFmOTZhMTZmMDFkMzM3ZTk2YmFlMDUzM2EyZDZkOWNmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:15:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1197552581\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1216731655 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pricing-plans/10/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Pricing plan updated successfully.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1216731655\", {\"maxDepth\":0})</script>\n"}}