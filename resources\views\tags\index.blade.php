@extends('layouts.admin')

@section('page-title')
    {{ __('Tags Management') }}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Tags Management') }}</li>
@endsection

@section('action-btn')
    @can('create label')
        <div class="d-flex flex-wrap align-items-center gap-2">
            <a href="#" data-size="md" data-url="{{ route('tags.create') }}" data-ajax-popup="true" data-bs-toggle="tooltip"
                title="{{ __('Create Tag') }}" class="btn btn-sm btn-primary">
                <i class="ti ti-plus"></i>
            </a>
        </div>
    @endcan
@endsection

@section('content')
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header p-3">
                    <h5 class="mb-1">{{ __('Tags Management') }}</h5>
                    <small class="text-muted">{{ __('Manage all tags used in the system for leads and deals.') }}</small>
                </div>
                <div class="card-body p-3">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>{{ __('Tag Name') }}</th>
                                    <th>{{ __('Created At') }}</th>
                                    <th>{{ __('Updated At') }}</th>
                                    <th class="text-end">{{ __('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($tags as $tag)
                                    <tr>
                                        <td>
                                            <span class="badge bg-primary">{{ $tag->name }}</span>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $tag->created_at->format('M d, Y H:i') }}</small>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $tag->updated_at->format('M d, Y H:i') }}</small>
                                        </td>
                                        <td class="text-end">
                                            <div class="d-flex gap-2 justify-content-end">
                                                @can('edit label')
                                                    <a href="#" class="btn btn-sm btn-outline-primary"
                                                        data-url="{{ URL::to('tags/' . $tag->id . '/edit') }}"
                                                        data-ajax-popup="true" data-size="md" data-bs-toggle="tooltip"
                                                        title="{{ __('Edit') }}"
                                                        data-title="{{ __('Edit Tag') }}">
                                                        <i class="ti ti-pencil"></i>
                                                    </a>
                                                @endcan
                                                @can('delete label')
                                                    {!! Form::open(['method' => 'DELETE', 'route' => ['tags.destroy', $tag->id], 'class' => 'd-inline']) !!}
                                                    <a href="#" class="btn btn-sm btn-outline-danger bs-pass-para"
                                                        data-bs-toggle="tooltip" title="{{ __('Delete') }}"
                                                        data-confirm="{{ __('Are You Sure?') }}"
                                                        data-text="{{ __('This action can not be undone. Do you want to continue?') }}"
                                                        data-confirm-yes="delete-form-{{ $tag->id }}">
                                                        <i class="ti ti-trash"></i>
                                                    </a>
                                                    {!! Form::close() !!}
                                                @endcan
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ti ti-tags ti-3x mb-3 d-block"></i>
                                                <h6>{{ __('No tags found') }}</h6>
                                                <p class="mb-0">{{ __('Create your first tag to get started.') }}</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
