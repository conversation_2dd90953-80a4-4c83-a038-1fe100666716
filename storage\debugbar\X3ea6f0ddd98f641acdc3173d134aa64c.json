{"__meta": {"id": "X3ea6f0ddd98f641acdc3173d134aa64c", "datetime": "2025-07-31 06:29:03", "utime": **********.732246, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943341.745737, "end": **********.732295, "duration": 1.986557960510254, "duration_str": "1.99s", "measures": [{"label": "Booting", "start": 1753943341.745737, "relative_start": 0, "end": **********.557943, "relative_end": **********.557943, "duration": 1.8122060298919678, "duration_str": "1.81s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.557988, "relative_start": 1.****************, "end": **********.7323, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3gGz7hIz7py1ZtV8D4FaYGZpuSSr0sCLW9RYmr2G", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-93382524 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-93382524\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-923611740 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-923611740\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-55426502 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-55426502\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-604106112 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-604106112\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-826677273 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-826677273\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2036676043 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:29:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFxcEQyUm1FMGNxbTdjSmloYndlanc9PSIsInZhbHVlIjoiTlB6QUFKbzRzMktabC9TQ2RGRERnWU16UDJORHpYMWU2N3AzRTRDa3ZHektRYjdNeVdpUlZZYjN5VUdORkRIcUlxRWJRbWl4MFVMb1E2bDd6VnoxQ29PRnZnSGxkVHBUcUxGMmJTaExvakh6cXFMd2Z2dFFuTFA0bGpuMDV5NS9PVE44c25vZUFibGFPV3p2N3VZei9IN2kzUTVJMUVMZHZoV1daMEh2ZnpFYlIyL3JRb2Urd3N1ODgyQ3V1L0F4S1h5Z0xPdEhpWVZzb0dqOVdSVGZUZVQvVTFQaktHYmpYNnZsVHVVWnR1WHZiOTZwdE5JelpiNGg0R01zeHFScDVBODhHdGtqeVBQK29MVURHbmt1NS95NmkwLy9uM2hjaitaOHJQQWdMTkdjend4cFdQcGIzNDJWa1plZFM3NHZ3MWJwODREVHRYRm9UdG1ZNnA5UlJra285MWFDS0Iyb3N1WFRzdE9hbmFVakIvY1JDcy9qak5zc2ppZTc5M0Yzbjh6K3VXMkQvbXF2RlJxZkNWenpKUlZIZy9kOEJVWFN4cXlTaEtySTQrUlhVeElncExJbnZpNHRiNi9BY1YxODE4UjhGeXl1MU9tK1VjZVFVdzFVZ01EdE1GV1kzbTZCV0pnbTZMdVJ3TjA0bEZoVzVSdlJHa1dOTEhFWUM5YVYiLCJtYWMiOiJhNTNhOTY5NDRkOWEzNmVlODU5OTcwZDc5OGI3N2IzZDAwNjgwN2VmZDgyYjI1NDllOWJmNTY1ZDJkYTAwZWNhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlFwSHA5QUxSWlEzVDZBQ3VVNlN1OEE9PSIsInZhbHVlIjoiWTdkNjliNXpCVjFoM01FMFdVRXJPMWpJRWs0SEtpU3U3WG9MOHlScFFyalQ2U0VCRFlZcUNFSkNYTmtXMTRTV2JKVlNReG9IOEs5MWNub040ZkF4eWlQSnZPRk9ZWFdsSzlMSzdsRi9uSENFMWtUcUI1QU43RVZoZmZWRnZUNFlKRm0rSmx6Y2lnYjlIVzVXdW1BVE42Yjl1anpRaUJzSmdkMm9HRVJMNHNIQk1ETU82ZDZPbnVvYVJGMkU4QlA0NmVNREtmZVN1MFZ6MlNLOGFoR3RZNGx5L2VTWkYwZGdmRkhhcDZNa1ppVTRzUENwTWdJWFZlMTV5MzdVQ2lPTUtjOTR0U0RXM0Jpc2M1YU50TmV2L1NpN1pVU0l6b3dWWnV6RmozdVdNMTBhS3JKQUpVS2FFUXNjNVo1UjdGYSs0d1BEVTdYNFR2THpQeklRM1d4VGRjNEFKbWVtbkJBYzJKMTlmOHF1Q0tEOFkxbHhLS245RFFvYkhWQk1Ib1k2U2RLM3F4ZkRWV2tTWkdyUFBaOWtPRzZCaEx5eVpmZlJhbHdKanIvT1cvK0o3dVR1cnFWbnVFMFlLN25KSVlIblF6VnA3NEkyd1RHUTV3LzJ3aW03eGx3WXp6djlyWnhUS3RMMHdTUkZJMi92U2JXdGlxK2lMZXBHSVF3dTBRZ2EiLCJtYWMiOiIyNWE1ZjJhNmQxNmYyYWJiMDc4NDA2NDE1YTFkYTE2MmRlNDgwMGE1NzBlZjU4NDk1NzkwZWJlZDAwMDgwMTMwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:29:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFxcEQyUm1FMGNxbTdjSmloYndlanc9PSIsInZhbHVlIjoiTlB6QUFKbzRzMktabC9TQ2RGRERnWU16UDJORHpYMWU2N3AzRTRDa3ZHektRYjdNeVdpUlZZYjN5VUdORkRIcUlxRWJRbWl4MFVMb1E2bDd6VnoxQ29PRnZnSGxkVHBUcUxGMmJTaExvakh6cXFMd2Z2dFFuTFA0bGpuMDV5NS9PVE44c25vZUFibGFPV3p2N3VZei9IN2kzUTVJMUVMZHZoV1daMEh2ZnpFYlIyL3JRb2Urd3N1ODgyQ3V1L0F4S1h5Z0xPdEhpWVZzb0dqOVdSVGZUZVQvVTFQaktHYmpYNnZsVHVVWnR1WHZiOTZwdE5JelpiNGg0R01zeHFScDVBODhHdGtqeVBQK29MVURHbmt1NS95NmkwLy9uM2hjaitaOHJQQWdMTkdjend4cFdQcGIzNDJWa1plZFM3NHZ3MWJwODREVHRYRm9UdG1ZNnA5UlJra285MWFDS0Iyb3N1WFRzdE9hbmFVakIvY1JDcy9qak5zc2ppZTc5M0Yzbjh6K3VXMkQvbXF2RlJxZkNWenpKUlZIZy9kOEJVWFN4cXlTaEtySTQrUlhVeElncExJbnZpNHRiNi9BY1YxODE4UjhGeXl1MU9tK1VjZVFVdzFVZ01EdE1GV1kzbTZCV0pnbTZMdVJ3TjA0bEZoVzVSdlJHa1dOTEhFWUM5YVYiLCJtYWMiOiJhNTNhOTY5NDRkOWEzNmVlODU5OTcwZDc5OGI3N2IzZDAwNjgwN2VmZDgyYjI1NDllOWJmNTY1ZDJkYTAwZWNhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlFwSHA5QUxSWlEzVDZBQ3VVNlN1OEE9PSIsInZhbHVlIjoiWTdkNjliNXpCVjFoM01FMFdVRXJPMWpJRWs0SEtpU3U3WG9MOHlScFFyalQ2U0VCRFlZcUNFSkNYTmtXMTRTV2JKVlNReG9IOEs5MWNub040ZkF4eWlQSnZPRk9ZWFdsSzlMSzdsRi9uSENFMWtUcUI1QU43RVZoZmZWRnZUNFlKRm0rSmx6Y2lnYjlIVzVXdW1BVE42Yjl1anpRaUJzSmdkMm9HRVJMNHNIQk1ETU82ZDZPbnVvYVJGMkU4QlA0NmVNREtmZVN1MFZ6MlNLOGFoR3RZNGx5L2VTWkYwZGdmRkhhcDZNa1ppVTRzUENwTWdJWFZlMTV5MzdVQ2lPTUtjOTR0U0RXM0Jpc2M1YU50TmV2L1NpN1pVU0l6b3dWWnV6RmozdVdNMTBhS3JKQUpVS2FFUXNjNVo1UjdGYSs0d1BEVTdYNFR2THpQeklRM1d4VGRjNEFKbWVtbkJBYzJKMTlmOHF1Q0tEOFkxbHhLS245RFFvYkhWQk1Ib1k2U2RLM3F4ZkRWV2tTWkdyUFBaOWtPRzZCaEx5eVpmZlJhbHdKanIvT1cvK0o3dVR1cnFWbnVFMFlLN25KSVlIblF6VnA3NEkyd1RHUTV3LzJ3aW03eGx3WXp6djlyWnhUS3RMMHdTUkZJMi92U2JXdGlxK2lMZXBHSVF3dTBRZ2EiLCJtYWMiOiIyNWE1ZjJhNmQxNmYyYWJiMDc4NDA2NDE1YTFkYTE2MmRlNDgwMGE1NzBlZjU4NDk1NzkwZWJlZDAwMDgwMTMwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:29:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2036676043\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1160835701 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3gGz7hIz7py1ZtV8D4FaYGZpuSSr0sCLW9RYmr2G</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1160835701\", {\"maxDepth\":0})</script>\n"}}