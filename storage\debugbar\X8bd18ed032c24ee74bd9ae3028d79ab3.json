{"__meta": {"id": "X8bd18ed032c24ee74bd9ae3028d79ab3", "datetime": "2025-07-31 05:21:56", "utime": **********.42167, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753939315.580535, "end": **********.421724, "duration": 0.8411891460418701, "duration_str": "841ms", "measures": [{"label": "Booting", "start": 1753939315.580535, "relative_start": 0, "end": **********.353403, "relative_end": **********.353403, "duration": 0.7728681564331055, "duration_str": "773ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.353421, "relative_start": 0.****************, "end": **********.421728, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "68.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QS15RPS1BG3SDXZ0Gyag9PA79v0rKKwKH7dwJy0P", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1215848338 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1215848338\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-206205324 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-206205324\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-297500475 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-297500475\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1278997652 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278997652\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-978974556 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-978974556\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1168687257 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:21:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1vSUJ3bHJhU2oyZXNJaTBKTjBTOEE9PSIsInZhbHVlIjoiNzA1dDA3Qm53UTJOM2xyTnVFc3h1eklvOGdSckYrT1BXdk5pQ0VNZ0ZybnJVN2g5UkpzR1dtWXRnbzZHMkRpWWQ5bTNpM3lrWVVMdkc1RDlUTnh6VjFpNUpGVWh4STFYbE1JNzVkSHc3TkFsQk8xZHIyUkV4QjlhbXUxKzlwVzcralF0WVhzdlJaOGtHd3VLRUFoNUM5K3RQZ05ZWVEwdzRPVWkrZ0RrU2VSelI1L2grNEgvN2JvV0VBblNFMDdkQ0VCQ2ppZzZUWUxMbVdxWUdiUXh6UlcwVHRsRDI5MG1iSVRzcmhpbjJwNE1oYkNwQzlkTFpkSGRxeXhuZnpwalJZcU0yVytjdGxZdERjOGtlTlJhMCsyQ0EwOENsVDZXcm9JcjRuWmRrQVI3bU5mbmgzTzJZbE81WG1NQ1VPeGhoZU5uZ3hkZkI0SVJXdERIUmhyUEM1SUs1cHFkNk5RL2VtUWE2VmNia3NmWHVsR0ozRjYvcENrdTZvWG1xZUN6T1lLMVBWRjRHSWxUb213TFNqNmh6emRVOGc3dmx1d25wTGppWkQrRERGRDdxWmVrTDhuL3NoMnFIL0NJRm1TV0tvYTV0bTJSaWZLNDNoZnVCWHlHNWNGcVR4OFM2NXY0SkcyUkFOTmsxWTZqb2tYU3plNWlPNFNlbXdHdjR5dWEiLCJtYWMiOiIzOWNmNTlmODJkYzE2MzQ4OThkMDhmNTE3YjY0Y2Q2ZGYwMWFjZTJiNTdkZWE2YjYxMDYxYWFjYjRhNjNkODczIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:21:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikd0RkhueEtZWFZuQ3NKTEN6VVk5RGc9PSIsInZhbHVlIjoia1JiWThqUDlPS1hsMDlmVmVVMUYxRVZ4SnNrZFJ1SERtQW1INk9mS0w4dFQ2Ymx0WmsrMW5TYmR6ZzdNWmFEVWo4YTNwZ2NaZlU2cVdmc3pQcStoT0lvMW0wSE8wWWc2Q2RDNEhsekRSOFJuaVUwbGprOFBpY3VZRDlCdnYrcWJCQmJ6VXJvcnJoYkNYZEFMMXNtcDFsc1hhQVRLbytWdlYxRE5iU1VsTGo0WlFqMGNLR3JMa3pFakRZUDVRY01RcGZQQXNROG5mOENCc3YraUtybSswcTlIZXFabGpLblFjVzd4SnRzUnBKV0V4WHFBcm1aeDhmR1dCV2FkaStBUUFmV0NGK05sYXptOWVPVEw3V1Y4U0tRbnQ4d2VDUUx4c290MFBDeWRHK1NVWDNPYWoram9WUVgzSmhkSHBlaXhzRzdnSk12ZjM2YmsrcXNZR0tzbzdSamlaUWxVWWVUQ3dpQlBBYTFCL09ldU11bjh0VStGVndra0dzRU1RTjhCKzVEdXErK3RoQVJrVU5PRUJaZFZLT2VHSUh5K3BhRzl0M1A2bzd3a0xIMWhvNC9ITHJrZHBMbHlwenBCcXgzamZmT3QxbEZPdHk0TzFvSVlCUWRTdFdobVFQc0ZnY205VG13RU5CaVN3dmhWeTdYLytpbmluTEpvdzd5TFIyM2MiLCJtYWMiOiI4Y2YzMjVmYzJiOTgxZmY5OGEzYTUzYTM3N2M1YTBhY2NlM2VkOWRhOGEzZjFmNGQ0MTFjZWIwYjQyZDIyNTdhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:21:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1vSUJ3bHJhU2oyZXNJaTBKTjBTOEE9PSIsInZhbHVlIjoiNzA1dDA3Qm53UTJOM2xyTnVFc3h1eklvOGdSckYrT1BXdk5pQ0VNZ0ZybnJVN2g5UkpzR1dtWXRnbzZHMkRpWWQ5bTNpM3lrWVVMdkc1RDlUTnh6VjFpNUpGVWh4STFYbE1JNzVkSHc3TkFsQk8xZHIyUkV4QjlhbXUxKzlwVzcralF0WVhzdlJaOGtHd3VLRUFoNUM5K3RQZ05ZWVEwdzRPVWkrZ0RrU2VSelI1L2grNEgvN2JvV0VBblNFMDdkQ0VCQ2ppZzZUWUxMbVdxWUdiUXh6UlcwVHRsRDI5MG1iSVRzcmhpbjJwNE1oYkNwQzlkTFpkSGRxeXhuZnpwalJZcU0yVytjdGxZdERjOGtlTlJhMCsyQ0EwOENsVDZXcm9JcjRuWmRrQVI3bU5mbmgzTzJZbE81WG1NQ1VPeGhoZU5uZ3hkZkI0SVJXdERIUmhyUEM1SUs1cHFkNk5RL2VtUWE2VmNia3NmWHVsR0ozRjYvcENrdTZvWG1xZUN6T1lLMVBWRjRHSWxUb213TFNqNmh6emRVOGc3dmx1d25wTGppWkQrRERGRDdxWmVrTDhuL3NoMnFIL0NJRm1TV0tvYTV0bTJSaWZLNDNoZnVCWHlHNWNGcVR4OFM2NXY0SkcyUkFOTmsxWTZqb2tYU3plNWlPNFNlbXdHdjR5dWEiLCJtYWMiOiIzOWNmNTlmODJkYzE2MzQ4OThkMDhmNTE3YjY0Y2Q2ZGYwMWFjZTJiNTdkZWE2YjYxMDYxYWFjYjRhNjNkODczIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:21:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikd0RkhueEtZWFZuQ3NKTEN6VVk5RGc9PSIsInZhbHVlIjoia1JiWThqUDlPS1hsMDlmVmVVMUYxRVZ4SnNrZFJ1SERtQW1INk9mS0w4dFQ2Ymx0WmsrMW5TYmR6ZzdNWmFEVWo4YTNwZ2NaZlU2cVdmc3pQcStoT0lvMW0wSE8wWWc2Q2RDNEhsekRSOFJuaVUwbGprOFBpY3VZRDlCdnYrcWJCQmJ6VXJvcnJoYkNYZEFMMXNtcDFsc1hhQVRLbytWdlYxRE5iU1VsTGo0WlFqMGNLR3JMa3pFakRZUDVRY01RcGZQQXNROG5mOENCc3YraUtybSswcTlIZXFabGpLblFjVzd4SnRzUnBKV0V4WHFBcm1aeDhmR1dCV2FkaStBUUFmV0NGK05sYXptOWVPVEw3V1Y4U0tRbnQ4d2VDUUx4c290MFBDeWRHK1NVWDNPYWoram9WUVgzSmhkSHBlaXhzRzdnSk12ZjM2YmsrcXNZR0tzbzdSamlaUWxVWWVUQ3dpQlBBYTFCL09ldU11bjh0VStGVndra0dzRU1RTjhCKzVEdXErK3RoQVJrVU5PRUJaZFZLT2VHSUh5K3BhRzl0M1A2bzd3a0xIMWhvNC9ITHJrZHBMbHlwenBCcXgzamZmT3QxbEZPdHk0TzFvSVlCUWRTdFdobVFQc0ZnY205VG13RU5CaVN3dmhWeTdYLytpbmluTEpvdzd5TFIyM2MiLCJtYWMiOiI4Y2YzMjVmYzJiOTgxZmY5OGEzYTUzYTM3N2M1YTBhY2NlM2VkOWRhOGEzZjFmNGQ0MTFjZWIwYjQyZDIyNTdhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:21:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1168687257\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1624322444 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QS15RPS1BG3SDXZ0Gyag9PA79v0rKKwKH7dwJy0P</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1624322444\", {\"maxDepth\":0})</script>\n"}}