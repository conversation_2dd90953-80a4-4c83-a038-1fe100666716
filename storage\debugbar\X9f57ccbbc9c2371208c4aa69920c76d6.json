{"__meta": {"id": "X9f57ccbbc9c2371208c4aa69920c76d6", "datetime": "2025-07-31 06:32:20", "utime": **********.116888, "method": "GET", "uri": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753943537.373958, "end": **********.116932, "duration": 2.742973804473877, "duration_str": "2.74s", "measures": [{"label": "Booting", "start": 1753943537.373958, "relative_start": 0, "end": 1753943539.899806, "relative_end": 1753943539.899806, "duration": 2.525847911834717, "duration_str": "2.53s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753943539.899854, "relative_start": 2.***************, "end": **********.116935, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "217ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LRG5yPRPc2lBZlR3uD3M34XFkyGpSY6IiYfRoswb", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "status_code": "<pre class=sf-dump id=sf-dump-731929347 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-731929347\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1784896432 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1784896432\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1187044203 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1187044203\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-982359774 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-982359774\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-325764448 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-325764448\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-890790650 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 06:32:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhqQmRlaFZDRFQ2L0NJWlRWWGdKUVE9PSIsInZhbHVlIjoieHNWdTR1Rm5GR2RrUUh5cTYzeVg1Z3I4enYzYlE0cFVwUWVsY0VUTjNQVjE1VEVhR0V5emZ2dzVMMVBaWWh5ZDBha1JCWUlIcDZDV2JFckErRzcwM3lrTVg2REpDTzExZ2FwVjNsaEdaci91Y1JBWDBPbkF4cTBjZS9ha3pTNU9nbDhoeXJOM1E4MlhZT3FWY2pLbFRzWGNhbnZGUGJuYmwzVTczNkx2MDZ3S3BiNG9vZnhFSnVkOUtZR3lkWkluL1hiYzBySjRqcXFwUWRvdURaTkNreldwczJZTEpRZFpoT0hoWmJkOS9vak8xeURmWUhtSlVLQnArdVI0SU1nSVNCTTlNL1Y4aDRQK1NtT0w0Y200Nmp5ZnhnRVE5dnZQc1JmMnBDY1FBbEJISEp1dmhCNzNacXl6S29Cbnh1ZEoyTVoyTXprd3ZlYnVTbHFVZWlsOWgwMTcyQ05YZmxubGpWdUM2UlFMbk95bVU3ZTVWSjNiYUhSazhDTWhPQmxJeHMraGRnL3dYaVZHeFBtSm9FeVhPVFpXZXgwNWgzRHIySWttSEdMaGNSZlA4OERZd1hDaDRiZXFQWUlEZWE5SjlYVlVNTWh4VlkzRUdCY2Z1U3M2cHZmaXJEejZGR3kzVFFIK0xxcXpPZzVTcFFqLzQ2eHIzOFFOMlhSRlIycmIiLCJtYWMiOiI5OWVmYjRkYWFlZjM2MWM4M2IzODQ4OTBlNmU5YzQxNzZmNDhmZDM1OGM5YmY0ZTEzNzZkNzcyNDA0ZjRlMTAzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlZnS2dhR0RMbDBRRUE0SjUrRmpVdHc9PSIsInZhbHVlIjoiZGNpWXBYUG1yUU5YMnlJRU1QN0grcXUwdU8vbytFRFE5c24rWlk3Znd3Rlp2bVNoeWFWRW1uWWJnQ0YwOVBNU1ZJSGVaaXpwWXpmblRlOTY1b0dQRXhBUVBpdkRTYVF5R3o2MkpJZG9TUUtSOFF0eVFVd3BIY0lqWEtCd1A5bXc0TndybDNjL1JYWFdDVURqREJYKzk5UDVYc2RSTXN6Z2thMDB1UitmMVpyems0eURIRGNiZzUvemJHVW9jU0tKdVRhVkdWTUR6Ry8rUHM4R0lYT2xnajlSRWdxZ1RhbWNBVmNYYUVWZTRrRmxVUXpjb0k3QWduZTQzS1RmcVhTTWE0aHlkU0FDRkovZGRGZ0hDMEU5b2RzbUx6V0svMzVxamJzcTFlQjZvVnJoOUtnUnAwcVUxQ0puck53bk9BTUlhVkhVSkFXK1hWOXl5SG1ZZUdQbHRSdktxMTR6cm8vUWhxYWs0RzgwNFNhYmttY1cxT2Q1NUdCV3liVkxWWG95RHB6dWtjajN1aVB6TTViRzNCZ1hYTkx4M3hnVFhDNExWcUkwWll5ZGd3azIzRURrNmVsdmh0ZmdEdmthRkpvdWt2c2lzbE9MVlJyU0NDUnlvTy9vbzJDbGJPMGp4U2o5R3JCYlZpV29DanVlRi9xZUwwVGJFMEx0RFJnTnFodjYiLCJtYWMiOiIxMTlkYjQwM2RjMDYxYWVmOGVhMGU4NzE4MWUyNTQ1MDliYjMyYzY5NWQ3NjIyMDhjNzVlYTk0OTg2OTA2NGY0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 08:32:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhqQmRlaFZDRFQ2L0NJWlRWWGdKUVE9PSIsInZhbHVlIjoieHNWdTR1Rm5GR2RrUUh5cTYzeVg1Z3I4enYzYlE0cFVwUWVsY0VUTjNQVjE1VEVhR0V5emZ2dzVMMVBaWWh5ZDBha1JCWUlIcDZDV2JFckErRzcwM3lrTVg2REpDTzExZ2FwVjNsaEdaci91Y1JBWDBPbkF4cTBjZS9ha3pTNU9nbDhoeXJOM1E4MlhZT3FWY2pLbFRzWGNhbnZGUGJuYmwzVTczNkx2MDZ3S3BiNG9vZnhFSnVkOUtZR3lkWkluL1hiYzBySjRqcXFwUWRvdURaTkNreldwczJZTEpRZFpoT0hoWmJkOS9vak8xeURmWUhtSlVLQnArdVI0SU1nSVNCTTlNL1Y4aDRQK1NtT0w0Y200Nmp5ZnhnRVE5dnZQc1JmMnBDY1FBbEJISEp1dmhCNzNacXl6S29Cbnh1ZEoyTVoyTXprd3ZlYnVTbHFVZWlsOWgwMTcyQ05YZmxubGpWdUM2UlFMbk95bVU3ZTVWSjNiYUhSazhDTWhPQmxJeHMraGRnL3dYaVZHeFBtSm9FeVhPVFpXZXgwNWgzRHIySWttSEdMaGNSZlA4OERZd1hDaDRiZXFQWUlEZWE5SjlYVlVNTWh4VlkzRUdCY2Z1U3M2cHZmaXJEejZGR3kzVFFIK0xxcXpPZzVTcFFqLzQ2eHIzOFFOMlhSRlIycmIiLCJtYWMiOiI5OWVmYjRkYWFlZjM2MWM4M2IzODQ4OTBlNmU5YzQxNzZmNDhmZDM1OGM5YmY0ZTEzNzZkNzcyNDA0ZjRlMTAzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlZnS2dhR0RMbDBRRUE0SjUrRmpVdHc9PSIsInZhbHVlIjoiZGNpWXBYUG1yUU5YMnlJRU1QN0grcXUwdU8vbytFRFE5c24rWlk3Znd3Rlp2bVNoeWFWRW1uWWJnQ0YwOVBNU1ZJSGVaaXpwWXpmblRlOTY1b0dQRXhBUVBpdkRTYVF5R3o2MkpJZG9TUUtSOFF0eVFVd3BIY0lqWEtCd1A5bXc0TndybDNjL1JYWFdDVURqREJYKzk5UDVYc2RSTXN6Z2thMDB1UitmMVpyems0eURIRGNiZzUvemJHVW9jU0tKdVRhVkdWTUR6Ry8rUHM4R0lYT2xnajlSRWdxZ1RhbWNBVmNYYUVWZTRrRmxVUXpjb0k3QWduZTQzS1RmcVhTTWE0aHlkU0FDRkovZGRGZ0hDMEU5b2RzbUx6V0svMzVxamJzcTFlQjZvVnJoOUtnUnAwcVUxQ0puck53bk9BTUlhVkhVSkFXK1hWOXl5SG1ZZUdQbHRSdktxMTR6cm8vUWhxYWs0RzgwNFNhYmttY1cxT2Q1NUdCV3liVkxWWG95RHB6dWtjajN1aVB6TTViRzNCZ1hYTkx4M3hnVFhDNExWcUkwWll5ZGd3azIzRURrNmVsdmh0ZmdEdmthRkpvdWt2c2lzbE9MVlJyU0NDUnlvTy9vbzJDbGJPMGp4U2o5R3JCYlZpV29DanVlRi9xZUwwVGJFMEx0RFJnTnFodjYiLCJtYWMiOiIxMTlkYjQwM2RjMDYxYWVmOGVhMGU4NzE4MWUyNTQ1MDliYjMyYzY5NWQ3NjIyMDhjNzVlYTk0OTg2OTA2NGY0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 08:32:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-890790650\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1903117470 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LRG5yPRPc2lBZlR3uD3M34XFkyGpSY6IiYfRoswb</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1903117470\", {\"maxDepth\":0})</script>\n"}}