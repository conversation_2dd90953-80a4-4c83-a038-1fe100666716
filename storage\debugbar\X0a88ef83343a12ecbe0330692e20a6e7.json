{"__meta": {"id": "X0a88ef83343a12ecbe0330692e20a6e7", "datetime": "2025-07-31 05:34:32", "utime": **********.41233, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753940070.93283, "end": **********.412384, "duration": 1.4795539379119873, "duration_str": "1.48s", "measures": [{"label": "Booting", "start": 1753940070.93283, "relative_start": 0, "end": **********.260699, "relative_end": **********.260699, "duration": 1.327868938446045, "duration_str": "1.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.260722, "relative_start": 1.****************, "end": **********.412389, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "IJBwyaZAsT5rqE15Sv9mc7BU5PM0fwUqxHrDufW2", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-289206239 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-289206239\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1996752910 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1996752910\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1178471609 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1178471609\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-751633030 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-751633030\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1185077529 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1185077529\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1425695839 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:34:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZidWtZNzVCdm1vejZaMUwyUHBCQmc9PSIsInZhbHVlIjoiQlNXdmdMK3NFUWVTaHdMVG0xUGFmekdQNDZCQklqU3h5N0FyNlZCUm9oL0hZSDJuS3p0Vys4a3JQOFJnYlRqdFRZSXI3azFFVnY5MzE2aGdMQW5SOXQ3cTRPRnNFZ0JKTDlqSVlRMWQ5Zkc4NGdUb0pSQ2EwcmlBajFDakJVU0g5Q0dCcWVrRmg2eEdJWTBna1o4UmI1WXVDMDhRa3ZheTRRVm9nVmlucTdhNlBMc3k1OEVGVlVrSDc3YVVDd2R0TnRQaTRFWk9KZ3o5NmdxRmdaL284bWlkNS83cjQ3a2Q3N004UmdhemdhU3RTZFNWVEFvYkd3dWZOSkZCUnNsYlVOdDlFcU9IU0p5QjJVaGZwUmRYUW1DQktBTUhlaml2N2JSbC9IenZzZE1nNHF4QWh4K0NYT25JbFZ1Z0pvQWl5NWJzOXJLeFZYbFM3NXo4YWFLS3RTdHpRekNjei8yanlNTThaVW5COWxSWU01SjBvL2hkVVFEZFBpLzQ5VEFVRTlxeUhEWHhLUTJnTjBJUS9tbWw5ckZkb0hHdGxIYUtoYlFSWEl5UUpLQWQ3cUtyWitKTUxkbzBNczRWUSsrcU5nN2hoV0hoRnRseUJyWWFiYjlwejE4alhEOE45YXhpdXVNaitoVGJIenNoKzNFZGg4Y1ZMa2phd1VBNFNQR3EiLCJtYWMiOiIwNGVkMzc3NGFhYTNiMzI2MjU2NzIwMjM4MzRiMzE0NDI2MDk1MjlmYjUzZjRjZmE1NmZhNDA2OTJiYWU5NDU4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IngwOWpIeUg2OVE3QXB0Z1NSaWkrZHc9PSIsInZhbHVlIjoiU3dmd0FIZjIzbDdyQ2pxc2hCUm1hQTFIcXkwUlNnYk1rYzdUSW9sSWZzNUlFRkw2ZktTMUtIQVZwTDdydkQ1THk4QTFJVFdUeHc2b244SjJ6Q2RJRXg2N3BIY0xmU3phSWY4cE85clJETzl0d0JUbDlERnMvVGQrSThWQldsaFFFN3hYQkNUaUlDeUFBRnRMU3lPNkJ3QW05bmNQS3BoYmdiK3htcTBNK0gzUjhMbk1RNVpEdkd6ZE53cXp6Uk1QVTlENE5aNVJ2ay9hNEJNeStzTDl2RGdnaHBUVjAyWjdaYWxHSFpzUEZDNXNkYzgvM0hmdm00YkFpSWNqUXJ6MHo5M3FnSytPSVlMdnlJdWVDTEhjQm9mN2REKzMzZFppUGdsQVVtUVl1ZFZ4cUVIV1E4ZHplMDEzRnA4QUhZRGZNaFpUODIrNXJjRHlQRG5WSCtRdDJxdjFMSk1DdFdFVlVySG1CK1RIalRIeXEwNEk1YThDcWpGOGRZWkRwejEvL2l0LzVUdm12bFk0UHhIVkc4L2plM0VYTXJLelZZOUJEMkxNTTFQSzE4QWpRNEhoazA2d1hnNTJDc1Z2MG8vWmFkQStsN1orbGpGQzlram8zeUg4NWh2MXcwUEFWYlZMeXg1aExDcy9CVXdyOHZiZWxIWkZZN2puWjduTjBEUU0iLCJtYWMiOiJkZDRiNTg0NGNlZWRlNDAwODJhMjc2ZTBiMGEwMjIzOWM1NWEwMWE3MjY5MGRkN2Y5ZWZhODVhNjY1ZmMxZGY1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:34:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZidWtZNzVCdm1vejZaMUwyUHBCQmc9PSIsInZhbHVlIjoiQlNXdmdMK3NFUWVTaHdMVG0xUGFmekdQNDZCQklqU3h5N0FyNlZCUm9oL0hZSDJuS3p0Vys4a3JQOFJnYlRqdFRZSXI3azFFVnY5MzE2aGdMQW5SOXQ3cTRPRnNFZ0JKTDlqSVlRMWQ5Zkc4NGdUb0pSQ2EwcmlBajFDakJVU0g5Q0dCcWVrRmg2eEdJWTBna1o4UmI1WXVDMDhRa3ZheTRRVm9nVmlucTdhNlBMc3k1OEVGVlVrSDc3YVVDd2R0TnRQaTRFWk9KZ3o5NmdxRmdaL284bWlkNS83cjQ3a2Q3N004UmdhemdhU3RTZFNWVEFvYkd3dWZOSkZCUnNsYlVOdDlFcU9IU0p5QjJVaGZwUmRYUW1DQktBTUhlaml2N2JSbC9IenZzZE1nNHF4QWh4K0NYT25JbFZ1Z0pvQWl5NWJzOXJLeFZYbFM3NXo4YWFLS3RTdHpRekNjei8yanlNTThaVW5COWxSWU01SjBvL2hkVVFEZFBpLzQ5VEFVRTlxeUhEWHhLUTJnTjBJUS9tbWw5ckZkb0hHdGxIYUtoYlFSWEl5UUpLQWQ3cUtyWitKTUxkbzBNczRWUSsrcU5nN2hoV0hoRnRseUJyWWFiYjlwejE4alhEOE45YXhpdXVNaitoVGJIenNoKzNFZGg4Y1ZMa2phd1VBNFNQR3EiLCJtYWMiOiIwNGVkMzc3NGFhYTNiMzI2MjU2NzIwMjM4MzRiMzE0NDI2MDk1MjlmYjUzZjRjZmE1NmZhNDA2OTJiYWU5NDU4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IngwOWpIeUg2OVE3QXB0Z1NSaWkrZHc9PSIsInZhbHVlIjoiU3dmd0FIZjIzbDdyQ2pxc2hCUm1hQTFIcXkwUlNnYk1rYzdUSW9sSWZzNUlFRkw2ZktTMUtIQVZwTDdydkQ1THk4QTFJVFdUeHc2b244SjJ6Q2RJRXg2N3BIY0xmU3phSWY4cE85clJETzl0d0JUbDlERnMvVGQrSThWQldsaFFFN3hYQkNUaUlDeUFBRnRMU3lPNkJ3QW05bmNQS3BoYmdiK3htcTBNK0gzUjhMbk1RNVpEdkd6ZE53cXp6Uk1QVTlENE5aNVJ2ay9hNEJNeStzTDl2RGdnaHBUVjAyWjdaYWxHSFpzUEZDNXNkYzgvM0hmdm00YkFpSWNqUXJ6MHo5M3FnSytPSVlMdnlJdWVDTEhjQm9mN2REKzMzZFppUGdsQVVtUVl1ZFZ4cUVIV1E4ZHplMDEzRnA4QUhZRGZNaFpUODIrNXJjRHlQRG5WSCtRdDJxdjFMSk1DdFdFVlVySG1CK1RIalRIeXEwNEk1YThDcWpGOGRZWkRwejEvL2l0LzVUdm12bFk0UHhIVkc4L2plM0VYTXJLelZZOUJEMkxNTTFQSzE4QWpRNEhoazA2d1hnNTJDc1Z2MG8vWmFkQStsN1orbGpGQzlram8zeUg4NWh2MXcwUEFWYlZMeXg1aExDcy9CVXdyOHZiZWxIWkZZN2puWjduTjBEUU0iLCJtYWMiOiJkZDRiNTg0NGNlZWRlNDAwODJhMjc2ZTBiMGEwMjIzOWM1NWEwMWE3MjY5MGRkN2Y5ZWZhODVhNjY1ZmMxZGY1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:34:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1425695839\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-336144305 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IJBwyaZAsT5rqE15Sv9mc7BU5PM0fwUqxHrDufW2</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-336144305\", {\"maxDepth\":0})</script>\n"}}