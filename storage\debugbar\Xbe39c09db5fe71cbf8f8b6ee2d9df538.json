{"__meta": {"id": "Xbe39c09db5fe71cbf8f8b6ee2d9df538", "datetime": "2025-07-31 05:54:19", "utime": **********.365512, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753941257.26203, "end": **********.36556, "duration": 2.103530168533325, "duration_str": "2.1s", "measures": [{"label": "Booting", "start": 1753941257.26203, "relative_start": 0, "end": **********.22509, "relative_end": **********.22509, "duration": 1.9630601406097412, "duration_str": "1.96s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.225134, "relative_start": 1.***************, "end": **********.365565, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "140ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Wb2uHGp2ZLF2PNPZxwz96S9dmSq4adOnPRpozw2r", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-755101799 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-755101799\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1199476351 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1199476351\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-769605978 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-769605978\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2122438209 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2122438209\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1635241880 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1635241880\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1340344158 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:54:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNQWHB3dUFUVGxCK1lKQWpJSXY3elE9PSIsInZhbHVlIjoiV2tEbVhrUlJXU0llWGdObDBPRWoybjNBdHdPYjR6TVRUOFdHMnFxeUxlbnh1LzRQT3BGSnM4T1FSNklDRmpoUXU2Skc1bHdFV3lkeWh5VlJzZ3dkS2s4TlpPUUN0WUxtdzRabk1tM2RFUHlVWUQ5YWQwYnVDbkdOSjNEQnhSNDBVQzI1WFI4R3hyMG4vcGM5SncxRHcvckQ2SVVORm9KRXJnL0JNQjhZM0hIVFpwbURJWldvZ1NQZWFvckVHWEUrMTRXRWx3NHRmL3F4NXEwekxnZGdiSnNCUzdFbEpJY0NLZldhendjVWEvYjBPQjN1dmV6bzNuU3N1Q0EwazlSNklTdHRGdnB0N21RRVNBbkRkR3B2L2JBcTNsQTJaK2F3RXpOQU82eW1GNnJ1UHpCKzR0ZUg2NUZYTlp2VGRYemZSdHNodGRrZ1lrc3pwKzdyeFUrTjBPbExha1o1U1FIREc2UFF2S3ZBa3hHREZ3TDNoeGJQNjhnc0t2cWN6c09oeHhGNm1JaTZ6ZGljM2RuV25lcHZRUE5ETW11TVBoMkQ1eHFHcXdDTXdXN2czS1p4TmFaUzdwMld0WXFVSlJUY3dTcEFlbzQzK0FwdExNRk1sSVd1UUZiZEVoVlBaVi91b1d6YXVzSFdTUWFuR052YXZxZ1c4akF6cTJ1a3hFRmEiLCJtYWMiOiJjNDUwYTRjMWRiZWMyZjRjOGZmYTA4MzQ4MWE2ZTFiMzdlNmMzNGFjYWM2YjkyMjZlMGQyMzE3NzY1YmM5ZTAxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:54:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IllKRzlFY2Rhdm5tblFRenlVMlRnSXc9PSIsInZhbHVlIjoiRVUxZG5zdWVwdThIWE5XeUxUN2hsbVdMeCsvK1JiWFJhTFp2TU9sdDA4Q2x4ZUlqZDVadDQ4M3MrRUtJMzN5VHBKeUZUek1oY29OMjVaUUd3OGQvWVVHWG9SbzJXT003VGRXaEFidzRoNnJUWkQwMFV1Ny94dTRPUnhTQW1KR0h5ZFd0VGIzaEpEbU9hdnpkMllGQVFPTVp6WlhLa3l5c3JhWDFqSVBXdFFEVXV5OWxaNUk2NTBmR1hmL0tRMDRyV1FzejNocEQ5RHE0cGdHT3NhemJEckg2QlRyeUNDWHc0SmRPRWREUnB6MDgwQ0RlRkpzTFk0bktNNGlyVWdhODJ0bU5jNS85MFhwVXJKQVZyTld3SThWazRDb0VXcWNSd0xYSWVkNis1L0wyRUI0eUIzemtmVFRKYTJmaTBuT3ducXZPdWt2QXN5eW4wRGJEbjNSL0sxcnIvNG91aEJmZ1Z4SHh1cGtuMUREZEdlanpTaFhEUlkzc0lpM2lGc0V0Mjc2WW1idTZDTk1oSTBaL20ybWNiRllwK3RYd3VGeHplVHNOUXEvc0orbTRpb2R5bnVrYnM1ek9rcW9FZzZMUnhjNEcyZDNSMm94VFcvZitWRzFYMkljWjM0OTNFS3hHNDVzNk1SV1Y5ZFFxaUVJYllxSWh2SXVTVGdzU2pWRkkiLCJtYWMiOiJlMWYyODJlNGRmMDQ5Nzc4MmRiNzU1Mjc4ZTMyODNjZGU2ZWRmMDUzZGZiMzVkYjZkYjhlZTYwMzdlOTdiMmJiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:54:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNQWHB3dUFUVGxCK1lKQWpJSXY3elE9PSIsInZhbHVlIjoiV2tEbVhrUlJXU0llWGdObDBPRWoybjNBdHdPYjR6TVRUOFdHMnFxeUxlbnh1LzRQT3BGSnM4T1FSNklDRmpoUXU2Skc1bHdFV3lkeWh5VlJzZ3dkS2s4TlpPUUN0WUxtdzRabk1tM2RFUHlVWUQ5YWQwYnVDbkdOSjNEQnhSNDBVQzI1WFI4R3hyMG4vcGM5SncxRHcvckQ2SVVORm9KRXJnL0JNQjhZM0hIVFpwbURJWldvZ1NQZWFvckVHWEUrMTRXRWx3NHRmL3F4NXEwekxnZGdiSnNCUzdFbEpJY0NLZldhendjVWEvYjBPQjN1dmV6bzNuU3N1Q0EwazlSNklTdHRGdnB0N21RRVNBbkRkR3B2L2JBcTNsQTJaK2F3RXpOQU82eW1GNnJ1UHpCKzR0ZUg2NUZYTlp2VGRYemZSdHNodGRrZ1lrc3pwKzdyeFUrTjBPbExha1o1U1FIREc2UFF2S3ZBa3hHREZ3TDNoeGJQNjhnc0t2cWN6c09oeHhGNm1JaTZ6ZGljM2RuV25lcHZRUE5ETW11TVBoMkQ1eHFHcXdDTXdXN2czS1p4TmFaUzdwMld0WXFVSlJUY3dTcEFlbzQzK0FwdExNRk1sSVd1UUZiZEVoVlBaVi91b1d6YXVzSFdTUWFuR052YXZxZ1c4akF6cTJ1a3hFRmEiLCJtYWMiOiJjNDUwYTRjMWRiZWMyZjRjOGZmYTA4MzQ4MWE2ZTFiMzdlNmMzNGFjYWM2YjkyMjZlMGQyMzE3NzY1YmM5ZTAxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:54:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IllKRzlFY2Rhdm5tblFRenlVMlRnSXc9PSIsInZhbHVlIjoiRVUxZG5zdWVwdThIWE5XeUxUN2hsbVdMeCsvK1JiWFJhTFp2TU9sdDA4Q2x4ZUlqZDVadDQ4M3MrRUtJMzN5VHBKeUZUek1oY29OMjVaUUd3OGQvWVVHWG9SbzJXT003VGRXaEFidzRoNnJUWkQwMFV1Ny94dTRPUnhTQW1KR0h5ZFd0VGIzaEpEbU9hdnpkMllGQVFPTVp6WlhLa3l5c3JhWDFqSVBXdFFEVXV5OWxaNUk2NTBmR1hmL0tRMDRyV1FzejNocEQ5RHE0cGdHT3NhemJEckg2QlRyeUNDWHc0SmRPRWREUnB6MDgwQ0RlRkpzTFk0bktNNGlyVWdhODJ0bU5jNS85MFhwVXJKQVZyTld3SThWazRDb0VXcWNSd0xYSWVkNis1L0wyRUI0eUIzemtmVFRKYTJmaTBuT3ducXZPdWt2QXN5eW4wRGJEbjNSL0sxcnIvNG91aEJmZ1Z4SHh1cGtuMUREZEdlanpTaFhEUlkzc0lpM2lGc0V0Mjc2WW1idTZDTk1oSTBaL20ybWNiRllwK3RYd3VGeHplVHNOUXEvc0orbTRpb2R5bnVrYnM1ek9rcW9FZzZMUnhjNEcyZDNSMm94VFcvZitWRzFYMkljWjM0OTNFS3hHNDVzNk1SV1Y5ZFFxaUVJYllxSWh2SXVTVGdzU2pWRkkiLCJtYWMiOiJlMWYyODJlNGRmMDQ5Nzc4MmRiNzU1Mjc4ZTMyODNjZGU2ZWRmMDUzZGZiMzVkYjZkYjhlZTYwMzdlOTdiMmJiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:54:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1340344158\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-930173411 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Wb2uHGp2ZLF2PNPZxwz96S9dmSq4adOnPRpozw2r</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-930173411\", {\"maxDepth\":0})</script>\n"}}