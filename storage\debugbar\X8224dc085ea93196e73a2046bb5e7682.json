{"__meta": {"id": "X8224dc085ea93196e73a2046bb5e7682", "datetime": "2025-07-31 05:10:26", "utime": **********.220979, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:10:26] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.214099, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753938625.094892, "end": **********.221028, "duration": 1.126136064529419, "duration_str": "1.13s", "measures": [{"label": "Booting", "start": 1753938625.094892, "relative_start": 0, "end": **********.052039, "relative_end": **********.052039, "duration": 0.9571468830108643, "duration_str": "957ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.052092, "relative_start": 0.9572000503540039, "end": **********.221031, "relative_end": 2.86102294921875e-06, "duration": 0.16893887519836426, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50614136, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.196965, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02259, "accumulated_duration_str": "22.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.130229, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 15.405}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.150371, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 15.405, "width_percent": 3.099}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.159599, "duration": 0.01736, "duration_str": "17.36ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 18.504, "width_percent": 76.848}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.181212, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 95.352, "width_percent": 4.648}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1586455422 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1586455422\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-509833985 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-509833985\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-91702337 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-91702337\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1287343670 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InBBdjUyelJtNWNrYURVOUVKTVVJeUE9PSIsInZhbHVlIjoiWFNRSHJVdTFONnE0MUprWkU3UnhmcXdsRWxKS1lmMUJsZ2pKelJqcjlzZU5aTkpsQjE4TlRJYVJRRERhTlZ5b2JhcTNXZU9xZGl4OGRnSTVLcE5oZFY1RmFGYlZBSUkrYUNxNkJiR3pFc2lVSUl4TjFVS2VkQ0VrblZLZmlMaG9zOG8wV2RmakExMWxSRlQ2OEJvZUtURWx5TjlieTBYSklWSkRRNG9MTzNWWHhoc2g0eElqVkI3alV1ZmJFR2NLRHhnc2xkTS8wSXZmN0RGeCtiVCtESERUVSt2U0NJZGlNK24yV3ZUVUFjMGNWbSsyY1duS1owT20remhFNktPZ0wrUFlEMjZmR2toU0ptZ0JrMEdNKzZKK0NOcHE1N3pCa2ZuRUl3Umxqd3J1OXc1emlvOE8yeG9kYzAwU2FwaHhzUUtVOEQwRWsyNGpkMW83N3huR29ScXoxK2tYOVJ5bXVVRk1ReFhlZk0wVmI0ZmRVdmVYVUZ1Y0poRmx4b3dXMm9xUld6Slc1ZjNEYlU1UHJlUzFYMFE3L0tvcy9VWGxzc0tUUDRPbTN6d1lxTWNwWGtQanNaT1lLNmpod1pIb3c5QzdsRFplbm1zRDV6NmNWQkdqMTZMMUlXekpGczlpZ3Q5ZHBIc3MxTjhaMkQ0anhqWEN6WGdDYTdHbFdtU08iLCJtYWMiOiJiZmJlYzQ1ZTRhNWQxZjdkMjIzZGFiNWY5NmU5ODBmNjUyNDQ3MTQwNDQyYmY0N2MwM2U2ZTQ4NjBiODRiNWY2IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InFnWTNIV2xWNkVQNW9vTFpBeHBad3c9PSIsInZhbHVlIjoibTFQRjdrUWJQejdzVTBmdlg2NkdjZVZXRkYvVDZmekF5WUdyR2NZeHZVWlBod3JzWlBCaEF3VllSQ3hma0RUaFVOQ25Wc2t6dnZiMCtraURGN25UbmFpbEtpb3VwaUVCK1NpYTdLWkM1TXBuTld1VnFTYVJtQVRZL3RXNk1BMHdKMWx2akhKY005QUFlZElCZmZqWCtqcTlFaDBiMW1SQzQ3c3pUQjFVTWZWOTYycklyNEJ6UU5wNlV6bHIvbE5Bbm9SMmp2T29qeWtrZzBPQld1RGZhcFdtRmxYV0MzRlg1Z3Jib2NlSWptUWlGOEY5ZXlHcmxYTmh6UXBaVGdvT24wWWdCMUROWkhGZmpiWG92VGZTMlZ2ZUl2SU5zTFUyTXJxSzcrTWVHaSsvQ3A2MzVRNnlHYlJ2dzRnUzQyckdONXMwb09QNHd6VHNhK1FOTmRHQ2o3YXJ4UnJIS3VPMjNmc0FrUWIvZTZRM0NhdlRhSHltRk9NMU5tSWhCaDNvOXBabkpyakFhem5sWFpSUmI0OGFFcEc4UkRKVE9lRHI0SkpTdjZWT0NZQ3ptMzJhNG16blZtTVdmc0U3K0lhcXBZMXFRSWhUd1BRYmNPaWNrVXRibHFoSzQxaXQ1TUppbUd6Q093THR0ZmE0YlU3TXpFak83eVAyWDN1VnBYMzciLCJtYWMiOiJkNDc1NTQ3NmY5YmVhZmZiY2NlYTBmYTgwYWE2NzcwZjI5MjkyZDM3ZGNhZWVkZDEwMTkxYWZjODI5NTc4ODY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1287343670\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2112549387 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VXcIUJMcydAyqtnEKWEUzIzoSEq1x7CzphaduhZT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112549387\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:10:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdQMXJuVlNNb2FyTzVQblhYRzhnOUE9PSIsInZhbHVlIjoia0tid3c1YW1IUFdoQzlrVnJTUm85MHhsakFUUFVKdlZoc1FHWE52NVcxMDVpYmxPZEMveFJROHBQQS85OWdNNS9DUjltcmZPd1RoTlNPK0Y5UlNqeHZadDFBZkZZOWtRVFdpU25YSTRtemtBQU5Tb1ZlTzY4dzhaQ2Uvc2JpYmZFV3F0c20yc2xrRGE0MU9pNFRndGtXYnArcmllVWxiZjRyM2xsMW5EaHhSSDlxT2pDUnB3V09tcFFvNW82eWRUMHRUVDZQSHFETzFpWitEV2Jmay9DN01YajZSYW5iTmhqL0JSVmgrWU8zZG1YNTVNVlJSb1ErUjc5L0RUWSszOG1nZWlKeS9YTVJaYXc5NHBtak01TzBtdUUzcExJRFVkdHNjRVVKaU9XcUtyZTZ2MFRsSjh0MWd0TVpTTU14ODJrR29lVFpNU0wxZGlXSDFyZ3JEYlpKQk1IeExSQUJSdlJkbWpWeWJYankvTVY0QzVBelg3d0xnSjhQMzBsVW9GNkVGSHpxSDA0YmxqSHNWVkQwRWpvK01LL3RudWdhU2M4SGFVbC9RYTdvRzFlVHIxeUNVQmh5SnNaK2NwckRKalpKRWlDeVQyQ0xaMW16ODFFb0VUMlZMcU9CWVZUNnp0NE9iSFA2bjlxMVBXMkZLMUo3ZHN0a1M5Z29TMnUyQ2UiLCJtYWMiOiI5MjIwYWM4ZTZlZTE4NDgwNDc4ZDZmNTcwMjljNWQyN2EzYTQ5ZDBhMzhmMDJlMmI0YTdhNTYxOWQwYWE2YTY5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:10:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlRLb25TWmM4Zk9kbkNDRU5sVkkrd1E9PSIsInZhbHVlIjoiZEdFK3BMQnZleWRNai9ZOTlKT2dOdEhXaUdnSzZVcGpSQy9xbFpXSlhwRkpUenZsNmx6bGFXeVR2d3Q1dVAvV3NlWDdIb1NZbmJaNWEvTFRiNkdwM3Nzak5VcUp1aXV3MFJIUVBDbVhrODk3WURJTiszVmhOWVNWc0xCRlJUckJleTRrcHpuOGoyaDBQaStGV213dzhUZk5tWENoQXVDR0xLMUJscXdvVjZoRDU2OEdqbVNmcFBhSytiNlZ3bFJMNFI0SGNJNE5tYm40TStzaUU3d3NHZDNubFMrRnhvMmNYNExIbG1leEhVTnh5SzZ0R1FMbzJXQk9uVHZlYzM0a1BEQjFHVGdXeG1rQ3VLdUVvUUZlMCtYMTJqS2gyelI1enhyNFNPb3pmLzdmMTlMYndDQVFqVXZ5YWdSa1M1dC9nTmZ5SHNtRnh1UXVVRkxRZ2FHKzUwMy9HR0pJT1Y3U2tNZWwzNjRMYlJxUnoySUJxMG1ZeHVvRVJpWmdPSkphUDVpcGFHZk9pcHlxb25QRjlzVkdlejQ5S0VnWVZ0S09HOURKWlFzT0N2cEFRdVRITWVYNWpUdU1DRjk1OFlxdThSdGZGUDVlRVRjSUF2ZXN4VGhWWkFoQllzdStwU3ZVdFBia2FLY2t6K2g5ZFJaemlYcHVWSXVoNElaQjV2cVEiLCJtYWMiOiJjMjlhNmMwNzc2MTQ1MTY1MDhjMTA5ODBlMzkzMzU0YTdhOTQ5YjFlNjBiMjYwYmJjZWVlMDk5YjE2YmIzNmYzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:10:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdQMXJuVlNNb2FyTzVQblhYRzhnOUE9PSIsInZhbHVlIjoia0tid3c1YW1IUFdoQzlrVnJTUm85MHhsakFUUFVKdlZoc1FHWE52NVcxMDVpYmxPZEMveFJROHBQQS85OWdNNS9DUjltcmZPd1RoTlNPK0Y5UlNqeHZadDFBZkZZOWtRVFdpU25YSTRtemtBQU5Tb1ZlTzY4dzhaQ2Uvc2JpYmZFV3F0c20yc2xrRGE0MU9pNFRndGtXYnArcmllVWxiZjRyM2xsMW5EaHhSSDlxT2pDUnB3V09tcFFvNW82eWRUMHRUVDZQSHFETzFpWitEV2Jmay9DN01YajZSYW5iTmhqL0JSVmgrWU8zZG1YNTVNVlJSb1ErUjc5L0RUWSszOG1nZWlKeS9YTVJaYXc5NHBtak01TzBtdUUzcExJRFVkdHNjRVVKaU9XcUtyZTZ2MFRsSjh0MWd0TVpTTU14ODJrR29lVFpNU0wxZGlXSDFyZ3JEYlpKQk1IeExSQUJSdlJkbWpWeWJYankvTVY0QzVBelg3d0xnSjhQMzBsVW9GNkVGSHpxSDA0YmxqSHNWVkQwRWpvK01LL3RudWdhU2M4SGFVbC9RYTdvRzFlVHIxeUNVQmh5SnNaK2NwckRKalpKRWlDeVQyQ0xaMW16ODFFb0VUMlZMcU9CWVZUNnp0NE9iSFA2bjlxMVBXMkZLMUo3ZHN0a1M5Z29TMnUyQ2UiLCJtYWMiOiI5MjIwYWM4ZTZlZTE4NDgwNDc4ZDZmNTcwMjljNWQyN2EzYTQ5ZDBhMzhmMDJlMmI0YTdhNTYxOWQwYWE2YTY5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:10:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlRLb25TWmM4Zk9kbkNDRU5sVkkrd1E9PSIsInZhbHVlIjoiZEdFK3BMQnZleWRNai9ZOTlKT2dOdEhXaUdnSzZVcGpSQy9xbFpXSlhwRkpUenZsNmx6bGFXeVR2d3Q1dVAvV3NlWDdIb1NZbmJaNWEvTFRiNkdwM3Nzak5VcUp1aXV3MFJIUVBDbVhrODk3WURJTiszVmhOWVNWc0xCRlJUckJleTRrcHpuOGoyaDBQaStGV213dzhUZk5tWENoQXVDR0xLMUJscXdvVjZoRDU2OEdqbVNmcFBhSytiNlZ3bFJMNFI0SGNJNE5tYm40TStzaUU3d3NHZDNubFMrRnhvMmNYNExIbG1leEhVTnh5SzZ0R1FMbzJXQk9uVHZlYzM0a1BEQjFHVGdXeG1rQ3VLdUVvUUZlMCtYMTJqS2gyelI1enhyNFNPb3pmLzdmMTlMYndDQVFqVXZ5YWdSa1M1dC9nTmZ5SHNtRnh1UXVVRkxRZ2FHKzUwMy9HR0pJT1Y3U2tNZWwzNjRMYlJxUnoySUJxMG1ZeHVvRVJpWmdPSkphUDVpcGFHZk9pcHlxb25QRjlzVkdlejQ5S0VnWVZ0S09HOURKWlFzT0N2cEFRdVRITWVYNWpUdU1DRjk1OFlxdThSdGZGUDVlRVRjSUF2ZXN4VGhWWkFoQllzdStwU3ZVdFBia2FLY2t6K2g5ZFJaemlYcHVWSXVoNElaQjV2cVEiLCJtYWMiOiJjMjlhNmMwNzc2MTQ1MTY1MDhjMTA5ODBlMzkzMzU0YTdhOTQ5YjFlNjBiMjYwYmJjZWVlMDk5YjE2YmIzNmYzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:10:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1050846197 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1050846197\", {\"maxDepth\":0})</script>\n"}}