{"__meta": {"id": "Xd5dcea370c115b0cd00c63075de04b2b", "datetime": "2025-07-31 05:17:15", "utime": **********.628716, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:17:15] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.623986, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753939034.584424, "end": **********.62874, "duration": 1.044316053390503, "duration_str": "1.04s", "measures": [{"label": "Booting", "start": 1753939034.584424, "relative_start": 0, "end": **********.364399, "relative_end": **********.364399, "duration": 0.7799749374389648, "duration_str": "780ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.364422, "relative_start": 0.7799980640411377, "end": **********.628743, "relative_end": 2.86102294921875e-06, "duration": 0.26432085037231445, "duration_str": "264ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51800176, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.525666, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.03634999999999999, "accumulated_duration_str": "36.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.444754, "duration": 0.00509, "duration_str": "5.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 14.003}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.47085, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 14.003, "width_percent": 5.887}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 79 or `ch_messages`.`to_id` = 79 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["79", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.480498, "duration": 0.02204, "duration_str": "22.04ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 19.89, "width_percent": 60.633}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 79", "type": "query", "params": [], "bindings": ["client", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 364}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.508515, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:364", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=364", "ajax": false, "filename": "MessagesController.php", "line": "364"}, "connection": "radhe_same", "start_percent": 80.523, "width_percent": 3.109}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.543154, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 83.631, "width_percent": 1.458}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.557534, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 85.089, "width_percent": 1.733}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.563118, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 86.823, "width_percent": 1.843}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.571462, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 88.666, "width_percent": 11.334}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 551, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlhqaG0zWnJzeDU0VHFLTFFwMTlvNHc9PSIsInZhbHVlIjoiU09BbzhwVEJSbEhDY3VxNm53NnFYWnVFaGEzOHJQTHRleUFIdWkxTnBOR0dCQmUrdmNXNThuVjZvbkNUU3dMNlJMVUk0aXMycEN1czdmVUpaNDVOU21zdlpZRDNwUVQvMGJUa2UxU1F5S2JCYVJJZGx5aUNnbk9WRVcvVkd2TUtoS1JVNGVjSytua2JPTkZxcW1GQk53K214L1FDczlEcHZ3Yi9rOFdxa21yaC8zclQ5Sy90TTYyVXBxUVB4TFdsNGdmR0hreW5SWVd0N3kyTEplMXVjQ2wzbTFTRU0rOWhJQTFESVN4SHFVTDY5ZlRYcGljTEk2N1owenJiYWcyVVZlR1MzS1VZZTYvQ1FxNHJJT25GVE0xLzlGejIvSWVncDZDTFVPL1pnVmFLbnF6dXB4MWY5TS81YThNbUxLemkrR0ZDTnh0ekh1czMxd1dNTkpGZU1WRW9aaEtxUjAyV1ZEOXNSQjAwQ0hyY2JkSmQyYW5hU3VaMFpOTlpMVlZhaC8xdFd2d3VPVU9WLzFIcVN6OXhrelhOc2lnK2cya0xxSXBxUVk3TG4zd3JGeWwxanhKMnIyemozUXozdUFoaFhxTjZjdW0yUFgrOTdJS08zWGw2V2oxZHVqVGpWS1IzQWZad3c3bjF2OW1xbHl2V1doMi9hemFOcTQ4U3hQZ0EiLCJtYWMiOiIzNTRiNWI2MzY2M2NmZmM3MWE5NThjOTE1ZjgwZDE4OTgyOGIwOGZlYjQ5ZGIyMjg1NzY3NzEyY2FkMWE4NmE5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkpQbDBreUV4bzJOM01pNm5jaVdqcmc9PSIsInZhbHVlIjoiSHdQazI5dmlaajhvczFOZ2M4OVBZb1hnQm81TVAxL2dWdmtYT0poSi9zbUphS0Nqb081ZlZvZ0NmalFtdFdMMWt4QVFmRCsxU0p5S08ydUFtY0tiU1RycXNUU1BmbUx4aWh2eE5iRjRkNWJaSHBWMkk2Z0pVV09neXRJMG54M0hrZEFrQ1Nyb1ZUNDd6MGNDTDhCYWJaalludGx4TEZCTGp0SEhGWDI0dHRCQithV1pQWUVocVJjNSt1M3Y1TzdXZEZlR1h5NGVJS0V4NHpRRy9nYTV6bjFjRDIyTXdoViszY3FwT1ptTmNMcWNqMXBpcnpINmM0aFY3bjNuT3RjYkdlS2pjMDB2ak1wMzBXaEExcFlrS3hqVXJ2RzhYMVNUUkZpSjVNY3lGRFZKeDdacjliNkRrQlVoZUEvQ3hDb3FhbGRsU0c5RSt3dUtRQ0dIOThQUW1Ha3gwUVVTUGFOYXh6TmhYSHBqRDRCdk5USGt4TjJsMDRrNmVDdDU1dkVRQk14amFiT0c3V3N6cE1mYS9LODRMSFprdXl4QUZtS3g2Z2NlakZkRVk4UjhQNmovNHBQcmdNUi83ODYvSTNrLzJ0Z1FTRDcwSzhacWVKSUdIaFFaMFhXTjRsdnJKMFBpeXpHQWNBWERHV3JRcUt0VURPNDlaa013U0FBK2JpOHQiLCJtYWMiOiIxYzE0NGZmOGNhZGNiNWY3NmI1ZDk3OWNlYWI5ZDgzMjM4YTJjYmJmMDk2ZGJkN2ZmZDU3YzkxYmM2NWViMGE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">f24xnIHkwms8v0rbgfC16W0wpwmuAXK5XTVisj1V</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1858696216 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 05:17:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFYQzdKeHNoeTZTa2NpQlFwMmFKWlE9PSIsInZhbHVlIjoieXJtMTJIY25EVjZGM012RmZIc3V4NWlCTzFjNVBKUXVNb2VlbmZDdS84USt3WldYUi9wSlNSeSs1dC9XQm84Nk1uYXk1c055ZVQ3ZEZXc00vQjdmQVFoYXF6ZVJnako3Q3dLWThKNnVvdDZ0SDZvSFFQYndCaDhPZTRBQXVOei9XM3IvT2RLSGcwTUtqTzVMU1M5NWRIQU9GVHRMNnpsSjNNT1N2WTN0OVJMbDBHVHpSNFlPcnVKMDVzS2RRTTI2SmdSRWRwN3pRQ0hyUDg1OC9LMFlaUEIyUUxFa2Y1YmpneHZOY21ibmhjTlVyblVQS1Nia3Y3OWR2eXUrVElIZXVHR3FJQzl3aG1ield6eWQxOUlmMVVXVXZuSjkrUE1zUDJBVE5jclVxZDc2aTVHOEtDQ3FvR3p2dTRXckkwcUFRUFBiR2JPTWJMNVlOU3RqeFQ4WU9nc1BDV0IxYng0NXk2Y2hGQzRCeWQ4cFF5U0pUbmFFV2E3UkhJUUtzU3FZZWVmZTV4TzBYazNkZUNoWW9RNkFCcnVyWG9qOUZ6ckdVK3JETWowZmtRU09SNHVoVlY1Qm1iZVpUYkNOeXlDQXdnUzFZQVJMbEFQRHRxVGFkeWJ4WWlvdmlyRHVGQ3ovWFlGc2dmRmFJc3BVNVFNM1Z4bEs1TjBVanllM0NWcDMiLCJtYWMiOiI2Y2M0MzI5Y2ZiNjc2ZjM0YjdlNDgwOWEzN2YwMmRiNzUzZTAxY2MxNjNkOWZjY2IyZmZmNTM2YWQ0OWQ4NDY2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:17:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImEreGl6WXBCKyt5bVcxNlE2Ulg2Z3c9PSIsInZhbHVlIjoiNW5WZWgyeUZTZEdUV1BJb28zUE4xc0RXQkF5MGczemtuTnA2dTZPSmdWcmV5WkNjdW9YOWtuSyt6eDVwRE5wVDhrd0ptWnFZVGRzamZob2hDQnFsUFlFdE9kQmFzRUJjTy82eTk0MVUwV0RyOG9yYjJiS0F3V0NVN2FnQlJhdXBxdDFJYVRZRUhtMElIV3FxOGxQWXlGTE1SWGFiZHlKai8xNzQ2aFVzRGRWN0ZTamZPL0FYTkQzVFU4Z2pKZlk2TldLNzNxN09XRDkyRlVYV01teU9DZDREcGdGa0FHbWRTTlpCS1NCNjFIMmxGQ0J0dnhHMEtzUXVVdTkrN2RYZVJzU2R0TlhOaU9LRGJBTDdBbnV2Q2xrbmlmamg0c2FEQXd4dUxOMHUxcXV5Yko2ZlJMeDFjd3orYmhGYkQ5cXhVUGR6NHJ5S1p5WWs3REhuSU9lT2xHWGorZ0h6QXFOVkxzRmczV1lNeHBrNTZaREhMMTBmaVNiOGpMOVdHSWRvYTRaajR2aE1BUnZxcmZhNERzR1cvQ0dFUHpsVC9qMUxQNDZxOHg1V3ZHQ2d6TzNaZXUrYlRDUHFGZW8xS0RYQXd1SWZscTNLcGVRb3MrdkVuVkZYS084T0c2Rm15MFV0bDUzKzFUQk4vQWhIMmw3bDg0Mk9Xa2puQ2xkUTZIMjQiLCJtYWMiOiIxZTkyZmQ3OTAxMjMxMjJjYTRlYzYyZDk1NzlkZGUyZjUxMjQ5MWFhZWQwMGVjMDgyNWU4MzUyODQ2ZTE1ZWQwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 07:17:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFYQzdKeHNoeTZTa2NpQlFwMmFKWlE9PSIsInZhbHVlIjoieXJtMTJIY25EVjZGM012RmZIc3V4NWlCTzFjNVBKUXVNb2VlbmZDdS84USt3WldYUi9wSlNSeSs1dC9XQm84Nk1uYXk1c055ZVQ3ZEZXc00vQjdmQVFoYXF6ZVJnako3Q3dLWThKNnVvdDZ0SDZvSFFQYndCaDhPZTRBQXVOei9XM3IvT2RLSGcwTUtqTzVMU1M5NWRIQU9GVHRMNnpsSjNNT1N2WTN0OVJMbDBHVHpSNFlPcnVKMDVzS2RRTTI2SmdSRWRwN3pRQ0hyUDg1OC9LMFlaUEIyUUxFa2Y1YmpneHZOY21ibmhjTlVyblVQS1Nia3Y3OWR2eXUrVElIZXVHR3FJQzl3aG1ield6eWQxOUlmMVVXVXZuSjkrUE1zUDJBVE5jclVxZDc2aTVHOEtDQ3FvR3p2dTRXckkwcUFRUFBiR2JPTWJMNVlOU3RqeFQ4WU9nc1BDV0IxYng0NXk2Y2hGQzRCeWQ4cFF5U0pUbmFFV2E3UkhJUUtzU3FZZWVmZTV4TzBYazNkZUNoWW9RNkFCcnVyWG9qOUZ6ckdVK3JETWowZmtRU09SNHVoVlY1Qm1iZVpUYkNOeXlDQXdnUzFZQVJMbEFQRHRxVGFkeWJ4WWlvdmlyRHVGQ3ovWFlGc2dmRmFJc3BVNVFNM1Z4bEs1TjBVanllM0NWcDMiLCJtYWMiOiI2Y2M0MzI5Y2ZiNjc2ZjM0YjdlNDgwOWEzN2YwMmRiNzUzZTAxY2MxNjNkOWZjY2IyZmZmNTM2YWQ0OWQ4NDY2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:17:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImEreGl6WXBCKyt5bVcxNlE2Ulg2Z3c9PSIsInZhbHVlIjoiNW5WZWgyeUZTZEdUV1BJb28zUE4xc0RXQkF5MGczemtuTnA2dTZPSmdWcmV5WkNjdW9YOWtuSyt6eDVwRE5wVDhrd0ptWnFZVGRzamZob2hDQnFsUFlFdE9kQmFzRUJjTy82eTk0MVUwV0RyOG9yYjJiS0F3V0NVN2FnQlJhdXBxdDFJYVRZRUhtMElIV3FxOGxQWXlGTE1SWGFiZHlKai8xNzQ2aFVzRGRWN0ZTamZPL0FYTkQzVFU4Z2pKZlk2TldLNzNxN09XRDkyRlVYV01teU9DZDREcGdGa0FHbWRTTlpCS1NCNjFIMmxGQ0J0dnhHMEtzUXVVdTkrN2RYZVJzU2R0TlhOaU9LRGJBTDdBbnV2Q2xrbmlmamg0c2FEQXd4dUxOMHUxcXV5Yko2ZlJMeDFjd3orYmhGYkQ5cXhVUGR6NHJ5S1p5WWs3REhuSU9lT2xHWGorZ0h6QXFOVkxzRmczV1lNeHBrNTZaREhMMTBmaVNiOGpMOVdHSWRvYTRaajR2aE1BUnZxcmZhNERzR1cvQ0dFUHpsVC9qMUxQNDZxOHg1V3ZHQ2d6TzNaZXUrYlRDUHFGZW8xS0RYQXd1SWZscTNLcGVRb3MrdkVuVkZYS084T0c2Rm15MFV0bDUzKzFUQk4vQWhIMmw3bDg0Mk9Xa2puQ2xkUTZIMjQiLCJtYWMiOiIxZTkyZmQ3OTAxMjMxMjJjYTRlYzYyZDk1NzlkZGUyZjUxMjQ5MWFhZWQwMGVjMDgyNWU4MzUyODQ2ZTE1ZWQwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 07:17:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1858696216\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}